-- Olympus Progression Systems - Complete Implementation
-- Based on Olympus Altis Life rank gates, time requirements, and unlock systems

Config = {}

-- Progression System Settings
Config.ProgressionSystem = {
    enabled = true,
    
    -- Time-based progression
    timeBased = true, -- Progression based on time in faction
    activityBased = true, -- Progression based on activity level
    performanceBased = true, -- Progression based on performance metrics
    
    -- Unlock system
    unlockSystem = {
        enabled = true,
        tieredUnlocks = true, -- Unlocks are tiered by rank/time
        prerequisiteSystem = true -- Some unlocks require prerequisites
    }
}

-- APD Progression System (Exact Olympus Implementation)
Config.APDProgression = {
    enabled = true,
    
    -- Rank Progression (Exact Olympus Time Requirements)
    rankProgression = {
        [1] = { -- Patrol Officer
            name = 'Patrol Officer',
            timeRequired = 0, -- Starting rank
            requirements = {
                application = true,
                interview = true,
                training = true
            },
            unlocks = {
                equipment = {'taser', 'spar16', 'basic_vest'},
                vehicles = {'hatchback_sport', 'suv'},
                permissions = {'patrol', 'traffic_stops', 'basic_arrests'}
            }
        },
        
        [2] = { -- Corporal
            name = 'Corporal',
            timeRequired = 604800, -- 1 week (7 days)
            requirements = {
                timeInGrade = 604800,
                quotaMet = true,
                noInfractions = true
            },
            unlocks = {
                equipment = {'mx', 'carrier_rig'},
                vehicles = {'offroad_covered'},
                permissions = {'training_junior', 'evidence_processing'}
            }
        },
        
        [3] = { -- Sergeant
            name = 'Sergeant',
            timeRequired = 1209600, -- 2 weeks total (1 week as Corporal)
            requirements = {
                timeInGrade = 604800, -- 1 week as Corporal
                quotaMet = true,
                leadershipTraining = true
            },
            unlocks = {
                equipment = {'spar17', 'enhanced_vest'},
                vehicles = {'hunter'},
                permissions = {'supervise_patrol', 'authorize_raids', 'issue_warrants'}
            }
        },
        
        [4] = { -- Lieutenant
            name = 'Lieutenant',
            timeRequired = 2419200, -- 4 weeks total (2 weeks as Sergeant)
            requirements = {
                timeInGrade = 1209600, -- 2 weeks as Sergeant
                quotaMet = true,
                commandTraining = true,
                recommendationRequired = true
            },
            unlocks = {
                equipment = {'mk18', 'special_vest'},
                vehicles = {'strider'},
                permissions = {'command_operations', 'disciplinary_action', 'budget_management'}
            }
        },
        
        [5] = { -- Captain
            name = 'Captain',
            timeRequired = 4838400, -- 8 weeks total (4 weeks as Lieutenant)
            requirements = {
                timeInGrade = 2419200, -- 4 weeks as Lieutenant
                quotaMet = true,
                seniorCommandTraining = true,
                boardApproval = true
            },
            unlocks = {
                equipment = {'mk200', 'command_vest'},
                vehicles = {'blackfish', 'kajman'},
                permissions = {'strategic_planning', 'policy_creation', 'major_operations'}
            }
        },
        
        [6] = { -- Deputy Chief
            name = 'Deputy Chief',
            timeRequired = 7257600, -- 12 weeks total (4 weeks as Captain)
            requirements = {
                timeInGrade = 2419200, -- 4 weeks as Captain
                quotaMet = true,
                executiveTraining = true,
                chiefNomination = true
            },
            unlocks = {
                equipment = {'all_weapons', 'executive_vest'},
                vehicles = {'all_vehicles'},
                permissions = {'department_oversight', 'external_relations', 'crisis_management'}
            }
        },
        
        [7] = { -- Chief of Police
            name = 'Chief of Police',
            timeRequired = 9676800, -- 16 weeks total (4 weeks as Deputy Chief)
            requirements = {
                timeInGrade = 2419200, -- 4 weeks as Deputy Chief
                quotaMet = true,
                executiveApproval = true,
                communitySupport = true
            },
            unlocks = {
                equipment = {'unlimited_access'},
                vehicles = {'unlimited_access'},
                permissions = {'department_control', 'policy_authority', 'disciplinary_authority'}
            }
        }
    },
    
    -- Flight Certification System (Exact Olympus Implementation)
    flightCertification = {
        enabled = true,
        
        -- Certification Levels
        levels = {
            basic = {
                name = 'Basic Flight Certification',
                rankRequired = 2, -- Corporal+
                timeRequired = 1209600, -- 2 weeks in APD
                
                requirements = {
                    flightHours = 10, -- 10 hours flight time
                    writtenTest = true, -- Written examination
                    practicalTest = true, -- Practical flight test
                    instructorApproval = true
                },
                
                unlocks = {
                    aircraft = {'hummingbird', 'orca'},
                    permissions = {'patrol_flights', 'transport_missions'}
                }
            },
            
            advanced = {
                name = 'Advanced Flight Certification',
                rankRequired = 3, -- Sergeant+
                timeRequired = 2419200, -- 4 weeks in APD
                
                requirements = {
                    basicCertification = true,
                    flightHours = 25, -- 25 hours flight time
                    advancedTraining = true,
                    emergencyProcedures = true
                },
                
                unlocks = {
                    aircraft = {'ghosthawk', 'hellcat'},
                    permissions = {'tactical_flights', 'search_rescue'}
                }
            },
            
            combat = {
                name = 'Combat Flight Certification',
                rankRequired = 4, -- Lieutenant+
                timeRequired = 4838400, -- 8 weeks in APD
                
                requirements = {
                    advancedCertification = true,
                    flightHours = 50, -- 50 hours flight time
                    combatTraining = true,
                    weaponsQualification = true
                },
                
                unlocks = {
                    aircraft = {'kajman', 'blackfish'},
                    permissions = {'combat_operations', 'federal_events'}
                }
            }
        }
    },
    
    -- Specialization Tracks (Exact Olympus Implementation)
    specializations = {
        enabled = true,
        
        -- Detective Track
        detective = {
            name = 'Detective Division',
            rankRequired = 3, -- Sergeant+
            timeRequired = 2419200, -- 4 weeks in APD
            
            requirements = {
                investigationTraining = true,
                caseloadManagement = true,
                evidenceHandling = true
            },
            
            unlocks = {
                equipment = {'undercover_gear', 'surveillance_equipment'},
                permissions = {'undercover_operations', 'investigation_lead', 'evidence_analysis'}
            }
        },
        
        -- SWAT Track
        swat = {
            name = 'SWAT Division',
            rankRequired = 3, -- Sergeant+
            timeRequired = 2419200, -- 4 weeks in APD
            
            requirements = {
                tacticalTraining = true,
                physicalFitness = true,
                marksmanship = true,
                teamworkAssessment = true
            },
            
            unlocks = {
                equipment = {'tactical_gear', 'breaching_equipment', 'sniper_rifles'},
                permissions = {'high_risk_operations', 'federal_response', 'hostage_rescue'}
            }
        },
        
        -- Traffic Division
        traffic = {
            name = 'Traffic Division',
            rankRequired = 2, -- Corporal+
            timeRequired = 1209600, -- 2 weeks in APD
            
            requirements = {
                trafficLawTraining = true,
                accidentInvestigation = true,
                pursuitTraining = true
            },
            
            unlocks = {
                equipment = {'speed_radar', 'breathalyzer', 'traffic_cones'},
                vehicles = {'interceptor_vehicles'},
                permissions = {'traffic_enforcement', 'pursuit_authorization', 'accident_investigation'}
            }
        }
    }
}

-- R&R Progression System (Exact Olympus Implementation)
Config.RnRProgression = {
    enabled = true,
    
    -- Rank Progression (Exact Olympus Time Requirements)
    rankProgression = {
        [1] = { -- EMT
            name = 'Emergency Medical Technician',
            timeRequired = 0, -- Starting rank
            requirements = {
                application = true,
                medicalTraining = true,
                firstAidCertification = true
            },
            unlocks = {
                equipment = {'basic_medical', 'emt_uniform'},
                vehicles = {'ambulance'},
                permissions = {'basic_medical_care', 'patient_transport'}
            }
        },
        
        [2] = { -- Paramedic
            name = 'Paramedic',
            timeRequired = 604800, -- 1 week (7 days)
            requirements = {
                timeInGrade = 604800,
                reviveQuota = true,
                noComplaints = true
            },
            unlocks = {
                equipment = {'advanced_medical', 'paramedic_uniform'},
                vehicles = {'advanced_ambulance'},
                permissions = {'advanced_medical_care', 'medication_administration'}
            }
        },
        
        [3] = { -- Senior Paramedic
            name = 'Senior Paramedic',
            timeRequired = 1209600, -- 2 weeks total
            requirements = {
                timeInGrade = 604800, -- 1 week as Paramedic
                reviveQuota = true,
                trainingCompleted = true
            },
            unlocks = {
                equipment = {'senior_medical', 'supervisor_uniform'},
                vehicles = {'supervisor_vehicle'},
                permissions = {'supervise_junior', 'training_authority', 'quality_assurance'}
            }
        },
        
        [4] = { -- Flight Paramedic
            name = 'Flight Paramedic',
            timeRequired = 2419200, -- 4 weeks total
            requirements = {
                timeInGrade = 1209600, -- 2 weeks as Senior Paramedic
                flightTraining = true,
                flightCertification = true
            },
            unlocks = {
                equipment = {'flight_medical', 'flight_suit'},
                vehicles = {'medical_helicopter'},
                permissions = {'air_medical_transport', 'emergency_flight_operations'}
            }
        },
        
        [5] = { -- Supervisor
            name = 'Supervisor',
            timeRequired = 3628800, -- 6 weeks total
            requirements = {
                timeInGrade = 1209600, -- 2 weeks as Flight Paramedic
                leadershipTraining = true,
                managementSkills = true
            },
            unlocks = {
                equipment = {'supervisor_gear', 'command_uniform'},
                vehicles = {'command_vehicle'},
                permissions = {'shift_supervision', 'disciplinary_action', 'resource_management'}
            }
        },
        
        [6] = { -- Assistant Chief
            name = 'Assistant Chief',
            timeRequired = 6048000, -- 10 weeks total
            requirements = {
                timeInGrade = 2419200, -- 4 weeks as Supervisor
                executiveTraining = true,
                chiefRecommendation = true
            },
            unlocks = {
                equipment = {'executive_gear', 'assistant_chief_uniform'},
                vehicles = {'executive_vehicle'},
                permissions = {'department_oversight', 'policy_development', 'external_coordination'}
            }
        },
        
        [7] = { -- Chief
            name = 'Chief of Emergency Medical Services',
            timeRequired = 8467200, -- 14 weeks total
            requirements = {
                timeInGrade = 2419200, -- 4 weeks as Assistant Chief
                boardApproval = true,
                communitySupport = true
            },
            unlocks = {
                equipment = {'unlimited_access'},
                vehicles = {'unlimited_access'},
                permissions = {'department_control', 'policy_authority', 'strategic_planning'}
            }
        }
    },
    
    -- Medical Certifications (Exact Olympus Implementation)
    certifications = {
        enabled = true,
        
        -- Basic Life Support
        bls = {
            name = 'Basic Life Support',
            rankRequired = 1, -- EMT+
            timeRequired = 0, -- Available immediately
            
            requirements = {
                writtenTest = true,
                practicalTest = true,
                cprCertification = true
            },
            
            unlocks = {
                procedures = {'basic_cpr', 'wound_care', 'patient_assessment'},
                equipment = {'basic_medical_kit'}
            }
        },
        
        -- Advanced Life Support
        als = {
            name = 'Advanced Life Support',
            rankRequired = 2, -- Paramedic+
            timeRequired = 604800, -- 1 week in R&R
            
            requirements = {
                blsCertification = true,
                advancedTraining = true,
                clinicalHours = 40
            },
            
            unlocks = {
                procedures = {'advanced_airway', 'iv_therapy', 'cardiac_monitoring'},
                equipment = {'advanced_medical_kit', 'cardiac_monitor'}
            }
        },
        
        -- Critical Care Transport
        cct = {
            name = 'Critical Care Transport',
            rankRequired = 4, -- Flight Paramedic+
            timeRequired = 2419200, -- 4 weeks in R&R
            
            requirements = {
                alsCertification = true,
                flightTraining = true,
                criticalCareTraining = true
            },
            
            unlocks = {
                procedures = {'ventilator_management', 'critical_medications', 'advanced_monitoring'},
                equipment = {'critical_care_kit', 'transport_ventilator'}
            }
        }
    }
}

-- Civilian Progression System (Exact Olympus Implementation)
Config.CivilianProgression = {
    enabled = true,

    -- Skill-Based Progression
    skillProgression = {
        enabled = true,

        -- Legal Job Skills
        legalJobs = {
            mining = {
                name = 'Mining Expertise',
                maxLevel = 10,

                -- Level Benefits
                levelBenefits = {
                    [1] = {xpRequired = 0, bonus = 0.0, unlocks = {}},
                    [2] = {xpRequired = 100, bonus = 0.05, unlocks = {'faster_mining'}},
                    [3] = {xpRequired = 250, bonus = 0.10, unlocks = {'ore_detection'}},
                    [4] = {xpRequired = 500, bonus = 0.15, unlocks = {'quality_bonus'}},
                    [5] = {xpRequired = 1000, bonus = 0.20, unlocks = {'rare_ore_chance'}},
                    [6] = {xpRequired = 1750, bonus = 0.25, unlocks = {'batch_processing'}},
                    [7] = {xpRequired = 2750, bonus = 0.30, unlocks = {'equipment_durability'}},
                    [8] = {xpRequired = 4000, bonus = 0.35, unlocks = {'master_techniques'}},
                    [9] = {xpRequired = 6000, bonus = 0.40, unlocks = {'legendary_finds'}},
                    [10] = {xpRequired = 10000, bonus = 0.50, unlocks = {'mining_mastery'}}
                },

                -- XP Sources
                xpSources = {
                    oreCollected = 1, -- 1 XP per ore collected
                    processedMaterial = 2, -- 2 XP per processed material
                    perfectRun = 50, -- 50 XP for perfect run (no failures)
                    rareFind = 25 -- 25 XP for rare material find
                }
            },

            fishing = {
                name = 'Fishing Expertise',
                maxLevel = 10,

                levelBenefits = {
                    [1] = {xpRequired = 0, bonus = 0.0, unlocks = {}},
                    [2] = {xpRequired = 150, bonus = 0.05, unlocks = {'better_bait'}},
                    [3] = {xpRequired = 350, bonus = 0.10, unlocks = {'fish_detection'}},
                    [4] = {xpRequired = 650, bonus = 0.15, unlocks = {'larger_catches'}},
                    [5] = {xpRequired = 1200, bonus = 0.20, unlocks = {'rare_fish_chance'}},
                    [6] = {xpRequired = 2000, bonus = 0.25, unlocks = {'weather_prediction'}},
                    [7] = {xpRequired = 3200, bonus = 0.30, unlocks = {'deep_water_access'}},
                    [8] = {xpRequired = 4800, bonus = 0.35, unlocks = {'commercial_techniques'}},
                    [9] = {xpRequired = 7200, bonus = 0.40, unlocks = {'legendary_catches'}},
                    [10] = {xpRequired = 12000, bonus = 0.50, unlocks = {'fishing_mastery'}}
                },

                xpSources = {
                    fishCaught = 2, -- 2 XP per fish caught
                    processedFish = 3, -- 3 XP per processed fish
                    rareCatch = 50, -- 50 XP for rare fish
                    perfectTrip = 75 -- 75 XP for perfect fishing trip
                }
            },

            trucking = {
                name = 'Commercial Driving',
                maxLevel = 10,

                levelBenefits = {
                    [1] = {xpRequired = 0, bonus = 0.0, unlocks = {}},
                    [2] = {xpRequired = 75, bonus = 0.05, unlocks = {'fuel_efficiency'}},
                    [3] = {xpRequired = 200, bonus = 0.10, unlocks = {'route_optimization'}},
                    [4] = {xpRequired = 400, bonus = 0.15, unlocks = {'cargo_handling'}},
                    [5] = {xpRequired = 750, bonus = 0.20, unlocks = {'hazmat_certification'}},
                    [6] = {xpRequired = 1300, bonus = 0.25, unlocks = {'oversized_loads'}},
                    [7] = {xpRequired = 2100, bonus = 0.30, unlocks = {'priority_contracts'}},
                    [8] = {xpRequired = 3200, bonus = 0.35, unlocks = {'international_routes'}},
                    [9] = {xpRequired = 4800, bonus = 0.40, unlocks = {'fleet_management'}},
                    [10] = {xpRequired = 8000, bonus = 0.50, unlocks = {'trucking_mastery'}}
                },

                xpSources = {
                    deliveryCompleted = 10, -- 10 XP per delivery
                    onTimeDelivery = 15, -- 15 XP for on-time delivery
                    noDamageBonus = 20, -- 20 XP for no damage delivery
                    longHaulBonus = 25 -- 25 XP for long-haul deliveries
                }
            }
        },

        -- Criminal Skills
        criminalSkills = {
            lockpicking = {
                name = 'Lockpicking Expertise',
                maxLevel = 10,

                levelBenefits = {
                    [1] = {xpRequired = 0, successRate = 0.5, unlocks = {}},
                    [2] = {xpRequired = 50, successRate = 0.55, unlocks = {'basic_techniques'}},
                    [3] = {xpRequired = 125, successRate = 0.60, unlocks = {'tool_efficiency'}},
                    [4] = {xpRequired = 250, successRate = 0.65, unlocks = {'advanced_picks'}},
                    [5] = {xpRequired = 450, successRate = 0.70, unlocks = {'silent_entry'}},
                    [6] = {xpRequired = 750, successRate = 0.75, unlocks = {'electronic_locks'}},
                    [7] = {xpRequired = 1200, successRate = 0.80, unlocks = {'master_techniques'}},
                    [8] = {xpRequired = 1800, successRate = 0.85, unlocks = {'security_bypass'}},
                    [9] = {xpRequired = 2700, successRate = 0.90, unlocks = {'legendary_skills'}},
                    [10] = {xpRequired = 4500, successRate = 0.95, unlocks = {'lockpicking_mastery'}}
                },

                xpSources = {
                    successfulPick = 5, -- 5 XP per successful lockpick
                    difficultLock = 10, -- 10 XP for difficult locks
                    noBreakage = 15, -- 15 XP for not breaking tools
                    speedBonus = 20 -- 20 XP for fast completion
                }
            },

            hacking = {
                name = 'Hacking Expertise',
                maxLevel = 10,

                levelBenefits = {
                    [1] = {xpRequired = 0, successRate = 0.3, unlocks = {}},
                    [2] = {xpRequired = 75, successRate = 0.35, unlocks = {'basic_scripts'}},
                    [3] = {xpRequired = 175, successRate = 0.40, unlocks = {'system_analysis'}},
                    [4] = {xpRequired = 350, successRate = 0.45, unlocks = {'encryption_bypass'}},
                    [5] = {xpRequired = 600, successRate = 0.50, unlocks = {'network_intrusion'}},
                    [6] = {xpRequired = 1000, successRate = 0.55, unlocks = {'advanced_exploits'}},
                    [7] = {xpRequired = 1600, successRate = 0.60, unlocks = {'stealth_techniques'}},
                    [8] = {xpRequired = 2400, successRate = 0.65, unlocks = {'zero_day_exploits'}},
                    [9] = {xpRequired = 3600, successRate = 0.70, unlocks = {'elite_hacking'}},
                    [10] = {xpRequired = 6000, successRate = 0.80, unlocks = {'hacking_mastery'}}
                },

                xpSources = {
                    successfulHack = 10, -- 10 XP per successful hack
                    complexSystem = 20, -- 20 XP for complex systems
                    undetectedHack = 30, -- 30 XP for undetected hacks
                    federalSystem = 50 -- 50 XP for federal systems
                }
            }
        }
    }
}
