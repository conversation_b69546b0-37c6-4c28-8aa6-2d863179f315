-- Olympus Items - Tools Client
-- Utility tools like fire axe, slim jim, spike strips, etc.

local isUsingTool = false
local deployedSpikeStrips = {}
local deployedGoKart = nil

-- Use Fire Axe function
function UseFireAxe(target)
    if isUsingTool then
        exports['olympus-items']:ShowNotification("You are already using a tool!", "error")
        return false
    end
    
    local ped = PlayerPedId()
    local door = target or GetNearbyDoor(ped)
    
    if not door then
        exports['olympus-items']:ShowNotification("No door found nearby!", "error")
        return false
    end
    
    -- Check distance
    local playerPos = GetEntityCoords(ped)
    local doorPos = GetEntityCoords(door)
    local distance = #(playerPos - doorPos)
    
    if distance > 3.0 then
        exports['olympus-items']:ShowNotification("You are too far from the door!", "error")
        return false
    end
    
    -- Start fire axe process
    return StartFireAxeProcess(door)
end

-- Get nearby door
function GetNearbyDoor(ped)
    local playerPos = GetEntityCoords(ped)
    local forward = GetEntityForwardVector(ped)
    local destination = playerPos + forward * 3.0
    
    local rayHandle = StartExpensiveSynchronousShapeTestLosProbe(
        playerPos.x, playerPos.y, playerPos.z,
        destination.x, destination.y, destination.z,
        1, ped, 0
    )
    
    local _, hit, _, _, entity = GetShapeTestResult(rayHandle)
    
    if hit and IsEntityABuilding(entity) then
        return entity
    end
    
    return nil
end

-- Check if entity is a building
function IsEntityABuilding(entity)
    return DoesEntityExist(entity) and not IsEntityAVehicle(entity) and not IsPedAPlayer(entity)
end

-- Start fire axe process
function StartFireAxeProcess(door)
    isUsingTool = true
    
    local ped = PlayerPedId()
    
    -- Play axe swinging animation
    local animDict = "melee@large_wpn@streamed_core"
    local animName = "ground_attack_on_spot"
    
    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(10)
    end
    
    TaskPlayAnim(ped, animDict, animName, 8.0, -8.0, 5000, 1, 0, false, false, false)
    
    -- Show progress bar
    local progressConfig = {
        label = "Breaking down door...",
        duration = 15000,
        useWhileDead = false,
        canCancel = true
    }
    
    exports['olympus-items']:ShowProgressBar(progressConfig, function()
        -- Progress completed
        CompleteFireAxeUse(door)
    end, function()
        -- Progress cancelled
        CancelToolUse()
    end)
    
    return true
end

-- Complete fire axe use
function CompleteFireAxeUse(door)
    isUsingTool = false
    
    -- Clear animation
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    -- Break down door
    TriggerServerEvent('olympus-items:server:breakDownDoor', {
        door = NetworkGetNetworkIdFromEntity(door),
        location = GetEntityCoords(ped)
    })
    
    -- Reduce durability
    TriggerServerEvent('olympus-items:server:reduceDurability', 'fireaxe')
    
    exports['olympus-items']:ShowNotification("Door broken down!", "success")
end

-- Use Slim Jim function
function UseSlimJim(target)
    if isUsingTool then
        exports['olympus-items']:ShowNotification("You are already using a tool!", "error")
        return false
    end
    
    local ped = PlayerPedId()
    local vehicle = target or GetVehiclePedIsLookingAt(ped)
    
    if not vehicle or vehicle == 0 then
        exports['olympus-items']:ShowNotification("No vehicle found!", "error")
        return false
    end
    
    -- Check if vehicle is locked
    if GetVehicleDoorLockStatus(vehicle) == 1 then
        exports['olympus-items']:ShowNotification("This vehicle is already unlocked!", "error")
        return false
    end
    
    -- Check distance
    local playerPos = GetEntityCoords(ped)
    local vehiclePos = GetEntityCoords(vehicle)
    local distance = #(playerPos - vehiclePos)
    
    if distance > 3.0 then
        exports['olympus-items']:ShowNotification("You are too far from the vehicle!", "error")
        return false
    end
    
    -- Start slim jim process
    return StartSlimJimProcess(vehicle)
end

-- Get vehicle player is looking at
function GetVehiclePedIsLookingAt(ped)
    local coords = GetEntityCoords(ped)
    local forward = GetEntityForwardVector(ped)
    local destination = coords + forward * 5.0
    
    local rayHandle = StartExpensiveSynchronousShapeTestLosProbe(
        coords.x, coords.y, coords.z,
        destination.x, destination.y, destination.z,
        10, ped, 0
    )
    
    local _, hit, _, _, entity = GetShapeTestResult(rayHandle)
    
    if hit and IsEntityAVehicle(entity) then
        return entity
    end
    
    return nil
end

-- Start slim jim process
function StartSlimJimProcess(vehicle)
    isUsingTool = true
    
    local ped = PlayerPedId()
    
    -- Play lockpicking animation
    exports['olympus-items']:PlayAnimation(Config.Animations.lockpick)
    
    -- Show progress bar
    local progressConfig = {
        label = "Using slim jim...",
        duration = 8000,
        useWhileDead = false,
        canCancel = true
    }
    
    exports['olympus-items']:ShowProgressBar(progressConfig, function()
        -- Progress completed
        CompleteSlimJimUse(vehicle)
    end, function()
        -- Progress cancelled
        CancelToolUse()
    end)
    
    return true
end

-- Complete slim jim use
function CompleteSlimJimUse(vehicle)
    isUsingTool = false
    
    -- Clear animation
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    -- Roll for success
    local successChance = Config.Items.slimjim.successChance
    local roll = math.random(1, 100)
    
    if roll <= successChance then
        -- Success - unlock vehicle
        SetVehicleDoorsLocked(vehicle, 1)
        
        TriggerServerEvent('olympus-items:server:slimJimSuccess', {
            vehicle = NetworkGetNetworkIdFromEntity(vehicle),
            plate = GetVehicleNumberPlateText(vehicle)
        })
        
        exports['olympus-items']:ShowNotification("Vehicle unlocked!", "success")
    else
        -- Failure
        TriggerServerEvent('olympus-items:server:slimJimFailed', {
            vehicle = NetworkGetNetworkIdFromEntity(vehicle),
            location = GetEntityCoords(ped)
        })
        
        exports['olympus-items']:ShowNotification("Failed to unlock vehicle!", "error")
    end
    
    -- Consume item
    TriggerServerEvent('olympus-items:server:consumeItem', 'slimjim', roll <= successChance)
end

-- Use Spike Strip function
function UseSpikeStrip()
    if isUsingTool then
        exports['olympus-items']:ShowNotification("You are already using a tool!", "error")
        return false
    end
    
    local ped = PlayerPedId()
    local playerPos = GetEntityCoords(ped)
    
    -- Check if on road
    if not IsOnRoad(playerPos) then
        exports['olympus-items']:ShowNotification("You must be on a road to deploy spike strips!", "error")
        return false
    end
    
    -- Check for nearby spike strips
    if HasNearbySpikeStrip(playerPos) then
        exports['olympus-items']:ShowNotification("There are already spike strips nearby!", "error")
        return false
    end
    
    -- Start spike strip deployment
    return StartSpikeStripDeployment()
end

-- Check if position is on road
function IsOnRoad(position)
    local roadNode = GetClosestRoad(position.x, position.y, position.z, 1, 1, false)
    return roadNode ~= 0
end

-- Check for nearby spike strips
function HasNearbySpikeStrip(position)
    for _, spikeStrip in pairs(deployedSpikeStrips) do
        if DoesEntityExist(spikeStrip.entity) then
            local spikePos = GetEntityCoords(spikeStrip.entity)
            if #(position - spikePos) < 20.0 then
                return true
            end
        end
    end
    return false
end

-- Start spike strip deployment
function StartSpikeStripDeployment()
    isUsingTool = true
    
    local ped = PlayerPedId()
    
    -- Play deployment animation
    exports['olympus-items']:PlayAnimation(Config.Animations.planting)
    
    -- Show progress bar
    local progressConfig = {
        label = "Deploying spike strip...",
        duration = 5000,
        useWhileDead = false,
        canCancel = true
    }
    
    exports['olympus-items']:ShowProgressBar(progressConfig, function()
        -- Progress completed
        CompleteSpikeStripDeployment()
    end, function()
        -- Progress cancelled
        CancelToolUse()
    end)
    
    return true
end

-- Complete spike strip deployment
function CompleteSpikeStripDeployment()
    isUsingTool = false
    
    -- Clear animation
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    local playerPos = GetEntityCoords(ped)
    local heading = GetEntityHeading(ped)
    
    -- Create spike strip object
    local spikeStripModel = GetHashKey("p_ld_stinger_s")
    RequestModel(spikeStripModel)
    while not HasModelLoaded(spikeStripModel) do
        Wait(10)
    end
    
    local spikeStrip = CreateObject(spikeStripModel, playerPos.x, playerPos.y, playerPos.z, true, true, true)
    SetEntityHeading(spikeStrip, heading)
    PlaceObjectOnGroundProperly(spikeStrip)
    
    -- Add to deployed spike strips
    local spikeData = {
        entity = spikeStrip,
        deployTime = GetGameTimer(),
        duration = Config.Items.spikestrip.duration * 1000
    }
    table.insert(deployedSpikeStrips, spikeData)
    
    -- Start spike strip monitoring
    StartSpikeStripMonitoring(spikeData)
    
    -- Consume item
    TriggerServerEvent('olympus-items:server:consumeItem', 'spikestrip', true)
    
    exports['olympus-items']:ShowNotification("Spike strip deployed!", "success")
end

-- Start spike strip monitoring
function StartSpikeStripMonitoring(spikeData)
    CreateThread(function()
        while DoesEntityExist(spikeData.entity) and GetGameTimer() - spikeData.deployTime < spikeData.duration do
            local spikePos = GetEntityCoords(spikeData.entity)
            local nearbyVehicles = GetNearbyVehicles(spikePos, 3.0)
            
            for _, vehicle in ipairs(nearbyVehicles) do
                if GetEntitySpeed(vehicle) > 5.0 then -- Only affect moving vehicles
                    -- Damage vehicle tires
                    for i = 0, 7 do
                        SetVehicleTyreBurst(vehicle, i, true, 1000.0)
                    end
                    
                    -- Notify server
                    TriggerServerEvent('olympus-items:server:spikeStripHit', {
                        vehicle = NetworkGetNetworkIdFromEntity(vehicle),
                        spikeStrip = NetworkGetNetworkIdFromEntity(spikeData.entity)
                    })
                    
                    -- Remove spike strip after use
                    DeleteEntity(spikeData.entity)
                    break
                end
            end
            
            Wait(100)
        end
        
        -- Remove spike strip after duration
        if DoesEntityExist(spikeData.entity) then
            DeleteEntity(spikeData.entity)
        end
        
        -- Remove from deployed list
        for i, spike in ipairs(deployedSpikeStrips) do
            if spike.entity == spikeData.entity then
                table.remove(deployedSpikeStrips, i)
                break
            end
        end
    end)
end

-- Get nearby vehicles
function GetNearbyVehicles(position, radius)
    local vehicles = {}
    local handle, vehicle = FindFirstVehicle()
    local success
    
    repeat
        local vehiclePos = GetEntityCoords(vehicle)
        if #(position - vehiclePos) <= radius then
            table.insert(vehicles, vehicle)
        end
        success, vehicle = FindNextVehicle(handle)
    until not success
    
    EndFindVehicle(handle)
    return vehicles
end

-- Use Pocket Go-Kart function
function UsePocketGoKart()
    if isUsingTool then
        exports['olympus-items']:ShowNotification("You are already using a tool!", "error")
        return false
    end
    
    if deployedGoKart and DoesEntityExist(deployedGoKart) then
        exports['olympus-items']:ShowNotification("You already have a go-kart deployed!", "error")
        return false
    end
    
    local ped = PlayerPedId()
    local playerPos = GetEntityCoords(ped)
    
    -- Check if there's enough space
    if not HasEnoughSpace(playerPos) then
        exports['olympus-items']:ShowNotification("Not enough space to deploy go-kart!", "error")
        return false
    end
    
    -- Start go-kart deployment
    return StartGoKartDeployment()
end

-- Check if there's enough space
function HasEnoughSpace(position)
    local nearbyVehicles = GetNearbyVehicles(position, 5.0)
    return #nearbyVehicles == 0
end

-- Start go-kart deployment
function StartGoKartDeployment()
    isUsingTool = true
    
    local ped = PlayerPedId()
    
    -- Play deployment animation
    exports['olympus-items']:PlayAnimation(Config.Animations.planting)
    
    -- Show progress bar
    local progressConfig = {
        label = "Deploying go-kart...",
        duration = 3000,
        useWhileDead = false,
        canCancel = true
    }
    
    exports['olympus-items']:ShowProgressBar(progressConfig, function()
        -- Progress completed
        CompleteGoKartDeployment()
    end, function()
        -- Progress cancelled
        CancelToolUse()
    end)
    
    return true
end

-- Complete go-kart deployment
function CompleteGoKartDeployment()
    isUsingTool = false
    
    -- Clear animation
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    local playerPos = GetEntityCoords(ped)
    local heading = GetEntityHeading(ped)
    
    -- Create go-kart vehicle
    local goKartModel = GetHashKey("veto") -- Using veto as go-kart
    RequestModel(goKartModel)
    while not HasModelLoaded(goKartModel) do
        Wait(10)
    end
    
    deployedGoKart = CreateVehicle(goKartModel, playerPos.x + 2.0, playerPos.y, playerPos.z, heading, true, false)
    SetEntityAsMissionEntity(deployedGoKart, true, true)
    
    -- Give player keys
    TriggerServerEvent('olympus-items:server:giveVehicleKeys', NetworkGetNetworkIdFromEntity(deployedGoKart))
    
    -- Consume item
    TriggerServerEvent('olympus-items:server:consumeItem', 'pocketgokart', true)
    
    exports['olympus-items']:ShowNotification("Go-kart deployed!", "success")
end

-- Cancel tool use
function CancelToolUse()
    isUsingTool = false
    
    -- Clear animation
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    exports['olympus-items']:ShowNotification("Tool use cancelled!", "info")
end

-- Export functions
exports('UseFireAxe', UseFireAxe)
exports('UseSlimJim', UseSlimJim)
exports('UseSpikeStrip', UseSpikeStrip)
exports('UsePocketGoKart', UsePocketGoKart)

-- Event handlers
RegisterNetEvent('olympus-items:client:toolInterrupted', function()
    if isUsingTool then
        CancelToolUse()
    end
end)
