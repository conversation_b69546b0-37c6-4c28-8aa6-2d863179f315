fx_version 'cerulean'
game 'gta5'

name 'Olympus Civilian Jobs'
description 'Complete civilian job system based on original Olympus fn_gather.sqf and fn_processAction.sqf'
author 'Olympus Development Team'
version '2.0.0'

-- Dependencies
dependencies {
    'olympus-core',
    'olympus-licenses',
    'olympus-gangs',
    'olympus-economy'
}

-- Shared scripts
shared_scripts {
    'config/shared.lua'
}

-- Client scripts
client_scripts {
    'client/main.lua'
}

-- Server scripts
server_scripts {
    'server/main.lua'
}

-- Client exports
exports {
    'GetPlayerJob',
    'StartJob',
    'EndJob',
    'GetJobProgress',
    'IsPlayerWorking',
    'GetResourceZones',
    'GetProcessingLocations'
}

-- Server exports
server_exports {
    'RegisterJob',
    'ProcessJobCompletion',
    'GetJobStatistics',
    'GetResourceZones',
    'GetProcessingConfig'
}
