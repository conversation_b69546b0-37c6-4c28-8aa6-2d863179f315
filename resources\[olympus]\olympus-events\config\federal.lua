-- Olympus Federal Events - Exact Replication
-- Complete implementation of all Olympus federal events with exact mechanics

Config = {}
Config.FederalEvents = {}

-- Federal Reserve Bank (Primary Federal Event)
Config.FederalReserve = {
    enabled = true,
    name = 'Federal Reserve Bank',
    location = vector3(243.2, 225.5, 106.3),

    -- Exact Olympus Requirements
    requirements = {
        minAPD = 4, -- Minimum 4 APD online to start
        items = {'blasting_charge'}, -- Requires blasting charge to start
        gang = true -- Must be in a gang to participate
    },

    -- Exact Olympus Timers
    timers = {
        bombTimer = 1200, -- 20 minutes exactly
        defuseTime = 30, -- 30 seconds to defuse
        plantTime = 15, -- 15 seconds to plant
        cooldownSuccess = 3000, -- 50 minutes after success
        cooldownFailure = 2100 -- 35 minutes after failure/defuse
    },

    -- Gold Bar Rewards (Exact Olympus Scaling)
    rewards = {
        goldBars = {
            -- Exact scaling based on APD count online
            scaling = {
                [4] = 150,   [5] = 175,   [6] = 200,   [7] = 225,
                [8] = 250,   [9] = 275,   [10] = 300,  [11] = 325,
                [12] = 350,  [13] = 375,  [14] = 400,  [15] = 425
            },
            maxBars = 425, -- Maximum possible (15+ APD)
            baseValue = 2500, -- $2,500 per gold bar
            sellLocations = {
                vector3(-1526.9, -1447.1, 1.8), -- Gold Trader 1
                vector3(1441.5, 3618.2, 35.0),  -- Gold Trader 2
                vector3(-622.8, -230.8, 38.1)   -- Gold Trader 3
            }
        }
    },

    -- Anti-Air System (Exact Olympus Implementation)
    antiAir = {
        enabled = true,
        radius = 1500, -- 1.5km radius from fed
        hackingTerminal = {
            location = vector3(220.5, 215.8, 106.3),
            hackTime = 45, -- 45 seconds to hack
            items = {'hacking_device'},
            disableTime = 300 -- 5 minutes disabled after hack
        },
        missiles = {
            damage = 100, -- Instant kill
            lockTime = 3, -- 3 seconds lock-on time
            range = 2000 -- 2km range
        }
    },

    -- Wave Rules (Exact Olympus APD Response Rules)
    waveRules = {
        enabled = true,
        maxWaves = 999, -- Unlimited waves until resolved
        waveDelay = 30, -- 30 seconds between waves
        mustReturnToHQ = true, -- Must return to HQ between waves
        exceptions = {
            pursuit = true, -- Can re-enter if pursuing suspect
            firstWaveBackup = true -- First wave can call backup
        }
    },

    -- KOS Zones (Exact Olympus Kill-On-Sight Rules)
    kosZones = {
        -- Inner dome (immediate KOS)
        innerDome = {
            center = vector3(243.2, 225.5, 106.3),
            radius = 150,
            kosActive = true, -- No RP required to engage
            description = 'Federal Reserve Inner Dome'
        },
        -- Outer anti-air circle
        outerCircle = {
            center = vector3(243.2, 225.5, 106.3),
            radius = 1500,
            kosActive = false, -- RP required outside inner dome
            description = 'Federal Reserve Anti-Air Zone'
        }
    },

    -- APD Response Rules (Exact Olympus Procedures)
    apdRules = {
        priority = 1, -- Highest priority federal event
        responseRequired = true, -- APD must respond if 4+ online
        lethalsAuthorized = true, -- Lethals authorized immediately
        equipmentRestrictions = {
            noVans = true, -- No APD vans allowed
            noQilinSmoke = false, -- SGT+ can use Qilin Smoke
            deputyLethals = true -- Deputies can use lethals at fed
        }
    }
}

-- Blackwater Armory (Second Priority Federal Event)
Config.BlackwaterArmory = {
    enabled = true,
    name = 'Blackwater Armory',
    location = vector3(2447.9, 1576.9, 33.0),

    -- Exact Olympus Requirements
    requirements = {
        minAPD = 3, -- Minimum 3 APD online
        items = {'blasting_charge'},
        gang = true
    },

    -- Exact Olympus Timers
    timers = {
        bombTimer = 900, -- 15 minutes
        defuseTime = 30,
        plantTime = 15,
        cooldownSuccess = 2400, -- 40 minutes after success
        cooldownFailure = 1800 -- 30 minutes after failure
    },

    -- Blackwater Rewards (High-End Military Equipment)
    rewards = {
        weapons = {
            -- Exact Olympus Blackwater weapons
            'spar_16_bw', 'mk20_bw', 'mx_bw', 'mxc_bw', 'mxm_bw',
            'katiba_bw', 'katiba_c_bw', 'mk18_bw', 'spmg_bw',
            'navid_bw', 'lynx_bw', 'mar10_bw'
        },
        items = {
            -- Exact Olympus Blackwater items
            'nvg_bw', 'rangefinder_bw', 'toolkit_bw',
            'defusal_kit_bw', 'lockpick_bw'
        },
        vehicles = {
            -- Exact Olympus Blackwater vehicles
            'ifrit_bw', 'strider_bw', 'hunter_bw'
        },
        scaling = {
            -- Rewards scale with APD count
            [3] = {weapons = 15, items = 10, vehicles = 1},
            [4] = {weapons = 20, items = 15, vehicles = 1},
            [5] = {weapons = 25, items = 20, vehicles = 2},
            [6] = {weapons = 30, items = 25, vehicles = 2},
            [7] = {weapons = 35, items = 30, vehicles = 2},
            [8] = {weapons = 40, items = 35, vehicles = 3}
        }
    }
}

-- Evidence Lockup (Blue Zone Federal Event)
Config.EvidenceLockup = {
    enabled = true,
    name = 'Evidence Lockup',
    location = vector3(425.1, -979.5, 30.7), -- LSPD HQ

    -- Exact Olympus Requirements
    requirements = {
        minAPD = 3,
        items = {'hacking_device'},
        gang = true
    },

    -- Exact Olympus Timers (Blue Zone Rules)
    timers = {
        initialTimer = 240, -- 4 minutes initial
        checkpointTimer = 150, -- 2:30 per checkpoint
        totalCheckpoints = 3,
        cooldownSuccess = 1800, -- 30 minutes
        cooldownFailure = 1200 -- 20 minutes
    },

    -- Evidence Rewards
    rewards = {
        evidence = {
            'seized_weapons', 'seized_drugs', 'seized_money',
            'case_files', 'witness_statements', 'forensic_evidence'
        },
        money = {
            base = 50000, -- Base $50k
            perAPD = 10000 -- +$10k per APD online
        }
    },

    -- Blue Zone Rules (No Wave Rule)
    blueZone = {
        enabled = true,
        noWaveRule = true, -- APD doesn't need to follow wave rule
        engagementRequired = true -- Must engage before shooting
    }
}

-- Jail Break (Prison Federal Event)
Config.JailBreak = {
    enabled = true,
    name = 'Jail Break',
    location = vector3(1845.9, 2585.9, 46.0), -- Prison

    -- Exact Olympus Requirements
    requirements = {
        minAPD = 2, -- Only 2 APD required
        items = {'blasting_charge'},
        gang = true,
        prisonersRequired = true -- Must have gang members in prison
    },

    -- Exact Olympus Timers
    timers = {
        bombTimer = 600, -- 10 minutes
        defuseTime = 30,
        plantTime = 15,
        cooldownSuccess = 1800, -- 30 minutes
        cooldownFailure = 1200 -- 20 minutes
    },

    -- Jail Break Mechanics
    mechanics = {
        freePrisoners = true, -- Frees all gang members in prison
        prisonUniform = false, -- Freed prisoners spawn in civilian clothes
        escapeVehicles = {
            'offroad', 'hatchback_sport', 'suv'
        }
    },

    -- Rewards (Freedom + Equipment)
    rewards = {
        freedom = true, -- Primary reward is freedom
        equipment = {
            'pistol', 'smg', 'vest', 'backpack'
        },
        money = 25000 -- $25k per freed prisoner
    }
}

-- Global Federal Event Settings
Config.GlobalSettings = {
    -- Event Priorities (1 = Highest)
    priorities = {
        federal_reserve = 1,
        blackwater_armory = 2,
        evidence_lockup = 3,
        jail_break = 4
    },

    -- Global Cooldowns
    globalCooldown = 1800, -- 30 minutes between any federal events

    -- APD Response Requirements
    apdResponse = {
        mustRespond = true, -- APD must respond if minimum online
        responseTime = 300, -- 5 minutes to respond
        abandonmentPenalty = true -- Penalty for abandoning response
    }
}

-- Federal Event Zones Configuration
Config.FederalZones = {
    -- Event Zones
    zones = {
        {
            name = 'Main Vault',
            coords = vector3(243.2, 225.5, 106.3),
            radius = 50.0,
            type = 'objective',
            required = true
        },
        {
            name = 'Security Office',
            coords = vector3(238.1, 228.4, 106.3),
            radius = 15.0,
            type = 'hack',
            required = true
        },
        {
            name = 'Escape Route',
            coords = vector3(250.8, 220.2, 106.3),
            radius = 25.0,
            type = 'escape',
            required = false
        }
    }
}

-- Federal Event Phases Configuration
Config.FederalPhases = {
    -- Event Phases
    phases = {
        [1] = {
            name = 'Breach',
            duration = 300, -- 5 minutes
            objectives = {
                'Plant blasting charges on vault door',
                'Eliminate security guards',
                'Disable alarm system'
            },
            requirements = {
                items = {'blasting_charge'},
                skills = {'explosives'}
            }
        },
        [2] = {
            name = 'Hack',
            duration = 180, -- 3 minutes
            objectives = {
                'Hack security terminal',
                'Download bank records',
                'Disable vault locks'
            },
            requirements = {
                items = {'hacking_terminal'},
                skills = {'hacking'}
            }
        },
        [3] = {
            name = 'Loot',
            duration = 600, -- 10 minutes
            objectives = {
                'Collect gold bars from vault',
                'Secure evidence',
                'Prepare for extraction'
            },
            requirements = {
                items = {'duffel_bag'},
                skills = {'lockpicking'}
            }
        },
        [4] = {
            name = 'Escape',
            duration = 900, -- 15 minutes
            objectives = {
                'Evade police pursuit',
                'Reach safe house',
                'Launder stolen goods'
            },
            requirements = {
                vehicles = {'getaway_car'},
                skills = {'driving'}
            }
        }
    },
    
    -- Rewards
    rewards = {
        success = {
            money = 500000,
            items = {
                {item = 'gold_bar', amount = 10, chance = 1.0},
                {item = 'money_bag', amount = 5, chance = 0.8},
                {item = 'rare_diamond', amount = 2, chance = 0.3}
            },
            experience = 1000,
            reputation = 100
        },
        participation = {
            money = 50000,
            experience = 200,
            reputation = 25
        },
        failure = {
            money = 10000,
            experience = 50,
            reputation = 5
        }
    },
    
    -- Police Response
    policeResponse = {
        automatic = true,
        responseTime = 120, -- 2 minutes
        units = {
            {type = 'patrol', count = 2, delay = 0},
            {type = 'swat', count = 1, delay = 300},
            {type = 'helicopter', count = 1, delay = 600}
        },
        escalation = {
            [300] = 'Additional patrol units',
            [600] = 'SWAT deployment',
            [900] = 'Air support',
            [1200] = 'Federal agents'
        }
    },
    
    -- Wave Rules
    waveRules = {
        enabled = true,
        apd = {
            maxSimultaneous = 8,
            respawnDelay = 300, -- 5 minutes
            equipment = 'federal_response'
        },
        civilians = {
            maxSimultaneous = 15,
            respawnDelay = 900, -- 15 minutes (NLR)
            equipment = 'standard'
        }
    }
}

-- Blackwater Armory
Config.FederalEvents['blackwater'] = {
    name = 'Blackwater Armory',
    displayName = 'Blackwater Private Military Compound',
    location = vector3(2447.9, 1576.9, 33.0),
    
    requirements = {
        minAPD = 4,
        minCivilians = 6,
        minGangMembers = 4,
        timeRestriction = {
            start = 18, -- 6 PM
            stop = 4 -- 4 AM
        },
        cooldown = 5400, -- 1.5 hours
        globalCooldown = 7200
    },
    
    zones = {
        {
            name = 'Main Compound',
            coords = vector3(2447.9, 1576.9, 33.0),
            radius = 100.0,
            type = 'combat',
            required = true
        },
        {
            name = 'Weapons Vault',
            coords = vector3(2455.2, 1583.1, 33.0),
            radius = 20.0,
            type = 'objective',
            required = true
        },
        {
            name = 'Command Center',
            coords = vector3(2440.6, 1570.7, 33.0),
            radius = 15.0,
            type = 'hack',
            required = true
        }
    },
    
    phases = {
        [1] = {
            name = 'Assault',
            duration = 600, -- 10 minutes
            objectives = {
                'Breach perimeter defenses',
                'Eliminate PMC guards',
                'Secure landing zone'
            },
            requirements = {
                items = {'assault_rifle', 'body_armor'},
                skills = {'combat'}
            }
        },
        [2] = {
            name = 'Infiltration',
            duration = 300, -- 5 minutes
            objectives = {
                'Hack security systems',
                'Disable automated defenses',
                'Access weapons vault'
            },
            requirements = {
                items = {'hacking_terminal', 'emp_device'},
                skills = {'hacking', 'electronics'}
            }
        },
        [3] = {
            name = 'Extraction',
            duration = 900, -- 15 minutes
            objectives = {
                'Collect military weapons',
                'Steal classified documents',
                'Escape with loot'
            },
            requirements = {
                vehicles = {'helicopter', 'armored_truck'},
                skills = {'piloting', 'driving'}
            }
        }
    },
    
    rewards = {
        success = {
            money = 750000,
            items = {
                {item = 'military_rifle', amount = 3, chance = 1.0},
                {item = 'body_armor', amount = 5, chance = 1.0},
                {item = 'classified_docs', amount = 1, chance = 0.5}
            },
            experience = 1500,
            reputation = 150
        },
        participation = {
            money = 75000,
            experience = 300,
            reputation = 30
        }
    },
    
    policeResponse = {
        automatic = true,
        responseTime = 180, -- 3 minutes
        units = {
            {type = 'patrol', count = 3, delay = 0},
            {type = 'swat', count = 2, delay = 300},
            {type = 'helicopter', count = 2, delay = 600}
        }
    },
    
    waveRules = {
        enabled = true,
        apd = {
            maxSimultaneous = 10,
            respawnDelay = 300,
            equipment = 'military_response'
        },
        civilians = {
            maxSimultaneous = 20,
            respawnDelay = 900,
            equipment = 'military'
        }
    }
}

-- Altis Penitentiary (Jail Break)
Config.FederalEvents['jail_break'] = {
    name = 'Jail Break',
    displayName = 'Altis Penitentiary Prison Break',
    location = vector3(1845.9, 2585.9, 46.0),
    
    requirements = {
        minAPD = 3,
        minCivilians = 5,
        minGangMembers = 2,
        prisonersRequired = 1, -- Must have gang member in jail
        timeRestriction = {
            start = 20, -- 8 PM
            stop = 6 -- 6 AM
        },
        cooldown = 7200, -- 2 hours
        globalCooldown = 7200
    },
    
    zones = {
        {
            name = 'Prison Yard',
            coords = vector3(1845.9, 2585.9, 46.0),
            radius = 75.0,
            type = 'combat',
            required = true
        },
        {
            name = 'Cell Block',
            coords = vector3(1838.2, 2578.4, 46.0),
            radius = 30.0,
            type = 'objective',
            required = true
        },
        {
            name = 'Security Office',
            coords = vector3(1853.6, 2593.4, 46.0),
            radius = 15.0,
            type = 'hack',
            required = true
        }
    },
    
    phases = {
        [1] = {
            name = 'Breach',
            duration = 240, -- 4 minutes
            objectives = {
                'Breach prison walls',
                'Eliminate guard towers',
                'Disable security systems'
            },
            requirements = {
                items = {'blasting_charge', 'emp_device'},
                skills = {'explosives', 'electronics'}
            }
        },
        [2] = {
            name = 'Liberation',
            duration = 180, -- 3 minutes
            objectives = {
                'Free imprisoned gang members',
                'Secure weapons from armory',
                'Control cell blocks'
            },
            requirements = {
                items = {'keycard', 'lockpick'},
                skills = {'lockpicking'}
            }
        },
        [3] = {
            name = 'Escape',
            duration = 600, -- 10 minutes
            objectives = {
                'Escort prisoners to safety',
                'Evade police response',
                'Reach extraction point'
            },
            requirements = {
                vehicles = {'prison_bus', 'getaway_cars'},
                skills = {'driving', 'evasion'}
            }
        }
    },
    
    rewards = {
        success = {
            money = 300000,
            items = {
                {item = 'prison_uniform', amount = 1, chance = 1.0},
                {item = 'contraband', amount = 5, chance = 0.8},
                {item = 'guard_keycard', amount = 1, chance = 0.4}
            },
            experience = 800,
            reputation = 80,
            special = 'freed_gang_members'
        },
        participation = {
            money = 30000,
            experience = 150,
            reputation = 15
        }
    },
    
    policeResponse = {
        automatic = true,
        responseTime = 90, -- 1.5 minutes
        units = {
            {type = 'patrol', count = 4, delay = 0},
            {type = 'swat', count = 2, delay = 180},
            {type = 'helicopter', count = 1, delay = 300}
        }
    },
    
    waveRules = {
        enabled = true,
        apd = {
            maxSimultaneous = 12,
            respawnDelay = 240, -- 4 minutes
            equipment = 'riot_control'
        },
        civilians = {
            maxSimultaneous = 15,
            respawnDelay = 900,
            equipment = 'standard'
        }
    }
}

-- Evidence Lockup (Blue Zone Event)
Config.FederalEvents['evidence_lockup'] = {
    name = 'Evidence Lockup',
    displayName = 'APD Evidence Facility Raid',
    location = vector3(471.0, -1007.6, 26.3),
    
    requirements = {
        minAPD = 4,
        minCivilians = 6,
        minGangMembers = 3,
        timeRestriction = {
            start = 22, -- 10 PM
            stop = 4 -- 4 AM
        },
        cooldown = 10800, -- 3 hours
        globalCooldown = 7200
    },
    
    zones = {
        {
            name = 'Evidence Vault',
            coords = vector3(471.0, -1007.6, 26.3),
            radius = 40.0,
            type = 'objective',
            required = true
        },
        {
            name = 'Security Checkpoint',
            coords = vector3(468.2, -1010.8, 26.3),
            radius = 20.0,
            type = 'hack',
            required = true
        }
    },
    
    phases = {
        [1] = {
            name = 'Infiltration',
            duration = 300, -- 5 minutes
            objectives = {
                'Bypass security systems',
                'Eliminate guards silently',
                'Access evidence vault'
            },
            requirements = {
                items = {'silenced_weapon', 'hacking_terminal'},
                skills = {'stealth', 'hacking'}
            }
        },
        [2] = {
            name = 'Retrieval',
            duration = 240, -- 4 minutes
            objectives = {
                'Locate gang evidence',
                'Destroy incriminating files',
                'Steal valuable evidence'
            },
            requirements = {
                items = {'thermite', 'evidence_scanner'},
                skills = {'investigation'}
            }
        },
        [3] = {
            name = 'Exfiltration',
            duration = 360, -- 6 minutes
            objectives = {
                'Escape without detection',
                'Avoid police response',
                'Deliver evidence safely'
            },
            requirements = {
                vehicles = {'stealth_vehicle'},
                skills = {'evasion'}
            }
        }
    },
    
    rewards = {
        success = {
            money = 200000,
            items = {
                {item = 'destroyed_evidence', amount = 1, chance = 1.0},
                {item = 'police_intel', amount = 3, chance = 0.7},
                {item = 'confiscated_items', amount = 5, chance = 0.5}
            },
            experience = 600,
            reputation = 60,
            special = 'reduced_wanted_level'
        },
        participation = {
            money = 25000,
            experience = 120,
            reputation = 12
        }
    },
    
    policeResponse = {
        automatic = true,
        responseTime = 60, -- 1 minute (it's a police station)
        units = {
            {type = 'patrol', count = 6, delay = 0},
            {type = 'swat', count = 3, delay = 120},
            {type = 'helicopter', count = 2, delay = 240}
        }
    },
    
    waveRules = {
        enabled = false, -- Blue zone - no wave rules
        killOnSight = true, -- Civilians can kill cops on sight
        noRP = true -- No roleplay required
    }
}

return Config
