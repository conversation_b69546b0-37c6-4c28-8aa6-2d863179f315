// Olympus Betting System JavaScript

let selectedCoin = 'heads';
let isOpen = false;

// Initialize when document is ready
$(document).ready(function() {
    $('.betting-container').hide();
    
    // Coin selection
    $('.coin-option').click(function() {
        $('.coin-option').removeClass('selected');
        $(this).addClass('selected');
        selectedCoin = $(this).data('coin');
    });
    
    // Place bet button
    $('#placeBet').click(function() {
        const amount = parseInt($('#betAmount').val());
        
        if (!amount || amount <= 0) {
            showNotification('Please enter a valid bet amount!', 'error');
            return;
        }
        
        if (amount < 1000) {
            showNotification('Minimum bet is $1,000!', 'error');
            return;
        }
        
        if (amount > 1000000) {
            showNotification('Maximum bet is $1,000,000!', 'error');
            return;
        }
        
        // Send bet to server
        $.post('https://olympus-betting/placeBet', JSON.stringify({
            amount: amount,
            choice: selectedCoin
        }));
        
        closeBetting();
    });
    
    // Cancel button
    $('#cancelBet').click(function() {
        closeBetting();
    });
    
    // Close on escape
    $(document).keyup(function(e) {
        if (e.keyCode === 27 && isOpen) { // ESC
            closeBetting();
        }
    });
});

// Listen for NUI messages
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.action) {
        case 'openBetting':
            openBetting();
            break;
        case 'closeBetting':
            closeBetting();
            break;
        case 'showResult':
            showBetResult(data.result, data.won, data.amount);
            break;
    }
});

// Open betting interface
function openBetting() {
    if (isOpen) return;
    
    isOpen = true;
    $('.betting-container').fadeIn(300);
    $('#betAmount').val('');
    $('#betAmount').focus();
}

// Close betting interface
function closeBetting() {
    if (!isOpen) return;
    
    isOpen = false;
    $('.betting-container').fadeOut(300);
    
    // Notify server
    $.post('https://olympus-betting/closeBetting', JSON.stringify({}));
}

// Show bet result
function showBetResult(result, won, amount) {
    const resultText = won ? 
        `You won! The coin landed on ${result}. You received $${amount.toLocaleString()}!` :
        `You lost! The coin landed on ${result}. You lost $${amount.toLocaleString()}.`;
    
    const resultType = won ? 'success' : 'error';
    showNotification(resultText, resultType);
}

// Show notification (placeholder - would integrate with main UI system)
function showNotification(message, type) {
    console.log(`[${type.toUpperCase()}] ${message}`);
    
    // Create temporary notification
    const notification = $(`
        <div class="notification ${type}" style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#ff4444' : '#44ff44'};
            color: white;
            padding: 15px;
            border-radius: 5px;
            z-index: 9999;
            max-width: 300px;
        ">
            ${message}
        </div>
    `);
    
    $('body').append(notification);
    
    setTimeout(() => {
        notification.fadeOut(300, function() {
            $(this).remove();
        });
    }, 3000);
}
