-- Olympus Items - Blasting Charge Client
-- Based on original fn_blastingCharge.sqf from Olympus Altis Life

local isPlantingCharge = false
local chargeTarget = nil

-- Use blasting charge function
function UseBlastingCharge(target)
    if isPlantingCharge then
        exports['olympus-items']:ShowNotification("You are already planting a charge!", "error")
        return false
    end
    
    local ped = PlayerPedId()
    local vault = target or GetVaultPedIsLookingAt(ped)
    
    if not vault or vault == 0 then
        exports['olympus-items']:ShowNotification("No valid vault found!", "error")
        return false
    end
    
    -- Check distance
    local playerPos = GetEntityCoords(ped)
    local vaultPos = GetEntityCoords(vault)
    local distance = #(playerPos - vaultPos)
    
    if distance > 5.0 then
        exports['olympus-items']:ShowNotification("You are too close to the vault!", "error")
        return false
    end
    
    -- Check if it's a valid vault
    if not IsValidBlastingTarget(vault) then
        exports['olympus-items']:ShowNotification("You cannot use explosives on this!", "error")
        return false
    end
    
    -- Check if vault is already blown
    if IsVaultAlreadyBlown(vault) then
        exports['olympus-items']:ShowNotification("This vault has already been blown!", "error")
        return false
    end
    
    -- Get vault type and requirements
    local vaultType = GetVaultType(vault)
    local requirements = GetVaultRequirements(vaultType)
    
    -- Check cop requirements
    if not HasEnoughCops(requirements.cops) then
        exports['olympus-items']:ShowNotification(string.format("There needs to be %d or more cops online!", requirements.cops), "error")
        return false
    end
    
    -- Check civilian requirements
    if not HasEnoughCiviliansNearby(requirements.civs) then
        exports['olympus-items']:ShowNotification(string.format("There needs to be %d or more civilians nearby!", requirements.civs), "error")
        return false
    end
    
    -- Check federal cooldown
    if IsOnFederalCooldown() then
        exports['olympus-items']:ShowNotification("Federal facilities are under lockdown!", "error")
        return false
    end
    
    -- Check if player has required items
    if not HasRequiredItems(vaultType) then
        exports['olympus-items']:ShowNotification("You don't have the required items!", "error")
        return false
    end
    
    -- Start planting process
    return StartPlantingCharge(vault, vaultType)
end

-- Get vault player is looking at
function GetVaultPedIsLookingAt(ped)
    local coords = GetEntityCoords(ped)
    local forward = GetEntityForwardVector(ped)
    local destination = coords + forward * 5.0
    
    local rayHandle = StartExpensiveSynchronousShapeTestLosProbe(
        coords.x, coords.y, coords.z,
        destination.x, destination.y, destination.z,
        1, ped, 0
    )
    
    local _, hit, _, _, entity = GetShapeTestResult(rayHandle)
    
    if hit and IsValidBlastingTarget(entity) then
        return entity
    end
    
    return nil
end

-- Check if target is valid for blasting
function IsValidBlastingTarget(entity)
    if not DoesEntityExist(entity) then return false end
    
    local model = GetEntityModel(entity)
    local validModels = {
        GetHashKey("land_cargobox_v1_f"), -- Bank vault
        GetHashKey("land_cargobox_v2_f"), -- Federal vault
        GetHashKey("land_cargobox_v3_f"), -- Blackwater vault
        GetHashKey("land_cargobox_v4_f"), -- Prison vault
        GetHashKey("land_dome_big_f"),    -- Federal Reserve
        GetHashKey("land_research_house_v1_f") -- Research facility
    }
    
    for _, validModel in ipairs(validModels) do
        if model == validModel then
            return true
        end
    end
    
    return false
end

-- Check if vault is already blown
function IsVaultAlreadyBlown(vault)
    -- This would check server-side vault state
    -- For now, we'll use a simple check
    return GetEntityHealth(vault) < GetEntityMaxHealth(vault)
end

-- Get vault type
function GetVaultType(vault)
    local model = GetEntityModel(vault)
    
    if model == GetHashKey("land_cargobox_v1_f") then
        return "bank"
    elseif model == GetHashKey("land_cargobox_v2_f") then
        return "fed"
    elseif model == GetHashKey("land_cargobox_v3_f") then
        return "blackwater"
    elseif model == GetHashKey("land_cargobox_v4_f") then
        return "jail"
    elseif model == GetHashKey("land_dome_big_f") then
        return "fed"
    elseif model == GetHashKey("land_research_house_v1_f") then
        return "fed"
    end
    
    return "unknown"
end

-- Get vault requirements
function GetVaultRequirements(vaultType)
    local requirements = Config.Items.blastingcharge.requiredCops
    local civRequirements = Config.Items.blastingcharge.requiredCivs
    
    return {
        cops = requirements[vaultType] or 5,
        civs = civRequirements[vaultType] or 2
    }
end

-- Check if enough cops online
function HasEnoughCops(required)
    -- This would check with the server for online cop count
    local result = false
    
    TriggerServerEvent('olympus-items:server:checkCopCount', required, function(hasEnough)
        result = hasEnough
    end)
    
    -- For now, return true (placeholder)
    return true
end

-- Check for required civilians nearby
function HasEnoughCiviliansNearby(required)
    local playerPos = GetEntityCoords(PlayerPedId())
    local nearbyPlayers = GetPlayersInArea(playerPos, 100.0)
    local civilianCount = 0
    
    for _, player in ipairs(nearbyPlayers) do
        local playerData = exports['olympus-core']:GetPlayerDataByServerId(GetPlayerServerId(player))
        if playerData and playerData.faction == 'civilian' then
            civilianCount = civilianCount + 1
        end
    end
    
    return civilianCount >= required
end

-- Get players in area
function GetPlayersInArea(center, radius)
    local players = {}
    local allPlayers = GetActivePlayers()
    
    for _, player in ipairs(allPlayers) do
        local ped = GetPlayerPed(player)
        local pos = GetEntityCoords(ped)
        
        if #(center - pos) <= radius then
            table.insert(players, player)
        end
    end
    
    return players
end

-- Check federal cooldown
function IsOnFederalCooldown()
    -- This would check server-side federal cooldown
    return false -- Placeholder
end

-- Check if player has required items
function HasRequiredItems(vaultType)
    -- For some vaults, additional items might be required
    if vaultType == "blackwater" then
        -- Blackwater might require special tools
        return true -- Placeholder
    end
    
    return true
end

-- Start planting charge
function StartPlantingCharge(vault, vaultType)
    isPlantingCharge = true
    chargeTarget = vault
    
    local ped = PlayerPedId()
    local playerPos = GetEntityCoords(ped)
    
    -- Alert police/dispatch immediately
    TriggerServerEvent('olympus-items:server:alertFederalBreach', {
        location = playerPos,
        vaultType = vaultType,
        action = "planting_explosive"
    })
    
    -- Play animation
    exports['olympus-items']:PlayAnimation(Config.Animations.planting)
    
    -- Show progress bar
    local progressConfig = Config.ProgressBar.blastingcharge
    
    exports['olympus-items']:ShowProgressBar(progressConfig, function()
        -- Progress completed
        CompletePlantingCharge(vault, vaultType)
    end, function()
        -- Progress cancelled
        CancelPlantingCharge()
    end)
    
    return true
end

-- Complete planting charge
function CompletePlantingCharge(vault, vaultType)
    isPlantingCharge = false
    chargeTarget = nil
    
    -- Clear animation
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    -- Check if still in range
    local playerPos = GetEntityCoords(ped)
    local vaultPos = GetEntityCoords(vault)
    local distance = #(playerPos - vaultPos)
    
    if distance > 5.0 then
        exports['olympus-items']:ShowNotification("You moved too far away!", "error")
        return
    end
    
    -- Consume blasting charge
    TriggerServerEvent('olympus-items:server:consumeItem', 'blastingcharge', true)
    
    -- Plant the charge
    TriggerServerEvent('olympus-items:server:plantBlastingCharge', {
        vault = NetworkGetNetworkIdFromEntity(vault),
        vaultType = vaultType,
        location = vaultPos
    })
    
    exports['olympus-items']:ShowNotification("Explosive planted! Get to a safe distance!", "success")
    
    -- Start countdown timer
    StartChargeCountdown(vault, vaultType)
end

-- Start charge countdown
function StartChargeCountdown(vault, vaultType)
    local countdownTime = 30 -- 30 seconds
    
    CreateThread(function()
        for i = countdownTime, 1, -1 do
            if i <= 10 then
                exports['olympus-items']:ShowNotification(string.format("Explosion in %d seconds!", i), "warning")
            end
            Wait(1000)
        end
        
        -- Detonate
        DetonateCharge(vault, vaultType)
    end)
end

-- Detonate charge
function DetonateCharge(vault, vaultType)
    local vaultPos = GetEntityCoords(vault)
    
    -- Create explosion effect
    AddExplosion(vaultPos.x, vaultPos.y, vaultPos.z, 2, 10.0, true, false, 1.0)
    
    -- Damage vault
    SetEntityHealth(vault, 0)
    
    -- Notify server of successful detonation
    TriggerServerEvent('olympus-items:server:vaultDetonated', {
        vault = NetworkGetNetworkIdFromEntity(vault),
        vaultType = vaultType,
        location = vaultPos
    })
    
    exports['olympus-items']:ShowNotification("Vault breached!", "success")
end

-- Cancel planting charge
function CancelPlantingCharge()
    isPlantingCharge = false
    chargeTarget = nil
    
    -- Clear animation
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    exports['olympus-items']:ShowNotification("Planting cancelled!", "info")
end

-- Check if player moved too far during planting
CreateThread(function()
    while true do
        if isPlantingCharge and chargeTarget then
            local ped = PlayerPedId()
            local playerPos = GetEntityCoords(ped)
            local vaultPos = GetEntityCoords(chargeTarget)
            local distance = #(playerPos - vaultPos)
            
            if distance > 5.0 then
                CancelPlantingCharge()
            end
        end
        
        Wait(500)
    end
end)

-- Export functions
exports('UseBlastingCharge', UseBlastingCharge)

-- Event handlers
RegisterNetEvent('olympus-items:client:blastingChargeInterrupted', function()
    if isPlantingCharge then
        CancelPlantingCharge()
    end
end)

RegisterNetEvent('olympus-items:client:vaultExplosion', function(position)
    -- Handle remote vault explosions
    AddExplosion(position.x, position.y, position.z, 2, 10.0, true, false, 1.0)
end)
