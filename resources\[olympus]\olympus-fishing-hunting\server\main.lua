-- Olympus Fishing & Hunting System - Server Main
-- Based on original fn_catchFish.sqf, fn_dropFishingNet.sqf, fn_catchTurtle.sqf, fn_gutAnimal.sqf

local OlympusFishingHunting = {}
local activeFishingNets = {}
local spawnedAnimals = {}
local spawnedTurtles = {}
local playerCooldowns = {}

-- Initialize system
CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end
    
    print("[Olympus Fishing & Hunting] Server initialized")
    
    -- Initialize database tables
    OlympusFishingHunting.InitializeDatabase()
    
    -- Start animal spawning thread
    CreateThread(OlympusFishingHunting.AnimalSpawningThread)
    
    -- Start turtle spawning thread  
    CreateThread(OlympusFishingHunting.TurtleSpawningThread)
    
    -- Start cleanup thread
    CreateThread(OlympusFishingHunting.CleanupThread)
end)

-- Database initialization
function OlympusFishingHunting.InitializeDatabase()
    local success = pcall(function()
        -- Create fishing_hunting_logs table
        exports.oxmysql:execute([[
            CREATE TABLE IF NOT EXISTS fishing_hunting_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                player_id VARCHAR(50) NOT NULL,
                player_name VARCHAR(100) NOT NULL,
                action_type ENUM('fish_catch', 'turtle_catch', 'animal_gut', 'net_fishing') NOT NULL,
                item_caught VARCHAR(50) NOT NULL,
                quantity INT DEFAULT 1,
                location VARCHAR(255),
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_player_id (player_id),
                INDEX idx_action_type (action_type),
                INDEX idx_timestamp (timestamp)
            )
        ]])

        -- Create fishing_hunting_stats table
        exports.oxmysql:execute([[
            CREATE TABLE IF NOT EXISTS fishing_hunting_stats (
                id INT AUTO_INCREMENT PRIMARY KEY,
                player_id VARCHAR(50) NOT NULL UNIQUE,
                total_fish_caught INT DEFAULT 0,
                total_turtles_caught INT DEFAULT 0,
                total_animals_gutted INT DEFAULT 0,
                total_nets_used INT DEFAULT 0,
                best_fish VARCHAR(50),
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_player_id (player_id)
            )
        ]])
        
        print("[Olympus Fishing & Hunting] Database tables initialized")
    end)
    
    if not success then
        print("[Olympus Fishing & Hunting] ERROR: Failed to initialize database tables")
    end
end

-- Utility Functions
function OlympusFishingHunting.GetPlayerData(source)
    local success, result = pcall(function()
        return exports['olympus-core']:GetPlayerData(source)
    end)
    return success and result or nil
end

function OlympusFishingHunting.AddPlayerItem(source, item, quantity)
    local success, result = pcall(function()
        return exports['olympus-core']:AddItem(source, item, quantity or 1)
    end)
    return success and result or false
end

function OlympusFishingHunting.GetPlayerMoney(source, type)
    local Player = OlympusFishingHunting.GetPlayerData(source)
    if not Player then return 0 end
    
    if type == "cash" then
        return Player.money and Player.money.cash or 0
    elseif type == "bank" then
        return Player.money and Player.money.bank or 0
    end
    return 0
end

function OlympusFishingHunting.AddPlayerMoney(source, type, amount)
    local success = pcall(function()
        exports['olympus-core']:AddMoney(source, type, amount)
    end)
    return success
end

function OlympusFishingHunting.IsPlayerInWater(source)
    local playerPed = GetPlayerPed(source)
    local coords = GetEntityCoords(playerPed)
    return coords.z < 0.0 -- Simple water check
end

function OlympusFishingHunting.IsPlayerInVehicle(source, vehicleType)
    local playerPed = GetPlayerPed(source)
    local vehicle = GetVehiclePedIsIn(playerPed, false)
    
    if vehicle == 0 then return false end
    
    if vehicleType == "boat" then
        return GetVehicleClass(vehicle) == 14 -- Boat class
    end
    
    return vehicle ~= 0
end

function OlympusFishingHunting.GetPlayerZone(source, zoneType)
    local playerPed = GetPlayerPed(source)
    local coords = GetEntityCoords(playerPed)
    
    local zones = zoneType == "fishing" and Config.Fishing.zones or 
                  zoneType == "turtle" and Config.TurtlePoaching.zones or
                  zoneType == "hunting" and Config.Hunting.zones
    
    for _, zone in pairs(zones) do
        local distance = #(coords - zone.center)
        if distance <= zone.radius then
            return zone
        end
    end
    
    return nil
end

function OlympusFishingHunting.LogActivity(playerId, playerName, actionType, itemCaught, quantity, location)
    exports.oxmysql:execute('INSERT INTO fishing_hunting_logs (player_id, player_name, action_type, item_caught, quantity, location) VALUES (?, ?, ?, ?, ?, ?)', {
        playerId, playerName, actionType, itemCaught, quantity or 1, location or "Unknown"
    })
end

function OlympusFishingHunting.UpdatePlayerStats(playerId, actionType, itemCaught)
    exports.oxmysql:query('SELECT * FROM fishing_hunting_stats WHERE player_id = ?', {playerId}, function(result)
        if result and #result > 0 then
            local stats = result[1]
            local updateQuery = ""
            local updateParams = {}
            
            if actionType == "fish_catch" then
                updateQuery = "UPDATE fishing_hunting_stats SET total_fish_caught = total_fish_caught + 1, last_activity = NOW() WHERE player_id = ?"
                updateParams = {playerId}
            elseif actionType == "turtle_catch" then
                updateQuery = "UPDATE fishing_hunting_stats SET total_turtles_caught = total_turtles_caught + 1, last_activity = NOW() WHERE player_id = ?"
                updateParams = {playerId}
            elseif actionType == "animal_gut" then
                updateQuery = "UPDATE fishing_hunting_stats SET total_animals_gutted = total_animals_gutted + 1, last_activity = NOW() WHERE player_id = ?"
                updateParams = {playerId}
            elseif actionType == "net_fishing" then
                updateQuery = "UPDATE fishing_hunting_stats SET total_nets_used = total_nets_used + 1, last_activity = NOW() WHERE player_id = ?"
                updateParams = {playerId}
            end
            
            if updateQuery ~= "" then
                exports.oxmysql:execute(updateQuery, updateParams)
            end
        else
            -- Create new stats entry
            exports.oxmysql:execute('INSERT INTO fishing_hunting_stats (player_id) VALUES (?)', {playerId})
        end
    end)
end

-- Fish Catching System (based on fn_catchFish.sqf)
function OlympusFishingHunting.ProcessFishCatch(source, fishType)
    local Player = OlympusFishingHunting.GetPlayerData(source)
    if not Player then return false end
    
    -- Validate fish type
    if not Config.Fishing.fishTypes[fishType] then
        return false
    end
    
    -- Check if player is in water and in fishing zone
    if not OlympusFishingHunting.IsPlayerInWater(source) then
        TriggerClientEvent('olympus-fishing-hunting:client:notify', source, 'You need to be in water to catch fish', 'error')
        return false
    end
    
    local zone = OlympusFishingHunting.GetPlayerZone(source, "fishing")
    if not zone then
        TriggerClientEvent('olympus-fishing-hunting:client:notify', source, 'You need to be in a fishing zone', 'error')
        return false
    end
    
    -- Check if fish type is available in this zone
    local fishAvailable = false
    for _, availableFish in pairs(zone.fishTypes) do
        if availableFish == fishType then
            fishAvailable = true
            break
        end
    end
    
    if not fishAvailable then
        return false
    end
    
    -- Add fish to inventory
    if OlympusFishingHunting.AddPlayerItem(source, fishType, 1) then
        local fishData = Config.Fishing.fishTypes[fishType]
        
        -- Log activity
        OlympusFishingHunting.LogActivity(
            Player.citizenid,
            Player.charinfo.firstname .. " " .. Player.charinfo.lastname,
            "fish_catch",
            fishType,
            1,
            zone.name
        )
        
        -- Update stats
        OlympusFishingHunting.UpdatePlayerStats(Player.citizenid, "fish_catch", fishType)
        
        -- Notify player
        TriggerClientEvent('olympus-fishing-hunting:client:notify', source, 
            string.format('You caught a %s!', fishData.name), 'success')
        
        return true
    else
        TriggerClientEvent('olympus-fishing-hunting:client:notify', source, 'Your inventory is full', 'error')
        return false
    end
end

-- Fishing Net System (based on fn_dropFishingNet.sqf)
function OlympusFishingHunting.ProcessFishingNet(source)
    local Player = OlympusFishingHunting.GetPlayerData(source)
    if not Player then return false end
    
    -- Check cooldown
    local playerId = Player.citizenid
    if playerCooldowns[playerId] and GetGameTimer() < playerCooldowns[playerId] then
        TriggerClientEvent('olympus-fishing-hunting:client:notify', source, 'You must wait before using another fishing net', 'error')
        return false
    end
    
    -- Check if player is in boat
    if not OlympusFishingHunting.IsPlayerInVehicle(source, "boat") then
        TriggerClientEvent('olympus-fishing-hunting:client:notify', source, 'You need to be in a boat to use a fishing net', 'error')
        return false
    end
    
    -- Check if in fishing zone
    local zone = OlympusFishingHunting.GetPlayerZone(source, "fishing")
    if not zone then
        TriggerClientEvent('olympus-fishing-hunting:client:notify', source, 'You need to be in a fishing zone', 'error')
        return false
    end
    
    -- Set cooldown
    playerCooldowns[playerId] = GetGameTimer() + (Config.Fishing.fishingNet.cooldown * 1000)
    
    -- Start net fishing process
    TriggerClientEvent('olympus-fishing-hunting:client:startNetFishing', source, zone.fishTypes)
    
    return true
end

-- Complete Net Fishing Process
function OlympusFishingHunting.CompleteNetFishing(source, caughtFish)
    local Player = OlympusFishingHunting.GetPlayerData(source)
    if not Player then return end

    local totalCaught = 0
    local fishNames = {}

    for _, fishType in pairs(caughtFish) do
        if Config.Fishing.fishTypes[fishType] and OlympusFishingHunting.AddPlayerItem(source, fishType, 1) then
            totalCaught = totalCaught + 1
            table.insert(fishNames, Config.Fishing.fishTypes[fishType].name)

            -- Log each fish
            OlympusFishingHunting.LogActivity(
                Player.citizenid,
                Player.charinfo.firstname .. " " .. Player.charinfo.lastname,
                "net_fishing",
                fishType,
                1,
                "Fishing Net"
            )
        end
    end

    -- Update stats
    OlympusFishingHunting.UpdatePlayerStats(Player.citizenid, "net_fishing", "multiple")

    if totalCaught > 0 then
        TriggerClientEvent('olympus-fishing-hunting:client:notify', source,
            string.format('Your net caught %d fish: %s', totalCaught, table.concat(fishNames, ", ")), 'success')
    else
        TriggerClientEvent('olympus-fishing-hunting:client:notify', source, 'Your net came up empty', 'error')
    end
end

-- Turtle Catching System (based on fn_catchTurtle.sqf)
function OlympusFishingHunting.ProcessTurtleCatch(source, turtleEntity)
    local Player = OlympusFishingHunting.GetPlayerData(source)
    if not Player then return false end

    -- Check if player is in turtle poaching zone
    local zone = OlympusFishingHunting.GetPlayerZone(source, "turtle")
    if not zone then
        TriggerClientEvent('olympus-fishing-hunting:client:notify', source, 'You need to be in a turtle poaching zone to collect turtles', 'error')
        return false
    end

    -- Check if turtle is dead (in original it checks if alive)
    local playerPed = GetPlayerPed(source)
    local coords = GetEntityCoords(playerPed)

    -- Validate distance (original checks 3.5m distance)
    if turtleEntity and DoesEntityExist(turtleEntity) then
        local turtleCoords = GetEntityCoords(turtleEntity)
        local distance = #(coords - turtleCoords)

        if distance > Config.TurtlePoaching.requirements.maxDistance then
            TriggerClientEvent('olympus-fishing-hunting:client:notify', source, 'You need to be closer to the turtle', 'error')
            return false
        end

        -- Add turtle to inventory
        if OlympusFishingHunting.AddPlayerItem(source, "turtle", 1) then
            -- Remove turtle entity
            DeleteEntity(turtleEntity)

            -- Log activity
            OlympusFishingHunting.LogActivity(
                Player.citizenid,
                Player.charinfo.firstname .. " " .. Player.charinfo.lastname,
                "turtle_catch",
                "turtle",
                1,
                zone.name
            )

            -- Update stats
            OlympusFishingHunting.UpdatePlayerStats(Player.citizenid, "turtle_catch", "turtle")

            -- Notify player
            TriggerClientEvent('olympus-fishing-hunting:client:notify', source, 'You caught a turtle!', 'success')

            return true
        else
            TriggerClientEvent('olympus-fishing-hunting:client:notify', source, 'Your inventory is full', 'error')
            return false
        end
    end

    return false
end

-- Animal Gutting System (based on fn_gutAnimal.sqf)
function OlympusFishingHunting.ProcessAnimalGut(source, animalType, animalEntity)
    local Player = OlympusFishingHunting.GetPlayerData(source)
    if not Player then return false end

    -- Validate animal type
    if not Config.Hunting.animalTypes[animalType] then
        return false
    end

    local animalData = Config.Hunting.animalTypes[animalType]

    -- Check if player has required weapon
    local playerPed = GetPlayerPed(source)
    local currentWeapon = GetSelectedPedWeapon(playerPed)
    local hasRequiredWeapon = false

    for _, weapon in pairs(Config.Hunting.requirements.requiredWeapons) do
        if GetHashKey(weapon) == currentWeapon then
            hasRequiredWeapon = true
            break
        end
    end

    if not hasRequiredWeapon then
        TriggerClientEvent('olympus-fishing-hunting:client:notify', source, 'You need a knife or similar tool to gut animals', 'error')
        return false
    end

    -- Check distance to animal
    if animalEntity and DoesEntityExist(animalEntity) then
        local coords = GetEntityCoords(playerPed)
        local animalCoords = GetEntityCoords(animalEntity)
        local distance = #(coords - animalCoords)

        if distance > Config.Hunting.requirements.maxDistance then
            TriggerClientEvent('olympus-fishing-hunting:client:notify', source, 'You need to be closer to the animal', 'error')
            return false
        end

        -- Start gutting process
        TriggerClientEvent('olympus-fishing-hunting:client:startGutting', source, animalType, animalEntity)
        return true
    end

    return false
end

-- Complete Animal Gutting
function OlympusFishingHunting.CompleteAnimalGut(source, animalType, animalEntity)
    local Player = OlympusFishingHunting.GetPlayerData(source)
    if not Player then return false end

    local animalData = Config.Hunting.animalTypes[animalType]
    if not animalData then return false end

    -- Add raw meat to inventory
    if OlympusFishingHunting.AddPlayerItem(source, animalData.rawMeat, 1) then
        -- Remove animal entity
        if animalEntity and DoesEntityExist(animalEntity) then
            DeleteEntity(animalEntity)
        end

        -- Log activity
        OlympusFishingHunting.LogActivity(
            Player.citizenid,
            Player.charinfo.firstname .. " " .. Player.charinfo.lastname,
            "animal_gut",
            animalData.rawMeat,
            1,
            "Hunting"
        )

        -- Update stats
        OlympusFishingHunting.UpdatePlayerStats(Player.citizenid, "animal_gut", animalType)

        -- Notify player
        TriggerClientEvent('olympus-fishing-hunting:client:notify', source,
            string.format('You have collected some raw %s meat', animalData.name), 'success')

        return true
    else
        TriggerClientEvent('olympus-fishing-hunting:client:notify', source, 'Your inventory is full', 'error')
        return false
    end
end

-- Animal Spawning System
function OlympusFishingHunting.AnimalSpawningThread()
    while true do
        Wait(60000) -- Check every minute

        for _, zone in pairs(Config.Hunting.zones) do
            for _, animalType in pairs(zone.animalTypes) do
                local animalData = Config.Hunting.animalTypes[animalType]

                -- Check spawn rate
                if math.random() < animalData.spawnRate then
                    OlympusFishingHunting.SpawnAnimal(zone, animalType)
                end
            end
        end
    end
end

function OlympusFishingHunting.SpawnAnimal(zone, animalType)
    local animalData = Config.Hunting.animalTypes[animalType]

    -- Count existing animals of this type in zone
    local existingCount = 0
    for _, animal in pairs(spawnedAnimals) do
        if animal.type == animalType and animal.zone == zone.name then
            existingCount = existingCount + 1
        end
    end

    -- Don't spawn if too many already exist
    if existingCount >= 3 then return end

    -- Generate random spawn position within zone
    local angle = math.random() * 2 * math.pi
    local distance = math.random() * zone.radius * 0.8 -- Spawn within 80% of zone radius
    local spawnPos = vector3(
        zone.center.x + math.cos(angle) * distance,
        zone.center.y + math.sin(angle) * distance,
        zone.center.z
    )

    -- Create animal entity
    local animalHash = GetHashKey(animalData.model)
    RequestModel(animalHash)

    while not HasModelLoaded(animalHash) do
        Wait(100)
    end

    local animal = CreatePed(28, animalHash, spawnPos.x, spawnPos.y, spawnPos.z, 0.0, true, false)

    if DoesEntityExist(animal) then
        SetEntityHealth(animal, animalData.health)
        SetPedCanRagdoll(animal, true)
        SetEntityAsMissionEntity(animal, true, true)

        -- Store animal data
        spawnedAnimals[animal] = {
            type = animalType,
            zone = zone.name,
            spawnTime = GetGameTimer(),
            entity = animal
        }

        -- Set despawn timer
        SetTimeout(300000, function() -- 5 minutes
            if DoesEntityExist(animal) then
                DeleteEntity(animal)
                spawnedAnimals[animal] = nil
            end
        end)
    end

    SetModelAsNoLongerNeeded(animalHash)
end

-- Turtle Spawning System
function OlympusFishingHunting.TurtleSpawningThread()
    while true do
        Wait(60000) -- Check every minute

        for _, zone in pairs(Config.TurtlePoaching.zones) do
            -- Check spawn rate
            if math.random() < Config.TurtlePoaching.turtle.spawnRate then
                OlympusFishingHunting.SpawnTurtle(zone)
            end
        end
    end
end

function OlympusFishingHunting.SpawnTurtle(zone)
    -- Count existing turtles in zone
    local existingCount = 0
    for _, turtle in pairs(spawnedTurtles) do
        if turtle.zone == zone.name then
            existingCount = existingCount + 1
        end
    end

    -- Don't spawn if too many already exist
    if existingCount >= Config.TurtlePoaching.turtle.maxTurtles then return end

    -- Generate random spawn position within zone (near water)
    local angle = math.random() * 2 * math.pi
    local distance = math.random() * zone.radius * 0.6
    local spawnPos = vector3(
        zone.center.x + math.cos(angle) * distance,
        zone.center.y + math.sin(angle) * distance,
        zone.center.z - 2.0 -- Slightly underwater
    )

    -- Create turtle entity (using fish model as placeholder)
    local turtleHash = GetHashKey("a_c_fish")
    RequestModel(turtleHash)

    while not HasModelLoaded(turtleHash) do
        Wait(100)
    end

    local turtle = CreatePed(28, turtleHash, spawnPos.x, spawnPos.y, spawnPos.z, 0.0, true, false)

    if DoesEntityExist(turtle) then
        SetEntityHealth(turtle, 0) -- Spawn as dead
        SetPedCanRagdoll(turtle, true)
        SetEntityAsMissionEntity(turtle, true, true)

        -- Store turtle data
        spawnedTurtles[turtle] = {
            zone = zone.name,
            spawnTime = GetGameTimer(),
            entity = turtle
        }

        -- Set despawn timer
        SetTimeout(Config.TurtlePoaching.turtle.despawnTime * 1000, function()
            if DoesEntityExist(turtle) then
                DeleteEntity(turtle)
                spawnedTurtles[turtle] = nil
            end
        end)
    end

    SetModelAsNoLongerNeeded(turtleHash)
end

-- Cleanup System
function OlympusFishingHunting.CleanupThread()
    while true do
        Wait(300000) -- Check every 5 minutes

        -- Clean up despawned animals
        for entity, data in pairs(spawnedAnimals) do
            if not DoesEntityExist(entity) then
                spawnedAnimals[entity] = nil
            end
        end

        -- Clean up despawned turtles
        for entity, data in pairs(spawnedTurtles) do
            if not DoesEntityExist(entity) then
                spawnedTurtles[entity] = nil
            end
        end

        -- Clean up old cooldowns
        local currentTime = GetGameTimer()
        for playerId, cooldownTime in pairs(playerCooldowns) do
            if currentTime >= cooldownTime then
                playerCooldowns[playerId] = nil
            end
        end
    end
end

-- Event Handlers
RegisterNetEvent('olympus-fishing-hunting:server:catchFish', function(fishType)
    local src = source
    OlympusFishingHunting.ProcessFishCatch(src, fishType)
end)

RegisterNetEvent('olympus-fishing-hunting:server:useFishingNet', function()
    local src = source
    OlympusFishingHunting.ProcessFishingNet(src)
end)

RegisterNetEvent('olympus-fishing-hunting:server:completeNetFishing', function(caughtFish)
    local src = source
    OlympusFishingHunting.CompleteNetFishing(src, caughtFish)
end)

RegisterNetEvent('olympus-fishing-hunting:server:catchTurtle', function(turtleEntity)
    local src = source
    OlympusFishingHunting.ProcessTurtleCatch(src, turtleEntity)
end)

RegisterNetEvent('olympus-fishing-hunting:server:gutAnimal', function(animalType, animalEntity)
    local src = source
    OlympusFishingHunting.ProcessAnimalGut(src, animalType, animalEntity)
end)

RegisterNetEvent('olympus-fishing-hunting:server:completeGutting', function(animalType, animalEntity)
    local src = source
    OlympusFishingHunting.CompleteAnimalGut(src, animalType, animalEntity)
end)

RegisterNetEvent('olympus-fishing-hunting:server:requestPlayerStats', function()
    local src = source
    local Player = OlympusFishingHunting.GetPlayerData(src)
    if not Player then return end

    exports.oxmysql:query('SELECT * FROM fishing_hunting_stats WHERE player_id = ?', {Player.citizenid}, function(result)
        local stats = result and #result > 0 and result[1] or {
            total_fish_caught = 0,
            total_turtles_caught = 0,
            total_animals_gutted = 0,
            total_nets_used = 0,
            best_fish = "None"
        }

        TriggerClientEvent('olympus-fishing-hunting:client:receivePlayerStats', src, stats)
    end)
end)

-- Processing and Selling
RegisterNetEvent('olympus-fishing-hunting:server:sellItem', function(itemType, quantity, location)
    local src = source
    local Player = OlympusFishingHunting.GetPlayerData(src)
    if not Player then return end

    local itemData = nil
    local sellPrice = 0

    -- Determine item data and price
    if Config.Fishing.fishTypes[itemType] then
        itemData = Config.Fishing.fishTypes[itemType]
        sellPrice = itemData.sellPrice
    elseif Config.Hunting.animalTypes[itemType] then
        itemData = Config.Hunting.animalTypes[itemType]
        sellPrice = itemData.sellPrice
    elseif itemType == "turtle" then
        itemData = Config.TurtlePoaching.turtle
        sellPrice = itemData.sellPrice
    else
        TriggerClientEvent('olympus-fishing-hunting:client:notify', src, 'Invalid item type', 'error')
        return
    end

    -- Check if player has the item
    local success = pcall(function()
        return exports['olympus-core']:RemoveItem(src, itemType, quantity)
    end)

    if success then
        local totalPayout = sellPrice * quantity

        -- Apply location multiplier if applicable
        for _, processor in pairs(Config.Processing.fishProcessing) do
            if processor.name == location then
                totalPayout = totalPayout * processor.sellMultiplier
                break
            end
        end

        -- Add money to player
        OlympusFishingHunting.AddPlayerMoney(src, "cash", totalPayout)

        -- Notify player
        TriggerClientEvent('olympus-fishing-hunting:client:notify', src,
            string.format('Sold %dx %s for $%s', quantity, itemData.name, OlympusCore.Shared.CommaValue(totalPayout)), 'success')

        -- Log transaction
        OlympusFishingHunting.LogActivity(
            Player.citizenid,
            Player.charinfo.firstname .. " " .. Player.charinfo.lastname,
            "item_sale",
            itemType,
            quantity,
            location
        )
    else
        TriggerClientEvent('olympus-fishing-hunting:client:notify', src, 'You do not have enough of this item', 'error')
    end
end)

-- Export Functions
exports('ProcessFishCatch', OlympusFishingHunting.ProcessFishCatch)
exports('ProcessAnimalGut', OlympusFishingHunting.ProcessAnimalGut)
exports('ProcessTurtleCatch', OlympusFishingHunting.ProcessTurtleCatch)

exports('GetPlayerFishingData', function(source)
    local Player = OlympusFishingHunting.GetPlayerData(source)
    if not Player then return nil end

    return exports.oxmysql:query_async('SELECT * FROM fishing_hunting_stats WHERE player_id = ?', {Player.citizenid})
end)

exports('GetPlayerHuntingData', function(source)
    local Player = OlympusFishingHunting.GetPlayerData(source)
    if not Player then return nil end

    return exports.oxmysql:query_async('SELECT * FROM fishing_hunting_logs WHERE player_id = ? ORDER BY timestamp DESC LIMIT 10', {Player.citizenid})
end)

print("[Olympus Fishing & Hunting] Server module loaded")
