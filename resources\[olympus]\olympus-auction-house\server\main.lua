-- ========================================
-- OLYMPUS AUCTION HOUSE SYSTEM - SERVER MAIN
-- Complete recreation based on original Olympus auction mechanics
-- Handles player-to-player trading, bidding, and secure transactions
-- ========================================

local OlympusAuction = {}
OlympusAuction.ActiveAuctions = {} -- Cache of active auctions
OlympusAuction.AuctionTimers = {} -- Auction expiration timers

-- Load configuration
local Config = require('config.shared')

-- Initialize auction house system
function InitializeAuctionSystem()
    print("^2[Olympus Auction House]^7 Initializing auction system...")

    -- Load active auctions from database
    LoadActiveAuctions()

    -- Start auction cleanup system
    StartAuctionCleanup()

    -- Start auction expiration system
    StartAuctionExpirationSystem()

    print("^2[Olympus Auction House]^7 Auction system initialized!")
end

-- Load active auctions from database
function LoadActiveAuctions()
    local query = "SELECT * FROM auctions WHERE active = 1"
    exports['olympus-core']:FetchQuery(query, {}, function(result)
        if result then
            OlympusAuction.ActiveAuctions = {}

            for _, auction in ipairs(result) do
                OlympusAuction.ActiveAuctions[auction.aucID] = auction

                -- Calculate remaining time
                local startTime = os.time({
                    year = tonumber(string.sub(auction.started, 1, 4)),
                    month = tonumber(string.sub(auction.started, 6, 7)),
                    day = tonumber(string.sub(auction.started, 9, 10)),
                    hour = tonumber(string.sub(auction.started, 12, 13)),
                    min = tonumber(string.sub(auction.started, 15, 16)),
                    sec = tonumber(string.sub(auction.started, 18, 19))
                })

                local elapsed = os.time() - startTime
                local remaining = Config.AuctionHouseSystem.auctionDuration - elapsed

                if remaining > 0 then
                    -- Set expiration timer
                    SetAuctionTimer(auction.aucID, remaining)
                else
                    -- Auction expired, process it
                    ProcessExpiredAuction(auction.aucID)
                end
            end

            print("^3[Olympus Auction House]^7 Loaded " .. #result .. " active auctions")
        end
    end)
end

-- Check if player can access auction house
function CanAccessAuctionHouse(source)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData then
        return false, 'Player data not found'
    end

    -- Check if player is in excluded factions (APD or R&R)
    local excludedFactions = Config.AuctionHouseSystem.accessControl.excludedFactions
    for _, faction in pairs(excludedFactions) do
        if playerData.faction == faction then
            return false, Config.AuctionHouseSystem.accessControl.restrictionMessage
        end
    end

    return true
end

-- Create new auction listing
RegisterServerEvent('olympus-auction-house:server:createListing')
AddEventHandler('olympus-auction-house:server:createListing', function(listingData)
    local source = source
    local canAccess, reason = CanAccessAuctionHouse(source)

    if not canAccess then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Access Denied',
            message = reason,
            duration = 5000
        })
        return
    end

    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData then return end

    -- Validate listing data
    if not listingData.item or not listingData.price or not listingData.quantity then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Invalid Listing',
            message = 'Missing required listing information',
            duration = 5000
        })
        return
    end

    -- Check if player has the item
    local playerItem = exports['olympus-core']:GetPlayerItem(source, listingData.item)
    if not playerItem or playerItem.amount < listingData.quantity then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Insufficient Items',
            message = 'You don\'t have enough ' .. listingData.item .. ' to list',
            duration = 5000
        })
        return
    end

    -- Check listing fee
    local listingFee = math.floor(listingData.price * Config.AuctionHouseSystem.fees.listingFee)
    if playerData.money < listingFee then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Insufficient Funds',
            message = 'You need $' .. listingFee .. ' to list this item',
            duration = 5000
        })
        return
    end

    -- Remove item from player inventory
    exports['olympus-core']:RemovePlayerItem(source, listingData.item, listingData.quantity)

    -- Charge listing fee
    exports['olympus-core']:RemovePlayerMoney(source, listingFee)

    -- Create auction in database
    local query = "INSERT INTO auctions (playerID, playerName, className, price, quantity, type, active, server) VALUES (?, ?, ?, ?, ?, ?, 1, ?)"
    local auctionType = listingData.auctionType or 0 -- 0 = buy now, 1 = auction
    local serverID = GetConvar('olympus_server_id', '1')

    exports['olympus-core']:ExecuteQuery(query, {
        playerData.player_id,
        playerData.name,
        listingData.item,
        listingData.price,
        listingData.quantity,
        auctionType,
        serverID
    }, function(result)
        if result and result.insertId then
            local auctionID = result.insertId

            -- Add to active auctions cache
            OlympusAuction.ActiveAuctions[auctionID] = {
                aucID = auctionID,
                playerID = playerData.player_id,
                playerName = playerData.name,
                className = listingData.item,
                price = listingData.price,
                quantity = listingData.quantity,
                type = auctionType,
                active = 1,
                server = serverID,
                started = os.date('%Y-%m-%d %H:%M:%S'),
                curBidPrice = 0,
                bidderName = nil,
                bidderID = nil
            }

            -- Set expiration timer
            SetAuctionTimer(auctionID, Config.AuctionHouseSystem.auctionDuration)

            -- Notify player
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'success',
                title = 'Listing Created',
                message = string.format('Listed %dx %s for $%s (Fee: $%s)', listingData.quantity, listingData.item, listingData.price, listingFee),
                duration = 5000
            })

            -- Notify all clients of new listing
            TriggerClientEvent('olympus-auction-house:client:updateListings', -1, GetActiveListings())

            print(string.format("^3[Olympus Auction House]^7 %s listed %dx %s for $%s", playerData.name, listingData.quantity, listingData.item, listingData.price))
        else
            -- Failed to create listing, refund player
            exports['olympus-core']:AddPlayerItem(source, listingData.item, listingData.quantity)
            exports['olympus-core']:AddPlayerMoney(source, listingFee)

            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Listing Failed',
                message = 'Failed to create listing, items and fee refunded',
                duration = 5000
            })
        end
    end)
end)

-- Purchase auction item (Buy Now)
RegisterServerEvent('olympus-auction-house:server:purchaseItem')
AddEventHandler('olympus-auction-house:server:purchaseItem', function(auctionID)
    local source = source
    local canAccess, reason = CanAccessAuctionHouse(source)

    if not canAccess then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Access Denied',
            message = reason,
            duration = 5000
        })
        return
    end

    local auction = OlympusAuction.ActiveAuctions[auctionID]
    if not auction then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Invalid Auction',
            message = 'This auction no longer exists',
            duration = 5000
        })
        return
    end

    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData then return end

    -- Check if player is trying to buy their own item
    if playerData.player_id == auction.playerID then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Invalid Purchase',
            message = 'You cannot purchase your own listing',
            duration = 5000
        })
        return
    end

    -- Check if player has enough money
    local totalPrice = auction.price
    if playerData.money < totalPrice then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Insufficient Funds',
            message = 'You need $' .. totalPrice .. ' to purchase this item',
            duration = 5000
        })
        return
    end

    -- Process purchase
    ProcessAuctionSale(auctionID, source, totalPrice)
end)

-- Place bid on auction
RegisterServerEvent('olympus-auction-house:server:placeBid')
AddEventHandler('olympus-auction-house:server:placeBid', function(auctionID, bidAmount)
    local source = source
    local canAccess, reason = CanAccessAuctionHouse(source)

    if not canAccess then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Access Denied',
            message = reason,
            duration = 5000
        })
        return
    end

    local auction = OlympusAuction.ActiveAuctions[auctionID]
    if not auction or auction.type ~= 1 then -- Must be auction type
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Invalid Auction',
            message = 'This auction does not accept bids',
            duration = 5000
        })
        return
    end

    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData then return end

    -- Check if player is trying to bid on their own item
    if playerData.player_id == auction.playerID then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Invalid Bid',
            message = 'You cannot bid on your own auction',
            duration = 5000
        })
        return
    end

    -- Check minimum bid amount
    local minBid = math.max(auction.price, auction.curBidPrice + Config.AuctionHouseSystem.minimumBidIncrement)
    if bidAmount < minBid then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Bid Too Low',
            message = 'Minimum bid is $' .. minBid,
            duration = 5000
        })
        return
    end

    -- Check if player has enough money
    if playerData.money < bidAmount then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Insufficient Funds',
            message = 'You need $' .. bidAmount .. ' to place this bid',
            duration = 5000
        })
        return
    end

    -- Refund previous bidder if exists
    if auction.bidderID and auction.curBidPrice > 0 then
        local previousBidder = exports['olympus-core']:GetPlayerByIdentifier(auction.bidderID)
        if previousBidder then
            exports['olympus-core']:AddPlayerMoney(previousBidder, auction.curBidPrice)
            TriggerClientEvent('olympus:client:notify', previousBidder, {
                type = 'info',
                title = 'Bid Refunded',
                message = 'Your bid of $' .. auction.curBidPrice .. ' was refunded (outbid)',
                duration = 5000
            })
        else
            -- Player offline, add to database
            local query = "UPDATE players SET money = money + ? WHERE player_id = ?"
            exports['olympus-core']:ExecuteQuery(query, {auction.curBidPrice, auction.bidderID})
        end
    end

    -- Take money from new bidder
    exports['olympus-core']:RemovePlayerMoney(source, bidAmount)

    -- Update auction
    auction.curBidPrice = bidAmount
    auction.bidderID = playerData.player_id
    auction.bidderName = playerData.name

    -- Update database
    local query = "UPDATE auctions SET curBidPrice = ?, bidderID = ?, bidderName = ? WHERE aucID = ?"
    exports['olympus-core']:ExecuteQuery(query, {bidAmount, playerData.player_id, playerData.name, auctionID})

    -- Notify bidder
    TriggerClientEvent('olympus:client:notify', source, {
        type = 'success',
        title = 'Bid Placed',
        message = 'You bid $' .. bidAmount .. ' on ' .. auction.className,
        duration = 5000
    })

    -- Notify seller if online
    local seller = exports['olympus-core']:GetPlayerByIdentifier(auction.playerID)
    if seller then
        TriggerClientEvent('olympus:client:notify', seller, {
            type = 'info',
            title = 'New Bid',
            message = playerData.name .. ' bid $' .. bidAmount .. ' on your ' .. auction.className,
            duration = 5000
        })
    end

    -- Update all clients
    TriggerClientEvent('olympus-auction-house:client:updateListings', -1, GetActiveListings())

    print(string.format("^3[Olympus Auction House]^7 %s bid $%s on %s (Auction ID: %s)", playerData.name, bidAmount, auction.className, auctionID))
end)

-- Retract auction listing
RegisterServerEvent('olympus-auction-house:server:retractListing')
AddEventHandler('olympus-auction-house:server:retractListing', function(auctionID)
    local source = source
    local auction = OlympusAuction.ActiveAuctions[auctionID]

    if not auction then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Invalid Auction',
            message = 'This auction no longer exists',
            duration = 5000
        })
        return
    end

    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData then return end

    -- Check if player owns the auction
    if playerData.player_id ~= auction.playerID then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Access Denied',
            message = 'You can only retract your own listings',
            duration = 5000
        })
        return
    end

    -- Check if there are bids (can't retract if there are bids)
    if auction.curBidPrice > 0 then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Cannot Retract',
            message = 'Cannot retract auction with active bids',
            duration = 5000
        })
        return
    end

    -- Process retraction
    ProcessAuctionRetraction(auctionID, source)
end)

-- Process auction sale
function ProcessAuctionSale(auctionID, buyerSource, salePrice)
    local auction = OlympusAuction.ActiveAuctions[auctionID]
    if not auction then return end

    local buyerData = exports['olympus-core']:GetPlayerData(buyerSource)
    if not buyerData then return end

    -- Calculate fees
    local sellerFee = math.floor(salePrice * Config.AuctionHouseSystem.fees.sellerFee)
    local sellerPayout = salePrice - sellerFee

    -- Remove money from buyer
    exports['olympus-core']:RemovePlayerMoney(buyerSource, salePrice)

    -- Give item to buyer
    exports['olympus-core']:AddPlayerItem(buyerSource, auction.className, auction.quantity)

    -- Pay seller
    local seller = exports['olympus-core']:GetPlayerByIdentifier(auction.playerID)
    if seller then
        exports['olympus-core']:AddPlayerMoney(seller, sellerPayout)
        TriggerClientEvent('olympus:client:notify', seller, {
            type = 'success',
            title = 'Item Sold',
            message = string.format('Your %dx %s sold for $%s (Fee: $%s)', auction.quantity, auction.className, sellerPayout, sellerFee),
            duration = 5000
        })
    else
        -- Seller offline, add to database
        local query = "UPDATE players SET money = money + ? WHERE player_id = ?"
        exports['olympus-core']:ExecuteQuery(query, {sellerPayout, auction.playerID})
    end

    -- Notify buyer
    TriggerClientEvent('olympus:client:notify', buyerSource, {
        type = 'success',
        title = 'Purchase Complete',
        message = string.format('Purchased %dx %s for $%s', auction.quantity, auction.className, salePrice),
        duration = 5000
    })

    -- Remove auction
    RemoveAuction(auctionID)

    print(string.format("^3[Olympus Auction House]^7 %s purchased %dx %s for $%s from %s", buyerData.name, auction.quantity, auction.className, salePrice, auction.playerName))
end

-- Process auction retraction
function ProcessAuctionRetraction(auctionID, playerSource)
    local auction = OlympusAuction.ActiveAuctions[auctionID]
    if not auction then return end

    -- Return item to player
    exports['olympus-core']:AddPlayerItem(playerSource, auction.className, auction.quantity)

    -- Notify player
    TriggerClientEvent('olympus:client:notify', playerSource, {
        type = 'success',
        title = 'Listing Retracted',
        message = string.format('Retracted %dx %s listing', auction.quantity, auction.className),
        duration = 5000
    })

    -- Remove auction
    RemoveAuction(auctionID)

    print(string.format("^3[Olympus Auction House]^7 %s retracted listing for %dx %s", auction.playerName, auction.quantity, auction.className))
end

-- Remove auction from system
function RemoveAuction(auctionID)
    -- Remove from cache
    OlympusAuction.ActiveAuctions[auctionID] = nil

    -- Clear timer
    if OlympusAuction.AuctionTimers[auctionID] then
        OlympusAuction.AuctionTimers[auctionID] = nil
    end

    -- Update database
    local query = "UPDATE auctions SET active = 0 WHERE aucID = ?"
    exports['olympus-core']:ExecuteQuery(query, {auctionID})

    -- Update all clients
    TriggerClientEvent('olympus-auction-house:client:updateListings', -1, GetActiveListings())
end

-- Set auction expiration timer
function SetAuctionTimer(auctionID, duration)
    OlympusAuction.AuctionTimers[auctionID] = os.time() + duration

    CreateThread(function()
        Wait(duration * 1000)
        ProcessExpiredAuction(auctionID)
    end)
end

-- Process expired auction
function ProcessExpiredAuction(auctionID)
    local auction = OlympusAuction.ActiveAuctions[auctionID]
    if not auction then return end

    if auction.curBidPrice > 0 and auction.bidderID then
        -- Auction had bids, complete sale to highest bidder
        local buyerSource = exports['olympus-core']:GetPlayerByIdentifier(auction.bidderID)
        if buyerSource then
            -- Buyer is online, complete sale
            ProcessAuctionSale(auctionID, buyerSource, auction.curBidPrice)
        else
            -- Buyer is offline, complete sale via database
            ProcessOfflineAuctionSale(auctionID)
        end
    else
        -- No bids, return item to seller
        local seller = exports['olympus-core']:GetPlayerByIdentifier(auction.playerID)
        if seller then
            exports['olympus-core']:AddPlayerItem(seller, auction.className, auction.quantity)
            TriggerClientEvent('olympus:client:notify', seller, {
                type = 'info',
                title = 'Auction Expired',
                message = string.format('Your %dx %s auction expired with no bids', auction.quantity, auction.className),
                duration = 5000
            })
        else
            -- Seller offline, add item to their storage or mail system
            -- For now, we'll just log it
            print(string.format("^1[Olympus Auction House]^7 Expired auction items need to be returned to offline player: %s (%dx %s)", auction.playerName, auction.quantity, auction.className))
        end

        RemoveAuction(auctionID)
    end

    print(string.format("^3[Olympus Auction House]^7 Auction expired: %dx %s (ID: %s)", auction.quantity, auction.className, auctionID))
end

-- Process offline auction sale
function ProcessOfflineAuctionSale(auctionID)
    local auction = OlympusAuction.ActiveAuctions[auctionID]
    if not auction then return end

    local salePrice = auction.curBidPrice
    local sellerFee = math.floor(salePrice * Config.AuctionHouseSystem.fees.sellerFee)
    local sellerPayout = salePrice - sellerFee

    -- Add item to buyer's inventory (via database or mail system)
    -- Add money to seller's account
    local sellerQuery = "UPDATE players SET money = money + ? WHERE player_id = ?"
    exports['olympus-core']:ExecuteQuery(sellerQuery, {sellerPayout, auction.playerID})

    -- For now, log the item that needs to be given to offline buyer
    print(string.format("^1[Olympus Auction House]^7 Offline buyer needs to receive: %dx %s (Buyer: %s)", auction.quantity, auction.className, auction.bidderName))

    RemoveAuction(auctionID)
end

-- Get active listings for client
function GetActiveListings()
    local listings = {}

    for auctionID, auction in pairs(OlympusAuction.ActiveAuctions) do
        table.insert(listings, {
            id = auctionID,
            item = auction.className,
            quantity = auction.quantity,
            price = auction.price,
            currentBid = auction.curBidPrice,
            seller = auction.playerName,
            type = auction.type, -- 0 = buy now, 1 = auction
            timeRemaining = OlympusAuction.AuctionTimers[auctionID] and (OlympusAuction.AuctionTimers[auctionID] - os.time()) or 0
        })
    end

    return listings
end

-- Start auction cleanup system
function StartAuctionCleanup()
    CreateThread(function()
        while true do
            Wait(300000) -- Every 5 minutes

            -- Clean up expired timers
            for auctionID, expireTime in pairs(OlympusAuction.AuctionTimers) do
                if os.time() >= expireTime then
                    ProcessExpiredAuction(auctionID)
                end
            end
        end
    end)
end

-- Start auction expiration system
function StartAuctionExpirationSystem()
    CreateThread(function()
        while true do
            Wait(60000) -- Every minute

            -- Check for auctions that should have expired
            local query = "SELECT aucID FROM auctions WHERE active = 1 AND TIMESTAMPDIFF(SECOND, started, NOW()) > ?"
            exports['olympus-core']:FetchQuery(query, {Config.AuctionHouseSystem.auctionDuration}, function(result)
                if result then
                    for _, row in ipairs(result) do
                        ProcessExpiredAuction(row.aucID)
                    end
                end
            end)
        end
    end)
end

-- Handle auction house access request
RegisterServerEvent('olympus-auction-house:server:requestAccess')
AddEventHandler('olympus-auction-house:server:requestAccess', function()
    local source = source
    local canAccess, reason = CanAccessAuctionHouse(source)

    if canAccess then
        TriggerClientEvent('olympus-auction-house:client:openAuctionHouse', source, GetActiveListings())
    else
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Access Denied',
            message = reason,
            duration = 5000
        })
    end
end)

-- Initialize system when server starts
CreateThread(function()
    -- Wait for core system
    while not exports['olympus-core'] do
        Wait(100)
    end

    Wait(2000) -- Additional wait for database
    InitializeAuctionSystem()
end)

-- Export functions
exports('GetActiveListings', GetActiveListings)
exports('CanAccessAuctionHouse', CanAccessAuctionHouse)
exports('GetAuctionStats', function()
    return {
        totalListings = #OlympusAuction.ActiveAuctions,
        totalSales = 0 -- Could be tracked with additional database queries
    }
end)

-- Handle auction house access request
RegisterServerEvent('olympus-auction-house:server:requestAccess')
AddEventHandler('olympus-auction-house:server:requestAccess', function()
    local source = source
    local canAccess, reason = CanAccessAuctionHouse(source)

    if canAccess then
        -- Send auction house data to client
        TriggerClientEvent('olympus-auction-house:client:openAuctionHouse', source, Config.AuctionSystem)
    else
        -- Send access denied message
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Access Denied',
            message = reason
        })
    end
end)

-- Handle listing creation
RegisterServerEvent('olympus-auction-house:server:createListing')
AddEventHandler('olympus-auction-house:server:createListing', function(itemData)
    local source = source
    local canAccess, reason = CanAccessAuctionHouse(source)

    if not canAccess then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Access Denied',
            message = reason
        })
        return
    end

    -- TODO: Implement actual listing creation logic
    print(string.format("^3[Olympus Auction House]^7 %s created listing for %s", GetPlayerName(source), itemData.name or 'unknown item'))

    TriggerClientEvent('olympus:client:notify', source, {
        type = 'success',
        title = 'Listing Created',
        message = 'Your item has been listed for auction'
    })
end)

-- Exports
exports('CreateListing', function(source, itemData)
    local canAccess = CanAccessAuctionHouse(source)
    if not canAccess then return false end

    print("[Olympus Auction House] Creating listing...")
    return true
end)

exports('PurchaseListing', function(source, listingId)
    local canAccess = CanAccessAuctionHouse(source)
    if not canAccess then return false end

    print("[Olympus Auction House] Purchasing listing...")
    return true
end)

exports('RetractListing', function(source, listingId)
    local canAccess = CanAccessAuctionHouse(source)
    if not canAccess then return false end

    print("[Olympus Auction House] Retracting listing...")
    return true
end)

exports('GetAuctionStats', function()
    return {totalListings = 0, totalSales = 0}
end)

exports('CanAccessAuctionHouse', CanAccessAuctionHouse)

print("[Olympus Auction House] Server module loaded")
