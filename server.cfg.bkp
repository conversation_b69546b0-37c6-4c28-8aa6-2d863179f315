# This file is a minimal version of the default config file.
# This is NOT supposed to be enough for most servers.
# Please read the fivem documentation:
#   https://aka.cfx.re/server-commands
#   https://docs.fivem.net/docs/server-manual/setting-up-a-server/

## You SHOULD edit the following:
sv_hostname "Test Idea built with FiveM Basic Server!"
sets sv_projectName "[FiveM Basic Server] Test Idea"
sets sv_projectDesc "Recipe for the base resources required to run a minimal FiveM server."
sets tags "default, deployer"
sets locale "root-AQ"

## You CAN edit the following:
sv_enforceGameBuild 3258 #mp2024_01	- Bottom Dollar Bounties
sv_licenseKey "pq8bljns1gg7rozaxhqm0vmvv4y0ly4z"
sv_maxclients 48
endpoint_add_tcp "0.0.0.0:30120"
endpoint_add_udp "0.0.0.0:30120"
set steam_webApiKey "none"
set resources_useSystemChat true

## These resources will start by default.
ensure mapmanager
ensure chat
ensure spawnmanager
ensure sessionmanager
ensure basic-gamemode
ensure hardcap

## Add system admins
add_ace group.admin command allow # allow all commands
add_ace group.admin command.quit deny # but don't allow quit
add_principal identifier.fivem:534750 group.admin #zzz11
