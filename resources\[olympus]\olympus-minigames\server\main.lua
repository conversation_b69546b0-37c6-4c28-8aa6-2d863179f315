-- Olympus Mini-Games - Server Main

CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end
    
    print("[Olympus Mini-Games] Server initialized")
end)

exports('ValidateMinigame', function(source, gameType, result)
    print("[Olympus Mini-Games] Validating minigame:", gameType)
    return true
end)

exports('UpdateSkillLevel', function(source, skill, xp)
    print("[Olympus Mini-Games] Updating skill level:", skill)
end)

exports('ProcessCrafting', function(source, recipe)
    print("[Olympus Mini-Games] Processing crafting:", recipe)
end)

print("[Olympus Mini-Games] Server module loaded")
