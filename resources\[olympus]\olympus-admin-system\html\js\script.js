// Olympus Admin Panel JavaScript

let selectedPlayer = null;
let adminPanel = null;

document.addEventListener('DOMContentLoaded', function() {
    adminPanel = document.getElementById('admin-panel');
    
    // Tab switching
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.dataset.tab;
            
            // Remove active class from all tabs
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked tab
            this.classList.add('active');
            document.getElementById(tabName + '-tab').classList.add('active');
        });
    });
    
    // Close panel
    document.getElementById('close-btn').addEventListener('click', function() {
        closePanel();
    });
    
    // Player actions
    document.getElementById('teleport-to').addEventListener('click', function() {
        if (selectedPlayer) {
            fetch(`https://${GetParentResourceName()}/teleportToPlayer`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    playerId: selectedPlayer.id
                })
            });
        }
    });
    
    document.getElementById('bring-player').addEventListener('click', function() {
        if (selectedPlayer) {
            fetch(`https://${GetParentResourceName()}/bringPlayer`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    playerId: selectedPlayer.id
                })
            });
        }
    });
    
    document.getElementById('kick-player').addEventListener('click', function() {
        if (selectedPlayer) {
            showKickModal();
        }
    });
    
    document.getElementById('ban-player').addEventListener('click', function() {
        if (selectedPlayer) {
            showBanModal();
        }
    });
    
    // Modal handlers
    document.getElementById('confirm-kick').addEventListener('click', function() {
        const reason = document.getElementById('kick-reason').value;
        if (selectedPlayer) {
            fetch(`https://${GetParentResourceName()}/kickPlayer`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    playerId: selectedPlayer.id,
                    reason: reason
                })
            });
        }
        hideKickModal();
    });
    
    document.getElementById('cancel-kick').addEventListener('click', function() {
        hideKickModal();
    });
    
    document.getElementById('confirm-ban').addEventListener('click', function() {
        const reason = document.getElementById('ban-reason').value;
        const duration = document.getElementById('ban-duration').value;
        if (selectedPlayer) {
            fetch(`https://${GetParentResourceName()}/banPlayer`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    playerId: selectedPlayer.id,
                    reason: reason,
                    duration: parseInt(duration)
                })
            });
        }
        hideBanModal();
    });
    
    document.getElementById('cancel-ban').addEventListener('click', function() {
        hideBanModal();
    });
    
    // Player search
    document.getElementById('player-search').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const playerItems = document.querySelectorAll('.player-item');
        
        playerItems.forEach(item => {
            const playerName = item.querySelector('.player-name').textContent.toLowerCase();
            if (playerName.includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    });
});

// NUI Message Handler
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.type) {
        case 'togglePanel':
            if (data.show) {
                showPanel();
            } else {
                hidePanel();
            }
            break;
        case 'updatePlayers':
            updatePlayerList(data.players);
            break;
        case 'updateServerInfo':
            updateServerInfo(data.info);
            break;
        case 'updateLogs':
            updateLogs(data.logs);
            break;
    }
});

function showPanel() {
    adminPanel.classList.remove('hidden');
    document.body.style.cursor = 'default';
}

function hidePanel() {
    adminPanel.classList.add('hidden');
    document.body.style.cursor = 'none';
}

function closePanel() {
    fetch(`https://${GetParentResourceName()}/closePanel`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    });
}

function updatePlayerList(players) {
    const container = document.getElementById('players-container');
    container.innerHTML = '';
    
    players.forEach(player => {
        const playerItem = document.createElement('div');
        playerItem.className = 'player-item';
        playerItem.innerHTML = `
            <div class="player-info">
                <div class="player-name">${player.name}</div>
                <div class="player-id">ID: ${player.id}</div>
            </div>
            <div class="player-status">
                <span class="status-indicator ${player.online ? 'online' : 'offline'}"></span>
            </div>
        `;
        
        playerItem.addEventListener('click', function() {
            // Remove selection from other players
            document.querySelectorAll('.player-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // Select this player
            this.classList.add('selected');
            selectedPlayer = player;
        });
        
        container.appendChild(playerItem);
    });
}

function updateServerInfo(info) {
    document.getElementById('players-online').textContent = info.playersOnline;
    document.getElementById('server-uptime').textContent = info.uptime;
    document.getElementById('server-performance').textContent = info.performance;
}

function updateLogs(logs) {
    const container = document.getElementById('logs-list');
    container.innerHTML = '';
    
    logs.forEach(log => {
        const logItem = document.createElement('div');
        logItem.className = 'log-item';
        logItem.innerHTML = `
            <div class="log-timestamp">${new Date(log.timestamp * 1000).toLocaleString()}</div>
            <div><span class="log-action">${log.action}</span> by ${log.adminName}: ${log.details}</div>
        `;
        container.appendChild(logItem);
    });
}

function showKickModal() {
    document.getElementById('kick-modal').classList.remove('hidden');
    document.getElementById('kick-reason').value = '';
    document.getElementById('kick-reason').focus();
}

function hideKickModal() {
    document.getElementById('kick-modal').classList.add('hidden');
}

function showBanModal() {
    document.getElementById('ban-modal').classList.remove('hidden');
    document.getElementById('ban-reason').value = '';
    document.getElementById('ban-duration').value = '0';
    document.getElementById('ban-reason').focus();
}

function hideBanModal() {
    document.getElementById('ban-modal').classList.add('hidden');
}

// Escape key handler
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        if (!document.getElementById('kick-modal').classList.contains('hidden')) {
            hideKickModal();
        } else if (!document.getElementById('ban-modal').classList.contains('hidden')) {
            hideBanModal();
        } else if (!adminPanel.classList.contains('hidden')) {
            closePanel();
        }
    }
});

// Utility function to get resource name
function GetParentResourceName() {
    return 'olympus-admin-system';
}
