-- Olympus Community Goals - Server Main

CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end
    
    print("[Olympus Community Goals] Server initialized")
end)

exports('CreateCommunityGoal', function(goalData)
    print("[Olympus Community Goals] Creating goal:", goalData.name)
end)

exports('AddContribution', function(source, goalId, amount)
    print("[Olympus Community Goals] Adding contribution:", amount)
end)

exports('CompleteGoal', function(goalId)
    print("[Olympus Community Goals] Completing goal:", goalId)
end)

exports('GetGoalProgress', function(goalId)
    return {current = 0, target = 100}
end)

print("[Olympus Community Goals] Server module loaded")
