# Olympus UI Framework

A comprehensive UI framework for the Olympus Altis Life FiveM server, recreated from the original Arma 3 Altis Life server dump to provide an authentic Olympus experience.

## Features

### HUD System
- **Status Bars**: Health, Food, Water, Stamina, and Wanted Level
- **Info Display**: Cash, Bank, Duty Status, Location, and Time
- **Admin HUD**: Toggle displays for admin functions
- **Earplugs Indicator**: Visual indicator when earplugs are active
- **Real-time Updates**: Automatic updates every second

### Notification System
- **Multiple Types**: Info, Success, Error, Warning, Admin, Police, Medical
- **Sound Support**: Optional notification sounds
- **Auto-dismiss**: Configurable duration with automatic removal
- **Queue Management**: Limits maximum notifications displayed
- **Authentic Styling**: Matches original Olympus notification design

### Y Menu (P Menu)
- **12 Tabs**: Complete recreation of original Olympus P Menu
  - Licenses
  - Key Chain
  - Gang Management
  - Cell Phone
  - Wanted List
  - Admin Menu
  - Settings
  - Market
  - Statistics
  - Smart Phone
  - GPS
  - Sync Data
- **Dynamic Content**: Server-generated content based on player permissions
- **Interactive Elements**: Buttons, forms, and actions
- **Authentic Design**: Hexagonal grid layout matching original

### Progress Bar System
- **Interruptable Actions**: Based on original `fn_interruptableAction.sqf`
- **Control Disabling**: Configurable movement, combat, and mouse restrictions
- **Animation Support**: Play animations during progress
- **Cancellation**: ESC/X key cancellation with callbacks
- **Visual Feedback**: Smooth progress animation

### Title Text System
- **Large Messages**: Centered screen messages
- **Auto-fade**: Configurable duration and fade effects
- **Rich Text**: Support for HTML formatting
- **Based on Original**: Recreated from `fn_titleNotification.sqf`

## Installation

1. Ensure the resource is in your `resources/[olympus]/` directory
2. Add `ensure olympus-ui` to your server.cfg
3. Make sure `olympus-core` is loaded before this resource
4. Add required image and sound assets to the respective directories

## Dependencies

- **olympus-core**: Core framework (required)
- **olympus-apd**: For APD duty status (optional)
- **olympus-medical**: For R&R duty status (optional)
- **oxmysql**: Database operations (required)

## Configuration

Edit `config/shared.lua` to customize:
- HUD positioning and styling
- Notification types and durations
- Y Menu tab configuration
- Progress bar settings
- Title text styling

## Usage

### Client-side Exports

```lua
-- Notifications
exports['olympus-ui']:ShowNotification("Message", "type", duration)

-- Progress Bar
exports['olympus-ui']:ShowProgressBar({
    text = "Processing...",
    duration = 5000,
    canCancel = true
})

-- Title Text
exports['olympus-ui']:ShowTitleText({
    text = "Large Message",
    duration = 3000
})

-- Y Menu
exports['olympus-ui']:ShowYMenu()
exports['olympus-ui']:HideYMenu()

-- HUD
exports['olympus-ui']:ToggleHUD(visible)
exports['olympus-ui']:UpdateHUD(data)
```

### Server-side Exports

```lua
-- Notifications
exports['olympus-ui']:Notify(source, "Message", "type", duration)
exports['olympus-ui']:NotifyAll("Message", "type", duration)

-- Client Messages (Original fn_clientMessage.sqf)
exports['olympus-ui']:SendClientMessage(source, message, from, messageType)

-- Title Text
exports['olympus-ui']:ShowTitleText(source, "Text", duration)
exports['olympus-ui']:ShowTitleTextAll("Text", duration)

-- Y Menu
exports['olympus-ui']:ShowYMenu(source, data)
exports['olympus-ui']:UpdateYMenuContent(source, data)
```

### Events

```lua
-- Client Events
TriggerEvent('olympus-ui:client:showNotification', data)
TriggerEvent('olympus-ui:client:showProgressBar', data)
TriggerEvent('olympus-ui:client:showTitleText', data)
TriggerEvent('olympus-ui:client:showYMenu', data)
TriggerEvent('olympus-ui:client:toggleHUD', visible)

-- Server Events
TriggerServerEvent('olympus-ui:server:requestYMenuContent', tabId)
TriggerServerEvent('olympus-ui:server:yMenuAction', action, data)
```

## Test Commands

For testing purposes, the following commands are available:

```
/testui notification [type] - Test notifications
/testui progress - Test progress bar
/testui title - Test title text
/testui ymenu - Test Y Menu
/testui hud - Toggle HUD
/testhud - Update HUD with sample data
```

## File Structure

```
olympus-ui/
├── fxmanifest.lua          # Resource manifest
├── config/
│   └── shared.lua          # Configuration file
├── client/
│   ├── main.lua           # Main client logic
│   ├── notifications.lua   # Notification system
│   ├── ymenu.lua          # Y Menu system
│   └── progressbar.lua    # Progress bar system
├── server/
│   └── main.lua           # Server-side logic
├── html/
│   ├── index.html         # Main UI HTML
│   ├── js/
│   │   └── script.js      # JavaScript functionality
│   ├── images/            # UI icons and images
│   └── sounds/            # Notification sounds
└── README.md              # This file
```

## Customization

### Adding New Notification Types

1. Add the type to `config/shared.lua`
2. Add CSS styling in `html/index.html`
3. Add sound file to `html/sounds/`

### Adding New Y Menu Tabs

1. Add tab configuration to `config/shared.lua`
2. Add content generator in `server/main.lua`
3. Add icon to `html/images/`

### Styling Changes

All styling is contained in `html/index.html` within the `<style>` tags. Modify the CSS to change colors, positioning, animations, etc.

## Compatibility

This UI framework is designed to be compatible with existing Olympus systems and provides compatibility functions for scripts that may use the old notification or progress bar systems.

## Support

This resource is based on the original Olympus Altis Life server dump and recreates the authentic Olympus UI experience. For issues or customization requests, refer to the original Olympus documentation or community resources.

## Version

Current Version: 1.0.0
Based on: Original Olympus Altis Life Server Dump
Compatible with: FiveM/CFX Lua Framework
