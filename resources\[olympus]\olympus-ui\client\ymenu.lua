-- ========================================
-- OLYMPUS UI FRAMEWORK - Y MENU
-- Based on Original Olympus P Menu
-- ========================================

local OlympusYMenu = {}
local yMenuOpen = false

-- ========================================
-- KEY BINDINGS
-- ========================================

RegisterKeyMapping('olympus_ymenu', 'Open Olympus Y Menu', 'keyboard', 'Y')

RegisterCommand('olympus_ymenu', function()
    if not yMenuOpen then
        OlympusYMenu.OpenYMenu()
    else
        OlympusYMenu.CloseYMenu()
    end
end, false)

-- ========================================
-- Y MENU FUNCTIONS
-- ========================================

function OlympusYMenu.OpenYMenu()
    if yMenuOpen then return end
    
    local playerData = exports['olympus-core']:GetPlayerData()
    if not playerData then
        exports['olympus-ui']:ShowNotification({
            message = "Player data not loaded yet. Please try again.",
            type = "error"
        })
        return
    end
    
    -- Check if player is in a restricted state
    if OlympusYMenu.IsRestricted() then
        exports['olympus-ui']:ShowNotification({
            message = "You cannot open the menu right now.",
            type = "error"
        })
        return
    end
    
    yMenuOpen = true
    
    -- Generate available tabs based on player permissions
    local availableTabs = OlympusYMenu.GetAvailableTabs(playerData)
    
    exports['olympus-ui']:ShowYMenu({
        tabs = availableTabs
    })
end

function OlympusYMenu.CloseYMenu()
    if not yMenuOpen then return end
    
    yMenuOpen = false
    exports['olympus-ui']:HideYMenu()
end

function OlympusYMenu.IsRestricted()
    local playerPed = PlayerPedId()
    
    -- Check if player is dead
    if IsEntityDead(playerPed) then
        return true
    end
    
    -- Check if player is in a vehicle and moving
    if IsPedInAnyVehicle(playerPed, false) then
        local vehicle = GetVehiclePedIsIn(playerPed, false)
        local speed = GetEntitySpeed(vehicle)
        if speed > 5.0 then -- Moving faster than 5 m/s
            return true
        end
    end
    
    -- Check if player is restrained
    if exports['olympus-player-interactions'] and exports['olympus-player-interactions']:IsRestrained() then
        return true
    end
    
    -- Check if player is in combat
    if GetPlayerTargetEntity(PlayerId()) ~= 0 then
        return true
    end
    
    return false
end

function OlympusYMenu.GetAvailableTabs(playerData)
    local tabs = {}
    
    -- Always available tabs
    table.insert(tabs, {
        id = 'licenses',
        name = 'Licenses',
        icon = 'license.png',
        enabled = true
    })
    
    table.insert(tabs, {
        id = 'keychain',
        name = 'Key Chain',
        icon = 'keys.png',
        enabled = true
    })
    
    table.insert(tabs, {
        id = 'settings',
        name = 'Settings',
        icon = 'settings.png',
        enabled = true
    })
    
    table.insert(tabs, {
        id = 'stats',
        name = 'Statistics',
        icon = 'stats.png',
        enabled = true
    })
    
    table.insert(tabs, {
        id = 'sync',
        name = 'Sync Data',
        icon = 'sync.png',
        enabled = true
    })
    
    -- Gang tab (if player is in a gang)
    if playerData.gang and playerData.gang ~= "" then
        table.insert(tabs, {
            id = 'gang',
            name = 'Gang',
            icon = 'gang.png',
            enabled = true
        })
    end
    
    -- Cell phone tab
    table.insert(tabs, {
        id = 'cell',
        name = 'Cell Phone',
        icon = 'phone.png',
        enabled = true
    })
    
    -- Smart phone tab (if player has smartphone)
    if OlympusYMenu.HasSmartphone(playerData) then
        table.insert(tabs, {
            id = 'smartphone',
            name = 'Smart Phone',
            icon = 'smartphone.png',
            enabled = true
        })
    end
    
    -- GPS tab
    table.insert(tabs, {
        id = 'gps',
        name = 'GPS',
        icon = 'gps.png',
        enabled = true
    })
    
    -- Market tab
    table.insert(tabs, {
        id = 'market',
        name = 'Market',
        icon = 'market.png',
        enabled = true
    })
    
    -- Faction-specific tabs
    if playerData.faction then
        if playerData.faction == 'police' then
            table.insert(tabs, {
                id = 'wanted',
                name = 'Wanted List',
                icon = 'wanted.png',
                enabled = true
            })
        end
    end
    
    -- Admin tab (if player is admin)
    if playerData.adminLevel and playerData.adminLevel > 0 then
        table.insert(tabs, {
            id = 'admin',
            name = 'Admin Menu',
            icon = 'admin.png',
            enabled = true
        })
    end
    
    return tabs
end

function OlympusYMenu.HasSmartphone(playerData)
    -- Check if player has smartphone item
    if playerData.inventory then
        for _, item in pairs(playerData.inventory) do
            if item.name == 'smartphone' and item.count > 0 then
                return true
            end
        end
    end
    return false
end

-- ========================================
-- EVENT HANDLERS
-- ========================================

RegisterNetEvent('olympus-ymenu:client:open', function()
    OlympusYMenu.OpenYMenu()
end)

RegisterNetEvent('olympus-ymenu:client:close', function()
    OlympusYMenu.CloseYMenu()
end)

RegisterNetEvent('olympus-ymenu:client:toggle', function()
    if yMenuOpen then
        OlympusYMenu.CloseYMenu()
    else
        OlympusYMenu.OpenYMenu()
    end
end)

RegisterNetEvent('olympus-ymenu:client:refresh', function()
    if yMenuOpen then
        OlympusYMenu.CloseYMenu()
        Wait(100)
        OlympusYMenu.OpenYMenu()
    end
end)

-- Handle Y Menu closing from UI
RegisterNetEvent('olympus-ui:client:yMenuClosed', function()
    yMenuOpen = false
end)

-- ========================================
-- EXPORTS
-- ========================================

exports('OpenYMenu', OlympusYMenu.OpenYMenu)
exports('CloseYMenu', OlympusYMenu.CloseYMenu)
exports('IsYMenuOpen', function() return yMenuOpen end)

-- ========================================
-- COMPATIBILITY
-- ========================================

-- For compatibility with existing scripts that might call the old P Menu
RegisterNetEvent('life_fnc:openPMenu', function()
    OlympusYMenu.OpenYMenu()
end)

RegisterNetEvent('life_fnc:closePMenu', function()
    OlympusYMenu.CloseYMenu()
end)

-- Command aliases
RegisterCommand('pmenu', function()
    OlympusYMenu.OpenYMenu()
end, false)

RegisterCommand('menu', function()
    OlympusYMenu.OpenYMenu()
end, false)
