-- Olympus Economy System - Complete Legal Runs Implementation
-- Based on exact Olympus Altis Life legal runs system with all mechanics

Config.LegalRuns = {}

-- Worker's Protection License System (Exact Olympus Implementation)
Config.WorkersProtection = {
    enabled = true,
    cost = 75000, -- $75,000 at gun store
    bonus = 0.15, -- 15% markup when selling legal goods
    processingBonus = true, -- Faster processing with WPL
    
    -- WPL Weapons (Exact Olympus List)
    weapons = {
        {name = '4-Five .45', price = 7500, caliber = '.45 ACP', magazine = 11, magPrice = 500},
        {name = 'Vermin', price = 40000, caliber = '.45 ACP', magazine = 30, magPrice = 500},
        {name = 'TRG-20', price = 50000, caliber = '5.56x45mm NATO', magazine = 30, magPrice = 400},
        {name = 'MXC', price = 50000, caliber = '6.5x39mm NATO', magazine = 30, magPrice = 650},
        {name = 'SPAR-16 (Khaki)', price = 55000, caliber = '5.56x45mm NATO', magazine = 30, magPrice = 400, dlc = 'Apex'},
        {name = 'Katiba Carbine', price = 65000, caliber = '6.5x39mm Caseless', magazine = 30, magPrice = 650}
    },
    
    -- WPL Equipment (Exact Olympus List)
    equipment = {
        {name = 'First Aid Kit', price = 500},
        {name = 'First Aid Kit +', price = 500},
        {name = 'Tool Kit', price = 500},
        {name = 'GPS', price = 50},
        {name = 'Map', price = 50},
        {name = 'Compass', price = 50},
        {name = 'Watch', price = 50},
        {name = 'Binocular', price = 1000},
        {name = 'Rangefinder', price = 2000},
        {name = 'Night Vision', price = 1000},
        {name = 'Fullscreen NVGs', price = 5000}
    },
    
    -- WPL Attachments (Exact Olympus List)
    attachments = {
        {name = 'Flashlight', price = 500},
        {name = 'Pistol Flashlight', price = 500},
        {name = 'MRD', price = 1000},
        {name = 'ACO', price = 1000},
        {name = 'MK17 Holosight', price = 1000},
        {name = 'MRCO Scope', price = 1000},
        {name = 'RCO Scope', price = 1000},
        {name = 'ARCO Scope', price = 1000},
        {name = 'ERCO', price = 1000}
    }
}

-- Salt Run (Exact Olympus Implementation)
Config.LegalRuns['salt'] = {
    name = 'Salt',
    displayName = 'Salt Mining',
    
    -- Exact Olympus Locations
    locations = {
        mine = vector3(2100.5, 1850.2, 35.0), -- Southwest of Sofia
        processor = vector3(2150.8, 1900.5, 35.0), -- North of salt mine
        trader = vector3(2120.2, 1820.8, 35.0) -- South of processor
    },
    
    -- Exact Olympus Requirements
    requirements = {
        license = 'salt_processing',
        licenseCost = 12000, -- $12,000 at DMV
        pickaxe = true, -- Required for collecting
        vehicle = true,
        backpack = true,
        items = {'food', 'water'}
    },
    
    -- Exact Olympus Processing Mechanics
    processing = {
        time = 5, -- 5 seconds per item
        yield = 1, -- 1:1 ratio (salt to refined salt)
        range = 25, -- Must stay within 25m of machine
        cancellable = true, -- Can cancel and keep progress
        wplBonus = true -- Faster with Worker's Protection License
    },
    
    -- Exact Olympus Pricing
    pricing = {
        base = 1915, -- Base price per refined salt
        wplBonus = 0.15, -- 15% bonus with WPL
        
        -- Market fluctuation
        fluctuation = {
            enabled = true,
            min = 0.8,
            max = 1.2
        }
    },
    
    -- Exact Olympus Payouts (Box Truck + Bergen = 210 refined salt)
    payouts = {
        standard = 402150, -- $402,150 without WPL
        wpl = 462473 -- $462,473 with WPL (15% bonus)
    },
    
    -- Run Time (Exact Olympus Data)
    runTime = {
        standard = 1500, -- 25 minutes
        wpl = 1320 -- 20-22 minutes with WPL processing bonus
    }
}

-- Copper Run (Exact Olympus Implementation)
Config.LegalRuns['copper'] = {
    name = 'Copper',
    displayName = 'Copper Mining',
    
    -- Exact Olympus Locations
    locations = {
        mine = vector3(1500.2, 2800.5, 35.0), -- East of Kavala
        processor = vector3(1450.8, 2850.2, 35.0), -- Northwest of mine
        trader = vector3(1420.5, 2780.8, 35.0) -- South of processor
    },
    
    requirements = {
        license = 'copper_processing',
        licenseCost = 8000, -- $8,000 at DMV
        pickaxe = true,
        vehicle = true,
        backpack = true,
        items = {'food', 'water'}
    },
    
    processing = {
        time = 5,
        yield = 1, -- Copper to copper ingots
        range = 25,
        cancellable = true,
        wplBonus = true
    },
    
    pricing = {
        base = 1996, -- Base price per copper ingot
        wplBonus = 0.15,
        
        fluctuation = {
            enabled = true,
            min = 0.8,
            max = 1.2
        }
    },
    
    payouts = {
        standard = 419160, -- $419,160 without WPL
        wpl = 482034 -- $482,034 with WPL
    },
    
    runTime = {
        standard = 1650, -- 27 minutes 30 seconds
        wpl = 1440 -- 23-25 minutes with WPL
    }
}

-- Iron Run (Exact Olympus Implementation)
Config.LegalRuns['iron'] = {
    name = 'Iron',
    displayName = 'Iron Mining',
    
    locations = {
        mine = vector3(2200.5, 1200.8, 35.0), -- Iron mine location
        processor = vector3(2150.2, 1250.5, 35.0), -- Iron processor
        trader = vector3(2100.8, 1180.2, 35.0) -- Iron trader
    },
    
    requirements = {
        license = 'iron_processing',
        licenseCost = 9500, -- $9,500 at DMV
        pickaxe = true,
        vehicle = true,
        backpack = true,
        items = {'food', 'water'}
    },
    
    processing = {
        time = 5,
        yield = 1, -- Iron ore to iron ingots
        range = 25,
        cancellable = true,
        wplBonus = true
    },
    
    pricing = {
        base = 2100, -- Base price per iron ingot
        wplBonus = 0.15,
        
        fluctuation = {
            enabled = true,
            min = 0.8,
            max = 1.2
        }
    },
    
    payouts = {
        standard = 441000, -- Estimated payout
        wpl = 507150 -- With WPL bonus
    },
    
    runTime = {
        standard = 1800, -- 30 minutes
        wpl = 1560 -- 26 minutes with WPL
    }
}

-- Oil Run (Exact Olympus Implementation)
Config.LegalRuns['oil'] = {
    name = 'Oil',
    displayName = 'Oil Extraction',
    
    locations = {
        field = vector3(1800.5, 3200.2, 35.0), -- Oil field
        processor = vector3(1750.8, 3150.5, 35.0), -- Oil refinery
        trader = vector3(1720.2, 3100.8, 35.0) -- Oil trader
    },
    
    requirements = {
        license = 'oil_processing',
        licenseCost = 10000, -- $10,000 at DMV
        pickaxe = false, -- Oil doesn't require pickaxe
        vehicle = true,
        backpack = true,
        items = {'food', 'water'}
    },
    
    processing = {
        time = 6, -- Slightly longer processing
        yield = 1, -- Crude oil to refined oil
        range = 25,
        cancellable = true,
        wplBonus = true
    },
    
    pricing = {
        base = 2300, -- Higher value than basic minerals
        wplBonus = 0.15,
        
        fluctuation = {
            enabled = true,
            min = 0.8,
            max = 1.2
        }
    },
    
    payouts = {
        standard = 483000, -- Higher payout
        wpl = 555450 -- With WPL bonus
    },
    
    runTime = {
        standard = 2100, -- 35 minutes (longer processing)
        wpl = 1800 -- 30 minutes with WPL
    }
}

-- Diamond Run (Exact Olympus Implementation)
Config.LegalRuns['diamond'] = {
    name = 'Diamond',
    displayName = 'Diamond Mining',
    
    locations = {
        mine = vector3(2500.2, 2800.5, 35.0), -- Diamond mine
        processor = vector3(2450.8, 2750.2, 35.0), -- Diamond processor
        trader = vector3(2400.5, 2700.8, 35.0) -- Diamond trader
    },
    
    requirements = {
        license = 'diamond_processing',
        licenseCost = 35000, -- $35,000 at DMV (expensive)
        pickaxe = true,
        vehicle = true,
        backpack = true,
        items = {'food', 'water'}
    },
    
    processing = {
        time = 8, -- Longer processing for valuable items
        yield = 1, -- Raw diamonds to cut diamonds
        range = 25,
        cancellable = true,
        wplBonus = true
    },
    
    pricing = {
        base = 3500, -- High value item
        wplBonus = 0.15,
        
        fluctuation = {
            enabled = true,
            min = 0.8,
            max = 1.2
        }
    },
    
    payouts = {
        standard = 735000, -- High payout
        wpl = 845250 -- With WPL bonus
    },
    
    runTime = {
        standard = 2400, -- 40 minutes (long processing)
        wpl = 2040 -- 34 minutes with WPL
    }
}

-- Silver Run (Exact Olympus Implementation)
Config.LegalRuns['silver'] = {
    name = 'Silver',
    displayName = 'Silver Mining',

    locations = {
        mine = vector3(2300.5, 2400.2, 35.0), -- Silver mine
        processor = vector3(2250.8, 2450.5, 35.0), -- Silver processor
        trader = vector3(2200.2, 2380.8, 35.0) -- Silver trader
    },

    requirements = {
        license = 'silver_processing',
        licenseCost = 9000, -- $9,000 at DMV
        pickaxe = true,
        vehicle = true,
        backpack = true,
        items = {'food', 'water'}
    },

    processing = {
        time = 6,
        yield = 1, -- Silver ore to silver ingots
        range = 25,
        cancellable = true,
        wplBonus = true
    },

    pricing = {
        base = 2200,
        wplBonus = 0.15,

        fluctuation = {
            enabled = true,
            min = 0.8,
            max = 1.2
        }
    },

    payouts = {
        standard = 462000,
        wpl = 531300
    },

    runTime = {
        standard = 1920, -- 32 minutes
        wpl = 1680 -- 28 minutes with WPL
    }
}

-- Platinum Run (Exact Olympus Implementation)
Config.LegalRuns['platinum'] = {
    name = 'Platinum',
    displayName = 'Platinum Mining',

    locations = {
        mine = vector3(2600.2, 2200.5, 35.0), -- Platinum mine
        processor = vector3(2550.8, 2250.2, 35.0), -- Platinum processor
        trader = vector3(2500.5, 2180.8, 35.0) -- Platinum trader
    },

    requirements = {
        license = 'platinum_processing',
        licenseCost = 10000, -- $10,000 at DMV
        pickaxe = true,
        vehicle = true,
        backpack = true,
        items = {'food', 'water'}
    },

    processing = {
        time = 7,
        yield = 1, -- Platinum ore to platinum ingots
        range = 25,
        cancellable = true,
        wplBonus = true
    },

    pricing = {
        base = 2800, -- Higher than silver
        wplBonus = 0.15,

        fluctuation = {
            enabled = true,
            min = 0.8,
            max = 1.2
        }
    },

    payouts = {
        standard = 588000,
        wpl = 676200
    },

    runTime = {
        standard = 2160, -- 36 minutes
        wpl = 1860 -- 31 minutes with WPL
    }
}

-- Glass Run (Sand Processing - Exact Olympus Implementation)
Config.LegalRuns['glass'] = {
    name = 'Glass',
    displayName = 'Sand Processing',

    locations = {
        mine = vector3(1900.5, 3400.2, 35.0), -- Sand mine
        processor = vector3(1850.8, 3450.5, 35.0), -- Glass processor
        trader = vector3(1800.2, 3380.8, 35.0) -- Glass trader
    },

    requirements = {
        license = 'sand_processing',
        licenseCost = 14500, -- $14,500 at DMV
        pickaxe = true,
        vehicle = true,
        backpack = true,
        items = {'food', 'water'}
    },

    processing = {
        time = 6,
        yield = 1, -- Sand to glass
        range = 25,
        cancellable = true,
        wplBonus = true
    },

    pricing = {
        base = 2400,
        wplBonus = 0.15,

        fluctuation = {
            enabled = true,
            min = 0.8,
            max = 1.2
        }
    },

    payouts = {
        standard = 504000,
        wpl = 579600
    },

    runTime = {
        standard = 2040, -- 34 minutes
        wpl = 1740 -- 29 minutes with WPL
    }
}

-- Cement Run (Exact Olympus Implementation)
Config.LegalRuns['cement'] = {
    name = 'Cement',
    displayName = 'Cement Mixing',

    locations = {
        quarry = vector3(2000.5, 3600.2, 35.0), -- Rock quarry
        processor = vector3(1950.8, 3650.5, 35.0), -- Cement mixer
        trader = vector3(1900.2, 3580.8, 35.0) -- Cement trader
    },

    requirements = {
        license = 'cement_mixing',
        licenseCost = 6500, -- $6,500 at DMV
        pickaxe = true,
        vehicle = true,
        backpack = true,
        items = {'food', 'water'}
    },

    processing = {
        time = 5,
        yield = 1, -- Rock to cement
        range = 25,
        cancellable = true,
        wplBonus = true
    },

    pricing = {
        base = 1800, -- Lower value basic material
        wplBonus = 0.15,

        fluctuation = {
            enabled = true,
            min = 0.8,
            max = 1.2
        }
    },

    payouts = {
        standard = 378000,
        wpl = 434700
    },

    runTime = {
        standard = 1440, -- 24 minutes
        wpl = 1260 -- 21 minutes with WPL
    }
}
