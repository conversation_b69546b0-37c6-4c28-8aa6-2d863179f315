fx_version 'cerulean'
game 'gta5'

name 'Olympus Licenses System'
description 'Complete licensing system for all activities and professions'
author 'Olympus Development Team'
version '1.0.0'

-- Dependencies
dependencies {
    'olympus-core',
    'olympus-ui',
    'olympus-economy'
}

-- Shared scripts
shared_scripts {
    'config/shared.lua'
}

-- Client scripts
client_scripts {
    'client/main.lua'
}

-- Server scripts
server_scripts {
    'server/main.lua'
}

-- Client exports
exports {
    'HasLicense',
    'GetPlayerLicenses',
    'OpenLicenseMenu',
    'RefreshLicenses'
}

-- Server exports
server_exports {
    'GrantLicense',
    'RevokeLicense',
    'CheckLicense',
    'GetLicenseInfo',
    'GetPlayerLicenses'
}
