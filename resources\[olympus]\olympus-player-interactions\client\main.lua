-- ========================================
-- OLYMPUS PLAYER INTERACTIONS CLIENT
-- Based on Original Olympus Functions
-- ========================================

local OlympusPlayerInteractions = {}
local isEscorting = false
local isBeingEscorted = false
local isSurrendered = false
local isRestrained = false
local escortTarget = nil
local escortedBy = nil

-- ========================================
-- UTILITY FUNCTIONS
-- ========================================
function OlympusPlayerInteractions.Notify(message, type)
    -- Use your notification system here
    if exports['olympus-ui'] and exports['olympus-ui'].ShowNotification then
        exports['olympus-ui']:ShowNotification(message, type)
    else
        print(message)
    end
end

function OlympusPlayerInteractions.GetClosestPlayer()
    local players = GetActivePlayers()
    local ped = PlayerPedId()
    local pos = GetEntityCoords(ped)
    local closestDistance = -1
    local closestPlayer = -1
    
    for _, player in ipairs(players) do
        if player ~= PlayerId() then
            local targetPed = GetPlayerPed(player)
            local targetPos = GetEntityCoords(targetPed)
            local distance = #(pos - targetPos)
            
            if closestDistance == -1 or closestDistance > distance then
                closestDistance = distance
                closestPlayer = player
            end
        end
    end
    
    return closestPlayer, closestDistance
end

function OlympusPlayerInteractions.LoadAnimDict(dict)
    if not HasAnimDictLoaded(dict) then
        RequestAnimDict(dict)
        while not HasAnimDictLoaded(dict) do
            Wait(1)
        end
    end
end

function OlympusPlayerInteractions.PlayAnimation(dict, anim, flag, duration)
    OlympusPlayerInteractions.LoadAnimDict(dict)
    TaskPlayAnim(PlayerPedId(), dict, anim, 8.0, -8.0, duration or -1, flag or 0, 0, false, false, false)
end

-- ========================================
-- PLAYER ROBBING SYSTEM
-- Based on fn_robAction.sqf
-- ========================================
function OlympusPlayerInteractions.RobPlayer()
    local closestPlayer, distance = OlympusPlayerInteractions.GetClosestPlayer()
    
    if closestPlayer == -1 or distance > Config.PlayerInteractions.robbing.maxDistance then
        OlympusPlayerInteractions.Notify(Config.Notifications.robbing.tooFar, 'error')
        return
    end
    
    local targetId = GetPlayerServerId(closestPlayer)
    
    -- Play robbing animation
    OlympusPlayerInteractions.PlayAnimation(
        Config.Animations.robbing.dict,
        Config.Animations.robbing.anim,
        Config.Animations.robbing.flag,
        Config.PlayerInteractions.robbing.duration
    )
    
    -- Show progress bar
    exports['olympus-ui']:ShowProgressBar("Robbing player...", Config.PlayerInteractions.robbing.duration)
    
    Wait(Config.PlayerInteractions.robbing.duration)
    
    -- Trigger server event
    TriggerServerEvent('olympus-player-interactions:robPlayer', targetId)
end

-- ========================================
-- PLAYER ESCORTING SYSTEM
-- Based on fn_escortAction.sqf
-- ========================================
function OlympusPlayerInteractions.EscortPlayer()
    if isEscorting then
        OlympusPlayerInteractions.StopEscorting()
        return
    end
    
    local closestPlayer, distance = OlympusPlayerInteractions.GetClosestPlayer()
    
    if closestPlayer == -1 or distance > Config.PlayerInteractions.escorting.maxDistance then
        OlympusPlayerInteractions.Notify(Config.Notifications.escorting.tooFar, 'error')
        return
    end
    
    local targetId = GetPlayerServerId(closestPlayer)
    TriggerServerEvent('olympus-player-interactions:escortPlayer', targetId)
end

function OlympusPlayerInteractions.StopEscorting()
    if not isEscorting then return end
    
    TriggerServerEvent('olympus-player-interactions:stopEscorting')
end

function OlympusPlayerInteractions.StartEscort(targetId)
    isEscorting = true
    escortTarget = targetId
    
    local targetPed = GetPlayerPed(GetPlayerFromServerId(targetId))
    local playerPed = PlayerPedId()
    
    -- Attach target to player
    AttachEntityToEntity(
        targetPed, playerPed,
        GetPedBoneIndex(playerPed, 11816), -- Right hand bone
        Config.PlayerInteractions.escorting.attachOffset[1],
        Config.PlayerInteractions.escorting.attachOffset[2],
        Config.PlayerInteractions.escorting.attachOffset[3],
        0.0, 0.0, 0.0, false, false, false, false, 20, true
    )
    
    OlympusPlayerInteractions.Notify(Config.Notifications.escorting.start, 'success')
    
    -- Monitor escort
    CreateThread(function()
        while isEscorting do
            Wait(500)
            
            -- Check if conditions are still valid
            if not targetPed or not DoesEntityExist(targetPed) or not IsEntityAttached(targetPed) then
                OlympusPlayerInteractions.StopEscorting()
                break
            end
            
            -- Check if player is in vehicle
            if IsPedInAnyVehicle(playerPed, false) then
                OlympusPlayerInteractions.StopEscorting()
                break
            end
        end
    end)
end

function OlympusPlayerInteractions.StopEscortClient()
    if not isEscorting then return end
    
    isEscorting = false
    
    if escortTarget then
        local targetPed = GetPlayerPed(GetPlayerFromServerId(escortTarget))
        if targetPed and DoesEntityExist(targetPed) then
            DetachEntity(targetPed, true, false)
        end
        escortTarget = nil
    end
    
    OlympusPlayerInteractions.Notify(Config.Notifications.escorting.stop, 'info')
end

-- ========================================
-- SURRENDER SYSTEM
-- Based on fn_surrender.sqf
-- ========================================
function OlympusPlayerInteractions.ToggleSurrender()
    local playerPed = PlayerPedId()
    
    -- Check if player is in vehicle
    if IsPedInAnyVehicle(playerPed, false) then
        OlympusPlayerInteractions.Notify(Config.Notifications.surrender.inVehicle, 'error')
        return
    end
    
    -- Check if player is moving
    if GetEntitySpeed(playerPed) > 1.0 then
        OlympusPlayerInteractions.Notify(Config.Notifications.surrender.moving, 'error')
        return
    end
    
    -- Check if player is restrained
    if isRestrained then
        return
    end
    
    TriggerServerEvent('olympus-player-interactions:toggleSurrender')
end

function OlympusPlayerInteractions.SetSurrenderState(state)
    isSurrendered = state
    local playerPed = PlayerPedId()
    
    if state then
        OlympusPlayerInteractions.Notify(Config.Notifications.surrender.start, 'info')
        
        -- Start surrender animation loop
        CreateThread(function()
            while isSurrendered do
                if not IsEntityPlayingAnim(playerPed, "random@mugging3", "handsup_standing_base", 3) then
                    OlympusPlayerInteractions.LoadAnimDict("random@mugging3")
                    TaskPlayAnim(playerPed, "random@mugging3", "handsup_standing_base", 8.0, -8.0, -1, 49, 0, false, false, false)
                end
                
                -- Disable controls while surrendered
                DisableControlAction(0, 24, true) -- Attack
                DisableControlAction(0, 25, true) -- Aim
                DisableControlAction(0, 47, true) -- Weapon wheel
                DisableControlAction(0, 58, true) -- Weapon wheel
                DisableControlAction(0, 263, true) -- Melee Attack 1
                DisableControlAction(0, 264, true) -- Melee Attack 2
                DisableControlAction(0, 257, true) -- Attack 2
                DisableControlAction(0, 140, true) -- Melee Attack Light
                DisableControlAction(0, 141, true) -- Melee Attack Heavy
                DisableControlAction(0, 142, true) -- Melee Attack Alternate
                DisableControlAction(0, 143, true) -- Melee Block
                
                Wait(0)
            end
        end)
    else
        OlympusPlayerInteractions.Notify(Config.Notifications.surrender.stop, 'info')
        ClearPedTasks(playerPed)
    end
end

-- ========================================
-- RESTRAINING SYSTEM
-- ========================================
function OlympusPlayerInteractions.RestrainPlayer()
    local closestPlayer, distance = OlympusPlayerInteractions.GetClosestPlayer()
    
    if closestPlayer == -1 or distance > Config.PlayerInteractions.restraining.maxDistance then
        OlympusPlayerInteractions.Notify(Config.Notifications.restraining.tooFar, 'error')
        return
    end
    
    local targetId = GetPlayerServerId(closestPlayer)
    
    -- Play restraining animation
    OlympusPlayerInteractions.PlayAnimation(
        Config.Animations.restraining.dict,
        Config.Animations.restraining.anim,
        Config.Animations.restraining.flag,
        Config.PlayerInteractions.restraining.duration
    )
    
    -- Show progress bar
    exports['olympus-ui']:ShowProgressBar("Restraining player...", Config.PlayerInteractions.restraining.duration)
    
    Wait(Config.PlayerInteractions.restraining.duration)
    
    TriggerServerEvent('olympus-player-interactions:restrainPlayer', targetId)
end

function OlympusPlayerInteractions.UnrestrainPlayer()
    local closestPlayer, distance = OlympusPlayerInteractions.GetClosestPlayer()
    
    if closestPlayer == -1 or distance > Config.PlayerInteractions.restraining.maxDistance then
        OlympusPlayerInteractions.Notify(Config.Notifications.restraining.tooFar, 'error')
        return
    end
    
    local targetId = GetPlayerServerId(closestPlayer)
    TriggerServerEvent('olympus-player-interactions:unrestrainPlayer', targetId)
end

function OlympusPlayerInteractions.SetRestrainedState(state, restrainedBy)
    isRestrained = state
    local playerPed = PlayerPedId()
    
    if state then
        -- Stop surrender if surrendered
        if isSurrendered then
            isSurrendered = false
        end
        
        -- Start restrained animation loop
        CreateThread(function()
            while isRestrained do
                if not IsEntityPlayingAnim(playerPed, "mp_arresting", "idle", 3) then
                    OlympusPlayerInteractions.LoadAnimDict("mp_arresting")
                    TaskPlayAnim(playerPed, "mp_arresting", "idle", 8.0, -8.0, -1, 49, 0, false, false, false)
                end
                
                -- Disable most controls while restrained
                DisableAllControlActions(0)
                EnableControlAction(0, 1, true) -- Look LR
                EnableControlAction(0, 2, true) -- Look UD
                EnableControlAction(0, 245, true) -- Chat
                EnableControlAction(0, 249, true) -- Push to talk
                
                Wait(0)
            end
        end)
    else
        ClearPedTasks(playerPed)
    end
end

-- ========================================
-- COMMAND REGISTRATION
-- ========================================
RegisterCommand('rob', function()
    OlympusPlayerInteractions.RobPlayer()
end, false)

RegisterCommand('escort', function()
    OlympusPlayerInteractions.EscortPlayer()
end, false)

RegisterCommand('surrender', function()
    OlympusPlayerInteractions.ToggleSurrender()
end, false)

RegisterCommand('restrain', function()
    OlympusPlayerInteractions.RestrainPlayer()
end, false)

RegisterCommand('unrestrain', function()
    OlympusPlayerInteractions.UnrestrainPlayer()
end, false)

-- ========================================
-- EVENT HANDLERS
-- ========================================
RegisterNetEvent('olympus-player-interactions:notify', function(message, type)
    OlympusPlayerInteractions.Notify(message, type)
end)

RegisterNetEvent('olympus-player-interactions:startEscort', function(targetId)
    OlympusPlayerInteractions.StartEscort(targetId)
end)

RegisterNetEvent('olympus-player-interactions:stopEscort', function()
    OlympusPlayerInteractions.StopEscortClient()
end)

RegisterNetEvent('olympus-player-interactions:beingEscorted', function(escorterId)
    isBeingEscorted = true
    escortedBy = escorterId
end)

RegisterNetEvent('olympus-player-interactions:stopBeingEscorted', function()
    isBeingEscorted = false
    escortedBy = nil
end)

RegisterNetEvent('olympus-player-interactions:surrenderToggled', function(state)
    OlympusPlayerInteractions.SetSurrenderState(state)
end)

RegisterNetEvent('olympus-player-interactions:restrained', function(restrainedBy)
    OlympusPlayerInteractions.SetRestrainedState(true, restrainedBy)
end)

RegisterNetEvent('olympus-player-interactions:unrestrained', function()
    OlympusPlayerInteractions.SetRestrainedState(false)
end)

-- ========================================
-- STATE SYNC HANDLERS
-- ========================================
AddStateBagChangeHandler('surrendered', nil, function(bagName, key, value)
    if bagName:find('player:') then
        local playerId = tonumber(bagName:gsub('player:', ''))
        if playerId == GetPlayerServerId(PlayerId()) then
            OlympusPlayerInteractions.SetSurrenderState(value)
        end
    end
end)

AddStateBagChangeHandler('restrained', nil, function(bagName, key, value)
    if bagName:find('player:') then
        local playerId = tonumber(bagName:gsub('player:', ''))
        if playerId == GetPlayerServerId(PlayerId()) then
            OlympusPlayerInteractions.SetRestrainedState(value)
        end
    end
end)

-- ========================================
-- EXPORT FUNCTIONS
-- ========================================
exports('RobPlayer', OlympusPlayerInteractions.RobPlayer)
exports('EscortPlayer', OlympusPlayerInteractions.EscortPlayer)
exports('StopEscorting', OlympusPlayerInteractions.StopEscorting)
exports('ToggleSurrender', OlympusPlayerInteractions.ToggleSurrender)
exports('RestrainPlayer', OlympusPlayerInteractions.RestrainPlayer)
exports('UnrestrainPlayer', OlympusPlayerInteractions.UnrestrainPlayer)
exports('IsPlayerSurrendered', function() return isSurrendered end)
exports('IsPlayerEscorted', function() return isBeingEscorted end)
exports('IsPlayerRestrained', function() return isRestrained end)
exports('CanInteractWithPlayer', function()
    local _, distance = OlympusPlayerInteractions.GetClosestPlayer()
    return distance ~= -1 and distance <= Config.PlayerInteractions.robbing.maxDistance
end)

-- ========================================
-- INITIALIZATION
-- ========================================
CreateThread(function()
    print("^2[Olympus Player Interactions]^7 Client system initialized")
end)
