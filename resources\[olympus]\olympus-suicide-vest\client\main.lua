-- Olympus Suicide Vest System - Client Main
-- Based on original fn_suicideBomb.sqf from Olympus Altis Life

local OlympusSuicideVest = {}

-- Client state
local isDetonating = false
local vestCheckThread = nil

-- Initialize client system
CreateThread(function()
    while not exports['olympus-core'] do
        Wait(100)
    end
    
    print("[Olympus Suicide Vest] Client initialized - Based on original fn_suicideBomb.sqf")
    
    -- Start vest checking thread
    OlympusSuicideVest.StartVestChecking()
end)

-- Utility Functions
function OlympusSuicideVest.Notify(message, type)
    TriggerEvent('olympus-core:client:notify', message, type or 'info')
end

function OlympusSuicideVest.IsWearingVest()
    local ped = PlayerPedId()
    local vestHash = GetHashKey(Config.SuicideVest.vestModel)
    local currentVest = GetPedDrawableVariation(ped, 9) -- Vest component
    
    -- Check if wearing the specific suicide vest model
    -- This is a simplified check - in a real implementation you'd need proper vest detection
    return HasPedGotWeapon(ped, GetHashKey("WEAPON_STICKYBOMB"), false) -- Placeholder check
end

function OlympusSuicideVest.IsInVehicle()
    local ped = PlayerPedId()
    return IsPedInAnyVehicle(ped, false)
end

function OlympusSuicideVest.GetPlayerCoords()
    local ped = PlayerPedId()
    return GetEntityCoords(ped)
end

function OlympusSuicideVest.IsInJammerZone()
    local coords = OlympusSuicideVest.GetPlayerCoords()
    
    for _, zone in ipairs(Config.SuicideVest.jammerZones) do
        if zone.enabled ~= false then
            local distance = #(coords - zone.coords)
            if distance < zone.radius then
                return true, zone
            end
        end
    end
    return false, nil
end

-- Vest Checking System
function OlympusSuicideVest.StartVestChecking()
    if vestCheckThread then return end
    
    vestCheckThread = CreateThread(function()
        while true do
            Wait(1000) -- Check every second
            
            if OlympusSuicideVest.IsWearingVest() then
                -- Player is wearing suicide vest, enable detonation
                OlympusSuicideVest.EnableDetonation()
            else
                -- Player not wearing vest, disable detonation
                OlympusSuicideVest.DisableDetonation()
            end
        end
    end)
end

function OlympusSuicideVest.EnableDetonation()
    -- Enable suicide vest detonation controls
    -- This would typically involve key bindings or interaction prompts
end

function OlympusSuicideVest.DisableDetonation()
    -- Disable suicide vest detonation controls
    isDetonating = false
end

-- Detonation System (Exact Olympus Implementation)
function OlympusSuicideVest.AttemptDetonation()
    if isDetonating then return end
    
    -- Check if wearing vest (exact Olympus check)
    if not OlympusSuicideVest.IsWearingVest() then
        OlympusSuicideVest.Notify(Config.SuicideVest.messages.noVest, 'error')
        return
    end
    
    -- Check if in vehicle (exact Olympus check)
    if OlympusSuicideVest.IsInVehicle() then
        OlympusSuicideVest.Notify(Config.SuicideVest.messages.inVehicle, 'error')
        return
    end
    
    -- Check jammer zones (exact Olympus implementation)
    local inJammer, jammerZone = OlympusSuicideVest.IsInJammerZone()
    if inJammer then
        OlympusSuicideVest.Notify(jammerZone.message, 'error')
        PlaySoundFrontend(-1, Config.Sounds.jammer.name, Config.Sounds.jammer.ref, 1)
        return
    end
    
    isDetonating = true
    
    -- Play detonation animation
    OlympusSuicideVest.PlayDetonationAnimation()
    
    -- Wait for animation duration
    Wait(Config.Animations.detonation.duration)
    
    -- Trigger server-side detonation
    TriggerServerEvent('olympus-suicide-vest:server:detonateVest')
end

function OlympusSuicideVest.PlayDetonationAnimation()
    local ped = PlayerPedId()
    
    -- Load animation dictionary
    RequestAnimDict(Config.Animations.detonation.dict)
    while not HasAnimDictLoaded(Config.Animations.detonation.dict) do
        Wait(10)
    end
    
    -- Play detonation animation
    TaskPlayAnim(ped, Config.Animations.detonation.dict, Config.Animations.detonation.anim, 8.0, -8.0, Config.Animations.detonation.duration, Config.Animations.detonation.flag, 0, false, false, false)
    
    -- Show progress bar
    exports['olympus-core']:ShowProgressBar("Detonating vest...", Config.Animations.detonation.duration)
end

-- Explosion Creation (Client-side)
function OlympusSuicideVest.CreateExplosion(coords, explosionType, damage, radius)
    -- Create explosion at coordinates
    AddExplosion(coords.x, coords.y, coords.z, GetHashKey(explosionType), damage, true, false, radius, false)
    
    -- Play explosion sound
    PlaySoundFrontend(-1, Config.Sounds.detonation.name, Config.Sounds.detonation.ref, 1)
    
    -- Create screen effect for nearby players
    local playerCoords = OlympusSuicideVest.GetPlayerCoords()
    local distance = #(playerCoords - coords)
    
    if distance < 50.0 then
        -- Screen shake effect
        ShakeGameplayCam('SMALL_EXPLOSION_SHAKE', 0.5)
        
        -- Screen flash effect
        SetFlash(0, 0, 500, 700, 100)
    end
end

-- Remove Vest Function
function OlympusSuicideVest.RemoveVest()
    local ped = PlayerPedId()
    
    -- Remove vest component (simplified implementation)
    SetPedComponentVariation(ped, 9, 0, 0, 0) -- Remove vest
    
    -- Remove any vest-related weapons
    RemoveWeaponFromPed(ped, GetHashKey("WEAPON_STICKYBOMB"))
end

-- Event Handlers
RegisterNetEvent('olympus-suicide-vest:client:createExplosion', function(coords, explosionType, damage, radius)
    OlympusSuicideVest.CreateExplosion(coords, explosionType, damage, radius)
end)

RegisterNetEvent('olympus-suicide-vest:client:removeVest', function()
    OlympusSuicideVest.RemoveVest()
end)

RegisterNetEvent('olympus-suicide-vest:client:canDetonateResult', function(canDetonate, reason)
    if not canDetonate then
        OlympusSuicideVest.Notify(reason, 'error')
    end
end)

-- Commands
RegisterCommand('detonate', function()
    OlympusSuicideVest.AttemptDetonation()
end, false)

RegisterCommand('suicidevest', function()
    OlympusSuicideVest.AttemptDetonation()
end, false)

-- Key Bindings
RegisterKeyMapping('detonate', 'Detonate Suicide Vest', 'keyboard', 'F10')

-- Export Functions
exports('CanDetonate', function()
    return not isDetonating and OlympusSuicideVest.IsWearingVest() and not OlympusSuicideVest.IsInVehicle() and not OlympusSuicideVest.IsInJammerZone()
end)

exports('DetonateVest', function()
    OlympusSuicideVest.AttemptDetonation()
end)

exports('IsInJammerZone', function()
    return OlympusSuicideVest.IsInJammerZone()
end)

exports('IsWearingVest', function()
    return OlympusSuicideVest.IsWearingVest()
end)

print("[Olympus Suicide Vest] Client module loaded")
