-- Olympus Fishing & Hunting System - Client Main
-- Based on original fn_catchFish.sqf, fn_dropFishingNet.sqf, fn_catchTurtle.sqf, fn_gutAnimal.sqf

local OlympusFishingHunting = {}
local isInitialized = false
local currentActivity = nil
local fishingBlips = {}
local huntingBlips = {}
local turtleBlips = {}
local processingBlips = {}

-- Initialize client system
CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end
    
    OlympusFishingHunting.Initialize()
end)

function OlympusFishingHunting.Initialize()
    if isInitialized then return end
    
    print("[Olympus Fishing & Hunting] Client initialized")
    
    -- Create blips for fishing zones
    OlympusFishingHunting.CreateFishingBlips()
    
    -- Create blips for turtle poaching zones
    OlympusFishingHunting.CreateTurtleBlips()
    
    -- Create blips for hunting zones
    OlympusFishingHunting.CreateHuntingBlips()
    
    -- Create blips for processing locations
    OlympusFishingHunting.CreateProcessingBlips()
    
    -- Start interaction thread
    CreateThread(OlympusFishingHunting.InteractionThread)
    
    -- Start zone monitoring thread
    CreateThread(OlympusFishingHunting.ZoneMonitoringThread)
    
    isInitialized = true
end

-- Utility Functions
function OlympusFishingHunting.Notify(message, type)
    if type == 'success' then
        TriggerEvent('olympus-core:client:notify', message, 'success')
    elseif type == 'error' then
        TriggerEvent('olympus-core:client:notify', message, 'error')
    else
        TriggerEvent('olympus-core:client:notify', message, 'primary')
    end
end

function OlympusFishingHunting.IsPlayerInWater()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    return coords.z < 0.0 and IsPedSwimming(playerPed)
end

function OlympusFishingHunting.IsPlayerInVehicle(vehicleType)
    local playerPed = PlayerPedId()
    local vehicle = GetVehiclePedIsIn(playerPed, false)
    
    if vehicle == 0 then return false end
    
    if vehicleType == "boat" then
        return GetVehicleClass(vehicle) == 14 -- Boat class
    end
    
    return vehicle ~= 0
end

function OlympusFishingHunting.GetPlayerZone(zoneType)
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    
    local zones = zoneType == "fishing" and Config.Fishing.zones or 
                  zoneType == "turtle" and Config.TurtlePoaching.zones or
                  zoneType == "hunting" and Config.Hunting.zones
    
    for _, zone in pairs(zones) do
        local distance = #(coords - zone.center)
        if distance <= zone.radius then
            return zone
        end
    end
    
    return nil
end

function OlympusFishingHunting.PlayAnimation(animDict, animName, duration)
    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(100)
    end
    
    local playerPed = PlayerPedId()
    TaskPlayAnim(playerPed, animDict, animName, 8.0, -8.0, duration or -1, 1, 0, false, false, false)
end

function OlympusFishingHunting.ShowProgressBar(text, duration, onComplete)
    exports['progressbar']:Progress({
        name = "fishing_hunting_action",
        duration = duration,
        label = text,
        useWhileDead = false,
        canCancel = true,
        controlDisables = {
            disableMovement = false,
            disableCarMovement = false,
            disableMouse = false,
            disableCombat = true,
        }
    }, function(cancelled)
        if not cancelled and onComplete then
            onComplete()
        end
    end)
end

-- Blip Creation Functions
function OlympusFishingHunting.CreateFishingBlips()
    for i, zone in pairs(Config.Fishing.zones) do
        local blip = AddBlipForRadius(zone.center.x, zone.center.y, zone.center.z, zone.radius)
        SetBlipHighDetail(blip, true)
        SetBlipColour(blip, 3) -- Light blue
        SetBlipAlpha(blip, 128)
        
        local centerBlip = AddBlipForCoord(zone.center.x, zone.center.y, zone.center.z)
        SetBlipSprite(centerBlip, 68) -- Fish icon
        SetBlipDisplay(centerBlip, 4)
        SetBlipScale(centerBlip, 0.8)
        SetBlipColour(centerBlip, 3)
        SetBlipAsShortRange(centerBlip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(zone.name .. " (Fishing)")
        EndTextCommandSetBlipName(centerBlip)
        
        table.insert(fishingBlips, {radius = blip, center = centerBlip})
    end
end

function OlympusFishingHunting.CreateTurtleBlips()
    for i, zone in pairs(Config.TurtlePoaching.zones) do
        local blip = AddBlipForRadius(zone.center.x, zone.center.y, zone.center.z, zone.radius)
        SetBlipHighDetail(blip, true)
        SetBlipColour(blip, zone.blipColor or 1) -- Red
        SetBlipAlpha(blip, 128)
        
        local centerBlip = AddBlipForCoord(zone.center.x, zone.center.y, zone.center.z)
        SetBlipSprite(centerBlip, zone.blipSprite or 141) -- Turtle icon
        SetBlipDisplay(centerBlip, 4)
        SetBlipScale(centerBlip, 0.8)
        SetBlipColour(centerBlip, zone.blipColor or 1)
        SetBlipAsShortRange(centerBlip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(zone.name .. " (Turtle Poaching)")
        EndTextCommandSetBlipName(centerBlip)
        
        table.insert(turtleBlips, {radius = blip, center = centerBlip})
    end
end

function OlympusFishingHunting.CreateHuntingBlips()
    for i, zone in pairs(Config.Hunting.zones) do
        local blip = AddBlipForRadius(zone.center.x, zone.center.y, zone.center.z, zone.radius)
        SetBlipHighDetail(blip, true)
        SetBlipColour(blip, 2) -- Green
        SetBlipAlpha(blip, 128)
        
        local centerBlip = AddBlipForCoord(zone.center.x, zone.center.y, zone.center.z)
        SetBlipSprite(centerBlip, 141) -- Animal icon
        SetBlipDisplay(centerBlip, 4)
        SetBlipScale(centerBlip, 0.8)
        SetBlipColour(centerBlip, 2)
        SetBlipAsShortRange(centerBlip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(zone.name .. " (Hunting)")
        EndTextCommandSetBlipName(centerBlip)
        
        table.insert(huntingBlips, {radius = blip, center = centerBlip})
    end
end

function OlympusFishingHunting.CreateProcessingBlips()
    -- Fish processing blips
    for _, location in pairs(Config.Processing.fishProcessing) do
        local blip = AddBlipForCoord(location.coords.x, location.coords.y, location.coords.z)
        SetBlipSprite(blip, location.blip.sprite)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, location.blip.scale)
        SetBlipColour(blip, location.blip.color)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(location.name)
        EndTextCommandSetBlipName(blip)
        
        table.insert(processingBlips, blip)
    end
    
    -- Meat processing blips
    for _, location in pairs(Config.Processing.meatProcessing) do
        local blip = AddBlipForCoord(location.coords.x, location.coords.y, location.coords.z)
        SetBlipSprite(blip, location.blip.sprite)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, location.blip.scale)
        SetBlipColour(blip, location.blip.color)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(location.name)
        EndTextCommandSetBlipName(blip)
        
        table.insert(processingBlips, blip)
    end
    
    -- Turtle processing blips (hidden by default)
    for _, location in pairs(Config.Processing.turtleProcessing) do
        if not location.hidden then
            local blip = AddBlipForCoord(location.coords.x, location.coords.y, location.coords.z)
            SetBlipSprite(blip, location.blip.sprite)
            SetBlipDisplay(blip, 4)
            SetBlipScale(blip, location.blip.scale)
            SetBlipColour(blip, location.blip.color)
            SetBlipAsShortRange(blip, true)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(location.name)
            EndTextCommandSetBlipName(blip)
            
            table.insert(processingBlips, blip)
        end
    end
end

-- Interaction System
function OlympusFishingHunting.InteractionThread()
    while true do
        Wait(0)

        local playerPed = PlayerPedId()
        local coords = GetEntityCoords(playerPed)

        -- Check for nearby fish (for individual fishing)
        if OlympusFishingHunting.IsPlayerInWater() then
            local fishingZone = OlympusFishingHunting.GetPlayerZone("fishing")
            if fishingZone then
                -- Show fishing prompt
                if not currentActivity then
                    DrawText3D(coords.x, coords.y, coords.z + 1.0, "[E] Fish")

                    if IsControlJustPressed(0, 38) then -- E key
                        OlympusFishingHunting.StartFishing(fishingZone)
                    end
                end
            end
        end

        -- Check for fishing net usage (in boat)
        if OlympusFishingHunting.IsPlayerInVehicle("boat") then
            local fishingZone = OlympusFishingHunting.GetPlayerZone("fishing")
            if fishingZone then
                if not currentActivity then
                    DrawText3D(coords.x, coords.y, coords.z + 1.0, "[G] Use Fishing Net")

                    if IsControlJustPressed(0, 47) then -- G key
                        TriggerServerEvent('olympus-fishing-hunting:server:useFishingNet')
                    end
                end
            end
        end

        -- Check for turtle interaction
        local turtleZone = OlympusFishingHunting.GetPlayerZone("turtle")
        if turtleZone then
            local closestTurtle = OlympusFishingHunting.GetClosestTurtle()
            if closestTurtle then
                local turtleCoords = GetEntityCoords(closestTurtle)
                local distance = #(coords - turtleCoords)

                if distance <= 3.5 then
                    DrawText3D(turtleCoords.x, turtleCoords.y, turtleCoords.z + 1.0, "[E] Collect Turtle")

                    if IsControlJustPressed(0, 38) then -- E key
                        TriggerServerEvent('olympus-fishing-hunting:server:catchTurtle', closestTurtle)
                    end
                end
            end
        end

        -- Check for animal interaction
        local huntingZone = OlympusFishingHunting.GetPlayerZone("hunting")
        if huntingZone then
            local closestAnimal = OlympusFishingHunting.GetClosestDeadAnimal()
            if closestAnimal then
                local animalCoords = GetEntityCoords(closestAnimal.entity)
                local distance = #(coords - animalCoords)

                if distance <= 3.5 then
                    DrawText3D(animalCoords.x, animalCoords.y, animalCoords.z + 1.0, "[E] Gut " .. closestAnimal.type)

                    if IsControlJustPressed(0, 38) then -- E key
                        TriggerServerEvent('olympus-fishing-hunting:server:gutAnimal', closestAnimal.type, closestAnimal.entity)
                    end
                end
            end
        end

        -- Check for processing locations
        OlympusFishingHunting.CheckProcessingLocations()
    end
end

function OlympusFishingHunting.CheckProcessingLocations()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)

    -- Check fish processing locations
    for _, location in pairs(Config.Processing.fishProcessing) do
        local distance = #(coords - location.coords)
        if distance <= 3.0 then
            DrawText3D(location.coords.x, location.coords.y, location.coords.z + 1.0, "[E] " .. location.name)

            if IsControlJustPressed(0, 38) then -- E key
                OlympusFishingHunting.OpenProcessingMenu("fish", location)
            end
        end
    end

    -- Check meat processing locations
    for _, location in pairs(Config.Processing.meatProcessing) do
        local distance = #(coords - location.coords)
        if distance <= 3.0 then
            DrawText3D(location.coords.x, location.coords.y, location.coords.z + 1.0, "[E] " .. location.name)

            if IsControlJustPressed(0, 38) then -- E key
                OlympusFishingHunting.OpenProcessingMenu("meat", location)
            end
        end
    end

    -- Check turtle processing locations
    for _, location in pairs(Config.Processing.turtleProcessing) do
        local distance = #(coords - location.coords)
        if distance <= 3.0 then
            DrawText3D(location.coords.x, location.coords.y, location.coords.z + 1.0, "[E] " .. location.name)

            if IsControlJustPressed(0, 38) then -- E key
                OlympusFishingHunting.OpenProcessingMenu("turtle", location)
            end
        end
    end
end

-- Helper Functions
function OlympusFishingHunting.GetClosestTurtle()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    local closestTurtle = nil
    local closestDistance = 999.0

    -- Find closest turtle entity (using fish model as placeholder)
    local turtles = GetGamePool('CPed')
    for _, turtle in pairs(turtles) do
        if DoesEntityExist(turtle) and GetEntityModel(turtle) == GetHashKey("a_c_fish") then
            if GetEntityHealth(turtle) <= 0 then -- Must be dead
                local turtleCoords = GetEntityCoords(turtle)
                local distance = #(coords - turtleCoords)

                if distance < closestDistance and distance <= 10.0 then
                    closestDistance = distance
                    closestTurtle = turtle
                end
            end
        end
    end

    return closestTurtle
end

function OlympusFishingHunting.GetClosestDeadAnimal()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    local closestAnimal = nil
    local closestDistance = 999.0

    -- Find closest dead animal
    local animals = GetGamePool('CPed')
    for _, animal in pairs(animals) do
        if DoesEntityExist(animal) and GetEntityHealth(animal) <= 0 then
            local model = GetEntityModel(animal)
            local animalType = nil

            -- Determine animal type by model
            for type, data in pairs(Config.Hunting.animalTypes) do
                if GetHashKey(data.model) == model then
                    animalType = type
                    break
                end
            end

            if animalType then
                local animalCoords = GetEntityCoords(animal)
                local distance = #(coords - animalCoords)

                if distance < closestDistance and distance <= 10.0 then
                    closestDistance = distance
                    closestAnimal = {entity = animal, type = animalType}
                end
            end
        end
    end

    return closestAnimal
end

function DrawText3D(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())

    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x, _y)

    local factor = (string.len(text)) / 370
    DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 41, 11, 41, 68)
end

-- Fishing System
function OlympusFishingHunting.StartFishing(zone)
    if currentActivity then return end

    currentActivity = "fishing"

    -- Play fishing animation
    OlympusFishingHunting.PlayAnimation("amb@world_human_stand_fishing@idle_a", "idle_a")

    -- Show progress bar
    OlympusFishingHunting.ShowProgressBar("Fishing...", 5000, function()
        -- Determine what fish was caught based on zone and rarity
        local caughtFish = OlympusFishingHunting.DetermineFishCatch(zone)

        if caughtFish then
            TriggerServerEvent('olympus-fishing-hunting:server:catchFish', caughtFish)
        else
            OlympusFishingHunting.Notify("The fish got away!", "error")
        end

        currentActivity = nil
        ClearPedTasks(PlayerPedId())
    end)
end

function OlympusFishingHunting.DetermineFishCatch(zone)
    -- Calculate total rarity weight for available fish
    local totalWeight = 0
    for _, fishType in pairs(zone.fishTypes) do
        if Config.Fishing.fishTypes[fishType] then
            totalWeight = totalWeight + Config.Fishing.fishTypes[fishType].rarity
        end
    end

    -- Random selection based on rarity weights
    local random = math.random() * totalWeight
    local currentWeight = 0

    for _, fishType in pairs(zone.fishTypes) do
        if Config.Fishing.fishTypes[fishType] then
            currentWeight = currentWeight + Config.Fishing.fishTypes[fishType].rarity
            if random <= currentWeight then
                return fishType
            end
        end
    end

    return nil
end

-- Net Fishing System
function OlympusFishingHunting.StartNetFishing(availableFish)
    if currentActivity then return end

    currentActivity = "net_fishing"

    -- Play net fishing animation
    OlympusFishingHunting.PlayAnimation("amb@world_human_stand_fishing@idle_a", "idle_a")

    OlympusFishingHunting.Notify("Dropping fishing net...", "primary")

    -- Net deployment phase
    OlympusFishingHunting.ShowProgressBar("Deploying net...", 5000, function()
        OlympusFishingHunting.Notify("Net deployed! Waiting for fish...", "primary")

        -- Wait for fish to be caught
        Wait(3000)

        -- Determine caught fish
        local caughtFish = {}
        for i = 1, math.random(0, 6) do -- 0-6 fish per net
            local fishType = availableFish[math.random(#availableFish)]
            if Config.Fishing.fishTypes[fishType] and math.random() < Config.Fishing.fishTypes[fishType].rarity then
                table.insert(caughtFish, fishType)
            end
        end

        -- Pull up net
        OlympusFishingHunting.ShowProgressBar("Pulling up net...", 3000, function()
            TriggerServerEvent('olympus-fishing-hunting:server:completeNetFishing', caughtFish)
            currentActivity = nil
            ClearPedTasks(PlayerPedId())
        end)
    end)
end

-- Animal Gutting System
function OlympusFishingHunting.StartGutting(animalType, animalEntity)
    if currentActivity then return end

    currentActivity = "gutting"

    local animalData = Config.Hunting.animalTypes[animalType]
    if not animalData then return end

    -- Play gutting animation
    OlympusFishingHunting.PlayAnimation("amb@world_human_gardener_plant@male@base", "base")

    -- Show progress bar
    local gutTime = Config.Hunting.requirements.gutTime * 1000
    OlympusFishingHunting.ShowProgressBar("Gutting " .. animalData.name .. "...", gutTime, function()
        TriggerServerEvent('olympus-fishing-hunting:server:completeGutting', animalType, animalEntity)
        currentActivity = nil
        ClearPedTasks(PlayerPedId())
    end)
end

-- Processing Menu System
function OlympusFishingHunting.OpenProcessingMenu(processingType, location)
    local menuItems = {}

    if processingType == "fish" then
        for fishType, fishData in pairs(Config.Fishing.fishTypes) do
            local sellPrice = math.floor(fishData.sellPrice * location.sellMultiplier)
            table.insert(menuItems, {
                header = fishData.name,
                txt = "Sell for $" .. sellPrice .. " each",
                params = {
                    event = "olympus-fishing-hunting:client:sellItem",
                    args = {
                        itemType = fishType,
                        location = location.name,
                        sellPrice = sellPrice
                    }
                }
            })
        end
    elseif processingType == "meat" then
        for animalType, animalData in pairs(Config.Hunting.animalTypes) do
            local sellPrice = math.floor(animalData.sellPrice * location.sellMultiplier)
            table.insert(menuItems, {
                header = "Raw " .. animalData.name .. " Meat",
                txt = "Sell for $" .. sellPrice .. " each",
                params = {
                    event = "olympus-fishing-hunting:client:sellItem",
                    args = {
                        itemType = animalData.rawMeat,
                        location = location.name,
                        sellPrice = sellPrice
                    }
                }
            })
        end
    elseif processingType == "turtle" then
        local sellPrice = math.floor(Config.TurtlePoaching.turtle.sellPrice * location.sellMultiplier)
        table.insert(menuItems, {
            header = "Turtle",
            txt = "Sell for $" .. sellPrice .. " each",
            params = {
                event = "olympus-fishing-hunting:client:sellItem",
                args = {
                    itemType = "turtle",
                    location = location.name,
                    sellPrice = sellPrice
                }
            }
        })
    end

    table.insert(menuItems, {
        header = "Close",
        txt = "",
        params = {
            event = "qb-menu:client:closeMenu"
        }
    })

    exports['qb-menu']:openMenu(menuItems)
end

-- Zone Monitoring System
function OlympusFishingHunting.ZoneMonitoringThread()
    local lastZone = nil

    while true do
        Wait(1000) -- Check every second

        local currentZone = nil
        local zoneType = nil

        -- Check fishing zones
        local fishingZone = OlympusFishingHunting.GetPlayerZone("fishing")
        if fishingZone then
            currentZone = fishingZone.name
            zoneType = "fishing"
        end

        -- Check turtle zones
        local turtleZone = OlympusFishingHunting.GetPlayerZone("turtle")
        if turtleZone then
            currentZone = turtleZone.name
            zoneType = "turtle poaching"
        end

        -- Check hunting zones
        local huntingZone = OlympusFishingHunting.GetPlayerZone("hunting")
        if huntingZone then
            currentZone = huntingZone.name
            zoneType = "hunting"
        end

        -- Notify zone changes
        if currentZone ~= lastZone then
            if currentZone then
                if Config.General.notifications.showZoneNotifications then
                    OlympusFishingHunting.Notify("Entered " .. currentZone .. " (" .. zoneType .. " zone)", "primary")
                end
            else
                if lastZone and Config.General.notifications.showZoneNotifications then
                    OlympusFishingHunting.Notify("Left " .. zoneType .. " zone", "primary")
                end
            end
            lastZone = currentZone
        end
    end
end

-- Event Handlers
RegisterNetEvent('olympus-fishing-hunting:client:notify', function(message, type)
    OlympusFishingHunting.Notify(message, type)
end)

RegisterNetEvent('olympus-fishing-hunting:client:startNetFishing', function(availableFish)
    OlympusFishingHunting.StartNetFishing(availableFish)
end)

RegisterNetEvent('olympus-fishing-hunting:client:startGutting', function(animalType, animalEntity)
    OlympusFishingHunting.StartGutting(animalType, animalEntity)
end)

RegisterNetEvent('olympus-fishing-hunting:client:receivePlayerStats', function(stats)
    local message = string.format(
        "Fishing & Hunting Stats:\nFish Caught: %d\nTurtles Caught: %d\nAnimals Gutted: %d\nNets Used: %d\nBest Fish: %s",
        stats.total_fish_caught,
        stats.total_turtles_caught,
        stats.total_animals_gutted,
        stats.total_nets_used,
        stats.best_fish or "None"
    )

    OlympusFishingHunting.Notify(message, "primary")
end)

RegisterNetEvent('olympus-fishing-hunting:client:sellItem', function(data)
    local input = exports['qb-input']:ShowInput({
        header = "Sell " .. data.itemType,
        submitText = "Sell",
        inputs = {
            {
                text = "Quantity",
                name = "quantity",
                type = "number",
                isRequired = true,
                default = 1
            }
        }
    })

    if input and input.quantity then
        local quantity = tonumber(input.quantity)
        if quantity and quantity > 0 then
            TriggerServerEvent('olympus-fishing-hunting:server:sellItem', data.itemType, quantity, data.location)
        end
    end
end)

-- Commands
RegisterCommand('fishingstats', function()
    TriggerServerEvent('olympus-fishing-hunting:server:requestPlayerStats')
end, false)

-- Export Functions
exports('GetFishingStatus', function()
    return currentActivity == "fishing"
end)

exports('GetHuntingStatus', function()
    return currentActivity == "gutting"
end)

exports('StartFishing', function(zone)
    if zone then
        OlympusFishingHunting.StartFishing(zone)
        return true
    end
    return false
end)

exports('StartHunting', function(animalType, animalEntity)
    if animalType and animalEntity then
        OlympusFishingHunting.StartGutting(animalType, animalEntity)
        return true
    end
    return false
end)

exports('ProcessCatch', function(fishType)
    if fishType then
        TriggerServerEvent('olympus-fishing-hunting:server:catchFish', fishType)
        return true
    end
    return false
end)

exports('IsInFishingZone', function()
    return OlympusFishingHunting.GetPlayerZone("fishing") ~= nil
end)

exports('IsInHuntingZone', function()
    return OlympusFishingHunting.GetPlayerZone("hunting") ~= nil
end)

exports('IsInTurtleZone', function()
    return OlympusFishingHunting.GetPlayerZone("turtle") ~= nil
end)

print("[Olympus Fishing & Hunting] Client module loaded")
