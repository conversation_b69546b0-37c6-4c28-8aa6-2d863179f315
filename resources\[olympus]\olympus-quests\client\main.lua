-- Olympus Quest System - Client Main

CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end
    
    print("[Olympus Quests] Client initialized")
end)

exports('StartQuest', function(questId)
    print("[Olympus Quests] Starting quest:", questId)
end)

exports('GetActiveQuests', function()
    return {}
end)

exports('GetQuestProgress', function(questId)
    return 0
end)

exports('OpenQuestMenu', function()
    print("[Olympus Quests] Opening quest menu")
end)

print("[Olympus Quests] Client module loaded")
