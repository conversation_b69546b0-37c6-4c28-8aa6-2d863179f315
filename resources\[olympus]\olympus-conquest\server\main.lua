-- ============================================
-- OLYMPUS CONQUEST SYSTEM - SERVER
-- Based on original fn_conquestServer.sqf
-- ============================================

local OlympusConquest = {}

-- ============================================
-- INITIALIZATION
-- ============================================

-- Database initialization
CreateThread(function()
    -- Conquest system tables are already in olympus_schema.sql
    print("^2[Olympus Conquest]^7 System initialized")
end)

-- ============================================
-- CONQUEST STATE MANAGEMENT
-- ============================================

-- Global conquest state
local ConquestData = {
    active = false,
    zone = nil,
    gangs = {},
    points = {},
    gangNames = {},
    prizePool = 0,
    leaderboard = {{}, {}, {}}, -- First, second, third place
    maxPoints = 3000
}

local ConquestServer = {
    pointMarkers = {},
    lineMarkers = {},
    flags = {},
    prizePool = 0
}

local conquestVote = false
local conquestDeaths = {}
local cancelConquest = false
local secondConquest = false
local lastConquest = -1
local lockedChopShop = ""

-- ============================================
-- UTILITY FUNCTIONS
-- ============================================

function OlympusConquest.GetPlayerGangData(src)
    local success, result = pcall(function()
        return exports['olympus-gangs']:GetPlayerGang(src)
    end)
    
    if success and result then
        return result
    end
    return nil
end

function OlympusConquest.IsPlayerInGang(src)
    local gangData = OlympusConquest.GetPlayerGangData(src)
    return gangData ~= nil and gangData.id ~= nil
end

function OlympusConquest.GetOnlineGangMembers(gangId)
    local members = {}
    local players = GetPlayers()
    
    for _, playerId in ipairs(players) do
        local gangData = OlympusConquest.GetPlayerGangData(playerId)
        if gangData and gangData.id == gangId then
            table.insert(members, playerId)
        end
    end
    
    return members
end

function OlympusConquest.BroadcastToZone(message, zonePolygon)
    local players = GetPlayers()
    for _, playerId in ipairs(players) do
        local playerPos = GetEntityCoords(GetPlayerPed(playerId))
        if OlympusConquest.IsPointInPolygon(playerPos, zonePolygon) then
            TriggerClientEvent('chat:addMessage', playerId, {
                color = {255, 255, 0},
                multiline = true,
                args = {"Conquest", message}
            })
        end
    end
end

function OlympusConquest.IsPointInPolygon(point, polygon)
    local x, y = point.x, point.y
    local inside = false
    local j = #polygon
    
    for i = 1, #polygon do
        local xi, yi = polygon[i][1], polygon[i][2]
        local xj, yj = polygon[j][1], polygon[j][2]
        
        if ((yi > y) ~= (yj > y)) and (x < (xj - xi) * (y - yi) / (yj - yi) + xi) then
            inside = not inside
        end
        j = i
    end
    
    return inside
end

-- ============================================
-- CONQUEST MANAGEMENT FUNCTIONS
-- ============================================

function OlympusConquest.StartConquest(zoneIndex, scheduleId)
    if ConquestData.active then
        return false, "Conquest already active"
    end
    
    local zone = Config.Zones[zoneIndex + 1] -- Lua is 1-indexed
    if not zone then
        return false, "Invalid zone index"
    end
    
    print(string.format("^3[Olympus Conquest]^7 Starting conquest at %s", zone.name))
    
    -- Initialize conquest data
    ConquestData.active = true
    ConquestData.zone = zone
    ConquestData.gangs = {}
    ConquestData.points = {}
    ConquestData.gangNames = {}
    ConquestData.prizePool = Config.Settings.basePrizePool
    ConquestData.leaderboard = {{}, {}, {}}
    ConquestData.maxPoints = zone.maxPoints or Config.Settings.defaultMaxPoints
    
    -- Reset server data
    ConquestServer.pointMarkers = {}
    ConquestServer.lineMarkers = {}
    ConquestServer.flags = {}
    ConquestServer.prizePool = Config.Settings.basePrizePool
    
    -- Reset conquest deaths tracking
    conquestDeaths = {}
    
    -- Log conquest start
    local logData = {
        event = "Conquest Started",
        location = zone.name,
        zone_index = zoneIndex,
        schedule_id = scheduleId or -1
    }
    
    -- Database logging would go here
    
    -- Broadcast preparation message
    TriggerClientEvent('olympus-conquest:notify', -1, 
        string.format("Capture zones near %s will be available in 10 minutes.", zone.name))
    
    -- Start conquest preparation phase
    OlympusConquest.StartPreparationPhase(zone, scheduleId)
    
    return true, "Conquest started successfully"
end

function OlympusConquest.StartPreparationPhase(zone, scheduleId)
    -- Create point markers
    for i, point in ipairs(zone.capturePoints) do
        local markerId = string.format("conqPoint_%d", i - 1)
        ConquestServer.pointMarkers[markerId] = {
            coords = point,
            name = Config.PointNames[i] or "Unknown"
        }
        
        -- Notify clients to create markers
        TriggerClientEvent('olympus-conquest:createPointMarker', -1, markerId, point, Config.PointNames[i] or "Unknown")
    end
    
    -- Create zone boundary markers
    for i, point in ipairs(zone.polygon) do
        local nextIndex = (i % #zone.polygon) + 1
        local nextPoint = zone.polygon[nextIndex]
        
        local markerId = string.format("conqLine_%d", i - 1)
        ConquestServer.lineMarkers[markerId] = {
            start = point,
            endPoint = nextPoint
        }
        
        -- Notify clients to create line markers
        TriggerClientEvent('olympus-conquest:createLineMarker', -1, markerId, point, nextPoint)
    end
    
    -- Set locked chop shop
    lockedChopShop = zone.lockedChopShop
    TriggerClientEvent('olympus-conquest:setLockedChopShop', -1, lockedChopShop)
    
    -- Start preparation countdown
    CreateThread(function()
        local preparationTime = Config.Settings.preparationTime
        
        -- 10 minute warning
        Wait(preparationTime * 1000 - 300000) -- 5 minutes before
        if not cancelConquest then
            TriggerClientEvent('olympus-conquest:notify', -1, 
                string.format("Capture zones near %s will be available in 5 minutes.", zone.name))
        end
        
        -- 1 minute warning
        Wait(240000) -- 4 minutes later (1 minute before)
        if not cancelConquest then
            TriggerClientEvent('olympus-conquest:notify', -1, 
                string.format("Capture zones near %s will be available in 1 minute.", zone.name))
        end
        
        -- Start active phase
        Wait(60000) -- 1 minute later
        if not cancelConquest then
            TriggerClientEvent('olympus-conquest:notify', -1, 
                string.format("Capture zones near %s are available for capture!", zone.name))
            OlympusConquest.StartActivePhase(zone, scheduleId)
        end
    end)
end

function OlympusConquest.StartActivePhase(zone, scheduleId)
    -- Change markers to active (red)
    TriggerClientEvent('olympus-conquest:activateMarkers', -1)
    
    -- Create capture flags
    for i, point in ipairs(zone.capturePoints) do
        local flagData = {
            coords = point,
            owner = -1,
            phonetic = Config.PointNames[i] or "Unknown",
            capturing = false
        }
        
        ConquestServer.flags[i] = flagData
        TriggerClientEvent('olympus-conquest:createFlag', -1, i, flagData)
    end
    
    -- Unlock houses in conquest zone
    TriggerClientEvent('olympus-conquest:unlockHouses', -1, zone.polygon)
    
    -- Start point scoring loop
    OlympusConquest.StartScoringLoop(zone, scheduleId)
end

function OlympusConquest.StartScoringLoop(zone, scheduleId)
    CreateThread(function()
        local first = {id = -1, points = 0, name = ""}
        local second = {id = -1, points = 0, name = ""}
        local third = {id = -1, points = 0, name = ""}

        while ConquestData.active and not cancelConquest do
            Wait(Config.Settings.tickInterval)

            local changed = false

            -- Process each capture flag
            for flagIndex, flagData in pairs(ConquestServer.flags) do
                if flagData.owner ~= -1 then
                    local gangId = flagData.owner
                    local gangMembers = OlympusConquest.GetOnlineGangMembers(gangId)

                    -- Check if gang has too many players in zone
                    local playersInZone = 0
                    for _, memberId in ipairs(gangMembers) do
                        local playerPos = GetEntityCoords(GetPlayerPed(memberId))
                        if OlympusConquest.IsPointInPolygon(playerPos, zone.polygon) then
                            playersInZone = playersInZone + 1
                        end
                    end

                    if playersInZone <= Config.Settings.maxPlayersPerGang then
                        -- Award points to gang
                        local gangIndex = nil
                        for i, gId in ipairs(ConquestData.gangs) do
                            if gId == gangId then
                                gangIndex = i
                                break
                            end
                        end

                        if gangIndex then
                            ConquestData.points[gangIndex] = math.min(
                                ConquestData.points[gangIndex] + Config.Settings.pointsPerTick,
                                ConquestData.maxPoints
                            )
                        else
                            -- Add new gang to conquest
                            table.insert(ConquestData.gangs, gangId)
                            table.insert(ConquestData.points, Config.Settings.pointsPerTick)

                            -- Get gang name
                            local gangData = exports['olympus-gangs']:GetGangById(gangId)
                            table.insert(ConquestData.gangNames, gangData and gangData.name or "Unknown Gang")
                        end

                        changed = true
                    else
                        -- Apply cooldown for too many players
                        TriggerClientEvent('olympus-conquest:bigGangCooldown', -1, gangId)
                    end
                end
            end

            -- Update prize pool
            if ConquestData.prizePool ~= ConquestServer.prizePool then
                ConquestData.prizePool = ConquestServer.prizePool
                changed = true
            end

            -- Update leaderboard if changed
            if changed then
                first = {id = -1, points = 0, name = ""}
                second = {id = -1, points = 0, name = ""}
                third = {id = -1, points = 0, name = ""}

                for i, points in ipairs(ConquestData.points) do
                    local gangId = ConquestData.gangs[i]
                    local gangName = ConquestData.gangNames[i]

                    if points > first.points then
                        if gangId ~= first.id then
                            third = second
                            second = first
                        end
                        first = {id = gangId, points = points, name = gangName}
                    elseif points > second.points then
                        if gangId ~= second.id then
                            third = second
                        end
                        second = {id = gangId, points = points, name = gangName}
                    elseif points > third.points then
                        third = {id = gangId, points = points, name = gangName}
                    end
                end

                ConquestData.leaderboard = {first, second, third}

                -- Broadcast updated conquest data
                TriggerClientEvent('olympus-conquest:updateData', -1, ConquestData)
            end

            -- Check for winner
            if OlympusConquest.CheckForWinner() then
                OlympusConquest.EndConquest(zone, scheduleId)
                break
            end
        end

        if cancelConquest then
            OlympusConquest.CancelConquest()
        end
    end)
end

function OlympusConquest.CheckForWinner()
    for _, points in ipairs(ConquestData.points) do
        if points >= ConquestData.maxPoints then
            return true
        end
    end
    return false
end

function OlympusConquest.EndConquest(zone, scheduleId)
    print(string.format("^3[Olympus Conquest]^7 Ending conquest at %s", zone.name))

    -- Calculate final results
    local sortedResults = {}
    local totalPoints = 0

    for i, gangId in ipairs(ConquestData.gangs) do
        table.insert(sortedResults, {
            gangId = gangId,
            points = ConquestData.points[i],
            name = ConquestData.gangNames[i]
        })
        totalPoints = totalPoints + ConquestData.points[i]
    end

    -- Sort by points (descending)
    table.sort(sortedResults, function(a, b) return a.points > b.points end)

    if #sortedResults > 0 then
        local winner = sortedResults[1]
        TriggerClientEvent('olympus-conquest:notify', -1,
            string.format("The conquest zone has been dominated by: %s", winner.name))

        -- Process payouts
        OlympusConquest.ProcessPayouts(sortedResults, totalPoints, zone, scheduleId)
    end

    -- Cleanup conquest
    OlympusConquest.CleanupConquest(zone)

    -- Reset conquest data
    ConquestData.active = false
    ConquestData.zone = nil
    ConquestData.gangs = {}
    ConquestData.points = {}
    ConquestData.gangNames = {}
    ConquestData.prizePool = 0
    ConquestData.leaderboard = {{}, {}, {}}

    -- Clear locked chop shop after delay
    CreateThread(function()
        Wait(300000) -- 5 minutes
        lockedChopShop = ""
        TriggerClientEvent('olympus-conquest:setLockedChopShop', -1, "")
    end)
end

function OlympusConquest.ProcessPayouts(sortedResults, totalPoints, zone, scheduleId)
    if totalPoints < 1 then return end

    -- Insert conquest record
    local query = [[
        INSERT INTO conquests (server, pot, total_points, winner_id)
        VALUES (?, ?, ?, ?)
    ]]

    local serverId = 1 -- This should come from server config
    local winnerId = sortedResults[1].gangId

    exports['oxmysql']:execute(query, {
        serverId,
        ConquestData.prizePool,
        totalPoints,
        winnerId
    }, function(result)
        if result.insertId then
            local conquestId = result.insertId

            -- Calculate and distribute payouts
            local gangPlayerCounts = {}
            local payouts = {}
            local gangIds = {}

            for i, result in ipairs(sortedResults) do
                local multiplier = 1
                if i == 1 then
                    multiplier = Config.Settings.firstPlaceMultiplier
                elseif i == 2 then
                    multiplier = Config.Settings.secondPlaceMultiplier
                elseif i == 3 then
                    multiplier = Config.Settings.thirdPlaceMultiplier
                end

                local payout = math.floor(((result.points / totalPoints) * ConquestData.prizePool) * multiplier)
                table.insert(payouts, payout)
                table.insert(gangIds, result.gangId)

                -- Count players who participated from this gang
                local playerCount = 0
                for _, deathData in ipairs(conquestDeaths) do
                    if deathData.gangId == result.gangId then
                        playerCount = playerCount + 1
                    end
                end
                table.insert(gangPlayerCounts, math.max(playerCount, 1)) -- At least 1 to avoid division by zero

                -- Insert gang conquest record
                local gangQuery = [[
                    INSERT INTO conquest_gangs (conquest_id, gang_id, points, payout)
                    VALUES (?, ?, ?, ?)
                ]]

                exports['oxmysql']:execute(gangQuery, {
                    conquestId,
                    result.gangId,
                    result.points,
                    payout
                })
            end

            -- Distribute payouts to individual players
            for _, deathData in ipairs(conquestDeaths) do
                if deathData.playerId and deathData.gangId then
                    local gangIndex = nil
                    for i, gId in ipairs(gangIds) do
                        if gId == deathData.gangId then
                            gangIndex = i
                            break
                        end
                    end

                    if gangIndex and payouts[gangIndex] > 0 then
                        local playerPayout = math.floor(payouts[gangIndex] / gangPlayerCounts[gangIndex])

                        -- Add to player's deposit box
                        local updateQuery = [[
                            UPDATE players SET deposit_box = deposit_box + ? WHERE playerid = ?
                        ]]

                        exports['oxmysql']:execute(updateQuery, {
                            playerPayout,
                            deathData.playerId
                        })

                        -- Notify player if online
                        if deathData.source and GetPlayerName(deathData.source) then
                            TriggerClientEvent('olympus-conquest:payoutReceived', deathData.source, playerPayout)
                        end
                    end
                end
            end

            print(string.format("^2[Olympus Conquest]^7 Processed payouts for conquest %d", conquestId))
        end
    end)
end

function OlympusConquest.CleanupConquest(zone)
    -- Remove all markers
    TriggerClientEvent('olympus-conquest:cleanup', -1)

    -- Clear server data
    ConquestServer.pointMarkers = {}
    ConquestServer.lineMarkers = {}
    ConquestServer.flags = {}
    ConquestServer.prizePool = 0

    -- Notify about vehicle cleanup
    TriggerClientEvent('olympus-conquest:notify', -1,
        "Vehicles will despawn in 60 seconds! Garages will be active for 5 minutes!")

    -- Cleanup vehicles in zone after delay
    CreateThread(function()
        Wait(60000) -- 1 minute

        local vehicles = GetAllVehicles()
        for _, vehicle in ipairs(vehicles) do
            local vehiclePos = GetEntityCoords(vehicle)
            if OlympusConquest.IsPointInPolygon(vehiclePos, zone.polygon) then
                -- Check if vehicle is empty and no players nearby
                local occupants = GetVehicleNumberOfPassengers(vehicle)
                local playersNearby = false

                local players = GetPlayers()
                for _, playerId in ipairs(players) do
                    local playerPos = GetEntityCoords(GetPlayerPed(playerId))
                    if #(vehiclePos - playerPos) < 5.0 then
                        playersNearby = true
                        break
                    end
                end

                if occupants == 0 and not playersNearby then
                    DeleteEntity(vehicle)
                end
            end
        end
    end)
end

function OlympusConquest.CancelConquest()
    if not ConquestData.active then return end

    print("^1[Olympus Conquest]^7 Conquest cancelled")

    TriggerClientEvent('olympus-conquest:notify', -1,
        string.format("Conquest at %s has been canceled!", ConquestData.zone and ConquestData.zone.name or "Unknown"))

    -- Cleanup
    TriggerClientEvent('olympus-conquest:cleanup', -1)

    -- Reset all data
    ConquestData.active = false
    ConquestData.zone = nil
    ConquestData.gangs = {}
    ConquestData.points = {}
    ConquestData.gangNames = {}
    ConquestData.prizePool = 0
    ConquestData.leaderboard = {{}, {}, {}}

    ConquestServer.pointMarkers = {}
    ConquestServer.lineMarkers = {}
    ConquestServer.flags = {}
    ConquestServer.prizePool = 0

    cancelConquest = false
    conquestVote = false
    lockedChopShop = ""

    TriggerClientEvent('olympus-conquest:setLockedChopShop', -1, "")
end

-- ============================================
-- CONQUEST VOTING SYSTEM
-- ============================================

function OlympusConquest.StartVoting(scheduleId)
    if conquestVote then return false, "Vote already in progress" end

    conquestVote = true
    local conquestVotes = {}
    local votedPIDs = {}

    print("^3[Olympus Conquest]^7 Starting conquest vote")

    -- Broadcast vote start
    TriggerClientEvent('olympus-conquest:voteStart', -1)
    TriggerClientEvent('olympus-conquest:notify', -1,
        "The Conquest event is starting! Type /vote in chat to vote for a location! A map will be chosen in 2 minutes.")

    -- Handle vote submissions
    local voteHandler = function(playerId, zoneIndex)
        local playerData = exports['olympus-core']:GetPlayerData(playerId)
        if not playerData then return end

        local playerId = playerData.citizenid
        if not playerId then return end

        -- Check if already voted
        for _, pid in ipairs(votedPIDs) do
            if pid == playerId then
                TriggerClientEvent('olympus-conquest:notify', playerId, "You have already voted!")
                return
            end
        end

        -- Validate zone index
        if zoneIndex < 1 or zoneIndex > #Config.VoteZones then
            TriggerClientEvent('olympus-conquest:notify', playerId, "Invalid zone selection!")
            return
        end

        -- Record vote
        table.insert(conquestVotes, zoneIndex)
        table.insert(votedPIDs, playerId)

        TriggerClientEvent('olympus-conquest:notify', playerId,
            string.format("Vote recorded for %s!", Config.VoteZones[zoneIndex]))
    end

    -- Register temporary event handler
    RegisterNetEvent('olympus-conquest:submitVote', voteHandler)

    -- Vote countdown
    CreateThread(function()
        Wait(Config.Settings.voteTime * 1000) -- 2 minutes

        -- Remove event handler
        RemoveEventHandler('olympus-conquest:submitVote', voteHandler)

        if cancelConquest then
            conquestVote = false
            return
        end

        -- Count votes
        local voteCounts = {}
        for i = 1, #Config.VoteZones do
            voteCounts[i] = 0
        end

        for _, vote in ipairs(conquestVotes) do
            voteCounts[vote] = voteCounts[vote] + 1
        end

        -- Find winning zone
        local maxVotes = 0
        local winningZone = math.random(1, #Config.VoteZones) -- Default random

        for i, count in ipairs(voteCounts) do
            if count > maxVotes then
                maxVotes = count
                winningZone = i
            end
        end

        lastConquest = winningZone - 1 -- Convert to 0-indexed
        conquestVote = false

        print(string.format("^3[Olympus Conquest]^7 Vote ended, winner: %s", Config.VoteZones[winningZone]))

        -- Start conquest with voted zone
        OlympusConquest.StartConquest(lastConquest, scheduleId)
    end)

    return true, "Vote started successfully"
end

-- ============================================
-- EVENT HANDLERS
-- ============================================

-- Player attempts to capture a point
RegisterNetEvent('olympus-conquest:capturePoint', function(flagIndex)
    local src = source

    if not ConquestData.active then
        TriggerClientEvent('olympus-conquest:notify', src, "No conquest is currently active!")
        return
    end

    local gangData = OlympusConquest.GetPlayerGangData(src)
    if not gangData then
        TriggerClientEvent('olympus-conquest:notify', src, "You must be in a gang to capture!")
        return
    end

    local flagData = ConquestServer.flags[flagIndex]
    if not flagData then
        TriggerClientEvent('olympus-conquest:notify', src, "Invalid capture point!")
        return
    end

    if flagData.owner == gangData.id then
        TriggerClientEvent('olympus-conquest:notify', src, "Your gang already owns this capture point!")
        return
    end

    if flagData.capturing then
        TriggerClientEvent('olympus-conquest:notify', src, "This point is already being captured!")
        return
    end

    -- Check player weapon
    local playerPed = GetPlayerPed(src)
    local weapon = GetSelectedPedWeapon(playerPed)
    local hasValidWeapon = false

    for _, validWeapon in ipairs(Config.Settings.requiredWeaponCalibres) do
        if weapon == GetHashKey(validWeapon) then
            hasValidWeapon = true
            break
        end
    end

    if not hasValidWeapon then
        TriggerClientEvent('olympus-conquest:notify', src, "You need a 5.56 caliber gun or higher to capture!")
        return
    end

    -- Check ammo
    local ammo = GetAmmoInPedWeapon(playerPed, weapon)
    if ammo <= 0 then
        TriggerClientEvent('olympus-conquest:notify', src, "You need ammo to capture!")
        return
    end

    -- Start capture process
    flagData.capturing = true
    flagData.capturer = src

    TriggerClientEvent('olympus-conquest:startCapture', src, flagIndex)

    -- Broadcast capture attempt
    OlympusConquest.BroadcastToZone(
        string.format("Capture Point %s is being taken by: %s",
            flagData.phonetic, gangData.name),
        ConquestData.zone.polygon
    )
end)

-- Player completes capture
RegisterNetEvent('olympus-conquest:captureComplete', function(flagIndex)
    local src = source

    if not ConquestData.active then return end

    local gangData = OlympusConquest.GetPlayerGangData(src)
    if not gangData then return end

    local flagData = ConquestServer.flags[flagIndex]
    if not flagData or flagData.capturer ~= src then return end

    -- Update flag ownership
    flagData.owner = gangData.id
    flagData.capturing = false
    flagData.capturer = nil

    -- Notify all clients
    TriggerClientEvent('olympus-conquest:flagCaptured', -1, flagIndex, gangData.id, gangData.name)

    -- Broadcast capture success
    OlympusConquest.BroadcastToZone(
        string.format("Capture Point %s has been taken by: %s",
            flagData.phonetic, gangData.name),
        ConquestData.zone.polygon
    )
end)

-- Player cancels capture
RegisterNetEvent('olympus-conquest:captureCancel', function(flagIndex)
    local src = source

    local flagData = ConquestServer.flags[flagIndex]
    if flagData and flagData.capturer == src then
        flagData.capturing = false
        flagData.capturer = nil

        TriggerClientEvent('olympus-conquest:captureCancelled', -1, flagIndex)
    end
end)

-- Player death tracking for conquest
RegisterNetEvent('olympus-conquest:playerDeath', function(killerId)
    local src = source

    if not ConquestData.active then return end

    local playerData = exports['olympus-core']:GetPlayerData(src)
    local gangData = OlympusConquest.GetPlayerGangData(src)

    if playerData and gangData then
        table.insert(conquestDeaths, {
            playerId = playerData.citizenid,
            source = src,
            gangId = gangData.id,
            timestamp = os.time()
        })
    end
end)

-- Admin commands
RegisterNetEvent('olympus-conquest:adminStart', function(zoneIndex)
    local src = source

    -- Check admin permissions
    local playerData = exports['olympus-core']:GetPlayerData(src)
    if not playerData or not playerData.metadata.admin_level or playerData.metadata.admin_level < 3 then
        TriggerClientEvent('olympus-conquest:notify', src, "Insufficient permissions!")
        return
    end

    local success, message = OlympusConquest.StartConquest(zoneIndex, -1)
    TriggerClientEvent('olympus-conquest:notify', src, message)
end)

RegisterNetEvent('olympus-conquest:adminCancel', function()
    local src = source

    -- Check admin permissions
    local playerData = exports['olympus-core']:GetPlayerData(src)
    if not playerData or not playerData.metadata.admin_level or playerData.metadata.admin_level < 3 then
        TriggerClientEvent('olympus-conquest:notify', src, "Insufficient permissions!")
        return
    end

    cancelConquest = true
    TriggerClientEvent('olympus-conquest:notify', src, "Conquest will be cancelled")
end)

RegisterNetEvent('olympus-conquest:adminVote', function()
    local src = source

    -- Check admin permissions
    local playerData = exports['olympus-core']:GetPlayerData(src)
    if not playerData or not playerData.metadata.admin_level or playerData.metadata.admin_level < 3 then
        TriggerClientEvent('olympus-conquest:notify', src, "Insufficient permissions!")
        return
    end

    local success, message = OlympusConquest.StartVoting(-1)
    TriggerClientEvent('olympus-conquest:notify', src, message)
end)

-- ============================================
-- EXPORT FUNCTIONS
-- ============================================

exports('IsConquestActive', function()
    return ConquestData.active
end)

exports('GetConquestData', function()
    return ConquestData
end)

exports('StartConquest', function(zoneIndex, scheduleId)
    return OlympusConquest.StartConquest(zoneIndex, scheduleId)
end)

exports('CancelConquest', function()
    cancelConquest = true
    return true
end)

exports('GetConquestZones', function()
    return Config.Zones
end)

exports('StartVoting', function(scheduleId)
    return OlympusConquest.StartVoting(scheduleId)
end)

-- ============================================
-- COMMANDS
-- ============================================

RegisterCommand('conquest', function(source, args)
    local src = source

    if #args < 1 then
        TriggerClientEvent('olympus-conquest:notify', src, "Usage: /conquest [start|cancel|vote] [zone_index]")
        return
    end

    local action = args[1]:lower()

    if action == "start" then
        local zoneIndex = tonumber(args[2]) or 0
        TriggerEvent('olympus-conquest:adminStart', zoneIndex)
    elseif action == "cancel" then
        TriggerEvent('olympus-conquest:adminCancel')
    elseif action == "vote" then
        TriggerEvent('olympus-conquest:adminVote')
    else
        TriggerClientEvent('olympus-conquest:notify', src, "Invalid action. Use: start, cancel, or vote")
    end
end, false)

RegisterCommand('vote', function(source, args)
    local src = source

    if not conquestVote then
        TriggerClientEvent('olympus-conquest:notify', src, "No conquest vote is currently active!")
        return
    end

    if #args < 1 then
        TriggerClientEvent('olympus-conquest:openVoteMenu', src)
        return
    end

    local zoneIndex = tonumber(args[1])
    if zoneIndex then
        TriggerEvent('olympus-conquest:submitVote', zoneIndex)
    else
        TriggerClientEvent('olympus-conquest:openVoteMenu', src)
    end
end, false)

print("^2[Olympus Conquest]^7 Server system loaded successfully")
