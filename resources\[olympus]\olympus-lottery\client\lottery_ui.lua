-- Olympus Lottery - UI Client
-- Based on original buyLotteryTickets.hpp dialog

local isLotteryUIOpen = false

-- Open lottery UI with current lottery data
function OpenLotteryUI(lotteryData)
    if isLotteryUIOpen then return end

    isLotteryUIOpen = true
    SendNUIMessage({
        action = "openLottery",
        data = lotteryData or {}
    })
end

-- Close lottery UI
function CloseLotteryUI()
    if not isLotteryUIOpen then return end

    isLotteryUIOpen = false
    SendNUIMessage({
        action = "closeLottery"
    })
end

-- Update lottery data in UI
function UpdateLotteryData(data)
    if isLotteryUIOpen then
        SendNUIMessage({
            action = "updateLotteryData",
            data = data
        })
    end
end

-- Show error message in UI
function ShowLotteryError(message)
    if isLotteryUIOpen then
        SendNUIMessage({
            action = "showError",
            message = message
        })
    end
end

-- Show success message in UI
function ShowLotterySuccess(message)
    if isLotteryUIOpen then
        SendNUIMessage({
            action = "showSuccess",
            message = message
        })
    end
end

-- NUI Callbacks
RegisterNUICallback('closeLottery', function(data, cb)
    CloseLotteryUI()
    cb('ok')
end)

RegisterNUICallback('setNuiFocus', function(data, cb)
    SetNuiFocus(data.hasFocus, data.hasCursor)
    cb('ok')
end)

RegisterNUICallback('buyTickets', function(data, cb)
    local amount = data.amount or 1
    if amount >= 1 and amount <= 10 then
        TriggerServerEvent('olympus-lottery:server:buyTickets', amount)
    end
    cb('ok')
end)

-- Event handlers
RegisterNetEvent('olympus-lottery:client:openUI', function(lotteryData)
    OpenLotteryUI(lotteryData)
end)

RegisterNetEvent('olympus-lottery:client:updateLotteryData', function(data)
    UpdateLotteryData(data)
end)

RegisterNetEvent('olympus-lottery:client:showError', function(message)
    ShowLotteryError(message)
end)

RegisterNetEvent('olympus-lottery:client:showSuccess', function(message)
    ShowLotterySuccess(message)
end)

RegisterNetEvent('olympus-lottery:client:closeLottery', function()
    CloseLotteryUI()
end)

-- Export functions
exports('OpenLotteryUI', OpenLotteryUI)
exports('CloseLotteryUI', CloseLotteryUI)
exports('UpdateLotteryData', UpdateLotteryData)
