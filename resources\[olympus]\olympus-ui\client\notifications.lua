-- ========================================
-- OLYMPUS UI FRAMEWORK - NOTIFICATIONS
-- Based on Original Olympus Altis Life
-- ========================================

local OlympusNotifications = {}

-- ========================================
-- NOTIFICATION FUNCTIONS
-- ========================================

-- Main notification function (replaces hint system)
function OlympusNotifications.ShowHint(message, duration, type)
    exports['olympus-ui']:ShowNotification({
        message = message,
        type = type or 'info',
        duration = duration or 5000
    })
end

-- Title text function (replaces titleText)
function OlympusNotifications.ShowTitleText(text, duration, fadeOut)
    exports['olympus-ui']:ShowTitleText({
        text = text,
        duration = duration or 5000,
        fadeOut = fadeOut ~= false
    })
end

-- Notification with sound
function OlympusNotifications.ShowNotificationWithSound(message, type, sound)
    exports['olympus-ui']:ShowNotification({
        message = message,
        type = type or 'info',
        duration = 5000,
        sound = sound or 'notification_' .. (type or 'info') .. '.ogg'
    })
end

-- Success notification
function OlympusNotifications.ShowSuccess(message, duration)
    OlympusNotifications.ShowNotificationWithSound(message, 'success', 'notification_success.ogg')
end

-- Error notification
function OlympusNotifications.ShowError(message, duration)
    OlympusNotifications.ShowNotificationWithSound(message, 'error', 'notification_error.ogg')
end

-- Warning notification
function OlympusNotifications.ShowWarning(message, duration)
    OlympusNotifications.ShowNotificationWithSound(message, 'warning', 'notification_warning.ogg')
end

-- Info notification
function OlympusNotifications.ShowInfo(message, duration)
    OlympusNotifications.ShowNotificationWithSound(message, 'info', 'notification_info.ogg')
end

-- Admin notification
function OlympusNotifications.ShowAdmin(message, duration)
    OlympusNotifications.ShowNotificationWithSound(message, 'admin', 'notification_admin.ogg')
end

-- Police notification
function OlympusNotifications.ShowPolice(message, duration)
    OlympusNotifications.ShowNotificationWithSound(message, 'police', 'notification_police.ogg')
end

-- Medical notification
function OlympusNotifications.ShowMedical(message, duration)
    OlympusNotifications.ShowNotificationWithSound(message, 'medical', 'notification_medical.ogg')
end

-- ========================================
-- COMPATIBILITY FUNCTIONS
-- ========================================

-- Replace native hint function
function hint(message)
    OlympusNotifications.ShowHint(message)
end

-- Replace native titleText function
function titleText(text, type, speed, showInMap, showOnHUD)
    -- Convert Arma 3 titleText to our system
    local duration = 5000
    
    if type == "PLAIN" then
        duration = 5000
    elseif type == "PLAIN DOWN" then
        duration = 3000
    elseif type == "BLACK" then
        duration = 10000
    elseif type == "BLACK FADED" then
        duration = 8000
    end
    
    OlympusNotifications.ShowTitleText(text, duration)
end

-- Replace native titleFadeOut function
function titleFadeOut(duration)
    -- This would be handled automatically by our system
    Wait(duration * 1000)
end

-- ========================================
-- EXPORTS
-- ========================================

exports('ShowHint', OlympusNotifications.ShowHint)
exports('ShowTitleText', OlympusNotifications.ShowTitleText)
exports('ShowSuccess', OlympusNotifications.ShowSuccess)
exports('ShowError', OlympusNotifications.ShowError)
exports('ShowWarning', OlympusNotifications.ShowWarning)
exports('ShowInfo', OlympusNotifications.ShowInfo)
exports('ShowAdmin', OlympusNotifications.ShowAdmin)
exports('ShowPolice', OlympusNotifications.ShowPolice)
exports('ShowMedical', OlympusNotifications.ShowMedical)

-- ========================================
-- EVENT HANDLERS
-- ========================================

RegisterNetEvent('olympus-notifications:client:showHint', function(message, duration, type)
    OlympusNotifications.ShowHint(message, duration, type)
end)

RegisterNetEvent('olympus-notifications:client:showTitleText', function(text, duration)
    OlympusNotifications.ShowTitleText(text, duration)
end)

RegisterNetEvent('olympus-notifications:client:showSuccess', function(message, duration)
    OlympusNotifications.ShowSuccess(message, duration)
end)

RegisterNetEvent('olympus-notifications:client:showError', function(message, duration)
    OlympusNotifications.ShowError(message, duration)
end)

RegisterNetEvent('olympus-notifications:client:showWarning', function(message, duration)
    OlympusNotifications.ShowWarning(message, duration)
end)

RegisterNetEvent('olympus-notifications:client:showInfo', function(message, duration)
    OlympusNotifications.ShowInfo(message, duration)
end)

RegisterNetEvent('olympus-notifications:client:showAdmin', function(message, duration)
    OlympusNotifications.ShowAdmin(message, duration)
end)

RegisterNetEvent('olympus-notifications:client:showPolice', function(message, duration)
    OlympusNotifications.ShowPolice(message, duration)
end)

RegisterNetEvent('olympus-notifications:client:showMedical', function(message, duration)
    OlympusNotifications.ShowMedical(message, duration)
end)
