@echo off
echo Checking MySQL Database Setup...
echo.

REM Check if MySQL is running
tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [✓] MySQL service is running
) else (
    echo [✗] MySQL service is not running
    echo Please start MySQL service first
    pause
    exit /b 1
)

echo.
echo Attempting to connect to MySQL and create database...
echo.

REM Try to create the database using mysql command
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS olympus_altis_life CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; USE olympus_altis_life; SELECT 'Database setup complete!' as Status;"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo [✓] Database setup completed successfully!
    echo [✓] Database 'olympus_altis_life' is ready
    echo.
    echo Your server should now be able to connect to the database.
) else (
    echo.
    echo [✗] Database setup failed
    echo Please check your MySQL installation and credentials
)

echo.
pause
