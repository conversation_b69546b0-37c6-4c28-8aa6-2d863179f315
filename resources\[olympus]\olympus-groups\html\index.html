<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Olympus Groups</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="groupMenu" class="menu-container" style="display: none;">
        <!-- Group Browser (Join/Create) -->
        <div id="groupBrowser" class="menu-panel" style="display: none;">
            <div class="menu-header">
                <h2>Group Management</h2>
                <button class="close-btn" onclick="closeMenu()">&times;</button>
            </div>
            
            <div class="menu-content">
                <div class="group-list-container">
                    <h3>Available Groups</h3>
                    <div class="group-list" id="groupList">
                        <!-- Groups will be populated here -->
                    </div>
                    
                    <div class="group-actions">
                        <button class="btn btn-primary" onclick="openCreateGroup()">Create New Group</button>
                        <button class="btn btn-secondary" onclick="refreshGroupList()">Refresh</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Group Management (Current Group) -->
        <div id="groupManagement" class="menu-panel" style="display: none;">
            <div class="menu-header">
                <h2 id="groupTitle">Group Management</h2>
                <button class="close-btn" onclick="closeMenu()">&times;</button>
            </div>
            
            <div class="menu-content">
                <div class="group-info">
                    <div class="group-details">
                        <span id="groupName">Group Name</span>
                        <span id="memberCount">0/8 Members</span>
                        <span id="lockStatus">Unlocked</span>
                    </div>
                    
                    <div class="group-controls" id="leaderControls" style="display: none;">
                        <button class="btn btn-warning" id="lockToggle" onclick="toggleLock()">Lock Group</button>
                        <button class="btn btn-danger" onclick="leaveGroup()">Disband Group</button>
                    </div>
                    
                    <div class="member-controls" id="memberControls">
                        <button class="btn btn-danger" onclick="leaveGroup()">Leave Group</button>
                    </div>
                </div>
                
                <div class="member-list-container">
                    <h3>Group Members</h3>
                    <div class="member-list" id="memberList">
                        <!-- Members will be populated here -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Create Group Dialog -->
        <div id="createGroupDialog" class="dialog" style="display: none;">
            <div class="dialog-content">
                <div class="dialog-header">
                    <h3>Create New Group</h3>
                    <button class="close-btn" onclick="closeCreateGroup()">&times;</button>
                </div>
                
                <div class="dialog-body">
                    <div class="form-group">
                        <label for="groupNameInput">Group Name (Max 32 characters):</label>
                        <input type="text" id="groupNameInput" maxlength="32" placeholder="Enter group name">
                    </div>
                    
                    <div class="form-group">
                        <label for="groupPasswordInput">Password (Optional, Max 16 characters):</label>
                        <input type="password" id="groupPasswordInput" maxlength="16" placeholder="Enter password (optional)">
                    </div>
                    
                    <div class="form-info">
                        <p>Groups are free to create. Allowed characters: A-z, 0-9, underscore, and space.</p>
                    </div>
                </div>
                
                <div class="dialog-footer">
                    <button class="btn btn-primary" onclick="createGroup()">Create</button>
                    <button class="btn btn-secondary" onclick="closeCreateGroup()">Cancel</button>
                </div>
            </div>
        </div>
        
        <!-- Join Group Dialog -->
        <div id="joinGroupDialog" class="dialog" style="display: none;">
            <div class="dialog-content">
                <div class="dialog-header">
                    <h3 id="joinGroupTitle">Join Group</h3>
                    <button class="close-btn" onclick="closeJoinGroup()">&times;</button>
                </div>
                
                <div class="dialog-body">
                    <div class="form-group" id="passwordGroup">
                        <label for="joinPasswordInput">Password:</label>
                        <input type="password" id="joinPasswordInput" placeholder="Enter group password">
                    </div>
                </div>
                
                <div class="dialog-footer">
                    <button class="btn btn-primary" onclick="joinGroup()">Join</button>
                    <button class="btn btn-secondary" onclick="closeJoinGroup()">Cancel</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
