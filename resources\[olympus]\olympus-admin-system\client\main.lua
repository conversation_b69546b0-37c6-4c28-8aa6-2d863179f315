-- ========================================
-- OLYMPUS ADMIN SYSTEM - CLIENT MAIN
-- Complete recreation based on original Olympus admin mechanics
-- Handles client-side admin functionality, UI, and interactions
-- ========================================

local OlympusAdmin = {}
OlympusAdmin.IsVanished = false
OlympusAdmin.IsSpectating = false
OlympusAdmin.IsFrozen = false
OlympusAdmin.NoClipEnabled = false
OlympusAdmin.AdminPanelOpen = false
OlympusAdmin.VanishedPlayers = {}
OlympusAdmin.PlayerList = {}
OlympusAdmin.ReportList = {}

-- Load configuration
local Config = require('config.shared')

-- Initialize admin system
function InitializeAdminSystem()
    print("^2[Olympus Admin]^7 Initializing client admin system...")

    -- Register keybinds
    RegisterKeyMapping('admin_panel', 'Open Admin Panel', 'keyboard', 'F10')
    RegisterKeyMapping('admin_vanish', 'Toggle Vanish', 'keyboard', 'F11')
    RegisterKeyMapping('admin_noclip', 'Toggle NoClip', 'keyboard', 'F9')
    RegisterKeyMapping('report_menu', 'Open Report Menu', 'keyboard', 'F2')

    -- Register commands
    RegisterAdminCommands()

    print("^2[Olympus Admin]^7 Client admin system initialized!")
end

-- Initialize system when client starts
CreateThread(function()
    -- Wait for core system
    while not exports['olympus-core']:IsPlayerLoaded() do
        Wait(1000)
    end

    Wait(2000) -- Additional wait for UI system
    InitializeAdminSystem()
end)

-- Register admin commands
function RegisterAdminCommands()
    -- Admin panel command
    RegisterCommand('admin_panel', function()
        if IsPlayerAdmin() then
            ToggleAdminPanel()
        end
    end, false)

    -- Vanish command
    RegisterCommand('admin_vanish', function()
        if IsPlayerAdmin() then
            TriggerServerEvent('olympus-admin:server:toggleVanish')
        end
    end, false)

    -- NoClip command
    RegisterCommand('admin_noclip', function()
        if IsPlayerAdmin() then
            ToggleNoClip()
        end
    end, false)

    -- Report menu command
    RegisterCommand('report_menu', function()
        OpenReportMenu()
    end, false)

    -- Admin chat command
    RegisterCommand('a', function(source, args, rawCommand)
        if IsPlayerAdmin() then
            local message = table.concat(args, ' ')
            if message and message ~= '' then
                TriggerServerEvent('olympus-admin:server:adminChat', message)
            end
        end
    end, false)

    -- Teleport commands
    RegisterCommand('tp', function(source, args, rawCommand)
        if IsPlayerAdmin() then
            if args[1] then
                local targetId = tonumber(args[1])
                if targetId then
                    TriggerServerEvent('olympus-admin:server:teleportToPlayer', targetId)
                end
            end
        end
    end, false)

    RegisterCommand('bring', function(source, args, rawCommand)
        if IsPlayerAdmin() then
            if args[1] then
                local targetId = tonumber(args[1])
                if targetId then
                    TriggerServerEvent('olympus-admin:server:teleportPlayerToMe', targetId)
                end
            end
        end
    end, false)

    -- Punishment commands
    RegisterCommand('kick', function(source, args, rawCommand)
        if IsPlayerAdmin() then
            if args[1] then
                local targetId = tonumber(args[1])
                local reason = table.concat(args, ' ', 2) or 'No reason provided'
                if targetId then
                    TriggerServerEvent('olympus-admin:server:kickPlayer', targetId, reason)
                end
            end
        end
    end, false)

    RegisterCommand('ban', function(source, args, rawCommand)
        if GetPlayerAdminRank() >= 2 then
            if args[1] then
                local targetId = tonumber(args[1])
                local duration = tonumber(args[2]) or 0
                local reason = table.concat(args, ' ', 3) or 'No reason provided'
                if targetId then
                    TriggerServerEvent('olympus-admin:server:banPlayer', targetId, reason, duration, duration == 0)
                end
            end
        end
    end, false)

    RegisterCommand('warn', function(source, args, rawCommand)
        if IsPlayerAdmin() then
            if args[1] then
                local targetId = tonumber(args[1])
                local points = tonumber(args[2]) or 1
                local reason = table.concat(args, ' ', 3) or 'No reason provided'
                if targetId then
                    TriggerServerEvent('olympus-admin:server:warnPlayer', targetId, reason, points)
                end
            end
        end
    end, false)

    RegisterCommand('freeze', function(source, args, rawCommand)
        if IsPlayerAdmin() then
            if args[1] then
                local targetId = tonumber(args[1])
                if targetId then
                    TriggerServerEvent('olympus-admin:server:toggleFreeze', targetId)
                end
            end
        end
    end, false)

    RegisterCommand('spectate', function(source, args, rawCommand)
        if IsPlayerAdmin() then
            if args[1] then
                local targetId = tonumber(args[1])
                if targetId then
                    TriggerServerEvent('olympus-admin:server:toggleSpectate', targetId)
                end
            else
                -- Stop spectating
                TriggerServerEvent('olympus-admin:server:toggleSpectate', nil)
            end
        end
    end, false)
end

-- Check if player is admin
function IsPlayerAdmin()
    local playerData = exports['olympus-core']:GetPlayerData()
    return playerData and playerData.adminRank and playerData.adminRank > 0
end

-- Get player admin rank
function GetPlayerAdminRank()
    local playerData = exports['olympus-core']:GetPlayerData()
    return playerData and playerData.adminRank or 0
end

-- Toggle admin panel
function ToggleAdminPanel()
    if OlympusAdmin.AdminPanelOpen then
        CloseAdminPanel()
    else
        OpenAdminPanel()
    end
end

-- Open admin panel
function OpenAdminPanel()
    if not IsPlayerAdmin() then return end

    OlympusAdmin.AdminPanelOpen = true

    -- Request data from server
    TriggerServerEvent('olympus-admin:server:getOnlinePlayers')
    TriggerServerEvent('olympus-admin:server:getActiveReports')

    -- Open NUI
    SetNuiFocus(true, true)
    SendNUIMessage({
        type = 'openAdminPanel',
        playerRank = GetPlayerAdminRank(),
        playerList = OlympusAdmin.PlayerList,
        reportList = OlympusAdmin.ReportList
    })
end

-- Close admin panel
function CloseAdminPanel()
    OlympusAdmin.AdminPanelOpen = false
    SetNuiFocus(false, false)
    SendNUIMessage({
        type = 'closeAdminPanel'
    })
end

-- Open report menu
function OpenReportMenu()
    local players = GetActivePlayers()
    local playerList = {}

    for _, playerId in ipairs(players) do
        local playerName = GetPlayerName(playerId)
        if playerName and playerId ~= PlayerId() then
            table.insert(playerList, {
                id = GetPlayerServerId(playerId),
                name = playerName
            })
        end
    end

    SetNuiFocus(true, true)
    SendNUIMessage({
        type = 'openReportMenu',
        playerList = playerList,
        categories = {
            'RDM/VDM',
            'Exploiting/Hacking',
            'FailRP/Trolling',
            'Harassment',
            'Bug Report',
            'Other'
        }
    })
end

-- Toggle NoClip
function ToggleNoClip()
    OlympusAdmin.NoClipEnabled = not OlympusAdmin.NoClipEnabled

    if OlympusAdmin.NoClipEnabled then
        StartNoClip()
    else
        StopNoClip()
    end
end

-- Start NoClip
function StartNoClip()
    CreateThread(function()
        local playerPed = PlayerPedId()

        while OlympusAdmin.NoClipEnabled do
            Wait(0)

            SetEntityInvincible(playerPed, true)
            SetEntityVisible(playerPed, false, false)
            SetEntityCollision(playerPed, false, false)
            FreezeEntityPosition(playerPed, true)
            SetPlayerInvincible(PlayerId(), true)

            local x, y, z = table.unpack(GetEntityCoords(playerPed))
            local dx, dy, dz = GetCamDirection()

            if IsControlPressed(0, 32) then -- W
                x, y, z = x + dx * 2, y + dy * 2, z + dz * 2
            end
            if IsControlPressed(0, 33) then -- S
                x, y, z = x - dx * 2, y - dy * 2, z - dz * 2
            end
            if IsControlPressed(0, 34) then -- A
                x, y, z = x + dy * 2, y - dx * 2, z
            end
            if IsControlPressed(0, 35) then -- D
                x, y, z = x - dy * 2, y + dx * 2, z
            end
            if IsControlPressed(0, 44) then -- Q
                z = z - 2
            end
            if IsControlPressed(0, 38) then -- E
                z = z + 2
            end

            SetEntityCoords(playerPed, x, y, z, false, false, false, false)
        end

        SetEntityInvincible(playerPed, false)
        SetEntityVisible(playerPed, true, false)
        SetEntityCollision(playerPed, true, true)
        FreezeEntityPosition(playerPed, false)
        SetPlayerInvincible(PlayerId(), false)
    end)

    TriggerEvent('olympus:client:notify', {
        type = 'success',
        title = 'NoClip',
        message = 'NoClip enabled',
        duration = 3000
    })
end

-- Stop NoClip
function StopNoClip()
    TriggerEvent('olympus:client:notify', {
        type = 'info',
        title = 'NoClip',
        message = 'NoClip disabled',
        duration = 3000
    })
end

-- Get camera direction
function GetCamDirection()
    local heading = GetGameplayCamRelativeHeading() + GetEntityHeading(PlayerPedId())
    local pitch = GetGameplayCamRelativePitch()

    local x = -math.sin(heading * math.pi / 180.0)
    local y = math.cos(heading * math.pi / 180.0)
    local z = math.sin(pitch * math.pi / 180.0)

    local len = math.sqrt(x * x + y * y + z * z)
    if len ~= 0 then
        x = x / len
        y = y / len
        z = z / len
    end

    return x, y, z
end

-- Event Handlers
RegisterNetEvent('olympus-admin:client:setVanish')
AddEventHandler('olympus-admin:client:setVanish', function(isVanished)
    OlympusAdmin.IsVanished = isVanished
    local playerPed = PlayerPedId()

    if isVanished then
        SetEntityAlpha(playerPed, 76, false) -- 30% transparency
        SetEntityInvincible(playerPed, true)
        SetPlayerInvincible(PlayerId(), true)

        TriggerEvent('olympus:client:notify', {
            type = 'success',
            title = 'Vanish Mode',
            message = 'Vanish enabled',
            duration = 3000
        })
    else
        SetEntityAlpha(playerPed, 255, false)
        SetEntityInvincible(playerPed, false)
        SetPlayerInvincible(PlayerId(), false)

        TriggerEvent('olympus:client:notify', {
            type = 'info',
            title = 'Vanish Mode',
            message = 'Vanish disabled',
            duration = 3000
        })
    end
end)

RegisterNetEvent('olympus-admin:client:updateVanishedPlayer')
AddEventHandler('olympus-admin:client:updateVanishedPlayer', function(playerId, isVanished)
    OlympusAdmin.VanishedPlayers[playerId] = isVanished

    -- Hide/show vanished players for non-admins
    if not IsPlayerAdmin() then
        local playerPed = GetPlayerPed(GetPlayerFromServerId(playerId))
        if playerPed and playerPed ~= 0 then
            SetEntityVisible(playerPed, not isVanished, false)
        end
    end
end)

RegisterNetEvent('olympus-admin:client:setFreeze')
AddEventHandler('olympus-admin:client:setFreeze', function(isFrozen)
    OlympusAdmin.IsFrozen = isFrozen
    local playerPed = PlayerPedId()

    FreezeEntityPosition(playerPed, isFrozen)

    if isFrozen then
        TriggerEvent('olympus:client:notify', {
            type = 'warning',
            title = 'Frozen',
            message = 'You have been frozen by an administrator',
            duration = 5000
        })
    else
        TriggerEvent('olympus:client:notify', {
            type = 'info',
            title = 'Unfrozen',
            message = 'You have been unfrozen',
            duration = 3000
        })
    end
end)

RegisterNetEvent('olympus-admin:client:startSpectate')
AddEventHandler('olympus-admin:client:startSpectate', function(targetId)
    OlympusAdmin.IsSpectating = true
    local targetPed = GetPlayerPed(GetPlayerFromServerId(targetId))

    if targetPed and targetPed ~= 0 then
        local playerPed = PlayerPedId()
        SetEntityVisible(playerPed, false, false)
        SetEntityInvincible(playerPed, true)

        -- Attach camera to target
        local cam = CreateCam('DEFAULT_SCRIPTED_CAMERA', true)
        AttachCamToEntity(cam, targetPed, 0.0, -3.0, 1.0, true)
        SetCamActive(cam, true)
        RenderScriptCams(true, false, 0, true, true)

        TriggerEvent('olympus:client:notify', {
            type = 'info',
            title = 'Spectating',
            message = 'Now spectating ' .. GetPlayerName(GetPlayerFromServerId(targetId)),
            duration = 5000
        })
    end
end)

RegisterNetEvent('olympus-admin:client:stopSpectate')
AddEventHandler('olympus-admin:client:stopSpectate', function()
    OlympusAdmin.IsSpectating = false
    local playerPed = PlayerPedId()

    SetEntityVisible(playerPed, true, false)
    SetEntityInvincible(playerPed, false)

    -- Restore normal camera
    RenderScriptCams(false, false, 0, true, true)
    DestroyCam(cam, false)

    TriggerEvent('olympus:client:notify', {
        type = 'info',
        title = 'Spectating',
        message = 'Stopped spectating',
        duration = 3000
    })
end)

RegisterNetEvent('olympus-admin:client:updatePlayerList')
AddEventHandler('olympus-admin:client:updatePlayerList', function(playerList)
    OlympusAdmin.PlayerList = playerList

    if OlympusAdmin.AdminPanelOpen then
        SendNUIMessage({
            type = 'updatePlayerList',
            playerList = playerList
        })
    end
end)

RegisterNetEvent('olympus-admin:client:updateReportList')
AddEventHandler('olympus-admin:client:updateReportList', function(reportList)
    OlympusAdmin.ReportList = reportList

    if OlympusAdmin.AdminPanelOpen then
        SendNUIMessage({
            type = 'updateReportList',
            reportList = reportList
        })
    end
end)

-- NUI Callbacks
RegisterNUICallback('closePanel', function(data, cb)
    CloseAdminPanel()
    cb('ok')
end)

RegisterNUICallback('closeReportMenu', function(data, cb)
    SetNuiFocus(false, false)
    cb('ok')
end)

RegisterNUICallback('submitReport', function(data, cb)
    TriggerServerEvent('olympus-admin:server:submitReport', data.reportedId, data.category, data.reason, data.evidence)
    SetNuiFocus(false, false)
    cb('ok')
end)

RegisterNUICallback('teleportToPlayer', function(data, cb)
    TriggerServerEvent('olympus-admin:server:teleportToPlayer', data.playerId)
    cb('ok')
end)

RegisterNUICallback('bringPlayer', function(data, cb)
    TriggerServerEvent('olympus-admin:server:teleportPlayerToMe', data.playerId)
    cb('ok')
end)

RegisterNUICallback('kickPlayer', function(data, cb)
    TriggerServerEvent('olympus-admin:server:kickPlayer', data.playerId, data.reason)
    cb('ok')
end)

RegisterNUICallback('banPlayer', function(data, cb)
    TriggerServerEvent('olympus-admin:server:banPlayer', data.playerId, data.reason, data.duration, data.isPermanent)
    cb('ok')
end)

RegisterNUICallback('warnPlayer', function(data, cb)
    TriggerServerEvent('olympus-admin:server:warnPlayer', data.playerId, data.reason, data.points)
    cb('ok')
end)

RegisterNUICallback('freezePlayer', function(data, cb)
    TriggerServerEvent('olympus-admin:server:toggleFreeze', data.playerId)
    cb('ok')
end)

RegisterNUICallback('spectatePlayer', function(data, cb)
    TriggerServerEvent('olympus-admin:server:toggleSpectate', data.playerId)
    cb('ok')
end)

RegisterNUICallback('claimReport', function(data, cb)
    TriggerServerEvent('olympus-admin:server:claimReport', data.reportId)
    cb('ok')
end)

RegisterNUICallback('closeReport', function(data, cb)
    TriggerServerEvent('olympus-admin:server:closeReport', data.reportId, data.resolution)
    cb('ok')
end)

RegisterNUICallback('refreshData', function(data, cb)
    TriggerServerEvent('olympus-admin:server:getOnlinePlayers')
    TriggerServerEvent('olympus-admin:server:getActiveReports')
    cb('ok')
end)

-- Exports
exports('IsPlayerAdmin', IsPlayerAdmin)
exports('GetPlayerAdminRank', GetPlayerAdminRank)
exports('ToggleAdminPanel', ToggleAdminPanel)
exports('OpenReportMenu', OpenReportMenu)
exports('ToggleVanish', function()
    if IsPlayerAdmin() then
        TriggerServerEvent('olympus-admin:server:toggleVanish')
    end
end)
exports('ToggleNoClip', ToggleNoClip)

print("^2[Olympus Admin]^7 Client module loaded")
