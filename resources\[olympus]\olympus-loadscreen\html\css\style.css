/* Olympus Altis Life Loading Screen - Authentic Recreation */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body, html {
    height: 100%;
    font-family: 'Roboto', 'PuristaMedium', sans-serif;
    overflow: hidden;
    background: #000;
}

/* Main Container */
.loading-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    background: #000;
    overflow: hidden;
}

/* Background Image (Original Olympus Background) */
.background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/loadingBackground.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: 1;
}

/* Background Overlay */
.background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    z-index: 2;
}

/* Main Content */
.loading-content {
    position: relative;
    z-index: 10;
    width: 100%;
    height: 100%;
    color: white;
}

/* Status Bar (Top) */
.status-bar {
    position: absolute;
    top: 2.5%;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 1000px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 15;
}

.loading-status {
    text-align: center;
    font-size: 1.25rem;
    font-weight: 500;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

/* Loading Progress Container (Top Right) */
.loading-progress-container {
    position: absolute;
    top: 11.35%;
    right: 10.95%;
    display: flex;
    align-items: center;
    gap: 10px;
}

.loading-icon {
    width: 75px;
    height: 75px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.progress-percentage {
    font-size: 1rem;
    font-weight: 500;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    text-align: center;
    min-width: 50px;
}

.logo-icon {
    display: inline-block;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #4a90e2, #5cb85c);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow:
        0 0 30px rgba(74, 144, 226, 0.4),
        0 0 60px rgba(74, 144, 226, 0.2);
    animation: logoGlow 3s ease-in-out infinite alternate;
}

.logo-icon i {
    font-size: 2.5rem;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

@keyframes logoGlow {
    0% {
        box-shadow:
            0 0 30px rgba(74, 144, 226, 0.4),
            0 0 60px rgba(74, 144, 226, 0.2);
    }
    100% {
        box-shadow:
            0 0 40px rgba(74, 144, 226, 0.6),
            0 0 80px rgba(74, 144, 226, 0.3);
    }
}

.server-title {
    font-size: 4.5rem;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 
        0 0 20px rgba(74, 144, 226, 0.5),
        0 0 40px rgba(74, 144, 226, 0.3),
        2px 2px 4px rgba(0, 0, 0, 0.8);
    letter-spacing: 8px;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #ffffff, #4a90e2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.server-subtitle {
    font-size: 1.4rem;
    font-weight: 300;
    color: #cccccc;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    letter-spacing: 2px;
    text-transform: uppercase;
}

/* Return to Lobby Button (Bottom Left) */
.lobby-button-container {
    position: absolute;
    bottom: 14.5%;
    left: 3.5%;
}

.lobby-button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #ffffff;
    padding: 12px 35px;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.lobby-button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Main Content Area (Center) */
.main-content-area {
    position: absolute;
    top: 20%;
    left: 20%;
    width: 60%;
    height: 50%;
    display: flex;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 8px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Left Panel - Navigation Tree */
.left-panel {
    width: 33.33%;
    background: rgba(0, 0, 0, 0.45);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    overflow-y: auto;
    padding: 25px;
}

.content-tree {
    font-family: 'PuristaMedium', 'Roboto', sans-serif;
    font-size: 0.9rem;
    color: #ffffff;
}

.tree-item {
    margin-bottom: 8px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.tree-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.tree-parent {
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 5px;
}

.tree-child-1 {
    color: #e6e6e6;
    margin-left: 15px;
}

.tree-child-2 {
    color: #cccccc;
    margin-left: 30px;
}

.tree-child-3 {
    color: #b3b3b3;
    margin-left: 45px;
}

/* Right Panel - Content Display */
.right-panel {
    width: 66.67%;
    background: rgba(0, 0, 0, 0.45);
    overflow-y: auto;
    padding: 25px;
}

.content-display {
    font-family: 'Roboto', sans-serif;
    font-size: 0.95rem;
    line-height: 1.6;
    color: #ffffff;
}

.content-display h1, .content-display h2, .content-display h3 {
    margin-bottom: 15px;
    color: #ffffff;
}

.content-display p {
    margin-bottom: 12px;
}

.content-display ul, .content-display ol {
    margin-left: 20px;
    margin-bottom: 12px;
}

.content-display li {
    margin-bottom: 5px;
}

/* Scrollbar Styling */
.left-panel::-webkit-scrollbar,
.right-panel::-webkit-scrollbar {
    width: 8px;
}

.left-panel::-webkit-scrollbar-track,
.right-panel::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.left-panel::-webkit-scrollbar-thumb,
.right-panel::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

.left-panel::-webkit-scrollbar-thumb:hover,
.right-panel::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Animations */
@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content-area {
        left: 10%;
        width: 80%;
    }
}

@media (max-width: 768px) {
    .main-content-area {
        left: 5%;
        width: 90%;
        top: 15%;
        height: 60%;
        flex-direction: column;
    }

    .left-panel {
        width: 100%;
        height: 40%;
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .right-panel {
        width: 100%;
        height: 60%;
    }

    .loading-progress-container {
        right: 5%;
        top: 5%;
    }

    .lobby-button-container {
        left: 5%;
        bottom: 5%;
    }
}

.progress-container {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

.progress-bar {
    position: relative;
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 15px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

.progress-fill {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0%;
    background: linear-gradient(90deg, #4a90e2, #5cb85c, #4a90e2);
    background-size: 200% 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
    animation: progressShimmer 2s linear infinite;
}

.progress-glow {
    position: absolute;
    top: -2px;
    left: 0;
    height: calc(100% + 4px);
    width: 0%;
    background: rgba(74, 144, 226, 0.3);
    border-radius: 6px;
    filter: blur(4px);
    transition: width 0.3s ease;
}

@keyframes progressShimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: #cccccc;
}

#loading-text {
    font-weight: 400;
}

#progress-percentage {
    font-weight: 500;
    color: #4a90e2;
}

/* Server Info Section */
.info-section {
    margin-bottom: 40px;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.server-info {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #cccccc;
    background: rgba(255, 255, 255, 0.05);
    padding: 10px 15px;
    border-radius: 20px;
    border: 1px solid rgba(74, 144, 226, 0.2);
    backdrop-filter: blur(10px);
}

.info-item i {
    color: #4a90e2;
    font-size: 1rem;
}

/* Tips Section */
.tips-section {
    margin-bottom: 40px;
    animation: fadeInUp 1s ease-out 0.9s both;
}

.tip-container {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(74, 144, 226, 0.2);
    border-radius: 10px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.tip-container h3 {
    font-size: 1.1rem;
    color: #4a90e2;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tip-container p {
    font-size: 0.95rem;
    color: #cccccc;
    line-height: 1.4;
}

/* Footer Section */
.footer-section {
    animation: fadeInUp 1s ease-out 1.2s both;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 20px;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #cccccc;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    padding: 8px 15px;
    border-radius: 20px;
    border: 1px solid transparent;
}

.social-link:hover {
    color: #4a90e2;
    border-color: rgba(74, 144, 226, 0.3);
    background: rgba(74, 144, 226, 0.1);
    transform: translateY(-2px);
}

.social-link i {
    font-size: 1.1rem;
}

.copyright {
    text-align: center;
    font-size: 0.8rem;
    color: #888;
}

/* Particles Effect */
.particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 2;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: rgba(74, 144, 226, 0.6);
    border-radius: 50%;
    animation: float 6s linear infinite;
}

@keyframes float {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-10vh) translateX(100px);
        opacity: 0;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .loading-content {
        padding: 0 20px;
    }
    
    .server-title {
        font-size: 3rem;
        letter-spacing: 4px;
    }
    
    .server-subtitle {
        font-size: 1.1rem;
    }
    
    .server-info {
        gap: 20px;
    }
    
    .social-links {
        gap: 20px;
        flex-wrap: wrap;
    }
}
