fx_version 'cerulean'
game 'gta5'

name 'Olympus Core Framework'
description 'Core framework for Olympus Altis Life FiveM replication'
author 'Olympus Development Team'
version '1.0.0'

-- Core dependencies
dependencies {
    'oxmysql'
}

-- Shared scripts (run on both client and server)
shared_scripts {
    'config/shared.lua'
}

-- Server scripts
server_scripts {
    '@oxmysql/lib/MySQL.lua',
    'server/main.lua',
    'server/database.lua',
    'server/player.lua',
    'server/inventory.lua'
}

-- Client scripts
client_scripts {
    'client/main.lua',
    'client/blips.lua'
}

-- No UI files for core (UI is handled by olympus-ui resource)

-- Exports
exports {
    'GetPlayerData',
    'GetPlayerMoney',
    'AddPlayerMoney',
    'RemovePlayerMoney',
    'GetPlayerInventory',
    'AddItem',
    'RemoveItem',
    'GetPlayerVehicles',
    'SpawnVehicle',
    'GetPlayerGang',
    'GetPlayerFaction',
    'SendNotification',
    'ShowProgressBar',
    'OpenMenu'
}

server_exports {
    'GetPlayerDataServer',
    'SavePlayerData',
    'GetAllPlayers',
    'GetPlayerByIdentifier',
    'AddPlayerMoneyServer',
    'RemovePlayerMoneyServer',
    'LogAction',
    'BanPlayer',
    'KickPlayer',
    'SendAdminMessage'
}
