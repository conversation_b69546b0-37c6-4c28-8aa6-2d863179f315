Config = {}

-- APD Search & Seizure Settings
Config.SearchSeizure = {
    -- Player Search Settings
    playerSearch = {
        enabled = true,
        maxDistance = 5.0,
        duration = 2000, -- 2 seconds
        requireFaction = 'police', -- Only APD can search players
        allowVigilante = true -- Vigilantes can search for illegal items only
    },
    
    -- Vehicle Seizure Settings
    vehicleSeizure = {
        enabled = true,
        maxDistance = 10.0,
        duration = 9000, -- 9 seconds (matching original)
        requireFaction = 'police', -- Only APD can seize vehicles
        rewards = {
            car = 1000,
            boat = 800,
            air = 1500
        },
        blackwaterReward = 4000000, -- $4M for Blackwater vehicles
        blackwaterArmedReward = 3000000, -- $3M for armed Blackwater vehicles
        valueMultiplier = 0.3 -- 30% of vehicle value as reward
    },
    
    -- Item Seizure Settings
    itemSeizure = {
        enabled = true,
        allowedFactions = {'police'}, -- Only APD can seize items
        illegalItems = {
            -- Drugs
            'weed', 'cocaine', 'heroin', 'meth', 'lsd',
            'marijuana', 'cocainep', 'heroinp', 'methp',
            
            -- Weapons (illegal for civilians)
            'weapon_assaultrifle', 'weapon_carbinerifle', 'weapon_advancedrifle',
            'weapon_mg', 'weapon_combatmg', 'weapon_rpg', 'weapon_grenadelauncher',
            
            -- Illegal equipment
            'lockpick', 'boltcutter', 'drill',
            
            -- Stolen items
            'stolen_watch', 'stolen_jewelry', 'stolen_electronics'
        },
        illegalGear = {
            -- Illegal uniforms/vests
            'U_B_CombatUniform_mcam', 'U_O_CombatUniform_ocamo',
            'V_PlateCarrier1_rgr', 'V_PlateCarrier2_rgr'
        }
    }
}

-- Illegal Item Values (for contraband calculation)
Config.IllegalItemValues = {
    -- Drugs
    weed = 150,
    cocaine = 300,
    heroin = 400,
    meth = 500,
    lsd = 250,
    marijuana = 200,
    cocainep = 450,
    heroinp = 600,
    methp = 750,
    
    -- Weapons
    weapon_assaultrifle = 50000,
    weapon_carbinerifle = 45000,
    weapon_advancedrifle = 55000,
    weapon_mg = 75000,
    weapon_combatmg = 80000,
    weapon_rpg = 150000,
    weapon_grenadelauncher = 100000,
    
    -- Illegal equipment
    lockpick = 1000,
    boltcutter = 1500,
    drill = 2000,
    
    -- Stolen items
    stolen_watch = 5000,
    stolen_jewelry = 10000,
    stolen_electronics = 3000
}

-- Animation Settings
Config.Animations = {
    searching = {
        dict = "anim@gangops@facility@servers@bodysearch@",
        anim = "player_search",
        flag = 49
    },
    seizing = {
        dict = "anim@heists@narcotics@funding@gang_idle",
        anim = "gang_chatting_idle01",
        flag = 49
    }
}

-- Notification Settings
Config.Notifications = {
    search = {
        start = "Searching player...",
        success = "Player search completed",
        tooFar = "Player is too far away",
        noPermission = "You don't have permission to search players",
        notAlive = "Cannot search dead players",
        contraband = "%s was found with $%s worth of contraband",
        robber = "%s is a robbery suspect",
        noIllegal = "No illegal items found"
    },
    seizure = {
        start = "Seizing vehicle...",
        success = "Vehicle seized successfully",
        cancelled = "Vehicle seizure cancelled",
        tooFar = "Vehicle is too far away",
        tooRekt = "Vehicle is too damaged to seize",
        occupied = "Cannot seize vehicle with occupants",
        noPermission = "You don't have permission to seize vehicles",
        blackwater = "Blackwater vehicle seized",
        reward = "Vehicle seized. Reward: $%s"
    },
    itemSeizure = {
        success = "Items seized from %s",
        noItems = "No illegal items to seize",
        noPermission = "You don't have permission to seize items"
    }
}
