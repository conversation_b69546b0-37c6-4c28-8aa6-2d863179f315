-- Olympus Legal System - Client Main

CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end
    
    print("[Olympus Legal System] Client initialized")
end)

exports('IsPlayerLawyer', function()
    return false
end)

exports('ProcessTicket', function(ticketData)
    print("[Olympus Legal System] Processing ticket")
end)

exports('RequestPardon', function(pardonData)
    print("[Olympus Legal System] Requesting pardon")
end)

print("[Olympus Legal System] Client module loaded")
