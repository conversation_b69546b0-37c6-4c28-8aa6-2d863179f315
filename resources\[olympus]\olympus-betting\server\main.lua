-- Olympus Player Betting System - Server Main
-- Based on original fn_betMoney.sqf from Olympus Altis Life

local OlympusBetting = {}

-- Player state tracking
local playerBetStates = {}
local playerCooldowns = {}

-- Database initialization
CreateThread(function()
    while not exports['olympus-core'] do
        Wait(100)
    end

    -- Initialize database tables
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS betting_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            player_id VARCHAR(50) NOT NULL,
            player_name VARCHAR(100) NOT NULL,
            target_id VARCHAR(50) NOT NULL,
            target_name VARCHAR(100) NOT NULL,
            amount INT NOT NULL,
            winner_id VARCHAR(50) NOT NULL,
            winner_name VARCHAR(100) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            server VARCHAR(50) DEFAULT 'olympus-1'
        )
    ]])

    print("[Olympus Betting] Server initialized with database")
end)

-- Utility Functions
function OlympusBetting.GetPlayerData(src)
    local success, result = pcall(exports['olympus-core'].GetPlayerData, src)
    if success and result then
        return result
    end
    return nil
end

function OlympusBetting.GetPlayerMoney(src, type)
    local playerData = OlympusBetting.GetPlayerData(src)
    if not playerData then return 0 end

    if type == "cash" then
        return playerData.money.cash or 0
    elseif type == "bank" then
        return playerData.money.bank or 0
    end
    return 0
end

function OlympusBetting.AddPlayerMoney(src, type, amount)
    local success = pcall(exports['olympus-core'].AddMoney, src, type, amount)
    return success
end

function OlympusBetting.RemovePlayerMoney(src, type, amount)
    local success = pcall(exports['olympus-core'].RemoveMoney, src, type, amount)
    return success
end

function OlympusBetting.GetPlayerPlaytime(src)
    local playerData = OlympusBetting.GetPlayerData(src)
    if not playerData then return 0 end
    return playerData.metadata.playtime or 0
end

function OlympusBetting.IsPlayerAdmin(src)
    local playerData = OlympusBetting.GetPlayerData(src)
    if not playerData then return false end
    return (playerData.metadata.adminlevel or 0) > 2
end

function OlympusBetting.GetPlayerFaction(src)
    local playerData = OlympusBetting.GetPlayerData(src)
    if not playerData then return "civilian" end
    return playerData.job.name or "civilian"
end

function OlympusBetting.IsPlayerOnDuty(src)
    local playerData = OlympusBetting.GetPlayerData(src)
    if not playerData then return false end
    return playerData.job.onduty or false
end

function OlympusBetting.GetPlayerRestrictions(src)
    local playerData = OlympusBetting.GetPlayerData(src)
    if not playerData then return false end
    return playerData.metadata.restrictions or false
end

function OlympusBetting.GetPlayerBettingEnabled(src)
    local playerData = OlympusBetting.GetPlayerData(src)
    if not playerData then return false end
    return playerData.metadata.bettingEnabled or false
end

function OlympusBetting.FormatMoney(amount)
    return string.format("$%s", exports['olympus-core']:CommaValue(amount))
end

function OlympusBetting.Notify(src, message, type)
    TriggerClientEvent('olympus-core:client:notify', src, message, type or 'info')
end

function OlympusBetting.BroadcastMessage(message)
    TriggerClientEvent('chat:addMessage', -1, {
        color = {255, 255, 0},
        multiline = true,
        args = {"[Betting]", message}
    })
end

-- Initialize player bet state
function OlympusBetting.InitPlayerState(src)
    if not playerBetStates[src] then
        playerBetStates[src] = {
            inBet = false,
            betCooldown = false,
            actionInUse = false,
            useAtm = true
        }
    end
end

-- Bet Money System based on original fn_betMoney.sqf
RegisterNetEvent('olympus-betting:server:betMoney', function(type, val, player)
    local src = source
    OlympusBetting.InitPlayerState(src)

    if type == "amount" then
        -- Validation checks from original function
        if playerBetStates[src].betCooldown then
            OlympusBetting.Notify(src, "You have already bet money recently. Please try again later.", 'error')
            return
        end

        if OlympusBetting.IsPlayerAdmin(src) then
            OlympusBetting.Notify(src, "Staff can not bet!", 'error')
            return
        end

        local faction = OlympusBetting.GetPlayerFaction(src)
        if faction == "ambulance" then
            OlympusBetting.Notify(src, "Medics cannot bet!", 'error')
            return
        end

        if faction == "police" and OlympusBetting.IsPlayerOnDuty(src) then
            OlympusBetting.Notify(src, "Cops cannot bet!", 'error')
            return
        end

        if playerBetStates[src].inBet then
            OlympusBetting.Notify(src, "You are already in a bet!", 'error')
            return
        end

        if not OlympusBetting.GetPlayerBettingEnabled(src) then
            OlympusBetting.Notify(src, "You currently have betting disabled!", 'error')
            return
        end

        local playtime = OlympusBetting.GetPlayerPlaytime(src)
        if playtime < 1800 then -- 30 minutes in original (1800 seconds)
            OlympusBetting.Notify(src, "You must have at least 30 hours on the server to bet!", 'error')
            return
        end

        -- Open bet dialog
        TriggerClientEvent('olympus-betting:client:openBetDialog', src)

    elseif type == "bet" then
        -- Process bet request
        local amount = tonumber(val)
        if not amount then
            OlympusBetting.Notify(src, "Please enter a valid number!", 'error')
            return
        end

        local playtime = OlympusBetting.GetPlayerPlaytime(src)
        if playtime < 1800 then
            OlympusBetting.Notify(src, "You must have at least 30 hours on the server to bet!", 'error')
            return
        end

        if amount < 1000 then
            OlympusBetting.Notify(src, "You have to bet at least $1000.", 'error')
            return
        end

        if amount > 5000000 then
            OlympusBetting.Notify(src, "You may not bet more than $5,000,000 at a time.", 'error')
            return
        end

        if amount <= 0 then return end

        local cash = OlympusBetting.GetPlayerMoney(src, "cash")
        local bank = OlympusBetting.GetPlayerMoney(src, "bank")
        if cash < amount and bank < amount then
            OlympusBetting.Notify(src, "You do not have that much money to bet!", 'error')
            return
        end

        if not player or not GetPlayerName(player) then
            OlympusBetting.Notify(src, "Invalid target player!", 'error')
            return
        end

        if OlympusBetting.GetPlayerRestrictions(player) then
            OlympusBetting.Notify(src, "The other player is on restrictions and cannot be bet against.", 'error')
            return
        end

        local targetFaction = OlympusBetting.GetPlayerFaction(player)
        if targetFaction == "ambulance" then
            OlympusBetting.Notify(src, "You cannot bet medics!", 'error')
            return
        end

        if OlympusBetting.GetPlayerRestrictions(src) then
            OlympusBetting.Notify(src, "You are on restrictions and cannot bet.", 'error')
            return
        end

        local distance = #(GetEntityCoords(GetPlayerPed(src)) - GetEntityCoords(GetPlayerPed(player)))
        if distance > 20 then
            OlympusBetting.Notify(src, "The other player is too far away! Try again.", 'error')
            return
        end

        if not OlympusBetting.GetPlayerBettingEnabled(src) then
            OlympusBetting.Notify(src, "You currently have betting disabled!", 'error')
            return
        end

        -- Set player state
        playerBetStates[src].inBet = true
        playerBetStates[src].useAtm = false
        playerBetStates[src].actionInUse = true

        -- Send confirmation to target
        TriggerClientEvent('olympus-betting:client:betMoney', player, "confirm", amount, src)

        -- Set cooldown
        playerBetStates[src].betCooldown = true
        CreateThread(function()
            Wait(180000) -- 3 minutes
            if playerBetStates[src] then
                playerBetStates[src].betCooldown = false
                playerBetStates[src].inBet = false
                playerBetStates[src].actionInUse = false
                playerBetStates[src].useAtm = true
            end
        end)
    end
end)

-- Handle bet confirmation from target player
RegisterNetEvent('olympus-betting:server:betConfirm', function(type, val, player)
    local src = source
    OlympusBetting.InitPlayerState(src)

    if type == "confirm" then
        -- Validation checks for target player
        if playerBetStates[src].actionInUse then
            TriggerClientEvent('olympus-betting:client:betMoney', player, "failU")
            return
        end

        local cash = OlympusBetting.GetPlayerMoney(src, "cash")
        local bank = OlympusBetting.GetPlayerMoney(src, "bank")
        if cash < val and bank < val then
            TriggerClientEvent('olympus-betting:client:betMoney', player, "failM")
            return
        end

        if playerBetStates[src].inBet then
            TriggerClientEvent('olympus-betting:client:betMoney', player, "failB")
            return
        end

        if playerBetStates[src].betCooldown then
            TriggerClientEvent('olympus-betting:client:betMoney', player, "failC")
            return
        end

        if not OlympusBetting.GetPlayerBettingEnabled(src) or OlympusBetting.IsPlayerAdmin(src) then
            TriggerClientEvent('olympus-betting:client:betMoney', player, "failE")
            return
        end

        local playtime = OlympusBetting.GetPlayerPlaytime(src)
        if playtime < 1800 then
            TriggerClientEvent('olympus-betting:client:betMoney', player, "failT")
            return
        end

        -- Set target player state
        playerBetStates[src].inBet = true
        playerBetStates[src].useAtm = false
        playerBetStates[src].actionInUse = true

        -- Show confirmation dialog
        TriggerClientEvent('olympus-betting:client:showBetConfirmation', src, val, GetPlayerName(player), player)

    elseif type == "accept" then
        -- Process the bet
        local amount = val
        local winNum = math.random(0, 1) -- 0 or 1 for 50/50 chance

        -- Set cooldown for target
        playerBetStates[src].betCooldown = true
        CreateThread(function()
            Wait(180000) -- 3 minutes
            if playerBetStates[src] then
                playerBetStates[src].betCooldown = false
                playerBetStates[src].inBet = false
                playerBetStates[src].useAtm = true
                playerBetStates[src].actionInUse = false
            end
        end)

        if winNum == 1 then
            -- Target wins
            if not GetPlayerName(player) then
                OlympusBetting.Notify(src, "The person who bet against you left the game... what a loser..", 'info')
                return
            end
            TriggerClientEvent('olympus-betting:client:betMoney', player, "lose", amount, src)
        else
            -- Sender wins
            TriggerClientEvent('olympus-betting:client:betMoney', src, "lose", amount, player)
        end

    elseif type == "decline" then
        TriggerClientEvent('olympus-betting:client:betMoney', player, "no")
        playerBetStates[src].inBet = false
        playerBetStates[src].useAtm = true
        playerBetStates[src].actionInUse = false
    end
end)

-- Handle bet results
RegisterNetEvent('olympus-betting:server:betResult', function(type, amount, targetPlayer)
    local src = source

    if type == "win" then
        local taxedVal = math.floor(amount * 0.99) -- 1% tax like original
        OlympusBetting.AddPlayerMoney(src, "bank", taxedVal)
        OlympusBetting.Notify(src, string.format("Congrats you won the bet worth %s!", OlympusBetting.FormatMoney(amount)), 'success')

        -- Update statistics (placeholder for future implementation)
        -- ["bets_won_value", taxedVal] call OEC_fnc_statArrUp
        -- ["bets_won", 1] call OEC_fnc_statArrUp

        playerBetStates[src].inBet = false
        playerBetStates[src].useAtm = true
        playerBetStates[src].actionInUse = false

    elseif type == "lose" then
        if not GetPlayerName(targetPlayer) then
            OlympusBetting.Notify(src, "The person who bet against you left the game... what a loser..", 'info')
            playerBetStates[src].inBet = false
            playerBetStates[src].useAtm = true
            playerBetStates[src].actionInUse = false
            return
        end

        local cash = OlympusBetting.GetPlayerMoney(src, "cash")
        local bank = OlympusBetting.GetPlayerMoney(src, "bank")
        if cash < amount and bank < amount then
            TriggerClientEvent('olympus-betting:client:betMoney', targetPlayer, "failD", amount, src)

            -- Log exploit attempt
            exports.oxmysql:execute('INSERT INTO betting_logs (player_id, player_name, target_id, target_name, amount, winner_id, winner_name) VALUES (?, ?, ?, ?, ?, ?, ?)', {
                GetPlayerIdentifier(src, 0),
                GetPlayerName(src),
                GetPlayerIdentifier(targetPlayer, 0),
                GetPlayerName(targetPlayer),
                amount,
                'EXPLOIT',
                'Negative Money Exploit'
            })

            playerBetStates[src].inBet = false
            playerBetStates[src].useAtm = true
            playerBetStates[src].actionInUse = false
            return
        end

        -- Remove money from loser
        if cash >= amount then
            OlympusBetting.RemovePlayerMoney(src, "cash", amount)
        else
            OlympusBetting.RemovePlayerMoney(src, "bank", amount)
        end

        OlympusBetting.Notify(src, "You lost the bet. Better luck next time!", 'error')

        -- Update statistics (placeholder for future implementation)
        -- ["bets_lost_value", amount] call OEC_fnc_statArrUp
        -- ["bets_lost", 1] call OEC_fnc_statArrUp

        playerBetStates[src].inBet = false
        playerBetStates[src].useAtm = true
        playerBetStates[src].actionInUse = false

        -- Give money to winner
        TriggerClientEvent('olympus-betting:client:betMoney', targetPlayer, "win", amount, src)

        -- Broadcast result
        local winnerName = GetPlayerName(targetPlayer)
        local loserName = GetPlayerName(src)
        OlympusBetting.BroadcastMessage(string.format("%s won a bet worth %s against %s!", winnerName, OlympusBetting.FormatMoney(amount), loserName))

        -- Log bet result
        exports.oxmysql:execute('INSERT INTO betting_logs (player_id, player_name, target_id, target_name, amount, winner_id, winner_name) VALUES (?, ?, ?, ?, ?, ?, ?)', {
            GetPlayerIdentifier(src, 0),
            loserName,
            GetPlayerIdentifier(targetPlayer, 0),
            winnerName,
            amount,
            GetPlayerIdentifier(targetPlayer, 0),
            winnerName
        })
    end
end)

-- Handle player disconnect cleanup
AddEventHandler('playerDropped', function()
    local src = source
    if playerBetStates[src] then
        playerBetStates[src] = nil
    end
    if playerCooldowns[src] then
        playerCooldowns[src] = nil
    end
end)

-- Toggle betting enabled in Y Menu
RegisterNetEvent('olympus-betting:server:toggleBettingEnabled', function()
    local src = source
    local playerData = OlympusBetting.GetPlayerData(src)
    if not playerData then return end

    local currentSetting = playerData.metadata.bettingEnabled or false
    local newSetting = not currentSetting

    -- Update player metadata
    exports['olympus-core']:SetPlayerMetadata(src, 'bettingEnabled', newSetting)

    TriggerClientEvent('olympus-betting:client:updateBettingSettings', src, newSetting)
    OlympusBetting.Notify(src, string.format("Betting %s", newSetting and "enabled" or "disabled"), 'info')
end)

-- Get nearby players for betting
RegisterNetEvent('olympus-betting:server:getNearbyPlayers', function()
    local src = source
    local srcCoords = GetEntityCoords(GetPlayerPed(src))
    local nearbyPlayers = {}

    for _, playerId in ipairs(GetPlayers()) do
        local targetId = tonumber(playerId)
        if targetId and targetId ~= src then
            local targetCoords = GetEntityCoords(GetPlayerPed(targetId))
            local distance = #(srcCoords - targetCoords)

            if distance <= 20.0 then -- Within 20 meters like original
                local targetData = OlympusBetting.GetPlayerData(targetId)
                if targetData then
                    table.insert(nearbyPlayers, {
                        id = targetId,
                        name = GetPlayerName(targetId),
                        distance = math.floor(distance)
                    })
                end
            end
        end
    end

    TriggerClientEvent('olympus-betting:client:updateNearbyPlayers', src, nearbyPlayers)
end)

-- Export Functions
exports('CanPlayerBet', function(source)
    OlympusBetting.InitPlayerState(source)

    if playerBetStates[source].betCooldown then
        return false, 'You have already bet money recently. Please try again later.'
    end

    if OlympusBetting.IsPlayerAdmin(source) then
        return false, 'Staff can not bet!'
    end

    local faction = OlympusBetting.GetPlayerFaction(source)
    if faction == "ambulance" then
        return false, 'Medics cannot bet!'
    end

    if faction == "police" and OlympusBetting.IsPlayerOnDuty(source) then
        return false, 'Cops cannot bet!'
    end

    if playerBetStates[source].inBet then
        return false, 'You are already in a bet!'
    end

    if not OlympusBetting.GetPlayerBettingEnabled(source) then
        return false, 'You currently have betting disabled!'
    end

    local playtime = OlympusBetting.GetPlayerPlaytime(source)
    if playtime < 1800 then
        return false, 'You must have at least 30 hours on the server to bet!'
    end

    return true
end)

exports('IsPlayerInBet', function(source)
    OlympusBetting.InitPlayerState(source)
    return playerBetStates[source].inBet
end)

exports('GetBetCooldown', function(source)
    OlympusBetting.InitPlayerState(source)
    return playerBetStates[source].betCooldown
end)

exports('GetPlayerBettingHistory', function(source)
    local playerId = GetPlayerIdentifier(source, 0)
    local result = exports.oxmysql:executeSync('SELECT * FROM betting_logs WHERE player_id = ? OR target_id = ? ORDER BY created_at DESC LIMIT 50', {
        playerId, playerId
    })
    return result or {}
end)

print("[Olympus Betting] Server module loaded - Based on original fn_betMoney.sqf")
