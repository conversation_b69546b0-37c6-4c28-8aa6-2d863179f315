-- ========================================
-- OLYMPUS UI FRAMEWORK - CLIENT MAIN
-- Based on Original Olympus Altis Life
-- ========================================

local OlympusUI = {}
local isUIReady = false
local hudVisible = true
local yMenuOpen = false

-- ========================================
-- INITIALIZATION
-- ========================================

CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end
    
    -- Wait for UI to be ready
    while not isUIReady do
        Wait(100)
    end
    
    print("[Olympus UI] Client initialized")
    
    -- Start HUD update loop
    OlympusUI.StartHUDUpdates()
end)

-- ========================================
-- NUI CALLBACKS
-- ========================================

RegisterNUICallback('uiReady', function(data, cb)
    isUIReady = true
    cb('ok')
end)

RegisterNUICallback('setCursor', function(data, cb)
    SetNuiFocus(data.enabled, data.enabled)
    cb('ok')
end)

RegisterNUICallback('yMenuClosed', function(data, cb)
    yMenuOpen = false
    SetNuiFocus(false, false)
    cb('ok')
end)

RegisterNUICallback('yMenuTabChanged', function(data, cb)
    TriggerServerEvent('olympus-ui:server:requestYMenuContent', data.tabId)
    cb('ok')
end)

RegisterNUICallback('requestYMenuContent', function(data, cb)
    TriggerServerEvent('olympus-ui:server:requestYMenuContent', data.tabId)
    cb('ok')
end)

RegisterNUICallback('yMenuAction', function(data, cb)
    TriggerServerEvent('olympus-ui:server:yMenuAction', data.action, data.data)
    cb('ok')
end)

RegisterNUICallback('progressBarComplete', function(data, cb)
    TriggerEvent('olympus-ui:client:progressBarComplete')
    cb('ok')
end)

RegisterNUICallback('progressBarCancelled', function(data, cb)
    TriggerEvent('olympus-ui:client:progressBarCancelled')
    cb('ok')
end)

-- ========================================
-- HUD SYSTEM
-- ========================================

function OlympusUI.StartHUDUpdates()
    CreateThread(function()
        while true do
            if hudVisible and isUIReady then
                local playerData = exports['olympus-core']:GetPlayerData()
                if playerData then
                    local hudData = {
                        health = GetEntityHealth(PlayerPedId()) / 2, -- Convert to percentage
                        food = playerData.hunger or 100,
                        water = playerData.thirst or 100,
                        stamina = GetPlayerStamina(PlayerId()),
                        cash = playerData.cash or 0,
                        bank = playerData.bank or 0,
                        location = OlympusUI.GetCurrentLocation(),
                        time = OlympusUI.GetCurrentTime(),
                        wanted = playerData.wanted or 0
                    }
                    
                    -- Check duty status
                    local dutyInfo = OlympusUI.GetDutyInfo()
                    if dutyInfo then
                        hudData.duty = dutyInfo
                    end
                    
                    OlympusUI.UpdateHUD(hudData)
                end
            end
            
            Wait(1000) -- Update every second
        end
    end)
    
    -- Admin HUD update loop
    CreateThread(function()
        while true do
            if hudVisible and isUIReady then
                local playerData = exports['olympus-core']:GetPlayerData()
                if playerData and playerData.adminLevel and playerData.adminLevel > 0 then
                    local adminData = {
                        isAdmin = true,
                        toggles = {
                            godmode = GetPlayerInvincible(PlayerId()),
                            invisible = GetEntityAlpha(PlayerPedId()) < 255,
                            esp = false, -- Would need to track this
                            stase = false, -- Would need to track this
                            streamer = false, -- Would need to track this
                            fly = false -- Would need to track this
                        }
                    }
                    
                    OlympusUI.UpdateAdminHUD(adminData)
                else
                    OlympusUI.UpdateAdminHUD({ isAdmin = false })
                end
            end
            
            Wait(500) -- Update every 0.5 seconds
        end
    end)
end

function OlympusUI.UpdateHUD(data)
    SendNUIMessage({
        action = 'updateHUD',
        data = data
    })
end

function OlympusUI.UpdateAdminHUD(data)
    SendNUIMessage({
        action = 'updateAdminHUD',
        data = data
    })
end

function OlympusUI.ToggleHUD(visible)
    if visible ~= nil then
        hudVisible = visible
    else
        hudVisible = not hudVisible
    end
    
    SendNUIMessage({
        action = 'toggleHUD',
        visible = hudVisible
    })
end

function OlympusUI.GetCurrentLocation()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    local streetHash, crossingHash = GetStreetNameAtCoord(coords.x, coords.y, coords.z)
    local streetName = GetStreetNameFromHashKey(streetHash)
    
    if crossingHash ~= 0 then
        local crossingName = GetStreetNameFromHashKey(crossingHash)
        return streetName .. " / " .. crossingName
    else
        return streetName ~= "" and streetName or "Unknown"
    end
end

function OlympusUI.GetCurrentTime()
    local hour = GetClockHours()
    local minute = GetClockMinutes()
    return string.format("%02d:%02d", hour, minute)
end

function OlympusUI.GetDutyInfo()
    local playerData = exports['olympus-core']:GetPlayerData()
    if not playerData then return nil end
    
    -- Check APD duty
    if exports['olympus-apd'] and exports['olympus-apd']:IsOnDuty() then
        return {
            onDuty = true,
            faction = "APD"
        }
    end
    
    -- Check R&R duty
    if exports['olympus-medical'] and exports['olympus-medical']:IsOnDuty() then
        return {
            onDuty = true,
            faction = "R&R"
        }
    end
    
    return {
        onDuty = false
    }
end

-- ========================================
-- NOTIFICATION SYSTEM
-- ========================================

function OlympusUI.ShowNotification(data)
    if type(data) == "string" then
        data = { message = data, type = "info" }
    end
    
    data.duration = data.duration or 5000
    
    SendNUIMessage({
        action = 'showNotification',
        data = data
    })
end

-- ========================================
-- PROGRESS BAR SYSTEM
-- ========================================

function OlympusUI.ShowProgressBar(data)
    if type(data) == "string" then
        data = { text = data }
    end
    
    data.duration = data.duration or 5000
    
    SendNUIMessage({
        action = 'showProgressBar',
        data = data
    })
end

function OlympusUI.HideProgressBar()
    SendNUIMessage({
        action = 'hideProgressBar'
    })
end

-- ========================================
-- TITLE TEXT SYSTEM
-- ========================================

function OlympusUI.ShowTitleText(data)
    if type(data) == "string" then
        data = { text = data }
    end
    
    data.duration = data.duration or 5000
    
    SendNUIMessage({
        action = 'showTitleText',
        data = data
    })
end

function OlympusUI.HideTitleText()
    SendNUIMessage({
        action = 'hideTitleText'
    })
end

-- ========================================
-- Y MENU SYSTEM
-- ========================================

function OlympusUI.ShowYMenu(data)
    if yMenuOpen then return end
    
    yMenuOpen = true
    
    SendNUIMessage({
        action = 'showYMenu',
        data = data or {}
    })
    
    SetNuiFocus(true, true)
end

function OlympusUI.HideYMenu()
    if not yMenuOpen then return end
    
    yMenuOpen = false
    
    SendNUIMessage({
        action = 'hideYMenu'
    })
    
    SetNuiFocus(false, false)
end

function OlympusUI.UpdateYMenuContent(data)
    SendNUIMessage({
        action = 'updateYMenuContent',
        data = data
    })
end

-- ========================================
-- EARPLUGS SYSTEM
-- ========================================

function OlympusUI.ToggleEarplugs(visible)
    SendNUIMessage({
        action = 'toggleEarplugs',
        visible = visible
    })
end

-- ========================================
-- EXPORTS
-- ========================================

exports('ShowNotification', OlympusUI.ShowNotification)
exports('ShowProgressBar', OlympusUI.ShowProgressBar)
exports('HideProgressBar', OlympusUI.HideProgressBar)
exports('ShowTitleText', OlympusUI.ShowTitleText)
exports('HideTitleText', OlympusUI.HideTitleText)
exports('UpdateHUD', OlympusUI.UpdateHUD)
exports('ToggleHUD', OlympusUI.ToggleHUD)
exports('ShowYMenu', OlympusUI.ShowYMenu)
exports('HideYMenu', OlympusUI.HideYMenu)
exports('UpdateYMenuContent', OlympusUI.UpdateYMenuContent)
exports('ToggleEarplugs', OlympusUI.ToggleEarplugs)
exports('IsLoaded', function() return isUIReady end)

-- ========================================
-- EVENT HANDLERS
-- ========================================

RegisterNetEvent('olympus-ui:client:showNotification', function(data)
    OlympusUI.ShowNotification(data)
end)

RegisterNetEvent('olympus-ui:client:showProgressBar', function(data)
    OlympusUI.ShowProgressBar(data)
end)

RegisterNetEvent('olympus-ui:client:hideProgressBar', function()
    OlympusUI.HideProgressBar()
end)

RegisterNetEvent('olympus-ui:client:showTitleText', function(data)
    OlympusUI.ShowTitleText(data)
end)

RegisterNetEvent('olympus-ui:client:hideTitleText', function()
    OlympusUI.HideTitleText()
end)

RegisterNetEvent('olympus-ui:client:showYMenu', function(data)
    OlympusUI.ShowYMenu(data)
end)

RegisterNetEvent('olympus-ui:client:hideYMenu', function()
    OlympusUI.HideYMenu()
end)

RegisterNetEvent('olympus-ui:client:updateYMenuContent', function(data)
    OlympusUI.UpdateYMenuContent(data)
end)

RegisterNetEvent('olympus-ui:client:toggleHUD', function(visible)
    OlympusUI.ToggleHUD(visible)
end)

RegisterNetEvent('olympus-ui:client:toggleEarplugs', function(visible)
    OlympusUI.ToggleEarplugs(visible)
end)

-- ========================================
-- TEST COMMANDS (Remove in production)
-- ========================================

RegisterCommand('testui', function(source, args)
    local testType = args[1] or 'notification'

    if testType == 'notification' then
        OlympusUI.ShowNotification({
            message = "Test notification message!",
            type = args[2] or 'info',
            duration = 5000
        })
    elseif testType == 'progress' then
        OlympusUI.ShowProgressBar({
            text = "Testing progress bar...",
            duration = 5000
        })
    elseif testType == 'title' then
        OlympusUI.ShowTitleText({
            text = "Test Title Text",
            duration = 3000
        })
    elseif testType == 'ymenu' then
        OlympusUI.ShowYMenu()
    elseif testType == 'hud' then
        OlympusUI.ToggleHUD()
    end
end, false)

RegisterCommand('testhud', function()
    -- Test HUD with sample data
    OlympusUI.UpdateHUD({
        health = 85,
        food = 70,
        water = 60,
        stamina = 90,
        cash = 25000,
        bank = 150000,
        location = "Los Santos",
        time = "15:30",
        wanted = 0,
        duty = { onDuty = true, faction = "APD" }
    })
end, false)
