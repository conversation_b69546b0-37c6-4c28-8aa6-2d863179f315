<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Olympus Lottery</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: transparent;
            color: #ffffff;
            overflow: hidden;
        }

        .lottery-dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(20, 20, 20, 0.95));
            border: 2px solid rgba(58, 123, 213, 0.8);
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(58, 123, 213, 0.3);
            padding: 20px;
            display: none;
        }

        .lottery-dialog.show {
            display: block;
        }

        .dialog-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid rgba(58, 123, 213, 0.3);
            padding-bottom: 15px;
        }

        .dialog-title {
            font-size: 18px;
            font-weight: bold;
            color: rgba(58, 123, 213, 1);
            margin-bottom: 5px;
        }

        .dialog-subtitle {
            font-size: 14px;
            color: #cccccc;
        }

        .lottery-info {
            margin-bottom: 20px;
            text-align: center;
        }

        .jackpot-amount {
            font-size: 24px;
            font-weight: bold;
            color: #ffdd00;
            margin: 10px 0;
        }

        .lottery-status {
            font-size: 12px;
            color: #aaaaaa;
            margin-bottom: 15px;
        }

        .ticket-input-section {
            margin-bottom: 20px;
        }

        .input-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #ffffff;
        }

        .ticket-input {
            width: 100%;
            padding: 10px;
            font-size: 16px;
            background: rgba(40, 40, 40, 0.8);
            border: 1px solid rgba(58, 123, 213, 0.5);
            border-radius: 4px;
            color: #ffffff;
            text-align: center;
        }

        .ticket-input:focus {
            outline: none;
            border-color: rgba(58, 123, 213, 1);
            box-shadow: 0 0 5px rgba(58, 123, 213, 0.3);
        }

        .ticket-cost {
            text-align: center;
            margin: 10px 0;
            font-size: 12px;
            color: #cccccc;
        }

        .dialog-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .dialog-btn {
            padding: 10px 20px;
            font-size: 14px;
            font-weight: bold;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 80px;
        }

        .btn-primary {
            background: linear-gradient(135deg, rgba(58, 123, 213, 0.8), rgba(58, 180, 226, 0.8));
            color: #ffffff;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, rgba(58, 123, 213, 1), rgba(58, 180, 226, 1));
            transform: translateY(-1px);
        }

        .btn-primary:disabled {
            background: rgba(100, 100, 100, 0.5);
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: rgba(60, 60, 60, 0.8);
            color: #ffffff;
            border: 1px solid rgba(100, 100, 100, 0.5);
        }

        .btn-secondary:hover {
            background: rgba(80, 80, 80, 0.8);
            transform: translateY(-1px);
        }

        .error-message {
            color: #ff4444;
            font-size: 12px;
            text-align: center;
            margin-top: 10px;
            display: none;
        }

        .success-message {
            color: #44ff44;
            font-size: 12px;
            text-align: center;
            margin-top: 10px;
            display: none;
        }

        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div id="lottery-dialog" class="lottery-dialog">
        <div class="dialog-header">
            <div class="dialog-title">Olympus Lottery</div>
            <div class="dialog-subtitle">How many tickets would you like to buy?</div>
        </div>

        <div class="lottery-info">
            <div class="jackpot-amount" id="current-jackpot">$0</div>
            <div class="lottery-status" id="lottery-status">Next Draw: --:--</div>
        </div>

        <div class="ticket-input-section">
            <label class="input-label" for="ticket-amount">Number of Tickets (Max: 10)</label>
            <input type="number" id="ticket-amount" class="ticket-input" min="1" max="10" value="1" placeholder="Enter number of tickets">
            <div class="ticket-cost">
                Ticket Cost: $25,000 each | Total: <span id="total-cost">$25,000</span>
            </div>
        </div>

        <div class="dialog-buttons">
            <button id="buy-tickets-btn" class="dialog-btn btn-primary">BUY</button>
            <button id="cancel-btn" class="dialog-btn btn-secondary">CANCEL</button>
        </div>

        <div id="error-message" class="error-message"></div>
        <div id="success-message" class="success-message"></div>
    </div>

    <script>
        let isLotteryOpen = false;

        // Initialize lottery dialog
        document.addEventListener('DOMContentLoaded', function() {
            const ticketInput = document.getElementById('ticket-amount');
            const totalCostSpan = document.getElementById('total-cost');
            const buyBtn = document.getElementById('buy-tickets-btn');
            const cancelBtn = document.getElementById('cancel-btn');

            // Update total cost when ticket amount changes
            ticketInput.addEventListener('input', function() {
                const amount = parseInt(this.value) || 0;
                const total = amount * 25000;
                totalCostSpan.textContent = '$' + total.toLocaleString();

                buyBtn.disabled = amount < 1 || amount > 10;
            });

            // Buy tickets button
            buyBtn.addEventListener('click', function() {
                const amount = parseInt(ticketInput.value) || 0;
                if (amount >= 1 && amount <= 10) {
                    fetch(`https://${GetParentResourceName()}/buyTickets`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            amount: amount
                        })
                    });
                }
            });

            // Cancel button
            cancelBtn.addEventListener('click', function() {
                closeLottery();
            });

            // ESC key to close
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape' && isLotteryOpen) {
                    closeLottery();
                }
            });
        });

        // NUI Message Handler
        window.addEventListener('message', function(event) {
            const data = event.data;

            switch(data.action) {
                case 'openLottery':
                    openLottery(data.data);
                    break;
                case 'closeLottery':
                    closeLottery();
                    break;
                case 'updateLotteryData':
                    updateLotteryData(data.data);
                    break;
                case 'showError':
                    showMessage(data.message, 'error');
                    break;
                case 'showSuccess':
                    showMessage(data.message, 'success');
                    break;
            }
        });

        function openLottery(data) {
            if (isLotteryOpen) return;

            isLotteryOpen = true;
            const dialog = document.getElementById('lottery-dialog');
            dialog.classList.add('show');

            // Set focus for NUI
            fetch(`https://${GetParentResourceName()}/setNuiFocus`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    hasFocus: true,
                    hasCursor: true
                })
            });

            if (data) {
                updateLotteryData(data);
            }
        }

        function closeLottery() {
            if (!isLotteryOpen) return;

            isLotteryOpen = false;
            const dialog = document.getElementById('lottery-dialog');
            dialog.classList.remove('show');

            // Remove NUI focus
            fetch(`https://${GetParentResourceName()}/setNuiFocus`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    hasFocus: false,
                    hasCursor: false
                })
            });

            // Notify client that lottery was closed
            fetch(`https://${GetParentResourceName()}/closeLottery`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({})
            });
        }

        function updateLotteryData(data) {
            if (data.jackpot !== undefined) {
                document.getElementById('current-jackpot').textContent = '$' + data.jackpot.toLocaleString();
            }

            if (data.nextDraw) {
                document.getElementById('lottery-status').textContent = 'Next Draw: ' + data.nextDraw;
            }

            if (data.cooldown) {
                document.getElementById('lottery-status').textContent = 'Lottery on cooldown';
                document.getElementById('buy-tickets-btn').disabled = true;
            }
        }

        function showMessage(message, type) {
            const errorMsg = document.getElementById('error-message');
            const successMsg = document.getElementById('success-message');

            // Hide both messages first
            errorMsg.style.display = 'none';
            successMsg.style.display = 'none';

            if (type === 'error') {
                errorMsg.textContent = message;
                errorMsg.style.display = 'block';
                setTimeout(() => {
                    errorMsg.style.display = 'none';
                }, 5000);
            } else if (type === 'success') {
                successMsg.textContent = message;
                successMsg.style.display = 'block';
                setTimeout(() => {
                    successMsg.style.display = 'none';
                    closeLottery();
                }, 3000);
            }
        }

        // Utility function for resource name
        function GetParentResourceName() {
            return window.location.hostname;
        }
    </script>
</body>
</html>
