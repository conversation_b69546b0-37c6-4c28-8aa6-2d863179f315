fx_version 'cerulean'
game 'gta5'

name 'Olympus Progression System'
description 'Complete progression system for all factions and civilian skills'
author 'Olympus Development Team'
version '1.0.0'

-- Dependencies
dependencies {
    'olympus-core',
    'olympus-apd',
    'olympus-medical',
    'oxmysql'
}

-- Shared scripts
shared_scripts {
    'config/shared.lua'
}

-- Client scripts
client_scripts {
    'client/main.lua',
    'client/progression_ui.lua',
    'client/skill_tracking.lua',
    'client/unlock_system.lua'
}

-- Server scripts
server_scripts {
    '@oxmysql/lib/MySQL.lua',
    'server/main.lua',
    'server/progression_system.lua',
    'server/apd_progression.lua',
    'server/rnr_progression.lua',
    'server/civilian_progression.lua',
    'server/unlock_validation.lua'
}

-- Exports
exports {
    'GetPlayerRank',
    'GetPlayerSkills',
    'CheckUnlock',
    'GetProgressionInfo'
}

server_exports {
    'PromotePlayer',
    'UpdateSkillXP',
    'GrantUnlock',
    'GetRankRequirements'
}
