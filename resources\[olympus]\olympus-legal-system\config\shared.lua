-- Olympus Legal & Justice System - Complete Implementation
-- Based on Olympus Altis Life legal mechanics (No Courtrooms - Exactly as Olympus Uses)

Config = {}

-- Legal System Settings (Exact Olympus Implementation)
Config.LegalSystem = {
    enabled = true,
    
    -- No Courtroom System (Exact Olympus)
    courtrooms = false, -- Olympus does NOT use courtrooms
    trials = false, -- No trial system
    judges = false, -- No judge system
    
    -- Immediate Processing System (Exact Olympus)
    immediateProcessing = true, -- Tickets/charges processed immediately
    jailOnDenial = true, -- Denial results in immediate jail time
    noDelays = true -- No delay systems or waiting periods
}

-- Lawyer Role System (Exact Olympus Implementation)
Config.LawyerSystem = {
    enabled = true,
    
    -- Lawyer Registration
    registration = {
        enabled = true,
        applicationRequired = false, -- No formal application process
        selfDesignation = true, -- Players can designate themselves as lawyers
        
        -- Lawyer identification
        identification = {
            nameTag = '[LAWYER]', -- Lawyer name tag
            specialAccess = true, -- Access to detained players
            apdInteraction = true -- Can interact with APD during processing
        }
    },
    
    -- Lawyer Mechanics (Exact Olympus)
    mechanics = {
        -- Interaction with APD
        apdNegotiation = {
            enabled = true,
            chargeReduction = true, -- Can negotiate charge reductions
            jailTimeReduction = true, -- Can negotiate reduced jail time
            ticketNegotiation = true, -- Can negotiate ticket amounts
            
            -- Negotiation limits
            maxChargeReduction = 0.50, -- Maximum 50% charge reduction
            maxJailReduction = 0.75, -- Maximum 75% jail time reduction
            maxTicketReduction = 0.60 -- Maximum 60% ticket reduction
        },
        
        -- Client Representation
        clientRepresentation = {
            enabled = true,
            duringProcessing = true, -- Can represent during APD processing
            detainedAccess = true, -- Can access detained players
            communicationRights = true, -- Can communicate with client
            
            -- Representation limits
            oneClientAtTime = true, -- One client at a time
            proximityRequired = true, -- Must be near client/APD
            timeLimit = 900 -- 15 minutes maximum representation time
        },
        
        -- Payment System
        payment = {
            enabled = true,
            clientPays = true, -- Client pays lawyer fees
            
            -- Fee structure
            fees = {
                consultation = 5000, -- $5,000 consultation fee
                representation = 15000, -- $15,000 representation fee
                successBonus = 10000, -- $10,000 bonus for successful negotiation
                
                -- Payment methods
                cash = true, -- Cash payment
                bankTransfer = true -- Bank transfer
            }
        }
    },
    
    -- Lawyer Availability System
    availability = {
        enabled = true,
        
        -- When lawyers are available
        lawyerOnline = {
            apdMustWait = true, -- APD must wait for lawyer if requested
            maxWaitTime = 300, -- 5 minutes maximum wait
            fallbackProcessing = true -- APD can proceed if no lawyer available
        },
        
        -- When no lawyers are available
        noLawyerAvailable = {
            apdCanProceed = true, -- APD can proceed immediately
            reasonableDelay = 120, -- 2 minutes reasonable delay
            notificationSystem = true -- Notify potential lawyers
        }
    }
}

-- Ticket & Charge Flow System (Exact Olympus Implementation)
Config.TicketSystem = {
    enabled = true,
    
    -- Ticket Processing (Exact Olympus)
    processing = {
        immediateDecision = true, -- Player must decide immediately
        acceptOrDeny = true, -- Only two options: accept or deny
        noDelays = false, -- No delay options
        
        -- Accept Ticket
        acceptTicket = {
            payImmediately = false, -- Can pay later
            payAtATM = true, -- Pay at ATM or bank
            paymentPeriod = 3600, -- 1 hour to pay
            latePaymentPenalty = 0.25 -- 25% penalty for late payment
        },
        
        -- Deny Ticket
        denyTicket = {
            immediateJail = true, -- Results in immediate jail time
            noAppeal = true, -- No appeal process
            noCourtroom = true, -- No courtroom proceedings
            
            -- Jail time calculation
            jailTimeFormula = 'ticket_amount / 1000', -- $1000 = 1 minute jail
            minimumJailTime = 60, -- 1 minute minimum
            maximumJailTime = 1800 -- 30 minutes maximum
        }
    },
    
    -- Charge Processing (Exact Olympus)
    charges = {
        immediateProcessing = true, -- Charges processed immediately
        
        -- Charge Categories
        categories = {
            traffic = {
                baseJailTime = 60, -- 1 minute base
                fineMultiplier = 1.0
            },
            misdemeanor = {
                baseJailTime = 300, -- 5 minutes base
                fineMultiplier = 1.5
            },
            felony = {
                baseJailTime = 900, -- 15 minutes base
                fineMultiplier = 2.0
            },
            federal = {
                baseJailTime = 1800, -- 30 minutes base
                fineMultiplier = 3.0
            }
        }
    }
}

-- Pardon System (Exact Olympus Implementation)
Config.PardonSystem = {
    enabled = true,
    
    -- Pardon Application Process
    application = {
        enabled = true,
        
        -- Application Methods
        methods = {
            inGame = true, -- In-game pardon request system
            external = true, -- External website/forum system
            
            -- In-game application
            inGameSystem = {
                menuAccess = true, -- Access through player menu
                formFields = {
                    'player_name',
                    'steam_id',
                    'charges_to_pardon',
                    'reason_for_pardon',
                    'time_since_charges',
                    'behavior_since_charges'
                }
            }
        },
        
        -- Application Requirements
        requirements = {
            minimumTimeSince = 604800, -- 7 days since charges
            noRecentCharges = true, -- No charges in last 48 hours
            validReason = true, -- Must provide valid reason
            
            -- Cooldown system
            cooldownPeriod = 2592000, -- 30 days between applications
            maxApplicationsPerMonth = 1 -- One application per month
        }
    },
    
    -- Pardon Review Process
    review = {
        enabled = true,
        
        -- Review Authority
        reviewers = {
            doj = true, -- DOJ staff can review
            seniorAPD = true, -- Senior APD can review
            admins = true -- Admin staff can review
        },
        
        -- Review Criteria
        criteria = {
            timeElapsed = 0.3, -- 30% weight
            behaviorSince = 0.4, -- 40% weight
            reasonQuality = 0.2, -- 20% weight
            communityStanding = 0.1 -- 10% weight
        },
        
        -- Review Outcomes
        outcomes = {
            approved = 'full_pardon', -- All charges removed
            partialApproval = 'reduced_charges', -- Some charges removed
            denied = 'no_change', -- No changes made
            
            -- Approval rates (for reference)
            approvalRate = 0.25, -- 25% approval rate
            partialRate = 0.35, -- 35% partial approval rate
            denialRate = 0.40 -- 40% denial rate
        }
    },
    
    -- Pardon Effects
    effects = {
        chargeRemoval = true, -- Remove pardoned charges
        bountyReduction = true, -- Reduce bounty accordingly
        recordUpdate = true, -- Update criminal record
        
        -- Notification system
        notifications = {
            applicant = true, -- Notify applicant of decision
            apd = true, -- Notify APD of pardons
            logs = true -- Log all pardon activities
        }
    }
}

-- DOJ/APD Integration System (Exact Olympus Implementation)
Config.DOJIntegration = {
    enabled = true,
    
    -- DOJ Staff Permissions
    dojPermissions = {
        modifyBounties = true, -- Can modify player bounties
        expungeCharges = true, -- Can expunge charges
        viewTicketHistory = true, -- Can view full ticket history
        managePardons = true, -- Can manage pardon requests
        
        -- Advanced permissions
        systemOverride = true, -- Can override system decisions
        emergencyPardon = true, -- Can issue emergency pardons
        massExpungement = true -- Can perform mass expungements
    },
    
    -- APD Integration
    apdIntegration = {
        bountyModification = {
            enabled = true,
            rankRequired = 'sergeant', -- SGT+ can modify bounties
            
            -- Modification types
            modifications = {
                reduce = true, -- Can reduce bounties
                remove = true, -- Can remove bounties
                add = true, -- Can add charges
                modify = true -- Can modify existing charges
            }
        },
        
        -- Record Management
        recordManagement = {
            viewHistory = true, -- Can view full criminal history
            addNotes = true, -- Can add notes to records
            flagPlayers = true, -- Can flag problematic players
            
            -- Access levels by rank
            accessLevels = {
                patrol_officer = 'basic', -- Basic record access
                corporal = 'standard', -- Standard record access
                sergeant = 'advanced', -- Advanced record access
                lieutenant = 'full', -- Full record access
                captain = 'administrative', -- Administrative access
                deputy_chief = 'system', -- System-level access
                chief = 'unrestricted' -- Unrestricted access
            }
        }
    },
    
    -- Administrative Tools
    adminTools = {
        enabled = true,
        
        -- System Management
        systemManagement = {
            viewAllRecords = true, -- View all player records
            modifyAnyRecord = true, -- Modify any player record
            systemMaintenance = true, -- Perform system maintenance
            
            -- Audit capabilities
            auditTrail = true, -- Full audit trail
            actionLogging = true, -- Log all administrative actions
            reportGeneration = true -- Generate system reports
        }
    }
}
