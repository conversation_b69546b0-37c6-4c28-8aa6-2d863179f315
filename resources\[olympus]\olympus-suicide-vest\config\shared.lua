-- Olympus Suicide Vest System Configuration
-- Based on original fn_suicideBomb.sqf from Olympus Altis Life

Config = {}

-- Suicide Vest Configuration
Config.SuicideVest = {
    enabled = true,
    description = "Suicide vest system with radio frequency jammers",
    
    -- Vest Requirements
    vestModel = "V_HarnessOGL_brn", -- Brown tactical vest (exact Olympus requirement)
    
    -- Explosion Settings
    explosionType = "EXPLOSION_GRENADE", -- FiveM explosion type
    explosionDamage = 1000.0, -- High damage to ensure kill
    explosionRadius = 15.0, -- Explosion radius in meters
    
    -- Radio Frequency Jammer Zones (Exact Olympus Implementation)
    jammerZones = {
        {
            name = "Blackwater Armory",
            coords = vector3(3540.0, 3675.0, 28.0), -- Blackwater location
            radius = 70.0, -- 70 meter radius like original
            message = "Blackwater radio frequency jammers have prevented your vest from detonating."
        },
        {
            name = "Federal Reserve",
            coords = vector3(1400.0, 1100.0, 114.0), -- Federal Reserve location
            radius = 70.0, -- 70 meter radius like original
            message = "Federal Reserve radio frequency jammers have prevented your vest from detonating."
        },
        {
            name = "Prison",
            coords = vector3(1679.0, 2513.0, 45.0), -- Prison location
            radius = 70.0, -- 70 meter radius like original
            message = "Prison radio frequency jammers have prevented your vest from detonating.",
            enabled = false -- Commented out in original, can be enabled later
        }
    },
    
    -- Validation Settings
    validation = {
        requireVest = true, -- Must be wearing the specific vest
        preventInVehicle = true, -- Cannot detonate while in vehicle
        checkJammerZones = true, -- Check for radio frequency jammers
        requireAlive = true -- Must be alive to detonate
    },
    
    -- Notification Messages
    messages = {
        noVest = "You must be wearing a suicide vest to detonate it.",
        inVehicle = "You cannot detonate your vest from within a vehicle.",
        notAlive = "You cannot detonate your vest while unconscious.",
        detonationSuccess = "%s has set off their suicide vest.",
        detonationFailed = "Vest detonation failed."
    },
    
    -- Statistics Tracking
    statistics = {
        enabled = true,
        trackDetonations = true,
        trackAttempts = true,
        trackJammerBlocks = true
    },
    
    -- Logging Configuration
    logging = {
        enabled = true,
        logDetonations = true,
        logAttempts = true,
        logJammerBlocks = true,
        logPlayerStats = true
    }
}

-- Animation Settings
Config.Animations = {
    detonation = {
        dict = "weapons@first_person@aim_rng@generic@projectile@sticky_bomb@",
        anim = "plant_floor",
        duration = 2000, -- 2 seconds before explosion
        flag = 49
    }
}

-- Sound Effects
Config.Sounds = {
    detonation = {
        name = "EXPLOSION_MEGA",
        ref = "HUD_MINI_GAME_SOUNDSET"
    },
    jammer = {
        name = "ERROR",
        ref = "HUD_FRONTEND_DEFAULT_SOUNDSET"
    }
}
