fx_version 'cerulean'
game 'gta5'

name 'Olympus Community Goals'
description 'Server-wide community goals and faction objectives system'
author 'Olympus Development Team'
version '1.0.0'

-- Dependencies
dependencies {
    'olympus-core',
    'olympus-ui',
    'oxmysql'
}

-- Shared scripts
shared_scripts {
    'config/shared.lua'
}

-- Client scripts
client_scripts {
    'client/main.lua',
    'client/goals_ui.lua',
    'client/progress_tracking.lua',
    'client/y_menu_integration.lua'
}

-- Server scripts
server_scripts {
    '@oxmysql/lib/MySQL.lua',
    'server/main.lua',
    'server/goals_system.lua',
    'server/contribution_tracking.lua',
    'server/reward_system.lua',
    'server/leaderboard.lua'
}

-- UI files
ui_page 'html/index.html'

files {
    'html/index.html',
    'html/css/*.css',
    'html/js/*.js'
}

-- Exports
exports {
    'GetActiveGoals',
    'GetPlayerContribution',
    'OpenGoalsMenu'
}

server_exports {
    'CreateCommunityGoal',
    'AddContribution',
    'CompleteGoal',
    'GetGoalProgress'
}
