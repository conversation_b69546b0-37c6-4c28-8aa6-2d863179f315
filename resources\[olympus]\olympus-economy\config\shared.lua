-- Olympus Economy System - Shared Configuration
-- Based on Olympus Altis Life economic mechanics

Config = {}

-- Economy Settings
Config.StartingMoney = 5000
Config.StartingBank = 0
Config.MaxMoney = *********
Config.MaxBankBalance = *********
Config.BankInterestRate = 0.001 -- 0.1% per hour
Config.TaxRate = 0.05 -- 5% tax on most transactions

-- Market System Settings (Exact Olympus Implementation)
Config.MarketInfluenceEnabled = true -- Enable cross-item price influence
Config.MarketUpdateInterval = 240 -- 4 minutes base interval
Config.MarketCacheEnabled = true -- Enable market caching system

-- Banking System
Config.Banking = {
    interestInterval = 3600, -- 1 hour in seconds
    maxInterestPerHour = 10000,
    withdrawalFee = 0.01, -- 1% fee for withdrawals
    transferFee = 100, -- Flat fee for transfers
    maxTransferAmount = 1000000,
    minTransferAmount = 1,
    atmLocations = {
        vector3(147.0, -1045.2, 29.4), -- Fleeca Bank
        vector3(-351.0, -51.0, 49.0), -- Fleeca Bank
        vector3(314.0, -280.0, 54.2), -- Fleeca Bank
        vector3(-1213.0, -331.0, 37.8), -- Fleeca Bank
        vector3(-2957.0, 487.0, 15.5), -- Fleeca Bank
        vector3(1175.0, 2708.0, 38.1), -- Fleeca Bank
        vector3(243.2, 225.5, 106.3), -- Pacific Standard
        vector3(298.6, -1448.1, 29.9) -- Hospital ATM
    }
}

-- Job System
Config.Jobs = {
    ['unemployed'] = {
        name = 'Unemployed',
        payment = 0,
        paymentInterval = 0,
        description = 'No job'
    },
    ['taxi'] = {
        name = 'Taxi Driver',
        payment = 50,
        paymentInterval = 300, -- 5 minutes
        description = 'Transport passengers around the city',
        requirements = {
            license = 'drivers'
        }
    },
    ['trucker'] = {
        name = 'Truck Driver',
        payment = 100,
        paymentInterval = 600, -- 10 minutes
        description = 'Deliver goods across the map',
        requirements = {
            license = 'cdl'
        }
    },
    ['miner'] = {
        name = 'Miner',
        payment = 75,
        paymentInterval = 450, -- 7.5 minutes
        description = 'Extract valuable resources from mines',
        requirements = {
            item = 'pickaxe'
        }
    },
    ['fisherman'] = {
        name = 'Fisherman',
        payment = 60,
        paymentInterval = 400, -- 6.7 minutes
        description = 'Catch fish and sell them at market',
        requirements = {
            license = 'boat'
        }
    },
    ['farmer'] = {
        name = 'Farmer',
        payment = 65,
        paymentInterval = 420, -- 7 minutes
        description = 'Grow and harvest crops',
        requirements = {}
    },
    ['lumberjack'] = {
        name = 'Lumberjack',
        payment = 80,
        paymentInterval = 480, -- 8 minutes
        description = 'Cut down trees and process lumber',
        requirements = {
            item = 'chainsaw'
        }
    }
}

-- Market System (Dynamic Pricing)
Config.Markets = {
    updateInterval = 1800, -- 30 minutes
    priceFluctuation = 0.15, -- 15% max price change
    supplyDemandFactor = 0.1, -- How much supply/demand affects price
    
    items = {
        -- Legal Resources
        ['copper_ingot'] = {
            basePrice = 896,
            minPrice = 500,
            maxPrice = 1500,
            volatility = 0.1,
            category = 'legal'
        },
        ['iron_ingot'] = {
            basePrice = 1204,
            minPrice = 700,
            maxPrice = 2000,
            volatility = 0.12,
            category = 'legal'
        },
        ['diamond'] = {
            basePrice = 3500,
            minPrice = 2000,
            maxPrice = 5000,
            volatility = 0.15,
            category = 'legal'
        },
        ['oil'] = {
            basePrice = 2800,
            minPrice = 1500,
            maxPrice = 4000,
            volatility = 0.2,
            category = 'legal'
        },
        
        -- Illegal Drugs
        ['marijuana'] = {
            basePrice = 1382,
            minPrice = 800,
            maxPrice = 2200,
            volatility = 0.25,
            category = 'illegal'
        },
        ['cocaine'] = {
            basePrice = 1778,
            minPrice = 1000,
            maxPrice = 2800,
            volatility = 0.3,
            category = 'illegal'
        },
        ['heroin'] = {
            basePrice = 2100,
            minPrice = 1200,
            maxPrice = 3200,
            volatility = 0.35,
            category = 'illegal'
        },
        ['meth'] = {
            basePrice = 6174,
            minPrice = 3500,
            maxPrice = 9000,
            volatility = 0.4,
            category = 'illegal'
        },
        
        -- Federal Event Items
        ['gold_bar'] = {
            basePrice = 69063,
            minPrice = 40000,
            maxPrice = 100000,
            volatility = 0.2,
            category = 'federal'
        },
        ['money_bag'] = {
            basePrice = 30000,
            minPrice = 20000,
            maxPrice = 45000,
            volatility = 0.15,
            category = 'federal'
        }
    }
}

-- Shop System
Config.Shops = {
    ['general_store'] = {
        name = 'General Store',
        locations = {
            vector3(25.7, -1347.3, 29.5),
            vector3(-3038.9, 585.9, 7.9),
            vector3(-3241.9, 1001.5, 12.8),
            vector3(1728.7, 6414.2, 35.0),
            vector3(1697.9, 4924.4, 42.1),
            vector3(1961.5, 3740.0, 32.3),
            vector3(547.4, 2671.7, 42.2),
            vector3(1135.8, -982.3, 46.4),
            vector3(-1222.9, -906.9, 12.3),
            vector3(-1487.6, -379.1, 40.2),
            vector3(-2968.2, 390.9, 15.0),
            vector3(1166.0, 2708.9, 38.2),
            vector3(374.2, 325.9, 103.6)
        },
        items = {
            {item = 'water', price = 10},
            {item = 'tactical_bacon', price = 75},
            {item = 'redgull', price = 1500},
            {item = 'cupcake', price = 2500},
            {item = 'first_aid_kit', price = 500},
            {item = 'lockpick', price = 150},
            {item = 'ziptie', price = 500},
            {item = 'pickaxe', price = 1200},
            {item = 'fuel_can_full', price = 850},
            {item = 'toolkit', price = 1000}
        }
    },
    
    ['weapon_shop'] = {
        name = 'Weapon Shop',
        locations = {
            vector3(-662.1, -935.3, 21.8),
            vector3(810.2, -2157.3, 29.6),
            vector3(1693.4, 3759.5, 34.7),
            vector3(-330.2, 6083.8, 31.5),
            vector3(252.3, -50.0, 69.9),
            vector3(22.0, -1107.3, 29.8),
            vector3(2567.6, 294.3, 108.7),
            vector3(-1117.5, 2698.6, 18.6),
            vector3(842.4, -1033.4, 28.2)
        },
        items = {
            {item = 'weapon_knife', price = 500},
            {item = 'weapon_bat', price = 750},
            {item = 'weapon_pistol', price = 5000},
            {item = 'weapon_smg', price = 15000},
            {item = 'weapon_assaultrifle', price = 35000},
            {item = 'weapon_sniperrifle', price = 75000}
        },
        requirements = {
            license = 'weapon',
            faction = {'civilian', 'gang'}
        }
    },
    
    ['vehicle_shop'] = {
        name = 'Vehicle Dealership',
        locations = {
            vector3(-56.8, -1096.6, 26.4), -- Premium Deluxe
            vector3(-1255.6, -361.2, 36.9), -- Legendary Motorsport
            vector3(1224.9, 2728.2, 38.0), -- Sandy Shores
            vector3(1208.9, -3115.2, 5.5) -- Airport
        },
        categories = {
            ['economy'] = {
                name = 'Economy Cars',
                vehicles = {
                    {model = 'blista', price = 15000},
                    {model = 'dilettante', price = 18000},
                    {model = 'issi2', price = 12000},
                    {model = 'panto', price = 10000}
                }
            },
            ['sports'] = {
                name = 'Sports Cars',
                vehicles = {
                    {model = 'elegy2', price = 95000},
                    {model = 'jester', price = 240000},
                    {model = 'massacro', price = 275000},
                    {model = 'zentorno', price = 725000}
                }
            },
            ['motorcycles'] = {
                name = 'Motorcycles',
                vehicles = {
                    {model = 'bati', price = 15000},
                    {model = 'akuma', price = 9000},
                    {model = 'ruffian', price = 8500},
                    {model = 'sanchez', price = 7000}
                }
            }
        }
    },
    
    ['black_market'] = {
        name = 'Black Market',
        locations = {
            vector3(1392.3, 1141.9, 114.3), -- Gang territory
            vector3(-1172.4, -1572.1, 4.7), -- Underground
            vector3(2447.9, 1576.9, 33.0) -- Remote location
        },
        items = {
            {item = 'bolt_cutter', price = 2500},
            {item = 'blasting_charge', price = 25000},
            {item = 'hacking_terminal', price = 7000},
            {item = 'gps_tracker', price = 15000},
            {item = 'bloodbag', price = 7500},
            {item = 'epipen', price = 17500},
            {item = 'dopamine_shot', price = 100000}
        },
        requirements = {
            -- Allow access to anyone except APD and R&R
            excludedFactions = {'apd', 'rnr'}
        },
        hidden = true -- Not shown on map
    }
}

-- Auction System
Config.Auctions = {
    enabled = true,
    auctionHouse = vector3(147.0, -1045.2, 29.4),
    maxAuctions = 10, -- Per player
    auctionDuration = 86400, -- 24 hours
    bidIncrement = 1000, -- Minimum bid increase
    auctionFee = 0.05, -- 5% fee on successful sales
    categories = {
        'vehicles',
        'weapons',
        'items',
        'properties',
        'rare_items'
    }
}

-- Tax System
Config.Taxes = {
    enabled = true,
    rates = {
        income = 0.05, -- 5% income tax
        sales = 0.08, -- 8% sales tax
        property = 0.02, -- 2% property tax (weekly)
        vehicle = 0.01, -- 1% vehicle tax (weekly)
        luxury = 0.15 -- 15% luxury tax on expensive items
    },
    exemptions = {
        medical_items = true,
        basic_food = true,
        government_employees = 0.5 -- 50% tax reduction
    },
    collection = {
        automatic = true,
        interval = 604800, -- Weekly
        penalties = {
            late_fee = 0.1, -- 10% late fee
            max_penalty = 0.5 -- 50% max penalty
        }
    }
}

-- Economic Events
Config.EconomicEvents = {
    ['market_crash'] = {
        name = 'Market Crash',
        probability = 0.05, -- 5% chance per day
        duration = 7200, -- 2 hours
        effects = {
            all_prices = -0.3, -- 30% price drop
            bank_interest = -0.5 -- 50% less interest
        }
    },
    ['economic_boom'] = {
        name = 'Economic Boom',
        probability = 0.03, -- 3% chance per day
        duration = 10800, -- 3 hours
        effects = {
            all_prices = 0.2, -- 20% price increase
            job_payments = 0.5, -- 50% more job pay
            bank_interest = 1.0 -- Double interest
        }
    },
    ['drug_shortage'] = {
        name = 'Drug Shortage',
        probability = 0.1, -- 10% chance per day
        duration = 3600, -- 1 hour
        effects = {
            drug_prices = 0.5, -- 50% higher drug prices
            drug_availability = -0.3 -- 30% less availability
        }
    }
}

-- Money Laundering
Config.MoneyLaundering = {
    enabled = true,
    locations = {
        vector3(-1392.3, -588.4, 30.3), -- Casino
        vector3(1392.3, 1141.9, 114.3), -- Gang hideout
        vector3(-1172.4, -1572.1, 4.7) -- Underground
    },
    methods = {
        ['casino'] = {
            name = 'Casino Chips',
            fee = 0.15, -- 15% fee
            minAmount = 10000,
            maxAmount = 500000,
            cooldown = 3600 -- 1 hour
        },
        ['business'] = {
            name = 'Shell Company',
            fee = 0.10, -- 10% fee
            minAmount = 50000,
            maxAmount = 1000000,
            cooldown = 7200 -- 2 hours
        },
        ['offshore'] = {
            name = 'Offshore Account',
            fee = 0.05, -- 5% fee
            minAmount = 100000,
            maxAmount = 5000000,
            cooldown = 86400 -- 24 hours
        }
    }
}

return Config
