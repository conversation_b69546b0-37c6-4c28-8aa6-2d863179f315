-- Olympus Core Framework - Inventory Management
-- Handles player inventory, items, and item usage

-- Add item to player inventory
function AddItem(source, item, amount, metadata)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData then return false end
    
    if not playerData.inventory then
        playerData.inventory = {}
    end
    
    local itemData = Config.Items[item]
    if not itemData then
        print("^1[Olympus Core]^7 Item '" .. item .. "' does not exist")
        return false
    end
    
    amount = amount or 1
    
    -- Check weight limit
    local currentWeight = GetInventoryWeight(source)
    local itemWeight = itemData.weight * amount
    
    if currentWeight + itemWeight > Config.MaxInventoryWeight then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Inventory Full',
            message = 'Not enough space in inventory'
        })
        return false
    end
    
    -- Check slot limit
    local usedSlots = GetUsedSlots(source)
    if usedSlots >= Config.MaxInventorySlots and not playerData.inventory[item] then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Inventory Full',
            message = 'No more inventory slots available'
        })
        return false
    end
    
    -- Add item
    if playerData.inventory[item] then
        playerData.inventory[item].amount = playerData.inventory[item].amount + amount
    else
        playerData.inventory[item] = {
            amount = amount,
            metadata = metadata or {}
        }
    end
    
    exports['olympus-core']:UpdatePlayerData(source, 'inventory', playerData.inventory)
    
    -- Log item addition
    exports['olympus-core']:LogAction(source, 'item_add', {
        item = item,
        amount = amount,
        metadata = metadata
    })
    
    TriggerClientEvent('olympus:client:notify', source, {
        type = 'success',
        title = 'Item Added',
        message = 'Added ' .. amount .. 'x ' .. itemData.label
    })
    
    TriggerClientEvent('olympus:client:inventoryUpdated', source, playerData.inventory)
    
    return true
end

-- Remove item from player inventory
function RemoveItem(source, item, amount)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData or not playerData.inventory or not playerData.inventory[item] then
        return false
    end
    
    amount = amount or 1
    
    if playerData.inventory[item].amount < amount then
        return false
    end
    
    playerData.inventory[item].amount = playerData.inventory[item].amount - amount
    
    if playerData.inventory[item].amount <= 0 then
        playerData.inventory[item] = nil
    end
    
    exports['olympus-core']:UpdatePlayerData(source, 'inventory', playerData.inventory)
    
    -- Log item removal
    exports['olympus-core']:LogAction(source, 'item_remove', {
        item = item,
        amount = amount
    })
    
    local itemData = Config.Items[item]
    TriggerClientEvent('olympus:client:notify', source, {
        type = 'warning',
        title = 'Item Removed',
        message = 'Removed ' .. amount .. 'x ' .. (itemData and itemData.label or item)
    })
    
    TriggerClientEvent('olympus:client:inventoryUpdated', source, playerData.inventory)
    
    return true
end

-- Get item amount in inventory
function GetItemAmount(source, item)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData or not playerData.inventory or not playerData.inventory[item] then
        return 0
    end
    
    return playerData.inventory[item].amount
end

-- Check if player has item
function HasItem(source, item, amount)
    amount = amount or 1
    return GetItemAmount(source, item) >= amount
end

-- Get inventory weight
function GetInventoryWeight(source)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData or not playerData.inventory then
        return 0
    end
    
    local weight = 0
    for item, data in pairs(playerData.inventory) do
        local itemData = Config.Items[item]
        if itemData then
            weight = weight + (itemData.weight * data.amount)
        end
    end
    
    return weight
end

-- Get used inventory slots
function GetUsedSlots(source)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData or not playerData.inventory then
        return 0
    end
    
    local slots = 0
    for _, _ in pairs(playerData.inventory) do
        slots = slots + 1
    end
    
    return slots
end

-- Use item
RegisterServerEvent('olympus:server:useItem')
AddEventHandler('olympus:server:useItem', function(item, amount)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)
    
    if not playerData then return end
    
    local itemData = Config.Items[item]
    if not itemData or not itemData.useable then
        return
    end
    
    amount = amount or 1
    
    if not HasItem(source, item, amount) then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Item Error',
            message = 'You don\'t have enough ' .. itemData.label
        })
        return
    end
    
    -- Check if item has a cooldown
    if itemData.cooldown then
        local lastUsed = playerData.metadata['last_used_' .. item] or 0
        if os.time() - lastUsed < itemData.cooldown then
            local remaining = itemData.cooldown - (os.time() - lastUsed)
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Cooldown',
                message = 'You must wait ' .. remaining .. ' seconds'
            })
            return
        end
    end
    
    -- Remove item if it should be consumed
    if itemData.shouldClose then
        RemoveItem(source, item, amount)
    end
    
    -- Set cooldown
    if itemData.cooldown then
        if not playerData.metadata then
            playerData.metadata = {}
        end
        playerData.metadata['last_used_' .. item] = os.time()
        exports['olympus-core']:UpdatePlayerData(source, 'metadata', playerData.metadata)
    end
    
    -- Apply item effects
    if itemData.effects then
        TriggerClientEvent('olympus:client:applyItemEffects', source, itemData.effects)
    end
    
    -- Log item usage
    exports['olympus-core']:LogAction(source, 'item_use', {
        item = item,
        amount = amount
    })
    
    TriggerClientEvent('olympus:client:notify', source, {
        type = 'info',
        title = 'Item Used',
        message = 'Used ' .. amount .. 'x ' .. itemData.label
    })
    
    -- Trigger item-specific usage
    TriggerEvent('olympus:server:itemUsed', source, item, amount, itemData)
end)

-- Drop item
RegisterServerEvent('olympus:server:dropItem')
AddEventHandler('olympus:server:dropItem', function(item, amount)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)
    
    if not playerData then return end
    
    amount = amount or 1
    
    if not HasItem(source, item, amount) then
        return
    end
    
    -- Remove from inventory
    if RemoveItem(source, item, amount) then
        -- Create dropped item in world
        local coords = GetEntityCoords(GetPlayerPed(source))
        TriggerEvent('olympus:server:createDroppedItem', item, amount, coords, source)
        
        local itemData = Config.Items[item]
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'info',
            title = 'Item Dropped',
            message = 'Dropped ' .. amount .. 'x ' .. (itemData and itemData.label or item)
        })
    end
end)

-- Give item to another player
RegisterServerEvent('olympus:server:giveItem')
AddEventHandler('olympus:server:giveItem', function(targetId, item, amount)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)
    local targetData = exports['olympus-core']:GetPlayerData(targetId)
    
    if not playerData or not targetData then return end
    
    amount = amount or 1
    
    if not HasItem(source, item, amount) then
        return
    end
    
    -- Check distance
    local playerCoords = GetEntityCoords(GetPlayerPed(source))
    local targetCoords = GetEntityCoords(GetPlayerPed(targetId))
    local distance = #(playerCoords - targetCoords)
    
    if distance > Config.InteractionDistance then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Too Far',
            message = 'Player is too far away'
        })
        return
    end
    
    -- Remove from giver and add to receiver
    if RemoveItem(source, item, amount) and AddItem(targetId, item, amount) then
        local itemData = Config.Items[item]
        
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'success',
            title = 'Item Given',
            message = 'Gave ' .. amount .. 'x ' .. (itemData and itemData.label or item) .. ' to ' .. targetData.name
        })
        
        TriggerClientEvent('olympus:client:notify', targetId, {
            type = 'success',
            title = 'Item Received',
            message = 'Received ' .. amount .. 'x ' .. (itemData and itemData.label or item) .. ' from ' .. playerData.name
        })
        
        -- Log transaction
        exports['olympus-core']:LogAction(source, 'item_give', {
            item = item,
            amount = amount,
            target = targetData.name
        })
        
        exports['olympus-core']:LogAction(targetId, 'item_receive', {
            item = item,
            amount = amount,
            sender = playerData.name
        })
    end
end)

-- Load player inventory
RegisterServerEvent('olympus:server:loadInventory')
AddEventHandler('olympus:server:loadInventory', function()
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)
    
    if playerData then
        TriggerClientEvent('olympus:client:inventoryLoaded', source, playerData.inventory or {})
    end
end)

-- Server callbacks
exports['olympus-core']:RegisterServerCallback('olympus:getInventory', function(source, cb)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    cb(playerData and playerData.inventory or {})
end)

exports['olympus-core']:RegisterServerCallback('olympus:hasItem', function(source, cb, item, amount)
    cb(HasItem(source, item, amount))
end)

exports['olympus-core']:RegisterServerCallback('olympus:getItemAmount', function(source, cb, item)
    cb(GetItemAmount(source, item))
end)

exports['olympus-core']:RegisterServerCallback('olympus:getInventoryWeight', function(source, cb)
    cb(GetInventoryWeight(source))
end)

-- Exports
exports('AddItem', AddItem)
exports('RemoveItem', RemoveItem)
exports('GetItemAmount', GetItemAmount)
exports('HasItem', HasItem)
exports('GetInventoryWeight', GetInventoryWeight)
exports('GetUsedSlots', GetUsedSlots)
