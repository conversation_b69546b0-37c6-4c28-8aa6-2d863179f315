-- Olympus Casino System - Server Main

local OlympusCasino = {}
local playerAgeVerification = {}
local selfExcludedPlayers = {}

-- Initialize casino system
CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end
    
    print("[Olympus Casino] Server initialized")
    
    -- Initialize database tables
    InitializeCasinoTables()
    
    -- Load self-excluded players
    LoadSelfExcludedPlayers()
end)

-- Initialize database tables
function InitializeCasinoTables()
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS casino_self_exclusion (
            player_license VARCHAR(50) PRIMARY KEY,
            player_name VARCHAR(50) NOT NULL,
            exclusion_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            reason TEXT DEFAULT NULL
        )
    ]])
    
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS casino_statistics (
            id INT AUTO_INCREMENT PRIMARY KEY,
            player_license VARCHAR(50) NOT NULL,
            game_type ENUM('slots', 'roulette', 'blackjack') NOT NULL,
            bet_amount INT NOT NULL,
            win_amount INT DEFAULT 0,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ]])
end

-- Load self-excluded players
function LoadSelfExcludedPlayers()
    local result = MySQL.query.await('SELECT player_license FROM casino_self_exclusion')
    if result then
        for _, row in pairs(result) do
            selfExcludedPlayers[row.player_license] = true
        end
    end
end

-- Check if player can gamble
function CanPlayerGamble(source)
    local Player = OlympusCore.Functions.GetPlayer(source)
    if not Player then return false end
    
    -- Check self-exclusion
    if selfExcludedPlayers[Player.PlayerData.license] then
        return false, 'You are permanently excluded from gambling activities.'
    end
    
    -- Check age verification (10 hours playtime)
    local playtime = Player.PlayerData.metadata.playtime or 0
    if playtime < Config.CasinoSystem.ageVerification.minimumPlaytime then
        return false, 'You need at least 10 hours of playtime to access casino games.'
    end
    
    return true
end

-- Verify age
RegisterNetEvent('olympus-casino:server:verifyAge', function()
    local src = source
    local Player = OlympusCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local playtime = Player.PlayerData.metadata.playtime or 0
    local verified = playtime >= Config.CasinoSystem.ageVerification.minimumPlaytime
    
    if verified then
        playerAgeVerification[Player.PlayerData.license] = true
    end
    
    TriggerClientEvent('olympus-casino:client:ageVerified', src, verified)
end)

-- Check casino access
RegisterNetEvent('olympus-casino:server:checkAccess', function(gameType)
    local src = source
    local canGamble, reason = CanPlayerGamble(src)
    
    if not canGamble then
        TriggerClientEvent('olympus-casino:client:accessDenied', src, reason)
        return
    end
    
    -- Grant access with game data
    local gameData = GetGameData(gameType)
    TriggerClientEvent('olympus-casino:client:accessGranted', src, gameType, gameData)
end)

-- Get game data
function GetGameData(gameType)
    if gameType == 'slots' then
        return {
            minBet = Config.SlotMachines.betting.minimum,
            maxBet = Config.SlotMachines.betting.maximum,
            payoutMultiplier = Config.SlotMachines.payouts.multiplier
        }
    elseif gameType == 'roulette' then
        return {
            minBet = Config.RouletteSystem.betting.minimum,
            maxBet = Config.RouletteSystem.betting.maximum,
            payouts = Config.RouletteSystem.payouts
        }
    elseif gameType == 'blackjack' then
        return {
            minBet = Config.BlackjackSystem.betting.minimum,
            maxBet = Config.BlackjackSystem.betting.maximum,
            naturalMultiplier = Config.BlackjackSystem.payouts.naturalBlackjack
        }
    end
    
    return {}
end

-- Place bet
RegisterNetEvent('olympus-casino:server:placeBet', function(gameType, betData)
    local src = source
    local Player = OlympusCore.Functions.GetPlayer(src)
    if not Player then return end
    
    local canGamble, reason = CanPlayerGamble(src)
    if not canGamble then
        TriggerClientEvent('olympus-casino:client:accessDenied', src, reason)
        return
    end
    
    local betAmount = betData.amount
    
    -- Validate bet amount
    local gameConfig = GetGameConfig(gameType)
    if betAmount < gameConfig.minBet or betAmount > gameConfig.maxBet then
        TriggerClientEvent('olympus-casino:client:accessDenied', src, 'Invalid bet amount')
        return
    end
    
    -- Check if player has enough money
    if Player.Functions.GetMoney('cash') < betAmount then
        TriggerClientEvent('olympus-casino:client:accessDenied', src, 'Not enough cash')
        return
    end
    
    -- Remove bet amount
    Player.Functions.RemoveMoney('cash', betAmount)
    
    -- Process the game
    local result = ProcessGame(gameType, betData)
    result.betAmount = betAmount
    
    -- Add winnings if won
    if result.won then
        Player.Functions.AddMoney('cash', result.winAmount)
    end
    
    -- Log statistics
    LogCasinoActivity(Player.PlayerData.license, gameType, betAmount, result.won and result.winAmount or 0)
    
    -- Send result to client
    TriggerClientEvent('olympus-casino:client:gamblingResult', src, result)
end)

-- Get game configuration
function GetGameConfig(gameType)
    if gameType == 'slots' then
        return {
            minBet = Config.SlotMachines.betting.minimum,
            maxBet = Config.SlotMachines.betting.maximum
        }
    elseif gameType == 'roulette' then
        return {
            minBet = Config.RouletteSystem.betting.minimum,
            maxBet = Config.RouletteSystem.betting.maximum
        }
    elseif gameType == 'blackjack' then
        return {
            minBet = Config.BlackjackSystem.betting.minimum,
            maxBet = Config.BlackjackSystem.betting.maximum
        }
    end
    
    return {minBet = 0, maxBet = 0}
end

-- Process game logic
function ProcessGame(gameType, betData)
    if gameType == 'slots' then
        return ProcessSlots(betData)
    elseif gameType == 'roulette' then
        return ProcessRoulette(betData)
    elseif gameType == 'blackjack' then
        return ProcessBlackjack(betData)
    end
    
    return {won = false, winAmount = 0}
end

-- Process slots game
function ProcessSlots(betData)
    -- Simple slot machine logic
    local reels = {}
    for i = 1, 3 do
        reels[i] = math.random(1, 10)
    end
    
    local won = false
    local winAmount = 0
    
    -- Check for winning combinations
    if reels[1] == reels[2] and reels[2] == reels[3] then
        -- Three of a kind
        won = true
        winAmount = betData.amount * Config.SlotMachines.payouts.multiplier
    end
    
    return {
        won = won,
        winAmount = winAmount,
        reels = reels
    }
end

-- Process roulette game
function ProcessRoulette(betData)
    local winningNumber = math.random(0, 36)
    local won = false
    local winAmount = 0
    
    -- Check bet type
    if betData.betType == 'number' and betData.number == winningNumber then
        won = true
        winAmount = betData.amount * 35 -- 35:1 payout for single number
    elseif betData.betType == 'red' and IsRedNumber(winningNumber) then
        won = true
        winAmount = betData.amount * 2 -- 2:1 payout for red/black
    elseif betData.betType == 'black' and IsBlackNumber(winningNumber) then
        won = true
        winAmount = betData.amount * 2
    elseif betData.betType == 'green' and winningNumber == 0 then
        won = true
        winAmount = betData.amount * 14 -- 14:1 payout for green (Olympus specific)
    end
    
    return {
        won = won,
        winAmount = winAmount,
        winningNumber = winningNumber
    }
end

-- Process blackjack game
function ProcessBlackjack(betData)
    -- Simplified blackjack logic
    local playerTotal = math.random(16, 21)
    local dealerTotal = math.random(17, 21)
    
    local won = false
    local winAmount = 0
    
    if playerTotal == 21 and dealerTotal ~= 21 then
        -- Natural blackjack
        won = true
        winAmount = betData.amount * Config.BlackjackSystem.payouts.naturalBlackjack
    elseif playerTotal <= 21 and (dealerTotal > 21 or playerTotal > dealerTotal) then
        -- Regular win
        won = true
        winAmount = betData.amount * 2
    end
    
    return {
        won = won,
        winAmount = winAmount,
        playerTotal = playerTotal,
        dealerTotal = dealerTotal
    }
end

-- Helper function for roulette
function IsRedNumber(number)
    local redNumbers = {1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36}
    for _, red in pairs(redNumbers) do
        if number == red then return true end
    end
    return false
end

function IsBlackNumber(number)
    return number > 0 and not IsRedNumber(number)
end

-- Request self-exclusion
RegisterNetEvent('olympus-casino:server:requestSelfExclusion', function()
    local src = source
    local Player = OlympusCore.Functions.GetPlayer(src)
    if not Player then return end
    
    -- Add to database
    exports.oxmysql:execute('INSERT INTO casino_self_exclusion (player_license, player_name) VALUES (?, ?)', {
        Player.PlayerData.license,
        Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname
    })
    
    -- Add to memory
    selfExcludedPlayers[Player.PlayerData.license] = true
    
    TriggerClientEvent('olympus-casino:client:selfExcluded', src)
    
    print(string.format("[Olympus Casino] Player %s has self-excluded from gambling", GetPlayerName(src)))
end)

-- Log casino activity
function LogCasinoActivity(license, gameType, betAmount, winAmount)
    MySQL.insert('INSERT INTO casino_statistics (player_license, game_type, bet_amount, win_amount) VALUES (?, ?, ?, ?)', {
        license, gameType, betAmount, winAmount
    })
end

-- Exports
exports('CanPlayerGamble', CanPlayerGamble)
exports('ProcessGamble', ProcessGame)
exports('SetSelfExclusion', function(license)
    selfExcludedPlayers[license] = true
end)

print("[Olympus Casino] Server module loaded")
