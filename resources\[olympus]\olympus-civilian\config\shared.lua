-- Olympus Civilian System - Shared Configuration
-- Based on Olympus Altis Life civilian mechanics and jobs

Config = {}

-- General Settings
Config.MaxJobDistance = 100.0 -- Maximum distance to interact with job locations
Config.JobPaymentInterval = 300 -- 5 minutes in seconds
Config.LicenseRenewalTime = 2592000 -- 30 days in seconds
Config.MaxHousesPerPlayer = 5
Config.MaxBusinessesPerPlayer = 2

-- Job Categories
Config.JobCategories = {
    ['legal'] = {
        name = 'Legal Jobs',
        description = 'Legitimate employment opportunities',
        color = '#27ae60'
    },
    ['industrial'] = {
        name = 'Industrial Jobs',
        description = 'Resource gathering and processing',
        color = '#f39c12'
    },
    ['service'] = {
        name = 'Service Jobs',
        description = 'Customer service and transportation',
        color = '#3498db'
    },
    ['skilled'] = {
        name = 'Skilled Labor',
        description = 'Specialized professional work',
        color = '#9b59b6'
    }
}

-- Legal Jobs
Config.Jobs = {
    ['unemployed'] = {
        name = 'Unemployed',
        category = 'legal',
        payment = 0,
        paymentInterval = 0,
        description = 'No current employment',
        requirements = {},
        locations = {},
        equipment = {},
        progression = {}
    },
    
    ['taxi'] = {
        name = 'Taxi Driver',
        category = 'service',
        payment = 50,
        paymentInterval = 300,
        description = 'Transport passengers around the city',
        requirements = {
            licenses = {'drivers'},
            level = 0
        },
        locations = {
            {coords = vector3(909.0, -177.4, 74.2), name = 'Downtown Taxi'},
            {coords = vector3(-1037.0, -2737.0, 20.0), name = 'Airport Taxi'},
            {coords = vector3(1855.0, 3678.0, 33.0), name = 'Sandy Shores Taxi'}
        },
        equipment = {
            vehicles = {'taxi'},
            items = {'taxi_meter', 'gps_device'}
        },
        progression = {
            [50] = {bonus = 0.1, unlock = 'premium_taxi'},
            [100] = {bonus = 0.2, unlock = 'taxi_company_access'},
            [200] = {bonus = 0.3, unlock = 'taxi_dispatcher'}
        }
    },
    
    ['trucker'] = {
        name = 'Truck Driver',
        category = 'industrial',
        payment = 100,
        paymentInterval = 600,
        description = 'Deliver goods across the map',
        requirements = {
            licenses = {'cdl'},
            level = 0
        },
        locations = {
            {coords = vector3(1240.0, -3257.0, 6.0), name = 'Port of Los Santos'},
            {coords = vector3(2749.0, 3473.0, 55.7), name = 'Sandy Shores Depot'},
            {coords = vector3(-2179.0, 4289.0, 49.0), name = 'Paleto Bay Warehouse'}
        },
        equipment = {
            vehicles = {'phantom', 'hauler', 'packer'},
            items = {'delivery_manifest', 'cargo_scanner'}
        },
        progression = {
            [25] = {bonus = 0.15, unlock = 'hazmat_delivery'},
            [75] = {bonus = 0.25, unlock = 'oversized_loads'},
            [150] = {bonus = 0.4, unlock = 'international_routes'}
        }
    },
    
    ['miner'] = {
        name = 'Miner',
        category = 'industrial',
        payment = 75,
        paymentInterval = 450,
        description = 'Extract valuable resources from mines',
        requirements = {
            items = {'pickaxe'},
            level = 0
        },
        locations = {
            {coords = vector3(2946.5, 2793.2, 41.0), name = 'Quarry Mine'},
            {coords = vector3(-595.0, 2089.0, 131.4), name = 'Mount Chiliad Mine'},
            {coords = vector3(2832.0, 1449.0, 24.7), name = 'Desert Mine'}
        },
        equipment = {
            items = {'pickaxe', 'mining_helmet', 'safety_vest'},
            vehicles = {'dump', 'tipper'}
        },
        progression = {
            [30] = {bonus = 0.1, unlock = 'rare_minerals'},
            [80] = {bonus = 0.2, unlock = 'deep_mining'},
            [160] = {bonus = 0.35, unlock = 'mining_supervisor'}
        }
    },
    
    ['fisherman'] = {
        name = 'Fisherman',
        category = 'industrial',
        payment = 60,
        paymentInterval = 400,
        description = 'Catch fish and sell them at market',
        requirements = {
            licenses = {'boat'},
            level = 0
        },
        locations = {
            {coords = vector3(-1816.0, -1193.0, 14.3), name = 'Del Perro Pier'},
            {coords = vector3(1337.0, 4269.0, 31.5), name = 'Paleto Bay Dock'},
            {coords = vector3(-3426.0, 967.0, 8.3), name = 'Chumash Pier'}
        },
        equipment = {
            items = {'fishing_rod', 'bait', 'tackle_box'},
            vehicles = {'dinghy', 'seashark', 'marquis'}
        },
        progression = {
            [40] = {bonus = 0.12, unlock = 'deep_sea_fishing'},
            [90] = {bonus = 0.22, unlock = 'commercial_fishing'},
            [180] = {bonus = 0.4, unlock = 'fishing_charter'}
        }
    },
    
    ['farmer'] = {
        name = 'Farmer',
        category = 'industrial',
        payment = 65,
        paymentInterval = 420,
        description = 'Grow and harvest crops',
        requirements = {
            level = 0
        },
        locations = {
            {coords = vector3(2035.0, 4868.0, 41.0), name = 'Grapeseed Farm'},
            {coords = vector3(1698.0, 4924.0, 42.1), name = 'Sandy Shores Farm'},
            {coords = vector3(-1119.0, 2692.0, 18.6), name = 'Paleto Bay Farm'}
        },
        equipment = {
            items = {'seeds', 'fertilizer', 'watering_can'},
            vehicles = {'tractor', 'fieldmaster', 'scrap'}
        },
        progression = {
            [35] = {bonus = 0.1, unlock = 'greenhouse_farming'},
            [85] = {bonus = 0.2, unlock = 'organic_certification'},
            [170] = {bonus = 0.35, unlock = 'agricultural_business'}
        }
    },
    
    ['lumberjack'] = {
        name = 'Lumberjack',
        category = 'industrial',
        payment = 80,
        paymentInterval = 480,
        description = 'Cut down trees and process lumber',
        requirements = {
            items = {'chainsaw'},
            level = 0
        },
        locations = {
            {coords = vector3(-559.0, 5348.0, 70.2), name = 'Paleto Forest'},
            {coords = vector3(-1652.0, 4445.0, 15.8), name = 'Mount Chiliad Forest'},
            {coords = vector3(2549.0, 4664.0, 34.1), name = 'Grapeseed Forest'}
        },
        equipment = {
            items = {'chainsaw', 'safety_helmet', 'work_gloves'},
            vehicles = {'flatbed', 'phantom', 'benson'}
        },
        progression = {
            [45] = {bonus = 0.15, unlock = 'rare_wood_harvesting'},
            [95] = {bonus = 0.25, unlock = 'lumber_mill_access'},
            [190] = {bonus = 0.4, unlock = 'forestry_management'}
        }
    },
    
    ['mechanic'] = {
        name = 'Mechanic',
        category = 'skilled',
        payment = 90,
        paymentInterval = 360,
        description = 'Repair and modify vehicles',
        requirements = {
            items = {'toolkit'},
            level = 10
        },
        locations = {
            {coords = vector3(-356.0, -134.0, 39.0), name = 'LS Customs'},
            {coords = vector3(1174.8, 2640.0, 37.8), name = 'Sandy Shores Garage'},
            {coords = vector3(110.0, 6626.0, 31.9), name = 'Paleto Bay Garage'}
        },
        equipment = {
            items = {'toolkit', 'diagnostic_scanner', 'welding_torch'},
            vehicles = {'towtruck', 'flatbed'}
        },
        progression = {
            [60] = {bonus = 0.2, unlock = 'performance_tuning'},
            [120] = {bonus = 0.3, unlock = 'custom_modifications'},
            [240] = {bonus = 0.5, unlock = 'master_mechanic'}
        }
    },
    
    ['pilot'] = {
        name = 'Pilot',
        category = 'skilled',
        payment = 150,
        paymentInterval = 900,
        description = 'Fly aircraft for transportation and cargo',
        requirements = {
            licenses = {'pilot'},
            level = 25
        },
        locations = {
            {coords = vector3(-1037.0, -2737.0, 20.0), name = 'Los Santos Airport'},
            {coords = vector3(1747.0, 3273.0, 41.1), name = 'Sandy Shores Airfield'},
            {coords = vector3(-1652.0, -3142.0, 13.9), name = 'Devin Weston Hangar'}
        },
        equipment = {
            vehicles = {'luxor', 'shamal', 'velum', 'mammatus'},
            items = {'flight_plan', 'aviation_radio', 'navigation_charts'}
        },
        progression = {
            [100] = {bonus = 0.25, unlock = 'commercial_flights'},
            [200] = {bonus = 0.4, unlock = 'cargo_transport'},
            [400] = {bonus = 0.6, unlock = 'airline_captain'}
        }
    }
}

-- License System
Config.Licenses = {
    ['drivers'] = {
        name = 'Driver\'s License',
        description = 'Allows operation of standard vehicles',
        cost = 500,
        testRequired = true,
        renewalTime = 2592000, -- 30 days
        locations = {
            {coords = vector3(240.0, -1379.0, 33.7), name = 'DMV Los Santos'},
            {coords = vector3(1855.0, 3678.0, 33.0), name = 'DMV Sandy Shores'}
        },
        requirements = {
            age = 16,
            vision_test = true,
            written_test = true,
            driving_test = true
        }
    },
    
    ['weapon'] = {
        name = 'Weapon License',
        description = 'Permits carrying of legal firearms',
        cost = 5000,
        testRequired = true,
        renewalTime = 1296000, -- 15 days
        locations = {
            {coords = vector3(425.1, -979.5, 30.7), name = 'LSPD Headquarters'},
            {coords = vector3(1854.1, 3678.9, 34.3), name = 'Sandy Shores PD'}
        },
        requirements = {
            age = 21,
            background_check = true,
            safety_course = true,
            marksmanship_test = true,
            clean_record = true
        }
    },
    
    ['pilot'] = {
        name = 'Pilot License',
        description = 'Certification to operate aircraft',
        cost = 25000,
        testRequired = true,
        renewalTime = 5184000, -- 60 days
        locations = {
            {coords = vector3(-1037.0, -2737.0, 20.0), name = 'Flight School'}
        },
        requirements = {
            age = 18,
            medical_exam = true,
            written_test = true,
            flight_test = true,
            instrument_rating = true
        }
    },
    
    ['boat'] = {
        name = 'Boat License',
        description = 'Permits operation of watercraft',
        cost = 2500,
        testRequired = true,
        renewalTime = 2592000, -- 30 days
        locations = {
            {coords = vector3(-1816.0, -1193.0, 14.3), name = 'Marina Del Rey'},
            {coords = vector3(1337.0, 4269.0, 31.5), name = 'Paleto Bay Marina'}
        },
        requirements = {
            age = 16,
            swimming_test = true,
            navigation_test = true,
            safety_course = true
        }
    },
    
    ['cdl'] = {
        name = 'Commercial Driver\'s License',
        description = 'Allows operation of commercial vehicles',
        cost = 10000,
        testRequired = true,
        renewalTime = 2592000, -- 30 days
        locations = {
            {coords = vector3(240.0, -1379.0, 33.7), name = 'DMV Los Santos'},
            {coords = vector3(1855.0, 3678.0, 33.0), name = 'DMV Sandy Shores'}
        },
        requirements = {
            age = 21,
            drivers_license = true,
            medical_exam = true,
            written_test = true,
            skills_test = true,
            clean_record = true
        }
    },
    
    ['hunting'] = {
        name = 'Hunting License',
        description = 'Permits hunting of wildlife',
        cost = 1500,
        testRequired = true,
        renewalTime = 2592000, -- 30 days
        locations = {
            {coords = vector3(-679.0, 5834.0, 17.3), name = 'Paleto Bay Ranger Station'},
            {coords = vector3(2549.0, 4664.0, 34.1), name = 'Grapeseed Ranger Station'}
        },
        requirements = {
            age = 16,
            safety_course = true,
            marksmanship_test = true,
            conservation_test = true
        }
    },
    
    ['fishing'] = {
        name = 'Fishing License',
        description = 'Permits recreational and commercial fishing',
        cost = 750,
        testRequired = false,
        renewalTime = 2592000, -- 30 days
        locations = {
            {coords = vector3(-1816.0, -1193.0, 14.3), name = 'Del Perro Pier'},
            {coords = vector3(1337.0, 4269.0, 31.5), name = 'Paleto Bay Dock'}
        },
        requirements = {
            age = 12,
            conservation_course = true
        }
    }
}

-- Civilian Activities
Config.Activities = {
    ['treasure_hunting'] = {
        name = 'Treasure Hunting',
        description = 'Search for buried treasure using metal detectors',
        requirements = {
            items = {'metal_detector'},
            licenses = {},
            level = 5
        },
        locations = {
            {coords = vector3(-1816.0, -1193.0, 14.3), name = 'Beach Areas'},
            {coords = vector3(2832.0, 1449.0, 24.7), name = 'Desert Areas'},
            {coords = vector3(-559.0, 5348.0, 70.2), name = 'Forest Areas'}
        },
        rewards = {
            common = {money = 500, items = {'old_coin', 'scrap_metal'}},
            uncommon = {money = 1500, items = {'jewelry', 'antique'}},
            rare = {money = 5000, items = {'gold_nugget', 'rare_artifact'}}
        },
        cooldown = 1800 -- 30 minutes
    },
    
    ['photography'] = {
        name = 'Photography',
        description = 'Take photos of landmarks and wildlife for money',
        requirements = {
            items = {'camera'},
            licenses = {},
            level = 0
        },
        locations = {
            {coords = vector3(-1816.0, -1193.0, 14.3), name = 'Scenic Locations'},
            {coords = vector3(2832.0, 1449.0, 24.7), name = 'Wildlife Areas'},
            {coords = vector3(-559.0, 5348.0, 70.2), name = 'Landmarks'}
        },
        rewards = {
            landscape = {money = 200, experience = 10},
            wildlife = {money = 350, experience = 15},
            landmark = {money = 500, experience = 20}
        },
        cooldown = 600 -- 10 minutes
    },
    
    ['diving'] = {
        name = 'Scuba Diving',
        description = 'Explore underwater areas for salvage and treasure',
        requirements = {
            items = {'scuba_gear'},
            licenses = {'boat'},
            level = 10
        },
        locations = {
            {coords = vector3(-1816.0, -1193.0, 14.3), name = 'Coastal Waters'},
            {coords = vector3(1337.0, 4269.0, 31.5), name = 'Deep Ocean'},
            {coords = vector3(-3426.0, 967.0, 8.3), name = 'Underwater Caves'}
        },
        rewards = {
            salvage = {money = 800, items = {'scrap_metal', 'electronics'}},
            treasure = {money = 2500, items = {'gold_bar', 'precious_gems'}},
            artifacts = {money = 4000, items = {'ancient_artifact', 'rare_pearl'}}
        },
        cooldown = 2400 -- 40 minutes
    }
}

-- Housing System
Config.Housing = {
    maxHouses = 5,
    rentInterval = 604800, -- 7 days
    evictionTime = 1209600, -- 14 days
    
    types = {
        ['apartment'] = {
            name = 'Apartment',
            basePrice = 50000,
            rentPrice = 2500,
            storage = 100,
            garage = 2
        },
        ['house'] = {
            name = 'House',
            basePrice = 150000,
            rentPrice = 7500,
            storage = 250,
            garage = 4
        },
        ['mansion'] = {
            name = 'Mansion',
            basePrice = 500000,
            rentPrice = 25000,
            storage = 500,
            garage = 10
        }
    }
}

-- Business System
Config.Businesses = {
    maxBusinesses = 2,
    
    types = {
        ['shop'] = {
            name = 'Retail Shop',
            basePrice = 100000,
            dailyUpkeep = 1000,
            maxEmployees = 5,
            income = {min = 2000, max = 8000}
        },
        ['restaurant'] = {
            name = 'Restaurant',
            basePrice = 250000,
            dailyUpkeep = 2500,
            maxEmployees = 10,
            income = {min = 5000, max = 15000}
        },
        ['garage'] = {
            name = 'Auto Repair Shop',
            basePrice = 200000,
            dailyUpkeep = 2000,
            maxEmployees = 8,
            income = {min = 3000, max = 12000}
        }
    }
}
