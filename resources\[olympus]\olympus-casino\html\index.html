<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Olympus Casino</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: transparent;
            color: white;
            margin: 0;
            padding: 0;
        }
        
        .casino-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            border-radius: 10px;
            padding: 20px;
            min-width: 500px;
            display: none;
        }
        
        .casino-container.show {
            display: block;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .btn.danger:hover {
            background: #c82333;
        }
        
        input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .hidden {
            display: none !important;
        }
        
        .game-area {
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div id="casino-container" class="casino-container">
        <div class="header">
            <h2>Olympus Casino</h2>
        </div>
        
        <div id="casino-content" class="game-area">
            <p>Welcome to the casino!</p>
            <button class="btn" onclick="closeCasino()">Close</button>
        </div>
    </div>

    <script>
        window.addEventListener('message', function(event) {
            const data = event.data;
            
            if (data.type === 'openCasino') {
                const container = document.getElementById('casino-container');
                container.classList.add('show');
            } else if (data.type === 'closeCasino') {
                const container = document.getElementById('casino-container');
                container.classList.remove('show');
            }
        });
        
        function closeCasino() {
            fetch(`https://${GetParentResourceName()}/closeCasino`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({})
            });
        }
        
        function GetParentResourceName() {
            return 'olympus-casino';
        }
    </script>
</body>
</html>
