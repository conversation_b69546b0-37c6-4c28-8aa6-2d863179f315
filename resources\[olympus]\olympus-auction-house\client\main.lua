-- Olympus Auction House - Client Main

local OlympusAuction = {}
local auctionHouseOpen = false

CreateThread(function()
    while not exports['olympus-core']:IsPlayerLoaded() do
        Wait(1000)
    end

    print("[Olympus Auction House] Client initialized")

    -- Initialize auction house interactions
    InitializeAuctionHouse()
end)

-- Initialize auction house locations
function InitializeAuctionHouse()
    CreateThread(function()
        -- Wait for olympus-ui to be loaded
        while not exports['olympus-ui'] or not exports['olympus-ui']:IsLoaded() do
            Wait(1000)
        end

        -- Create auction house interactions at configured locations
        if Config.AuctionSystem and Config.AuctionSystem.locations then
            for locationName, locationData in pairs(Config.AuctionSystem.locations) do
                if locationData.npcs then
                    for npcType, npcCoords in pairs(locationData.npcs) do
                        exports['olympus-ui']:AddInteraction({
                            coords = npcCoords,
                            distance = 2.0,
                            text = 'Press [E] to access Auction House (' .. (npcType == 'seller' and 'Sell Items' or 'Buy Items') .. ')',
                            action = function()
                                TriggerServerEvent('olympus-auction-house:server:requestAccess')
                            end
                        })
                    end
                end
            end
        end

        print("^2[Olympus Auction House]^7 Auction house interactions initialized")
    end)
end

-- Handle auction house opening
RegisterNetEvent('olympus-auction-house:client:openAuctionHouse')
AddEventHandler('olympus-auction-house:client:openAuctionHouse', function(auctionData)
    if auctionHouseOpen then return end

    auctionHouseOpen = true
    SetNuiFocus(true, true)

    -- Send auction house data to NUI
    SendNUIMessage({
        type = 'openAuctionHouse',
        data = auctionData
    })
end)

-- Handle auction house closing
function CloseAuctionHouse()
    if not auctionHouseOpen then return end

    auctionHouseOpen = false
    SetNuiFocus(false, false)

    SendNUIMessage({
        type = 'closeAuctionHouse'
    })
end

-- NUI Callbacks
RegisterNUICallback('closeAuctionHouse', function(data, cb)
    CloseAuctionHouse()
    cb('ok')
end)

RegisterNUICallback('createListing', function(data, cb)
    TriggerServerEvent('olympus-auction-house:server:createListing', data)
    cb('ok')
end)

RegisterNUICallback('purchaseItem', function(data, cb)
    TriggerServerEvent('olympus-auction-house:server:purchaseItem', data.auctionID)
    cb('ok')
end)

RegisterNUICallback('placeBid', function(data, cb)
    TriggerServerEvent('olympus-auction-house:server:placeBid', data.auctionID, data.bidAmount)
    cb('ok')
end)

RegisterNUICallback('retractListing', function(data, cb)
    TriggerServerEvent('olympus-auction-house:server:retractListing', data.auctionID)
    cb('ok')
end)

-- Handle auction listings update
RegisterNetEvent('olympus-auction-house:client:updateListings')
AddEventHandler('olympus-auction-house:client:updateListings', function(listings)
    if auctionHouseOpen then
        -- Update UI with new listings
        SendNUIMessage({
            type = 'updateListings',
            data = listings
        })
    end
end)

-- Close auction house on ESC
CreateThread(function()
    while true do
        Wait(0)
        if auctionHouseOpen then
            if IsControlJustPressed(0, 322) then -- ESC key
                CloseAuctionHouse()
            end
        else
            Wait(500)
        end
    end
end)

-- Exports
exports('OpenAuctionHouse', function()
    TriggerServerEvent('olympus-auction-house:server:requestAccess')
end)

exports('GetPlayerListings', function()
    return {}
end)

exports('GetPlayerStorage', function()
    return {}
end)

print("[Olympus Auction House] Client module loaded")
