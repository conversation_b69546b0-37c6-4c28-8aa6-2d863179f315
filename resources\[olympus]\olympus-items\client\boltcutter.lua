-- Olympus Items - Bolt Cutter Client
-- Based on original fn_boltcutter.sqf from Olympus Altis Life

local isBoltcutting = false
local boltcutterTarget = nil
local boltcutterUses = 0

-- Use bolt cutter function
function UseBoltcutter(target)
    if isBoltcutting then
        exports['olympus-items']:ShowNotification("You are already using bolt cutters!", "error")
        return false
    end
    
    local ped = PlayerPedId()
    local building = target or GetBuildingPedIsLookingAt(ped)
    
    if not building or building == 0 then
        exports['olympus-items']:ShowNotification("No valid door found!", "error")
        return false
    end
    
    -- Check distance
    local playerPos = GetEntityCoords(ped)
    local buildingPos = GetEntityCoords(building)
    local distance = #(playerPos - buildingPos)
    
    if distance > 10.0 then
        exports['olympus-items']:ShowNotification("You are too far from the door!", "error")
        return false
    end
    
    -- Check if near restricted areas
    if IsNearRestrictedArea(playerPos) then
        return false
    end
    
    -- Check if it's a valid building
    if not IsValidBoltcutterTarget(building) then
        exports['olympus-items']:ShowNotification("You cannot use bolt cutters on this!", "error")
        return false
    end
    
    -- Find nearest door
    local doorIndex = GetNearestDoor(building, playerPos)
    if doorIndex == 0 then
        exports['olympus-items']:ShowNotification("No door found nearby!", "error")
        return false
    end
    
    -- Check if door is already unlocked
    if IsDoorUnlocked(building, doorIndex) then
        exports['olympus-items']:ShowNotification("This door is already unlocked!", "error")
        return false
    end
    
    -- Check cop requirements for federal buildings
    if IsFederalBuilding(building) then
        local requiredCops = GetRequiredCopsForBuilding(building)
        if not HasEnoughCops(requiredCops) then
            exports['olympus-items']:ShowNotification(string.format("There needs to be %d or more cops online!", requiredCops), "error")
            return false
        end
        
        -- Check federal cooldown
        if IsOnFederalCooldown() then
            exports['olympus-items']:ShowNotification("Federal facilities are under lockdown!", "error")
            return false
        end
        
        -- Check for required civilians nearby
        if not HasRequiredCiviliansNearby(building) then
            exports['olympus-items']:ShowNotification("There needs to be another civilian nearby!", "error")
            return false
        end
    end
    
    -- Start boltcutting process
    return StartBoltcutting(building, doorIndex)
end

-- Check if near restricted areas
function IsNearRestrictedArea(playerPos)
    -- Blackwater Facility
    local blackwaterPos = vector3(1000.0, 1000.0, 100.0) -- Example coordinates
    if #(playerPos - blackwaterPos) < 100.0 then
        exports['olympus-items']:ShowNotification("You cannot use bolt cutters near the Blackwater Facility!", "error")
        return true
    end
    
    -- Prison
    local prisonPos = vector3(1500.0, 1500.0, 100.0) -- Example coordinates
    if #(playerPos - prisonPos) < 150.0 then
        exports['olympus-items']:ShowNotification("You cannot use bolt cutters near the Prison!", "error")
        return true
    end
    
    return false
end

-- Check if building is valid for boltcutting
function IsValidBoltcutterTarget(building)
    if not DoesEntityExist(building) then return false end
    
    -- Check if it's a building/house
    local model = GetEntityModel(building)
    
    -- Gang sheds are not allowed
    if model == GetHashKey("land_i_shed_ind_f") then
        exports['olympus-items']:ShowNotification("You cannot break into a gang shed!", "error")
        return false
    end
    
    -- Must be a house
    if not IsEntityABuilding(building) then
        exports['olympus-items']:ShowNotification("You are not looking at a house door!", "error")
        return false
    end
    
    return true
end

-- Get building player is looking at
function GetBuildingPedIsLookingAt(ped)
    local coords = GetEntityCoords(ped)
    local forward = GetEntityForwardVector(ped)
    local destination = coords + forward * 10.0
    
    local rayHandle = StartExpensiveSynchronousShapeTestLosProbe(
        coords.x, coords.y, coords.z,
        destination.x, destination.y, destination.z,
        1, ped, 0
    )
    
    local _, hit, _, _, entity = GetShapeTestResult(rayHandle)
    
    if hit and IsEntityABuilding(entity) then
        return entity
    end
    
    return nil
end

-- Check if entity is a building
function IsEntityABuilding(entity)
    -- This is a simplified check - in reality you'd check against building models
    return DoesEntityExist(entity) and not IsEntityAVehicle(entity) and not IsPedAPlayer(entity)
end

-- Get nearest door index
function GetNearestDoor(building, playerPos)
    -- This would typically use the building's door configuration
    -- For now, we'll return a default door index
    return 1
end

-- Check if door is unlocked
function IsDoorUnlocked(building, doorIndex)
    -- This would check the door state from the server
    -- For now, we'll assume doors are locked by default
    return false
end

-- Check if building is federal
function IsFederalBuilding(building)
    local model = GetEntityModel(building)
    local federalModels = {
        GetHashKey("land_dome_big_f"), -- Federal Reserve
        GetHashKey("land_research_house_v1_f") -- Research facility
    }
    
    for _, federalModel in ipairs(federalModels) do
        if model == federalModel then
            return true
        end
    end
    
    return false
end

-- Get required cops for building
function GetRequiredCopsForBuilding(building)
    local model = GetEntityModel(building)
    
    if model == GetHashKey("land_dome_big_f") then
        return 5 -- Federal Reserve
    elseif model == GetHashKey("land_research_house_v1_f") then
        return 5 -- Research facility
    end
    
    return 0 -- Regular buildings
end

-- Check if enough cops online
function HasEnoughCops(required)
    -- This would check with the server for online cop count
    -- For now, we'll trigger a server event to check
    local result = false
    TriggerServerEvent('olympus-items:server:checkCopCount', required)
    
    -- Wait for response (this is simplified - normally you'd use callbacks)
    return true -- Placeholder
end

-- Check federal cooldown
function IsOnFederalCooldown()
    -- This would check server-side federal cooldown
    return false -- Placeholder
end

-- Check for required civilians nearby
function HasRequiredCiviliansNearby(building)
    local playerPos = GetEntityCoords(PlayerPedId())
    local nearbyPlayers = GetPlayersInArea(playerPos, 50.0)
    local civilianCount = 0
    
    for _, player in ipairs(nearbyPlayers) do
        local playerData = exports['olympus-core']:GetPlayerDataByServerId(GetPlayerServerId(player))
        if playerData and playerData.faction == 'civilian' then
            civilianCount = civilianCount + 1
        end
    end
    
    return civilianCount >= 2
end

-- Get players in area
function GetPlayersInArea(center, radius)
    local players = {}
    local allPlayers = GetActivePlayers()
    
    for _, player in ipairs(allPlayers) do
        local ped = GetPlayerPed(player)
        local pos = GetEntityCoords(ped)
        
        if #(center - pos) <= radius then
            table.insert(players, player)
        end
    end
    
    return players
end

-- Start boltcutting process
function StartBoltcutting(building, doorIndex)
    isBoltcutting = true
    boltcutterTarget = building
    
    local ped = PlayerPedId()
    
    -- Notify police/dispatch
    if IsFederalBuilding(building) then
        TriggerServerEvent('olympus-items:server:alertFederalBreach', {
            location = GetEntityCoords(ped),
            building = building
        })
    else
        TriggerServerEvent('olympus-items:server:alertHouseBreach', {
            location = GetEntityCoords(ped),
            building = building
        })
    end
    
    -- Play animation
    exports['olympus-items']:PlayAnimation(Config.Animations.boltcutter)
    
    -- Calculate progress rate based on building type
    local progressRate = GetProgressRateForBuilding(building)
    local duration = math.floor(Config.ProgressBar.boltcutter.duration * progressRate)
    
    -- Show progress bar
    local progressConfig = {
        label = "Cutting lock...",
        duration = duration,
        useWhileDead = false,
        canCancel = true
    }
    
    exports['olympus-items']:ShowProgressBar(progressConfig, function()
        -- Progress completed
        CompleteBoltcutting(building, doorIndex)
    end, function()
        -- Progress cancelled
        CancelBoltcutting()
    end)
    
    return true
end

-- Get progress rate for building type
function GetProgressRateForBuilding(building)
    local model = GetEntityModel(building)
    
    if model == GetHashKey("land_dome_big_f") then
        return 3.0 -- Federal Reserve takes longer
    elseif model == GetHashKey("land_research_house_v1_f") then
        return 6.0 -- Research facility takes much longer
    elseif model == GetHashKey("land_cargo_house_v4_f") then
        return 6.0 -- Cargo house takes longer
    end
    
    return 1.0 -- Regular buildings
end

-- Complete boltcutting
function CompleteBoltcutting(building, doorIndex)
    isBoltcutting = false
    boltcutterTarget = nil
    
    -- Clear animation
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    -- Check if still in range
    local playerPos = GetEntityCoords(ped)
    local buildingPos = GetEntityCoords(building)
    local distance = #(playerPos - buildingPos)
    
    if distance > 10.0 then
        exports['olympus-items']:ShowNotification("You moved too far away!", "error")
        return
    end
    
    -- Increment bolt cutter uses
    boltcutterUses = boltcutterUses + 1
    
    -- Check if bolt cutter breaks
    if boltcutterUses >= Config.Items.boltcutter.durability then
        TriggerServerEvent('olympus-items:server:consumeItem', 'boltcutter', true)
        boltcutterUses = 0
        exports['olympus-items']:ShowNotification("Your bolt cutter broke!", "info")
    end
    
    -- Unlock the door
    TriggerServerEvent('olympus-items:server:unlockDoor', {
        building = NetworkGetNetworkIdFromEntity(building),
        doorIndex = doorIndex,
        location = playerPos
    })
    
    exports['olympus-items']:ShowNotification("Successfully cut the lock!", "success")
end

-- Cancel boltcutting
function CancelBoltcutting()
    isBoltcutting = false
    boltcutterTarget = nil
    
    -- Clear animation
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    exports['olympus-items']:ShowNotification("Boltcutting cancelled!", "info")
end

-- Check if player moved too far during boltcutting
CreateThread(function()
    while true do
        if isBoltcutting and boltcutterTarget then
            local ped = PlayerPedId()
            local playerPos = GetEntityCoords(ped)
            local buildingPos = GetEntityCoords(boltcutterTarget)
            local distance = #(playerPos - buildingPos)
            
            if distance > 10.0 then
                CancelBoltcutting()
            end
        end
        
        Wait(500)
    end
end)

-- Export functions
exports('UseBoltcutter', UseBoltcutter)

-- Event handlers
RegisterNetEvent('olympus-items:client:boltcutterInterrupted', function()
    if isBoltcutting then
        CancelBoltcutting()
    end
end)
