-- ========================================
-- OLYMPUS HOUSING SYSTEM - CLIENT MAIN
-- Complete recreation based on original housing client functions
-- <PERSON>les house interactions, blips, and UI management
-- ========================================

local OlympusHousing = {}
OlympusHousing.Houses = {}
OlympusHousing.HouseBlips = {}
OlympusHousing.NearestHouse = nil
OlympusHousing.InHouseRange = false

-- Housing configuration
local HOUSING_CONFIG = {
    interactionDistance = 3.0, -- Distance to interact with house
    blipDistance = 100.0, -- Distance to show house blips
    checkInterval = 1000, -- Check for nearby houses every second
    markerType = 1, -- Marker type for house entrance
    markerSize = {x = 1.0, y = 1.0, z = 1.0},
    markerColor = {r = 0, g = 255, b = 0, a = 100} -- Green marker
}

-- House blip configuration (matches original house markers)
local HOUSE_BLIP_CONFIG = {
    owned = {sprite = 40, color = 2, scale = 0.8}, -- Green house for owned
    available = {sprite = 40, color = 0, scale = 0.8}, -- White house for available
    other = {sprite = 40, color = 1, scale = 0.8} -- Red house for other players
}

-- Initialize housing system
function InitializeHousingSystem()
    print("^2[Olympus Housing]^7 Initializing client housing system...")

    -- Start house proximity checker
    StartHouseProximityChecker()

    -- Start house interaction handler
    StartHouseInteractionHandler()

    -- Request house data from server
    TriggerServerEvent('olympus:server:requestHouseData')

    print("^2[Olympus Housing]^7 Client housing system initialized!")
end

-- Start house proximity checker (matches original house detection)
function StartHouseProximityChecker()
    CreateThread(function()
        while true do
            Wait(HOUSING_CONFIG.checkInterval)

            local playerCoords = GetEntityCoords(PlayerPedId())
            local nearestHouse = nil
            local nearestDistance = math.huge

            -- Find nearest house
            for houseId, house in pairs(OlympusHousing.Houses) do
                local distance = #(playerCoords - house.position)
                if distance < HOUSING_CONFIG.interactionDistance and distance < nearestDistance then
                    nearestDistance = distance
                    nearestHouse = {id = houseId, data = house, distance = distance}
                end
            end

            -- Update nearest house
            if nearestHouse ~= OlympusHousing.NearestHouse then
                if OlympusHousing.NearestHouse then
                    OnLeaveHouseRange(OlympusHousing.NearestHouse)
                end

                if nearestHouse then
                    OnEnterHouseRange(nearestHouse)
                end

                OlympusHousing.NearestHouse = nearestHouse
                OlympusHousing.InHouseRange = nearestHouse ~= nil
            end
        end
    end)
end

-- Handle entering house range
function OnEnterHouseRange(house)
    local playerData = exports['olympus-core']:GetPlayerData()
    if not playerData then return end

    local isOwner = house.data.owner_id == playerData.identifier
    local hasKey = HasHouseKey(house.data, playerData.identifier)

    if isOwner then
        exports['olympus-ui']:ShowNotification({
            type = 'info',
            title = 'Housing',
            message = 'Press [E] to access your house',
            duration = 3000
        })
    elseif hasKey then
        exports['olympus-ui']:ShowNotification({
            type = 'info',
            title = 'Housing',
            message = 'Press [E] to access house (You have a key)',
            duration = 3000
        })
    else
        exports['olympus-ui']:ShowNotification({
            type = 'info',
            title = 'Housing',
            message = string.format('House owned by %s', house.data.owner_name),
            duration = 3000
        })
    end
end

-- Handle leaving house range
function OnLeaveHouseRange(house)
    -- Clear any house-related UI
end

-- Start house interaction handler
function StartHouseInteractionHandler()
    CreateThread(function()
        while true do
            Wait(0)

            if OlympusHousing.InHouseRange and OlympusHousing.NearestHouse then
                local house = OlympusHousing.NearestHouse
                local playerData = exports['olympus-core']:GetPlayerData()

                if playerData then
                    local isOwner = house.data.owner_id == playerData.identifier
                    local hasKey = HasHouseKey(house.data, playerData.identifier)

                    -- Draw interaction marker
                    DrawMarker(
                        HOUSING_CONFIG.markerType,
                        house.data.position.x, house.data.position.y, house.data.position.z - 1.0,
                        0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                        HOUSING_CONFIG.markerSize.x, HOUSING_CONFIG.markerSize.y, HOUSING_CONFIG.markerSize.z,
                        HOUSING_CONFIG.markerColor.r, HOUSING_CONFIG.markerColor.g, HOUSING_CONFIG.markerColor.b, HOUSING_CONFIG.markerColor.a,
                        false, true, 2, false, nil, nil, false
                    )

                    -- Draw 3D text
                    if isOwner or hasKey then
                        DrawText3D(house.data.position.x, house.data.position.y, house.data.position.z + 1.0,
                            "[E] Access House", 255, 255, 255, 255)
                    else
                        DrawText3D(house.data.position.x, house.data.position.y, house.data.position.z + 1.0,
                            string.format("House owned by %s", house.data.owner_name), 255, 255, 255, 255)
                    end

                    -- Handle key press
                    if IsControlJustPressed(0, 38) then -- E key
                        if isOwner or hasKey then
                            OpenHouseMenu(house.id)
                        else
                            exports['olympus-ui']:ShowNotification({
                                type = 'error',
                                title = 'Housing',
                                message = 'You do not have access to this house'
                            })
                        end
                    end
                end
            else
                Wait(500) -- Reduce frequency when not near house
            end
        end
    end)
end

-- Check if player has house key
function HasHouseKey(house, playerIdentifier)
    if not house.key_players then return false end

    for _, keyHolder in ipairs(house.key_players) do
        if keyHolder == playerIdentifier then
            return true
        end
    end
    return false
end

-- Open house menu (matches original house menu functionality)
function OpenHouseMenu(houseId)
    local house = OlympusHousing.Houses[houseId]
    if not house then return end

    local playerData = exports['olympus-core']:GetPlayerData()
    if not playerData then return end

    local isOwner = house.owner_id == playerData.identifier
    local hasKey = HasHouseKey(house, playerData.identifier)

    if not isOwner and not hasKey then
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Housing',
            message = 'You do not have access to this house'
        })
        return
    end

    -- Prepare menu data
    local menuData = {
        title = isOwner and "Your House" or "House Access",
        subtitle = string.format("Owner: %s", house.owner_name),
        options = {}
    }

    -- Add menu options based on access level
    if isOwner then
        table.insert(menuData.options, {
            label = "Manage Storage",
            description = "Access house storage",
            action = "house_storage",
            args = {houseId = houseId, storageType = "virtual"}
        })

        table.insert(menuData.options, {
            label = "Manage Keys",
            description = "Give or remove house keys",
            action = "house_keys",
            args = {houseId = houseId}
        })

        table.insert(menuData.options, {
            label = "Lock/Unlock House",
            description = house.locked and "Unlock house" or "Lock house",
            action = "house_lock",
            args = {houseId = houseId, locked = house.locked}
        })

        table.insert(menuData.options, {
            label = "Sell House",
            description = "Sell this house",
            action = "house_sell",
            args = {houseId = houseId}
        })
    else
        table.insert(menuData.options, {
            label = "Access Storage",
            description = "Access house storage",
            action = "house_storage",
            args = {houseId = houseId, storageType = "virtual"}
        })
    end

    -- Open menu via olympus-ui
    exports['olympus-ui']:OpenMenu('house_menu', menuData)
end

-- Draw 3D text
function DrawText3D(x, y, z, text, r, g, b, a)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())
    local dist = GetDistanceBetweenCoords(px, py, pz, x, y, z, 1)

    local scale = (1 / dist) * 2
    local fov = (1 / GetGameplayCamFov()) * 100
    local scale = scale * fov

    if onScreen then
        SetTextScale(0.0 * scale, 0.55 * scale)
        SetTextFont(0)
        SetTextProportional(1)
        SetTextColour(r, g, b, a)
        SetTextDropshadow(0, 0, 0, 0, 255)
        SetTextEdge(2, 0, 0, 0, 150)
        SetTextDropShadow()
        SetTextOutline()
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x, _y)
    end
end

-- Initialize when loaded
CreateThread(function()
    -- Wait for core to be ready
    while not exports['olympus-core']:GetPlayerData do
        Wait(100)
    end

    Wait(5000) -- Additional delay for core system to load
    InitializeHousingSystem()
end)

-- Network events
RegisterNetEvent('olympus:client:updateHouses')
AddEventHandler('olympus:client:updateHouses', function(houses)
    OlympusHousing.Houses = houses
    UpdateHouseBlips()
end)

RegisterNetEvent('olympus:client:houseOwnership')
AddEventHandler('olympus:client:houseOwnership', function(houseId, ownershipType, playerIdentifier)
    -- ownershipType: 1 = bought, 2 = sold
    if ownershipType == 1 then
        -- House was bought - will be updated via updateHouses event
    elseif ownershipType == 2 then
        -- House was sold - remove from memory
        if OlympusHousing.Houses[houseId] then
            OlympusHousing.Houses[houseId] = nil
            UpdateHouseBlips()
        end
    end
end)

RegisterNetEvent('olympus:client:houseExpired')
AddEventHandler('olympus:client:houseExpired', function(houseId)
    if OlympusHousing.Houses[houseId] then
        OlympusHousing.Houses[houseId] = nil
        UpdateHouseBlips()
    end
end)

RegisterNetEvent('olympus:client:houseLockChanged')
AddEventHandler('olympus:client:houseLockChanged', function(houseId, locked)
    if OlympusHousing.Houses[houseId] then
        OlympusHousing.Houses[houseId].locked = locked
    end
end)

-- Menu action handlers
RegisterNetEvent('olympus:client:handleHouseMenuAction')
AddEventHandler('olympus:client:handleHouseMenuAction', function(action, args)
    if action == "house_storage" then
        TriggerServerEvent('olympus:server:openHouseStorage', args.houseId, args.storageType)
    elseif action == "house_keys" then
        TriggerServerEvent('olympus:server:openHouseKeys', args.houseId)
    elseif action == "house_lock" then
        TriggerServerEvent('olympus:server:toggleHouseLock', args.houseId)
    elseif action == "house_sell" then
        -- Confirm sale
        exports['olympus-ui']:ShowConfirmDialog({
            title = 'Sell House',
            message = 'Are you sure you want to sell this house? You will receive 75% of the original price.',
            confirmText = 'Sell',
            cancelText = 'Cancel',
            onConfirm = function()
                TriggerServerEvent('olympus:server:sellHouse', args.houseId)
            end
        })
    end
end)

-- Update house blips (matches original house marker management)
function UpdateHouseBlips()
    -- Clear existing blips
    for _, blip in pairs(OlympusHousing.HouseBlips) do
        if DoesBlipExist(blip) then
            RemoveBlip(blip)
        end
    end
    OlympusHousing.HouseBlips = {}

    local playerData = exports['olympus-core']:GetPlayerData()
    if not playerData then return end

    -- Create new blips
    for houseId, house in pairs(OlympusHousing.Houses) do
        local blip = AddBlipForCoord(house.position.x, house.position.y, house.position.z)

        -- Set blip properties based on ownership
        local isOwner = house.owner_id == playerData.identifier
        local hasKey = HasHouseKey(house, playerData.identifier)

        if isOwner then
            SetBlipSprite(blip, HOUSE_BLIP_CONFIG.owned.sprite)
            SetBlipColour(blip, HOUSE_BLIP_CONFIG.owned.color)
            SetBlipScale(blip, HOUSE_BLIP_CONFIG.owned.scale)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString("Your House")
            EndTextCommandSetBlipName(blip)
        elseif hasKey then
            SetBlipSprite(blip, HOUSE_BLIP_CONFIG.owned.sprite)
            SetBlipColour(blip, HOUSE_BLIP_CONFIG.owned.color)
            SetBlipScale(blip, HOUSE_BLIP_CONFIG.owned.scale)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(string.format("House (%s)", house.owner_name))
            EndTextCommandSetBlipName(blip)
        else
            SetBlipSprite(blip, HOUSE_BLIP_CONFIG.other.sprite)
            SetBlipColour(blip, HOUSE_BLIP_CONFIG.other.color)
            SetBlipScale(blip, HOUSE_BLIP_CONFIG.other.scale)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(string.format("House (%s)", house.owner_name))
            EndTextCommandSetBlipName(blip)
        end

        SetBlipAsShortRange(blip, true)
        OlympusHousing.HouseBlips[houseId] = blip
    end
end

-- Commands
RegisterCommand('buyhouse', function(source, args)
    local houseType = args[1] or 'small_house'
    local playerCoords = GetEntityCoords(PlayerPedId())

    -- Check if valid house type
    local validTypes = {'small_house', 'medium_house', 'large_house', 'mansion'}
    local isValid = false
    for _, validType in ipairs(validTypes) do
        if houseType == validType then
            isValid = true
            break
        end
    end

    if not isValid then
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Housing',
            message = 'Invalid house type. Use: small_house, medium_house, large_house, mansion'
        })
        return
    end

    TriggerServerEvent('olympus:server:buyHouse', playerCoords, houseType)
end, false)

-- Exports (matches original export structure)
exports('GetPlayerHouses', function()
    local playerData = exports['olympus-core']:GetPlayerData()
    if not playerData then return {} end

    local playerHouses = {}
    for houseId, house in pairs(OlympusHousing.Houses) do
        if house.owner_id == playerData.identifier then
            playerHouses[houseId] = house
        end
    end
    return playerHouses
end)

exports('IsPlayerNearHouse', function()
    return OlympusHousing.InHouseRange
end)

exports('GetNearestHouse', function()
    return OlympusHousing.NearestHouse
end)

exports('OpenHouseMenu', function(houseId)
    if houseId then
        OpenHouseMenu(houseId)
    elseif OlympusHousing.NearestHouse then
        OpenHouseMenu(OlympusHousing.NearestHouse.id)
    end
end)

exports('GetHouseData', function(houseId)
    return OlympusHousing.Houses[houseId]
end)

print("^2[Olympus Housing]^7 Client main loaded!")
