-- Olympus Shop Robbery System - Server Main
-- Based on original fn_robShops.sqf

local OlympusShopRobbery = {}
local activeRobberies = {}
local shopCooldowns = {}
local playerATMCooldowns = {}

-- Initialize system
CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end
    
    print("[Olympus Shop Robbery] Server initialized")
    
    -- Initialize database tables
    OlympusShopRobbery.InitializeDatabase()
    
    -- Start cleanup thread
    CreateThread(OlympusShopRobbery.CleanupThread)
end)

-- Database initialization
function OlympusShopRobbery.InitializeDatabase()
    local success = pcall(function()
        -- Create shop_robbery_logs table
        exports.oxmysql:execute([[
            CREATE TABLE IF NOT EXISTS shop_robbery_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                player_id VARCHAR(50) NOT NULL,
                player_name VARCHAR(100) NOT NULL,
                shop_name VARCHAR(255) NOT NULL,
                shop_coords VARCHAR(100) NOT NULL,
                payout_amount INT NOT NULL,
                cops_online INT DEFAULT 0,
                robbery_duration INT DEFAULT 0,
                success TINYINT(1) DEFAULT 1,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_player_id (player_id),
                INDEX idx_shop_name (shop_name),
                INDEX idx_timestamp (timestamp)
            )
        ]])
        
        print("[Olympus Shop Robbery] Database tables initialized")
    end)
    
    if not success then
        print("[Olympus Shop Robbery] ERROR: Failed to initialize database tables")
    end
end

-- Utility Functions
function OlympusShopRobbery.GetPlayerData(source)
    local success, result = pcall(function()
        return exports['olympus-core']:GetPlayerData(source)
    end)
    return success and result or nil
end

function OlympusShopRobbery.GetPlayerMoney(source, type)
    local Player = OlympusShopRobbery.GetPlayerData(source)
    if not Player then return 0 end
    
    if type == "cash" then
        return Player.money and Player.money.cash or 0
    elseif type == "bank" then
        return Player.money and Player.money.bank or 0
    end
    return 0
end

function OlympusShopRobbery.AddPlayerMoney(source, type, amount)
    local success = pcall(function()
        exports['olympus-core']:AddMoney(source, type, amount)
    end)
    return success
end

function OlympusShopRobbery.GetPlayerJob(source)
    local Player = OlympusShopRobbery.GetPlayerData(source)
    if not Player then return nil end
    
    return Player.job and Player.job.name or "unemployed"
end

function OlympusShopRobbery.IsPlayerOnDuty(source)
    local Player = OlympusShopRobbery.GetPlayerData(source)
    if not Player then return false end
    
    return Player.job and Player.job.onduty or false
end

function OlympusShopRobbery.GetOnlineCops()
    local cops = 0
    local players = GetPlayers()
    
    for _, playerId in pairs(players) do
        local Player = OlympusShopRobbery.GetPlayerData(tonumber(playerId))
        if Player and Player.job and Player.job.name == "police" and Player.job.onduty then
            cops = cops + 1
        end
    end
    
    return cops
end

function OlympusShopRobbery.GetPlayerWeapon(source)
    local playerPed = GetPlayerPed(source)
    return GetSelectedPedWeapon(playerPed)
end

function OlympusShopRobbery.IsValidWeapon(weaponHash)
    for _, weapon in pairs(Config.ShopRobbery.settings.requiredWeapons) do
        if GetHashKey(weapon) == weaponHash then
            return true
        end
    end
    return false
end

function OlympusShopRobbery.IsFakeWeapon(weaponHash)
    for _, weapon in pairs(Config.ShopRobbery.settings.fakeWeapons) do
        if GetHashKey(weapon) == weaponHash then
            return true
        end
    end
    return false
end

function OlympusShopRobbery.GetShopByCoords(coords)
    for i, shop in pairs(Config.ShopRobbery.shops) do
        local distance = #(coords - shop.coords)
        if distance <= 5.0 then
            return i, shop
        end
    end
    return nil, nil
end

function OlympusShopRobbery.CalculatePayout(copsOnline)
    local payout = Config.ShopRobbery.payouts.basePayout
    
    if copsOnline >= Config.ShopRobbery.payouts.minCopsForMax then
        payout = Config.ShopRobbery.payouts.maxPayout
    else
        payout = payout + (copsOnline * Config.ShopRobbery.payouts.copMultiplier)
    end
    
    return math.min(payout, Config.ShopRobbery.payouts.maxPayout)
end

function OlympusShopRobbery.LogRobbery(playerId, playerName, shopName, shopCoords, payoutAmount, copsOnline, duration, success)
    exports.oxmysql:execute('INSERT INTO shop_robbery_logs (player_id, player_name, shop_name, shop_coords, payout_amount, cops_online, robbery_duration, success) VALUES (?, ?, ?, ?, ?, ?, ?, ?)', {
        playerId, playerName, shopName, json.encode(shopCoords), payoutAmount, copsOnline, duration, success and 1 or 0
    })
end

-- Shop Robbery System (based on fn_robShops.sqf)
function OlympusShopRobbery.StartRobbery(source, shopIndex)
    local Player = OlympusShopRobbery.GetPlayerData(source)
    if not Player then return false end
    
    local shop = Config.ShopRobbery.shops[shopIndex]
    if not shop then return false end
    
    local playerPed = GetPlayerPed(source)
    local coords = GetEntityCoords(playerPed)
    
    -- Validation checks (matching original fn_robShops.sqf)
    
    -- Check if shop was robbed recently
    local shopKey = shopIndex
    local lastRobbed = shopCooldowns[shopKey] or 0
    if (os.time() - lastRobbed) < Config.ShopRobbery.settings.cooldownTime then
        TriggerClientEvent('olympus-shop-robbery:client:notify', source, 'This store was robbed recently and has no money left.', 'error')
        return false
    end
    
    -- Check if player is in vehicle
    local vehicle = GetVehiclePedIsIn(playerPed, false)
    if vehicle ~= 0 then
        TriggerClientEvent('olympus-shop-robbery:client:notify', source, 'You need to exit your vehicle!', 'error')
        return false
    end
    
    -- Check if player is restrained
    if Player.metadata and Player.metadata.ishandcuffed then
        TriggerClientEvent('olympus-shop-robbery:client:notify', source, 'You cannot rob while restrained!', 'error')
        return false
    end
    
    -- Check if player is alive
    if GetEntityHealth(playerPed) <= 0 then
        TriggerClientEvent('olympus-shop-robbery:client:notify', source, "Can't rob a gas station while dead.", 'error')
        return false
    end
    
    -- Check weapon requirement
    local currentWeapon = OlympusShopRobbery.GetPlayerWeapon(source)
    if currentWeapon == GetHashKey("weapon_unarmed") or OlympusShopRobbery.IsFakeWeapon(currentWeapon) then
        TriggerClientEvent('olympus-shop-robbery:client:notify', source, 'You need a gun to intimidate me!', 'error')
        return false
    end
    
    if not OlympusShopRobbery.IsValidWeapon(currentWeapon) then
        TriggerClientEvent('olympus-shop-robbery:client:notify', source, 'You need a proper weapon to rob this store!', 'error')
        return false
    end
    
    -- Check faction restrictions
    local playerJob = OlympusShopRobbery.GetPlayerJob(source)
    if playerJob == "police" and OlympusShopRobbery.IsPlayerOnDuty(source) then
        TriggerClientEvent('olympus-shop-robbery:client:notify', source, 'You cannot rob a gas station as a cop!', 'error')
        return false
    end
    
    -- Check if robbery is already in progress
    if activeRobberies[shopKey] then
        TriggerClientEvent('olympus-shop-robbery:client:notify', source, 'A robbery is already taking place!', 'error')
        return false
    end
    
    -- Start robbery process
    TriggerClientEvent('olympus-shop-robbery:client:notify', source, 'Intimidating clerk...', 'primary')
    
    -- Random delay (matching original)
    Wait(math.random(1000, 4000))
    
    -- Set shop as being robbed
    shopCooldowns[shopKey] = os.time()
    
    local copsOnline = OlympusShopRobbery.GetOnlineCops()
    local payout = OlympusShopRobbery.CalculatePayout(copsOnline)
    
    -- Create robbery data
    activeRobberies[shopKey] = {
        playerId = source,
        playerName = GetPlayerName(source),
        shopIndex = shopIndex,
        shopName = shop.name,
        startTime = os.time(),
        payout = payout,
        copsOnline = copsOnline,
        completed = false
    }
    
    -- Create map marker (matching original)
    local markerName = "storeRobbery_" .. shopIndex
    TriggerClientEvent('olympus-shop-robbery:client:createRobberyMarker', -1, markerName, shop.coords, shop.name)
    
    -- Notify police
    local nearestCity = "Unknown Location" -- Could be enhanced with actual city detection
    TriggerEvent('olympus-apd:server:sendDispatch', {
        code = "10-90",
        message = string.format("%s is robbing a gas station near %s!", GetPlayerName(source), nearestCity),
        coords = coords,
        priority = "high"
    })
    
    -- Send robbery warning to player
    TriggerClientEvent('olympus-shop-robbery:client:notify', source, 'Keep in mind that a gas station robbery is NOT a KOS area! You must still engage!', 'warning')
    
    -- Start robbery progress
    TriggerClientEvent('olympus-shop-robbery:client:startRobberyProgress', source, shopIndex, Config.ShopRobbery.settings.robberyDuration)
    
    -- Set server-side cleanup timer
    SetTimeout((Config.ShopRobbery.settings.robberyDuration + 50) * 1000, function()
        OlympusShopRobbery.CleanupRobbery(shopKey, false)
    end)
    
    return true
end

function OlympusShopRobbery.CompleteRobbery(source, shopIndex, success)
    local shopKey = shopIndex
    local robbery = activeRobberies[shopKey]

    if not robbery or robbery.playerId ~= source then
        return false
    end

    if robbery.completed then
        return false
    end

    robbery.completed = true
    local duration = os.time() - robbery.startTime

    if success then
        -- Give payout to player
        OlympusShopRobbery.AddPlayerMoney(source, "cash", robbery.payout)

        -- Set ATM cooldown for player
        playerATMCooldowns[source] = os.time() + Config.ShopRobbery.settings.atmCooldown

        TriggerClientEvent('olympus-shop-robbery:client:notify', source,
            string.format('Robbery completed! You received $%s', robbery.payout), 'success')

        -- Log successful robbery
        OlympusShopRobbery.LogRobbery(
            GetPlayerIdentifier(source, 0),
            robbery.playerName,
            robbery.shopName,
            Config.ShopRobbery.shops[shopIndex].coords,
            robbery.payout,
            robbery.copsOnline,
            duration,
            true
        )
    else
        TriggerClientEvent('olympus-shop-robbery:client:notify', source, 'Robbery failed!', 'error')

        -- Log failed robbery
        OlympusShopRobbery.LogRobbery(
            GetPlayerIdentifier(source, 0),
            robbery.playerName,
            robbery.shopName,
            Config.ShopRobbery.shops[shopIndex].coords,
            0,
            robbery.copsOnline,
            duration,
            false
        )
    end

    -- Cleanup robbery
    OlympusShopRobbery.CleanupRobbery(shopKey, success)

    return true
end

function OlympusShopRobbery.CleanupRobbery(shopKey, success)
    local robbery = activeRobberies[shopKey]
    if not robbery then return end

    -- Remove robbery marker
    local markerName = "storeRobbery_" .. robbery.shopIndex
    TriggerClientEvent('olympus-shop-robbery:client:removeRobberyMarker', -1, markerName)

    -- Clear active robbery
    activeRobberies[shopKey] = nil

    if Config.Debug then
        print(string.format("[Olympus Shop Robbery] Cleaned up robbery at shop %d (success: %s)",
            robbery.shopIndex, tostring(success)))
    end
end

function OlympusShopRobbery.CancelRobbery(source, shopIndex, reason)
    local shopKey = shopIndex
    local robbery = activeRobberies[shopKey]

    if not robbery or robbery.playerId ~= source then
        return false
    end

    TriggerClientEvent('olympus-shop-robbery:client:notify', source, reason or 'Robbery cancelled!', 'error')

    -- Log cancelled robbery
    local duration = os.time() - robbery.startTime
    OlympusShopRobbery.LogRobbery(
        GetPlayerIdentifier(source, 0),
        robbery.playerName,
        robbery.shopName,
        Config.ShopRobbery.shops[shopIndex].coords,
        0,
        robbery.copsOnline,
        duration,
        false
    )

    OlympusShopRobbery.CleanupRobbery(shopKey, false)

    return true
end

function OlympusShopRobbery.CheckRobberyDistance(source, shopIndex)
    local robbery = activeRobberies[shopIndex]
    if not robbery or robbery.playerId ~= source then
        return false
    end

    local playerPed = GetPlayerPed(source)
    local coords = GetEntityCoords(playerPed)
    local shop = Config.ShopRobbery.shops[shopIndex]

    local distance = #(coords - shop.coords)

    if distance > Config.ShopRobbery.settings.maxDistance then
        OlympusShopRobbery.CancelRobbery(source, shopIndex, 'You moved too far from the store!')
        return false
    end

    return true
end

-- Cleanup thread
function OlympusShopRobbery.CleanupThread()
    while true do
        Wait(30000) -- Check every 30 seconds

        local currentTime = os.time()

        -- Clean up expired ATM cooldowns
        for playerId, cooldownEnd in pairs(playerATMCooldowns) do
            if currentTime >= cooldownEnd then
                playerATMCooldowns[playerId] = nil
            end
        end

        -- Check for stuck robberies
        for shopKey, robbery in pairs(activeRobberies) do
            if currentTime - robbery.startTime > (Config.ShopRobbery.settings.robberyDuration + 60) then
                print(string.format("[Olympus Shop Robbery] Cleaning up stuck robbery at shop %d", robbery.shopIndex))
                OlympusShopRobbery.CleanupRobbery(shopKey, false)
            end
        end
    end
end

-- Event Handlers
RegisterNetEvent('olympus-shop-robbery:server:startRobbery', function(shopIndex)
    local source = source
    OlympusShopRobbery.StartRobbery(source, shopIndex)
end)

RegisterNetEvent('olympus-shop-robbery:server:completeRobbery', function(shopIndex, success)
    local source = source
    OlympusShopRobbery.CompleteRobbery(source, shopIndex, success)
end)

RegisterNetEvent('olympus-shop-robbery:server:cancelRobbery', function(shopIndex, reason)
    local source = source
    OlympusShopRobbery.CancelRobbery(source, shopIndex, reason)
end)

RegisterNetEvent('olympus-shop-robbery:server:checkDistance', function(shopIndex)
    local source = source
    OlympusShopRobbery.CheckRobberyDistance(source, shopIndex)
end)

-- Player disconnect cleanup
AddEventHandler('playerDropped', function(reason)
    local source = source

    -- Clean up any active robberies by this player
    for shopKey, robbery in pairs(activeRobberies) do
        if robbery.playerId == source then
            OlympusShopRobbery.CleanupRobbery(shopKey, false)
            break
        end
    end

    -- Clean up ATM cooldown
    playerATMCooldowns[source] = nil
end)

-- Export Functions
exports('ProcessRobbery', function(playerId, shopIndex)
    return OlympusShopRobbery.StartRobbery(playerId, shopIndex)
end)

exports('GetShopRobberyData', function(shopIndex)
    return activeRobberies[shopIndex]
end)

exports('ResetShopRobbery', function(shopIndex)
    local shopKey = shopIndex
    if activeRobberies[shopKey] then
        OlympusShopRobbery.CleanupRobbery(shopKey, false)
    end
    shopCooldowns[shopKey] = nil
    return true
end)

print("[Olympus Shop Robbery] Server module loaded")
