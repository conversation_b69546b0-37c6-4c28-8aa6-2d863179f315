-- Olympus Items - GPS Tracker Client
-- Based on original fn_gpsTracker.sqf from Olympus Altis Life

local isInstallingTracker = false
local trackedVehicles = {}

-- Use GPS Tracker function
function UseGPSTracker(target)
    if isInstallingTracker then
        exports['olympus-items']:ShowNotification("You are already installing a tracker!", "error")
        return false
    end
    
    local ped = PlayerPedId()
    local vehicle = target or GetVehiclePedIsLookingAt(ped)
    
    if not vehicle or vehicle == 0 then
        exports['olympus-items']:ShowNotification("No vehicle found!", "error")
        return false
    end
    
    -- Check if player is in the vehicle
    if IsPedInVehicle(ped, vehicle, false) then
        exports['olympus-items']:ShowNotification("You cannot install a tracker while inside the vehicle!", "error")
        return false
    end
    
    -- Check distance
    local playerPos = GetEntityCoords(ped)
    local vehiclePos = GetEntityCoords(vehicle)
    local distance = #(playerPos - vehiclePos)
    
    if distance > 5.0 then
        exports['olympus-items']:ShowNotification("You are too far from the vehicle!", "error")
        return false
    end
    
    -- Check if vehicle already has a tracker
    if HasGP<PERSON><PERSON>(vehicle) then
        exports['olympus-items']:ShowNotification("This vehicle already has a GPS tracker!", "error")
        return false
    end
    
    -- Start tracker installation
    return StartTrackerInstallation(vehicle)
end

-- Get vehicle player is looking at
function GetVehiclePedIsLookingAt(ped)
    local coords = GetEntityCoords(ped)
    local forward = GetEntityForwardVector(ped)
    local destination = coords + forward * 5.0
    
    local rayHandle = StartExpensiveSynchronousShapeTestLosProbe(
        coords.x, coords.y, coords.z,
        destination.x, destination.y, destination.z,
        10, ped, 0
    )
    
    local _, hit, _, _, entity = GetShapeTestResult(rayHandle)
    
    if hit and IsEntityAVehicle(entity) then
        return entity
    end
    
    return nil
end

-- Check if vehicle has GPS tracker
function HasGPSTracker(vehicle)
    local plate = GetVehicleNumberPlateText(vehicle)
    return trackedVehicles[plate] ~= nil
end

-- Start tracker installation
function StartTrackerInstallation(vehicle)
    isInstallingTracker = true
    
    local ped = PlayerPedId()
    
    -- Play installation animation
    local animDict = "anim@amb@clubhouse@tutorial@bkr_tut_ig3@"
    local animName = "machinic_loop_mechandplayer"
    
    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(10)
    end
    
    TaskPlayAnim(ped, animDict, animName, 8.0, -8.0, -1, 1, 0, false, false, false)
    
    -- Show progress bar
    local progressConfig = {
        label = "Installing GPS tracker...",
        duration = 10000,
        useWhileDead = false,
        canCancel = true
    }
    
    exports['olympus-items']:ShowProgressBar(progressConfig, function()
        -- Progress completed
        CompleteTrackerInstallation(vehicle)
    end, function()
        -- Progress cancelled
        CancelTrackerInstallation()
    end)
    
    return true
end

-- Complete tracker installation
function CompleteTrackerInstallation(vehicle)
    isInstallingTracker = false
    
    -- Clear animation
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    -- Check if still in range
    local playerPos = GetEntityCoords(ped)
    local vehiclePos = GetEntityCoords(vehicle)
    local distance = #(playerPos - vehiclePos)
    
    if distance > 5.0 then
        exports['olympus-items']:ShowNotification("You moved too far away!", "error")
        return
    end
    
    local plate = GetVehicleNumberPlateText(vehicle)
    local vehicleNetId = NetworkGetNetworkIdFromEntity(vehicle)
    
    -- Add to tracked vehicles
    trackedVehicles[plate] = {
        netId = vehicleNetId,
        installTime = GetGameTimer(),
        installedBy = GetPlayerServerId(PlayerId())
    }
    
    -- Notify server
    TriggerServerEvent('olympus-items:server:installGPSTracker', {
        vehicle = vehicleNetId,
        plate = plate,
        location = vehiclePos
    })
    
    -- Consume item
    TriggerServerEvent('olympus-items:server:consumeItem', 'gpstracker', true)
    
    exports['olympus-items']:ShowNotification("GPS tracker installed successfully!", "success")
    
    -- Start tracking
    StartVehicleTracking(plate)
end

-- Cancel tracker installation
function CancelTrackerInstallation()
    isInstallingTracker = false
    
    -- Clear animation
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    exports['olympus-items']:ShowNotification("Tracker installation cancelled!", "info")
end

-- Start vehicle tracking
function StartVehicleTracking(plate)
    CreateThread(function()
        while trackedVehicles[plate] do
            local vehicle = GetVehicleByPlate(plate)
            
            if vehicle and DoesEntityExist(vehicle) then
                local vehiclePos = GetEntityCoords(vehicle)
                
                -- Update tracker data
                trackedVehicles[plate].lastPosition = vehiclePos
                trackedVehicles[plate].lastUpdate = GetGameTimer()
                
                -- Send update to server
                TriggerServerEvent('olympus-items:server:updateGPSTracker', {
                    plate = plate,
                    position = vehiclePos,
                    speed = GetEntitySpeed(vehicle) * 3.6 -- Convert to km/h
                })
            else
                -- Vehicle not found, might be despawned
                trackedVehicles[plate].lastUpdate = GetGameTimer()
            end
            
            Wait(5000) -- Update every 5 seconds
        end
    end)
end

-- Get vehicle by plate
function GetVehicleByPlate(plate)
    local vehicles = GetGamePool('CVehicle')
    
    for _, vehicle in ipairs(vehicles) do
        if GetVehicleNumberPlateText(vehicle) == plate then
            return vehicle
        end
    end
    
    return nil
end

-- Remove GPS tracker
function RemoveGPSTracker(plate)
    if trackedVehicles[plate] then
        trackedVehicles[plate] = nil
        exports['olympus-items']:ShowNotification(string.format("GPS tracker removed from vehicle %s", plate), "info")
    end
end

-- Get tracked vehicles
function GetTrackedVehicles()
    local activeTrackers = {}
    
    for plate, data in pairs(trackedVehicles) do
        if data.lastUpdate and GetGameTimer() - data.lastUpdate < 30000 then -- Active within last 30 seconds
            table.insert(activeTrackers, {
                plate = plate,
                position = data.lastPosition,
                lastUpdate = data.lastUpdate,
                installedBy = data.installedBy
            })
        end
    end
    
    return activeTrackers
end

-- Show GPS tracker menu
function ShowGPSTrackerMenu()
    local trackers = GetTrackedVehicles()
    
    if #trackers == 0 then
        exports['olympus-items']:ShowNotification("No active GPS trackers found!", "info")
        return
    end
    
    local menuItems = {}
    
    for _, tracker in ipairs(trackers) do
        local timeSince = math.floor((GetGameTimer() - tracker.lastUpdate) / 1000)
        table.insert(menuItems, {
            header = string.format("Vehicle: %s", tracker.plate),
            txt = string.format("Last seen: %d seconds ago", timeSince),
            params = {
                event = "olympus-items:client:trackVehicle",
                args = {
                    plate = tracker.plate,
                    position = tracker.position
                }
            }
        })
    end
    
    -- Add close option
    table.insert(menuItems, {
        header = "Close",
        txt = "",
        params = {
            event = "olympus-ui:client:closeMenu"
        }
    })
    
    -- Show menu
    if exports['olympus-ui'] then
        exports['olympus-ui']:ShowMenu({
            header = "GPS Tracker",
            items = menuItems
        })
    end
end

-- Track specific vehicle
function TrackVehicle(plate, position)
    if not position then
        exports['olympus-items']:ShowNotification("Vehicle location unknown!", "error")
        return
    end
    
    -- Set waypoint to vehicle
    SetNewWaypoint(position.x, position.y)
    exports['olympus-items']:ShowNotification(string.format("Waypoint set to vehicle %s", plate), "success")
end

-- Check if player moved too far during installation
CreateThread(function()
    while true do
        if isInstallingTracker then
            local ped = PlayerPedId()
            
            -- Check if player is in a vehicle
            if IsPedInAnyVehicle(ped, false) then
                CancelTrackerInstallation()
            end
        end
        
        Wait(500)
    end
end)

-- Export functions
exports('UseGPSTracker', UseGPSTracker)
exports('RemoveGPSTracker', RemoveGPSTracker)
exports('GetTrackedVehicles', GetTrackedVehicles)
exports('ShowGPSTrackerMenu', ShowGPSTrackerMenu)

-- Event handlers
RegisterNetEvent('olympus-items:client:trackerInterrupted', function()
    if isInstallingTracker then
        CancelTrackerInstallation()
    end
end)

RegisterNetEvent('olympus-items:client:removeGPSTracker', function(plate)
    RemoveGPSTracker(plate)
end)

RegisterNetEvent('olympus-items:client:trackVehicle', function(data)
    TrackVehicle(data.plate, data.position)
end)

RegisterNetEvent('olympus-items:client:showGPSMenu', function()
    ShowGPSTrackerMenu()
end)
