-- ============================================
-- OLYMPUS VIGILANTE SYSTEM CONFIGURATION
-- Based on original Olympus vigilante mechanics
-- ============================================

Config = {}

-- ============================================
-- VIGILANTE SYSTEM SETTINGS
-- ============================================

Config.Vigilante = {
    enabled = true,
    
    -- Minimum bounty requirement for arrests
    minimumBounty = 75000, -- $75,000 minimum bounty
    
    -- Maximum bounty payout cap
    maximumPayout = 5000000, -- $5,000,000 maximum payout
    
    -- Tier progression requirements
    tierRequirements = {
        [1] = 0,    -- Tier 1: 0 arrests
        [2] = 25,   -- Tier 2: 25 arrests
        [3] = 50,   -- Tier 3: 50 arrests
        [4] = 100,  -- Tier 4: 100 arrests
        [5] = 200   -- Tier 5: 200 arrests
    },
    
    -- Tier payout multipliers
    tierMultipliers = {
        [1] = 1.0,  -- 100% payout
        [2] = 1.05, -- 105% payout
        [3] = 1.10, -- 110% payout
        [4] = 1.15, -- 115% payout
        [5] = 1.20  -- 120% payout
    },
    
    -- Arrest type modifiers
    arrestModifiers = {
        arrest = 1.0, -- Full bounty for arrests
        kill = 0.5    -- Half bounty for kills
    },
    
    -- Buddy system settings
    buddySystem = {
        enabled = true,
        splitPercentage = 0.5 -- 50/50 split between buddies
    }
}

-- ============================================
-- VIGILANTE EQUIPMENT BY TIER
-- Based on original vigilante tier system
-- ============================================

Config.Equipment = {
    -- Tier 1 Equipment (0 arrests)
    [1] = {
        name = "Vigilante Tier 1",
        weapons = {
            {name = "P07", hash = "WEAPON_PISTOL", ammo = 100}
        },
        armor = 0,
        description = "Basic vigilante equipment"
    },
    
    -- Tier 2 Equipment (25 arrests)
    [2] = {
        name = "Vigilante Tier 2", 
        weapons = {
            {name = "P07", hash = "WEAPON_PISTOL", ammo = 100},
            {name = "ACP-C2", hash = "WEAPON_COMBATPISTOL", ammo = 100},
            {name = "Sting", hash = "WEAPON_STUNGUN", ammo = 10}
        },
        armor = 0,
        description = "Enhanced sidearms and taser"
    },
    
    -- Tier 3 Equipment (50 arrests)
    [3] = {
        name = "Vigilante Tier 3",
        weapons = {
            {name = "P07", hash = "WEAPON_PISTOL", ammo = 100},
            {name = "ACP-C2", hash = "WEAPON_COMBATPISTOL", ammo = 100},
            {name = "Sting", hash = "WEAPON_STUNGUN", ammo = 10}
        },
        armor = 50, -- T3 Vest
        description = "Tier 2 equipment plus protective vest"
    },
    
    -- Tier 4 Equipment (100 arrests)
    [4] = {
        name = "Vigilante Tier 4",
        weapons = {
            {name = "P07", hash = "WEAPON_PISTOL", ammo = 100},
            {name = "ACP-C2", hash = "WEAPON_COMBATPISTOL", ammo = 100},
            {name = "Sting", hash = "WEAPON_STUNGUN", ammo = 10},
            {name = "SPAR16", hash = "WEAPON_CARBINERIFLE", ammo = 200},
            {name = "SPAR16-GL", hash = "WEAPON_CARBINERIFLE_MK2", ammo = 200}
        },
        armor = 50,
        description = "Advanced assault rifles and equipment"
    },
    
    -- Tier 5 Equipment (200 arrests)
    [5] = {
        name = "Vigilante Tier 5",
        weapons = {
            {name = "P07", hash = "WEAPON_PISTOL", ammo = 100},
            {name = "ACP-C2", hash = "WEAPON_COMBATPISTOL", ammo = 100},
            {name = "Sting", hash = "WEAPON_STUNGUN", ammo = 10},
            {name = "SPAR16", hash = "WEAPON_CARBINERIFLE", ammo = 200},
            {name = "SPAR16-GL", hash = "WEAPON_CARBINERIFLE_MK2", ammo = 200},
            {name = "SPAR16S", hash = "WEAPON_SPECIALCARBINE", ammo = 200}
        },
        armor = 75,
        specialAbilities = {"athira_spawn"}, -- Can spawn at Athira
        description = "Elite vigilante equipment and Athira spawn access"
    }
}

-- ============================================
-- ARREST STORAGE COSTS
-- Based on original fn_storeVigilanteArrests.sqf
-- ============================================

Config.StorageCosts = {
    baseCost = 400000, -- $400,000 base cost per tier
    
    -- Cost multipliers by arrest count
    tierMultipliers = {
        {min = 0,   max = 24,  multiplier = 1}, -- $400,000
        {min = 25,  max = 49,  multiplier = 2}, -- $800,000
        {min = 50,  max = 99,  multiplier = 3}, -- $1,200,000
        {min = 100, max = 199, multiplier = 4}, -- $1,600,000
        {min = 200, max = 999, multiplier = 5}  -- $2,000,000
    }
}

-- ============================================
-- VIGILANTE LOCATIONS
-- ============================================

Config.Locations = {
    -- Vigilante Outposts (Jail Transport Locations)
    outposts = {
        {
            name = "Kavala Vigilante Outpost",
            coords = vector3(3739.5, 4526.5, 22.5),
            blip = {sprite = 60, color = 5, scale = 0.8}
        },
        {
            name = "Athira Vigilante Outpost", 
            coords = vector3(3264.5, 5176.5, 18.5),
            blip = {sprite = 60, color = 5, scale = 0.8}
        },
        {
            name = "Pyrgos Vigilante Outpost",
            coords = vector3(1866.5, 2586.5, 45.5),
            blip = {sprite = 60, color = 5, scale = 0.8}
        }
    },
    
    -- Equipment Lockers
    equipment = {
        {
            name = "Kavala Vigilante Equipment",
            coords = vector3(3740.0, 4525.0, 22.5),
            heading = 180.0
        },
        {
            name = "Athira Vigilante Equipment",
            coords = vector3(3265.0, 4175.0, 18.5),
            heading = 90.0
        },
        {
            name = "Pyrgos Vigilante Equipment", 
            coords = vector3(1867.0, 2585.0, 45.5),
            heading = 270.0
        }
    },
    
    -- Arrest Storage Locations
    storage = {
        {
            name = "Vigilante Records Office",
            coords = vector3(3741.0, 4527.0, 22.5),
            description = "Store and claim vigilante arrests"
        }
    }
}

-- ============================================
-- VIGILANTE RULES AND RESTRICTIONS
-- Based on original vigilante rules
-- ============================================

Config.Rules = {
    -- Minimum bounty for arrests
    minimumBountyForArrest = 75000,
    
    -- Restrictions
    cannotArrestGangMembers = true,     -- Cannot arrest own gang members
    cannotArrestWhileWanted = true,     -- Cannot arrest while having bounty
    cannotRobThenArrest = true,         -- Cannot rob then arrest same player
    
    -- Equipment restrictions
    cannotHaveWeaponsInCities = true,   -- Must holster weapons in main cities
    mustFollowAPDOrders = true,         -- Must comply with APD requests
    
    -- Arrest requirements
    mustAnnounceBeforeArrest = true,    -- Must announce before arresting
    mustInformOfCharges = true,         -- Must tell player their charges
    mustInformOfBounty = true,          -- Must tell player their bounty
    mustTransportToJail = true,         -- Must take to vigilante outpost
    
    -- Tier degradation
    losesTierWhenArrested = true,       -- Loses tier when arrested by APD
    
    -- Buddy system rules
    buddyCanMakeArrest = true,          -- Buddy who tases can make arrest
    buddySplitsBounty = true            -- Bounty split between buddies
}

-- ============================================
-- VIGILANTE COMMANDS
-- ============================================

Config.Commands = {
    equipment = "vigiequip",        -- Get vigilante equipment
    stats = "vigistats",            -- View vigilante statistics  
    buddy = "vigibuddy",            -- Send buddy request
    endbuddy = "vigiendbuddy",      -- End buddy agreement
    store = "vigistore",            -- Store arrests
    claim = "vigiclaim"             -- Claim stored arrests
}
