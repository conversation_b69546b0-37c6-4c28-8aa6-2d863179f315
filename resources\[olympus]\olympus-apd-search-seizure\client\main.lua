-- ========================================
-- OLYMPUS APD SEARCH & SEIZURE CLIENT
-- Based on Original Olympus Functions
-- ========================================

local OlympusSearchSeizure = {}

-- ========================================
-- UTILITY FUNCTIONS
-- ========================================
function OlympusSearchSeizure.Notify(message, type)
    -- Use your notification system here
    if exports['olympus-ui'] and exports['olympus-ui'].ShowNotification then
        exports['olympus-ui']:ShowNotification(message, type)
    else
        print(message)
    end
end

function OlympusSearchSeizure.GetClosestPlayer()
    local playerPed = PlayerPedId()
    local pos = GetEntityCoords(playerPed)
    local closestPlayer = nil
    local closestDistance = Config.SearchSeizure.playerSearch.maxDistance
    
    for _, playerId in ipairs(GetActivePlayers()) do
        if playerId ~= PlayerId() then
            local targetPed = GetPlayerPed(playerId)
            local targetPos = GetEntityCoords(targetPed)
            local distance = #(pos - targetPos)
            
            if distance < closestDistance then
                closestPlayer = GetPlayerServerId(playerId)
                closestDistance = distance
            end
        end
    end
    
    return closestPlayer, closestDistance
end

function OlympusSearchSeizure.GetClosestVehicle()
    local playerPed = PlayerPedId()
    local pos = GetEntityCoords(playerPed)
    local vehicle = GetClosestVehicle(pos.x, pos.y, pos.z, Config.SearchSeizure.vehicleSeizure.maxDistance, 0, 71)
    
    if vehicle and vehicle ~= 0 then
        local vehiclePos = GetEntityCoords(vehicle)
        local distance = #(pos - vehiclePos)
        return vehicle, distance
    end
    
    return nil, -1
end

function OlympusSearchSeizure.LoadAnimDict(dict)
    if not HasAnimDictLoaded(dict) then
        RequestAnimDict(dict)
        while not HasAnimDictLoaded(dict) do
            Wait(1)
        end
    end
end

function OlympusSearchSeizure.PlayAnimation(dict, anim, flag, duration)
    OlympusSearchSeizure.LoadAnimDict(dict)
    TaskPlayAnim(PlayerPedId(), dict, anim, 8.0, -8.0, duration or -1, flag or 0, 0, false, false, false)
end

function OlympusSearchSeizure.ShowProgressBar(text, duration)
    if exports['olympus-ui'] and exports['olympus-ui'].ShowProgressBar then
        exports['olympus-ui']:ShowProgressBar(text, duration)
    end
end

-- ========================================
-- PLAYER SEARCH SYSTEM
-- Based on fn_searchAction.sqf
-- ========================================
function OlympusSearchSeizure.SearchPlayer()
    local targetId, distance = OlympusSearchSeizure.GetClosestPlayer()
    
    if not targetId or distance > Config.SearchSeizure.playerSearch.maxDistance then
        OlympusSearchSeizure.Notify(Config.Notifications.search.tooFar, 'error')
        return
    end
    
    -- Play animation and show progress
    OlympusSearchSeizure.PlayAnimation(
        Config.Animations.searching.dict,
        Config.Animations.searching.anim,
        Config.Animations.searching.flag,
        Config.SearchSeizure.playerSearch.duration
    )
    
    OlympusSearchSeizure.ShowProgressBar(Config.Notifications.search.start, Config.SearchSeizure.playerSearch.duration)
    
    Wait(Config.SearchSeizure.playerSearch.duration)
    
    -- Trigger server event
    TriggerServerEvent('olympus-apd-search-seizure:searchPlayer', targetId)
end

-- ========================================
-- VEHICLE SEIZURE SYSTEM
-- Based on fn_seizeAction.sqf
-- ========================================
function OlympusSearchSeizure.SeizeVehicle()
    local vehicle, distance = OlympusSearchSeizure.GetClosestVehicle()
    
    if not vehicle or distance > Config.SearchSeizure.vehicleSeizure.maxDistance then
        OlympusSearchSeizure.Notify(Config.Notifications.seizure.tooFar, 'error')
        return
    end
    
    -- Check if vehicle has occupants
    local occupants = GetVehicleNumberOfPassengers(vehicle)
    if occupants > 0 then
        OlympusSearchSeizure.Notify(Config.Notifications.seizure.occupied, 'error')
        return
    end
    
    -- Check vehicle damage
    local damage = GetVehicleEngineHealth(vehicle) / 1000.0
    if damage < 0.15 then
        OlympusSearchSeizure.Notify(Config.Notifications.seizure.tooRekt, 'error')
        return
    end
    
    -- Play animation and show progress
    OlympusSearchSeizure.PlayAnimation(
        Config.Animations.seizing.dict,
        Config.Animations.seizing.anim,
        Config.Animations.seizing.flag,
        Config.SearchSeizure.vehicleSeizure.duration
    )
    
    OlympusSearchSeizure.ShowProgressBar(Config.Notifications.seizure.start, Config.SearchSeizure.vehicleSeizure.duration)
    
    Wait(Config.SearchSeizure.vehicleSeizure.duration)
    
    -- Trigger server event
    local vehicleNetId = NetworkGetNetworkIdFromEntity(vehicle)
    TriggerServerEvent('olympus-apd-search-seizure:seizeVehicle', vehicleNetId)
end

-- ========================================
-- ITEM SEIZURE SYSTEM
-- ========================================
function OlympusSearchSeizure.SeizePlayerItems()
    local targetId, distance = OlympusSearchSeizure.GetClosestPlayer()
    
    if not targetId or distance > Config.SearchSeizure.playerSearch.maxDistance then
        OlympusSearchSeizure.Notify(Config.Notifications.search.tooFar, 'error')
        return
    end
    
    -- Trigger server event
    TriggerServerEvent('olympus-apd-search-seizure:seizePlayerItems', targetId)
end

-- ========================================
-- PLAYER EQUIPMENT COLLECTION
-- Based on fn_searchClient.sqf
-- ========================================
function OlympusSearchSeizure.GetPlayerEquipment()
    local playerPed = PlayerPedId()
    local equipment = {
        weapons = {},
        backpack = {},
        vest = {},
        uniform = {},
        cash = 0
    }
    
    -- Get weapons
    local weapons = {
        GetCurrentPedWeapon(playerPed, true),
        -- Add more weapon slots as needed
    }
    
    for _, weapon in ipairs(weapons) do
        if weapon and weapon ~= GetHashKey("WEAPON_UNARMED") then
            local weaponName = GetDisplayNameFromVehicleModel(weapon)
            table.insert(equipment.weapons, weaponName)
        end
    end
    
    -- Get player cash
    local success, result = pcall(function()
        return exports['olympus-core']:GetPlayerMoney(PlayerId(), 'cash')
    end)
    if success and result then
        equipment.cash = result
    end
    
    -- Get inventory items (simplified for now)
    local success, inventory = pcall(function()
        return exports['olympus-core']:GetPlayerInventory(PlayerId())
    end)
    if success and inventory then
        for item, amount in pairs(inventory) do
            -- Categorize items based on type
            if string.find(item:lower(), 'weapon') then
                table.insert(equipment.weapons, item .. ' x' .. amount)
            else
                table.insert(equipment.backpack, item .. ' x' .. amount)
            end
        end
    end
    
    return equipment
end

-- ========================================
-- COMMAND REGISTRATION
-- ========================================
RegisterCommand('search', function()
    OlympusSearchSeizure.SearchPlayer()
end, false)

RegisterCommand('seize', function()
    OlympusSearchSeizure.SeizeVehicle()
end, false)

RegisterCommand('seizeitems', function()
    OlympusSearchSeizure.SeizePlayerItems()
end, false)

-- ========================================
-- EVENT HANDLERS
-- ========================================
RegisterNetEvent('olympus-apd-search-seizure:notify', function(message, type)
    OlympusSearchSeizure.Notify(message, type)
end)

RegisterNetEvent('olympus-apd-search-seizure:broadcast', function(message, faction)
    -- Only show broadcasts to relevant factions
    local playerFaction = 'civilian' -- Get from core system
    local success, result = pcall(function()
        return exports['olympus-core']:GetPlayerData(PlayerId())
    end)
    if success and result then
        playerFaction = result.faction or 'civilian'
    end
    
    if faction == 'all' or faction == playerFaction then
        OlympusSearchSeizure.Notify(message, 'info')
    end
end)

RegisterNetEvent('olympus-apd-search-seizure:searchResult', function(data)
    local message = string.format("Search Results for %s:\n", data.targetName)
    
    if next(data.illegalItems) then
        message = message .. "Illegal Items Found:\n"
        for item, amount in pairs(data.illegalItems) do
            message = message .. string.format("- %s x%d\n", item, amount)
        end
        message = message .. string.format("Total Contraband Value: $%d\n", data.contrabandValue)
    else
        message = message .. Config.Notifications.search.noIllegal .. "\n"
    end
    
    if data.isRobber then
        message = message .. "ROBBERY SUSPECT\n"
    end
    
    OlympusSearchSeizure.Notify(message, data.contrabandValue > 0 and 'warning' or 'info')
end)

RegisterNetEvent('olympus-apd-search-seizure:requestPlayerEquipment', function(officerId)
    local equipment = OlympusSearchSeizure.GetPlayerEquipment()
    TriggerServerEvent('olympus-apd-search-seizure:playerEquipmentResponse', officerId, equipment)
end)

RegisterNetEvent('olympus-apd-search-seizure:playerEquipmentData', function(targetId, equipmentData)
    -- Display detailed equipment information to the searching officer
    local message = "Player Equipment:\n"
    
    if #equipmentData.weapons > 0 then
        message = message .. "Weapons:\n"
        for _, weapon in ipairs(equipmentData.weapons) do
            message = message .. "- " .. weapon .. "\n"
        end
    else
        message = message .. "No weapons found.\n"
    end
    
    if #equipmentData.backpack > 0 then
        message = message .. "Backpack:\n"
        for _, item in ipairs(equipmentData.backpack) do
            message = message .. "- " .. item .. "\n"
        end
    else
        message = message .. "No backpack items.\n"
    end
    
    if equipmentData.cash > 0 then
        message = message .. string.format("Cash: $%d\n", equipmentData.cash)
    else
        message = message .. "No cash on hand.\n"
    end
    
    OlympusSearchSeizure.Notify(message, 'info')
end)

-- ========================================
-- EXPORT FUNCTIONS
-- ========================================
exports('SearchPlayer', function()
    return OlympusSearchSeizure.SearchPlayer()
end)

exports('SeizeVehicle', function()
    return OlympusSearchSeizure.SeizeVehicle()
end)

exports('SeizePlayerItems', function()
    return OlympusSearchSeizure.SeizePlayerItems()
end)

exports('GetClosestPlayer', function()
    return OlympusSearchSeizure.GetClosestPlayer()
end)

exports('GetClosestVehicle', function()
    return OlympusSearchSeizure.GetClosestVehicle()
end)

exports('GetPlayerEquipment', function()
    return OlympusSearchSeizure.GetPlayerEquipment()
end)

-- ========================================
-- INITIALIZATION
-- ========================================
CreateThread(function()
    print("^2[Olympus APD Search & Seizure]^7 Client system initialized")
end)
