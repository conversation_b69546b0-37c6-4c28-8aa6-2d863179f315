-- Olympus Gang System - Map Blips
-- Creates blips for gang territories, cartels, and gang-related locations

local gangBlips = {}

-- Gang and Cartel Blip Configuration
local GangBlipConfig = {
    -- Cartel Locations (from config)
    cartels = {
        {
            coords = vector3(2447.9, 1576.9, 33.0), -- Arms Cartel
            sprite = 437, -- Gun icon
            color = 1, -- Red
            name = "Arms Cartel",
            scale = 1.2,
            shortRange = false -- Always visible
        },
        {
            coords = vector3(1958.5, 5179.8, 47.9), -- Church Cartel
            sprite = 514, -- Drug icon
            color = 3, -- Blue
            name = "Drug Cartel (Church)",
            scale = 1.2,
            shortRange = false
        },
        {
            coords = vector3(4991.2, -4920.8, 3.4), -- Alpha Point Cartel
            sprite = 514, -- Drug icon
            color = 5, -- Yellow
            name = "Drug Cartel (Alpha Point)",
            scale = 1.2,
            shortRange = false
        }
    },
    
    -- Gang Territories (example locations)
    territories = {
        {
            coords = vector3(-1387.0, -588.0, 30.0), -- Grove Street area
            sprite = 84, -- Gang icon
            color = 2, -- Green
            name = "Grove Street Territory",
            scale = 1.0,
            shortRange = true
        },
        {
            coords = vector3(321.0, -2040.0, 20.0), -- Ballas area
            sprite = 84, -- Gang icon
            color = 27, -- Purple
            name = "Ballas Territory",
            scale = 1.0,
            shortRange = true
        },
        {
            coords = vector3(1200.0, -1600.0, 35.0), -- Vagos area
            sprite = 84, -- Gang icon
            color = 5, -- Yellow
            name = "Vagos Territory",
            scale = 1.0,
            shortRange = true
        },
        {
            coords = vector3(-800.0, 180.0, 72.0), -- Lost MC area
            sprite = 84, -- Gang icon
            color = 1, -- Red
            name = "Lost MC Territory",
            scale = 1.0,
            shortRange = true
        }
    },
    
    -- Gang Shops and Services
    gangShops = {
        {
            coords = vector3(1692.0, 3759.0, 34.0), -- Sandy Shores Gun Shop
            sprite = 110, -- Ammu-Nation
            color = 1, -- Red
            name = "Rebel Outpost",
            scale = 1.0,
            shortRange = true
        },
        {
            coords = vector3(-1117.0, 2698.0, 18.0), -- Zancudo Gun Shop
            sprite = 110, -- Ammu-Nation
            color = 1, -- Red
            name = "Black Market",
            scale = 1.0,
            shortRange = true
        }
    },
    
    -- Processing Locations
    processing = {
        {
            coords = vector3(2433.0, 4969.0, 42.0), -- Weed Processing
            sprite = 469, -- Weed leaf
            color = 2, -- Green
            name = "Weed Processing",
            scale = 0.8,
            shortRange = true
        },
        {
            coords = vector3(1386.0, 3606.0, 38.0), -- Cocaine Processing
            sprite = 501, -- Briefcase
            color = 0, -- White
            name = "Cocaine Processing",
            scale = 0.8,
            shortRange = true
        },
        {
            coords = vector3(-1026.0, 4920.0, 210.0), -- Meth Processing
            sprite = 499, -- Chemical
            color = 4, -- Light Blue
            name = "Meth Processing",
            scale = 0.8,
            shortRange = true
        },
        {
            coords = vector3(1525.0, 1722.0, 109.0), -- Heroin Processing
            sprite = 403, -- Syringe
            color = 8, -- Dark Red
            name = "Heroin Processing",
            scale = 0.8,
            shortRange = true
        }
    },
    
    -- Drug Fields
    fields = {
        {
            coords = vector3(2224.0, 5577.0, 53.0), -- Weed Field
            sprite = 469, -- Weed leaf
            color = 2, -- Green
            name = "Weed Field",
            scale = 0.7,
            shortRange = true
        },
        {
            coords = vector3(1525.0, 1722.0, 109.0), -- Poppy Field
            sprite = 403, -- Syringe
            color = 8, -- Dark Red
            name = "Poppy Field",
            scale = 0.7,
            shortRange = true
        }
    }
}

-- Create gang blip function
function CreateGangBlip(coords, sprite, color, name, scale, shortRange)
    local blip = AddBlipForCoord(coords.x, coords.y, coords.z)
    SetBlipSprite(blip, sprite)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, scale or 1.0)
    SetBlipColour(blip, color)
    SetBlipAsShortRange(blip, shortRange or true)
    
    -- Add blip name
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(name)
    EndTextCommandSetBlipName(blip)
    
    return blip
end

-- Initialize gang blips
function InitializeGangBlips()
    print("^3[Olympus Gangs]^7 Creating gang and cartel blips...")
    
    -- Create cartel blips
    for _, cartel in pairs(GangBlipConfig.cartels) do
        local blip = CreateGangBlip(cartel.coords, cartel.sprite, cartel.color, cartel.name, cartel.scale, cartel.shortRange)
        table.insert(gangBlips, blip)
    end
    
    -- Create territory blips
    for _, territory in pairs(GangBlipConfig.territories) do
        local blip = CreateGangBlip(territory.coords, territory.sprite, territory.color, territory.name, territory.scale, territory.shortRange)
        table.insert(gangBlips, blip)
    end
    
    -- Create gang shop blips
    for _, shop in pairs(GangBlipConfig.gangShops) do
        local blip = CreateGangBlip(shop.coords, shop.sprite, shop.color, shop.name, shop.scale, shop.shortRange)
        table.insert(gangBlips, blip)
    end
    
    -- Create processing blips
    for _, processing in pairs(GangBlipConfig.processing) do
        local blip = CreateGangBlip(processing.coords, processing.sprite, processing.color, processing.name, processing.scale, processing.shortRange)
        table.insert(gangBlips, blip)
    end
    
    -- Create field blips
    for _, field in pairs(GangBlipConfig.fields) do
        local blip = CreateGangBlip(field.coords, field.sprite, field.color, field.name, field.scale, field.shortRange)
        table.insert(gangBlips, blip)
    end
    
    print(string.format("^2[Olympus Gangs]^7 Created %d gang and cartel blips", #gangBlips))
end

-- Update cartel ownership blips
function UpdateCartelOwnership(cartelName, gangName, gangColor)
    -- This would update the cartel blip color based on ownership
    -- Implementation would depend on how cartel ownership is tracked
    print(string.format("^3[Olympus Gangs]^7 Cartel %s now owned by %s", cartelName, gangName or "Nobody"))
end

-- Clean up gang blips
function CleanupGangBlips()
    for _, blip in pairs(gangBlips) do
        if DoesBlipExist(blip) then
            RemoveBlip(blip)
        end
    end
    gangBlips = {}
end

-- Initialize when resource starts
CreateThread(function()
    Wait(3000) -- Wait for everything to load
    InitializeGangBlips()
end)

-- Handle cartel ownership updates
RegisterNetEvent('olympus-gangs:client:updateCartelOwnership')
AddEventHandler('olympus-gangs:client:updateCartelOwnership', function(cartelName, gangData)
    UpdateCartelOwnership(cartelName, gangData and gangData.name, gangData and gangData.color)
end)

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        CleanupGangBlips()
    end
end)

-- Export functions
exports('UpdateCartelBlip', UpdateCartelOwnership)
exports('CreateCustomGangBlip', CreateGangBlip)

print("[Olympus Gangs] Blips system loaded")
