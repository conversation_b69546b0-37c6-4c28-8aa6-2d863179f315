-- ========================================
-- OLYMPUS VEHICLE SYSTEM - CLIENT MAIN
-- Complete recreation based on original vehicle functions
-- Handles vehicle keys, interaction, garage system, and UI integration
-- ========================================

local OlympusVehicles = {}
OlympusVehicles.PlayerVehicles = {} -- Player's owned vehicles
OlympusVehicles.NearbyVehicles = {} -- Vehicles near player
OlympusVehicles.VehicleKeys = {} -- Vehicles player has keys to
OlympusVehicles.CurrentVehicle = nil -- Vehicle player is currently in

-- Vehicle configuration
local VEHICLE_CONFIG = {
    interactionDistance = 3.0, -- Distance to interact with vehicles
    keyDistance = 100.0, -- Distance for key detection
    checkInterval = 1000, -- Check interval for nearby vehicles
    garageDistance = 10.0, -- Distance to access garage
    lockToggleKey = 'L', -- Key to toggle vehicle lock
    engineToggleKey = 'G' -- Key to toggle engine
}

-- Garage locations (matches original garage positions)
local GARAGE_LOCATIONS = {
    -- Civilian garages
    {
        name = "Kavala Garage",
        coords = vector3(462.0, -1014.4, 28.1),
        type = "civilian",
        blip = true
    },
    {
        name = "Pyrgos Garage",
        coords = vector3(1737.59, 3710.2, 34.14),
        type = "civilian",
        blip = true
    },
    {
        name = "Athira Garage",
        coords = vector3(2133.09, 4780.73, 40.97),
        type = "civilian",
        blip = true
    },
    -- Gang garages
    {
        name = "Gang Garage North",
        coords = vector3(970.31, -1826.99, 31.11),
        type = "gang",
        blip = false
    },
    {
        name = "Gang Garage South",
        coords = vector3(1408.42, -2096.7, 13.69),
        type = "gang",
        blip = false
    },
    -- Police garages
    {
        name = "Kavala PD Garage",
        coords = vector3(454.6, -1017.4, 28.4),
        type = "cop",
        blip = false
    },
    {
        name = "Pyrgos PD Garage",
        coords = vector3(1866.52, 3689.73, 33.59),
        type = "cop",
        blip = false
    },
    -- Medical garages
    {
        name = "Kavala Hospital Garage",
        coords = vector3(294.23, -1448.51, 29.97),
        type = "medical",
        blip = false
    }
}

-- Initialize vehicle system
function InitializeVehicleSystem()
    print("^2[Olympus Vehicles]^7 Initializing client vehicle system...")

    -- Create garage blips
    CreateGarageBlips()

    -- Start vehicle proximity checker
    StartVehicleProximityChecker()

    -- Start vehicle interaction handler
    StartVehicleInteractionHandler()

    -- Start key bindings
    StartKeyBindings()

    print("^2[Olympus Vehicles]^7 Client vehicle system initialized!")
end

-- Create garage blips (matches original garage blips)
function CreateGarageBlips()
    for _, garage in ipairs(GARAGE_LOCATIONS) do
        if garage.blip then
            local blip = AddBlipForCoord(garage.coords.x, garage.coords.y, garage.coords.z)
            SetBlipSprite(blip, 357) -- Garage icon
            SetBlipDisplay(blip, 4)
            SetBlipScale(blip, 0.8)
            SetBlipColour(blip, 3) -- Blue
            SetBlipAsShortRange(blip, true)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(garage.name)
            EndTextCommandSetBlipName(blip)
        end
    end
end

-- Start vehicle proximity checker
function StartVehicleProximityChecker()
    CreateThread(function()
        while true do
            Wait(VEHICLE_CONFIG.checkInterval)

            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)

            -- Check for nearby vehicles
            local vehicles = GetGamePool('CVehicle')
            OlympusVehicles.NearbyVehicles = {}

            for _, vehicle in ipairs(vehicles) do
                local vehicleCoords = GetEntityCoords(vehicle)
                local distance = #(playerCoords - vehicleCoords)

                if distance <= VEHICLE_CONFIG.keyDistance then
                    local vehicleData = GetVehicleData(vehicle)
                    if vehicleData then
                        OlympusVehicles.NearbyVehicles[vehicle] = vehicleData

                        -- Check if player has keys (matches original fn_hasKeys.sqf)
                        if HasVehicleKeys(vehicle) then
                            OlympusVehicles.VehicleKeys[vehicle] = true
                        end
                    end
                end
            end

            -- Update current vehicle
            local currentVehicle = GetVehiclePedIsIn(playerPed, false)
            if currentVehicle ~= 0 then
                OlympusVehicles.CurrentVehicle = currentVehicle
            else
                OlympusVehicles.CurrentVehicle = nil
            end
        end
    end)
end

-- Start vehicle interaction handler
function StartVehicleInteractionHandler()
    CreateThread(function()
        while true do
            Wait(0)

            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)
            local nearestVehicle = nil
            local nearestDistance = VEHICLE_CONFIG.interactionDistance

            -- Find nearest vehicle
            for vehicle, _ in pairs(OlympusVehicles.NearbyVehicles) do
                if DoesEntityExist(vehicle) then
                    local vehicleCoords = GetEntityCoords(vehicle)
                    local distance = #(playerCoords - vehicleCoords)

                    if distance < nearestDistance then
                        nearestVehicle = vehicle
                        nearestDistance = distance
                    end
                end
            end

            -- Show interaction prompt
            if nearestVehicle then
                local vehicleData = GetVehicleData(nearestVehicle)
                if vehicleData then
                    -- Draw 3D text (matches original interaction system)
                    local vehicleCoords = GetEntityCoords(nearestVehicle)
                    DrawText3D(vehicleCoords.x, vehicleCoords.y, vehicleCoords.z + 1.0,
                        string.format("~w~%s~n~~g~[E] ~w~Interact", vehicleData.displayName or "Vehicle"))

                    -- Handle interaction
                    if IsControlJustPressed(0, 38) then -- E key
                        OpenVehicleInteractionMenu(nearestVehicle)
                    end
                end
            end

            -- Check for garage proximity
            for _, garage in ipairs(GARAGE_LOCATIONS) do
                local distance = #(playerCoords - garage.coords)
                if distance <= VEHICLE_CONFIG.garageDistance then
                    DrawText3D(garage.coords.x, garage.coords.y, garage.coords.z + 1.0,
                        string.format("~w~%s~n~~g~[F] ~w~Open Garage", garage.name))

                    if IsControlJustPressed(0, 23) then -- F key
                        OpenGarageMenu(garage)
                    end
                end
            end
        end
    end)
end

-- Start key bindings (matches original key system)
function StartKeyBindings()
    CreateThread(function()
        while true do
            Wait(0)

            -- Vehicle lock toggle (matches original lock system)
            if IsControlJustPressed(0, 182) then -- L key
                local vehicle = GetVehiclePedIsIn(PlayerPedId(), true)
                if vehicle ~= 0 and HasVehicleKeys(vehicle) then
                    ToggleVehicleLock(vehicle)
                end
            end

            -- Engine toggle (matches original engine system)
            if IsControlJustPressed(0, 47) then -- G key
                local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)
                if vehicle ~= 0 and HasVehicleKeys(vehicle) then
                    ToggleVehicleEngine(vehicle)
                end
            end
        end
    end)
end

-- Check if player has vehicle keys (matches original fn_hasKeys.sqf)
function HasVehicleKeys(vehicle)
    if not DoesEntityExist(vehicle) then return false end

    local playerData = exports['olympus-core']:GetPlayerData()
    if not playerData then return false end

    -- Check if it's an owned vehicle (matches original oev_vehicles check)
    local vehicleState = Entity(vehicle).state
    if vehicleState.owner == playerData.identifier then
        return true
    end

    -- Check if player is in key holders list (matches original keyPlayers check)
    if vehicleState.keyPlayers then
        for _, keyHolder in ipairs(vehicleState.keyPlayers) do
            if keyHolder == playerData.identifier then
                return true
            end
        end
    end

    -- Check gang vehicle access (matches original gang vehicle logic)
    if vehicleState.isGangVehicle and playerData.gang and playerData.gang.id > 0 then
        if vehicleState.owner == playerData.gang.id then
            return true
        end
    end

    return false
end

-- Get vehicle data from entity
function GetVehicleData(vehicle)
    if not DoesEntityExist(vehicle) then return nil end

    local vehicleState = Entity(vehicle).state
    return {
        id = vehicleState.vehicleId,
        owner = vehicleState.owner,
        isGangVehicle = vehicleState.isGangVehicle,
        locked = vehicleState.locked,
        keyPlayers = vehicleState.keyPlayers,
        inventory = vehicleState.inventory,
        insured = vehicleState.insured,
        displayName = GetDisplayNameFromVehicleModel(GetEntityModel(vehicle)),
        plate = GetVehicleNumberPlateText(vehicle)
    }
end

-- Toggle vehicle lock (matches original lock system)
function ToggleVehicleLock(vehicle)
    if not DoesEntityExist(vehicle) or not HasVehicleKeys(vehicle) then return end

    local vehicleState = Entity(vehicle).state
    local newLockState = not vehicleState.locked

    -- Update state
    Entity(vehicle).state:set('locked', newLockState, true)

    -- Apply lock to vehicle
    SetVehicleDoorsLocked(vehicle, newLockState and 2 or 1)

    -- Play sound and show notification
    PlaySoundFrontend(-1, newLockState and "Remote_Control_Close" or "Remote_Control_Open", "PI_Menu_Sounds", true)

    exports['olympus-ui']:ShowNotification({
        type = 'info',
        title = 'Vehicle',
        message = newLockState and 'Vehicle locked' or 'Vehicle unlocked'
    })
end

-- Toggle vehicle engine (matches original engine system)
function ToggleVehicleEngine(vehicle)
    if not DoesEntityExist(vehicle) or not HasVehicleKeys(vehicle) then return end

    local engineRunning = GetIsVehicleEngineRunning(vehicle)
    SetVehicleEngineOn(vehicle, not engineRunning, false, true)

    exports['olympus-ui']:ShowNotification({
        type = 'info',
        title = 'Vehicle',
        message = engineRunning and 'Engine turned off' or 'Engine started'
    })
end

-- Open vehicle interaction menu
function OpenVehicleInteractionMenu(vehicle)
    if not DoesEntityExist(vehicle) then return end

    local vehicleData = GetVehicleData(vehicle)
    if not vehicleData then return end

    local hasKeys = HasVehicleKeys(vehicle)
    local menuOptions = {}

    -- Basic options available to everyone
    table.insert(menuOptions, {
        title = "Vehicle Information",
        description = string.format("Plate: %s | Owner: %s", vehicleData.plate, vehicleData.owner or "Unknown"),
        disabled = true
    })

    if hasKeys then
        -- Options for key holders
        table.insert(menuOptions, {
            title = vehicleData.locked and "Unlock Vehicle" or "Lock Vehicle",
            description = "Toggle vehicle lock status",
            onSelect = function()
                ToggleVehicleLock(vehicle)
            end
        })

        table.insert(menuOptions, {
            title = "Toggle Engine",
            description = "Start or stop the engine",
            onSelect = function()
                ToggleVehicleEngine(vehicle)
            end
        })

        table.insert(menuOptions, {
            title = "Open Trunk",
            description = "Access vehicle storage",
            onSelect = function()
                OpenVehicleInventory(vehicle)
            end
        })

        -- Owner-only options
        local playerData = exports['olympus-core']:GetPlayerData()
        if playerData and vehicleData.owner == playerData.identifier then
            table.insert(menuOptions, {
                title = "Give Keys",
                description = "Give keys to nearby player",
                onSelect = function()
                    GiveVehicleKeys(vehicle)
                end
            })

            table.insert(menuOptions, {
                title = "Store Vehicle",
                description = "Store vehicle in garage",
                onSelect = function()
                    StoreVehicle(vehicle)
                end
            })
        end
    else
        -- Options for non-key holders
        table.insert(menuOptions, {
            title = "Lockpick Vehicle",
            description = "Attempt to break into the vehicle",
            onSelect = function()
                AttemptLockpick(vehicle)
            end
        })
    end

    -- Open context menu via olympus-ui
    exports['olympus-ui']:OpenContextMenu({
        title = string.format("Vehicle - %s", vehicleData.displayName),
        options = menuOptions
    })
end

-- Open garage menu
function OpenGarageMenu(garage)
    local playerData = exports['olympus-core']:GetPlayerData()
    if not playerData then return end

    -- Check if player has access to this garage type
    local hasAccess = false
    if garage.type == "civilian" then
        hasAccess = true
    elseif garage.type == "gang" and playerData.gang and playerData.gang.id > 0 then
        hasAccess = true
    elseif garage.type == "cop" and playerData.job == "cop" then
        hasAccess = true
    elseif garage.type == "medical" and playerData.job == "medical" then
        hasAccess = true
    end

    if not hasAccess then
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Garage',
            message = 'You do not have access to this garage'
        })
        return
    end

    -- Request player vehicles from server
    TriggerServerEvent('olympus:server:requestPlayerVehicles')
end

-- Helper functions
function DrawText3D(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())

    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x, _y)

    local factor = (string.len(text)) / 370
    DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 41, 11, 41, 68)
end

-- Initialize when core is ready
CreateThread(function()
    while not exports['olympus-core']:IsPlayerLoaded() do
        Wait(1000)
    end

    Wait(2000) -- Additional delay for other systems
    InitializeVehicleSystem()
end)

-- Exports
exports('HasVehicleKeys', HasVehicleKeys)
exports('GetVehicleData', GetVehicleData)
exports('GetNearestGarage', function()
    local playerCoords = GetEntityCoords(PlayerPedId())
    local nearestGarage = nil
    local nearestDistance = math.huge

    for _, garage in ipairs(GARAGE_LOCATIONS) do
        local distance = #(playerCoords - garage.coords)
        if distance < nearestDistance then
            nearestGarage = garage
            nearestDistance = distance
        end
    end

    return nearestGarage, nearestDistance
end)

print("^2[Olympus Vehicles]^7 Client main loaded!")
