// Olympus Groups UI Script

let currentGroups = [];
let currentGroup = null;
let currentMembers = [];
let selectedGroupId = null;
let config = {};

// Initialize
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.action) {
        case 'openGroupBrowser':
            openGroupBrowser(data.groups, data.config);
            break;
        case 'openGroupManagement':
            openGroupManagement(data.group, data.members, data.config);
            break;
        case 'openCreateGroup':
            openCreateGroupDialog(data.config);
            break;
        case 'openJoinGroup':
            openJoinGroupDialog(data.group, data.config);
            break;
        case 'updateGroupList':
            updateGroupList(data.groups);
            break;
        case 'updateGroupMembers':
            updateGroupMembers(data.members, data.group);
            break;
        case 'closeMenu':
            closeMenu();
            break;
    }
});

// Group Browser Functions
function openGroupBrowser(groups, cfg) {
    config = cfg;
    currentGroups = groups;
    
    document.getElementById('groupMenu').style.display = 'flex';
    document.getElementById('groupBrowser').style.display = 'block';
    document.getElementById('groupManagement').style.display = 'none';
    
    renderGroupList();
}

function renderGroupList() {
    const groupList = document.getElementById('groupList');
    groupList.innerHTML = '';
    
    if (currentGroups.length === 0) {
        groupList.innerHTML = '<div class="no-groups">No groups available. Create one to get started!</div>';
        return;
    }
    
    currentGroups.forEach(group => {
        const groupItem = document.createElement('div');
        groupItem.className = 'group-item';
        groupItem.onclick = () => selectGroup(group.id);
        
        groupItem.innerHTML = `
            <div class="group-info-left">
                <div class="group-name">${group.name}</div>
                <div class="group-details">Leader: ${group.leader}</div>
            </div>
            <div class="group-status">
                <span class="status-badge ${group.locked ? 'status-locked' : 'status-unlocked'}">
                    ${group.locked ? 'Locked' : 'Open'}
                </span>
                <span class="member-count">${group.memberCount}/${group.maxMembers}</span>
            </div>
        `;
        
        groupList.appendChild(groupItem);
    });
}

function selectGroup(groupId) {
    selectedGroupId = groupId;
    const group = currentGroups.find(g => g.id === groupId);
    
    if (group) {
        if (group.locked) {
            openJoinGroupDialog(group, config);
        } else {
            // Join directly for unlocked groups
            fetch(`https://${GetParentResourceName()}/joinGroup`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ groupId: groupId, password: '' })
            });
        }
    }
}

function updateGroupList(groups) {
    currentGroups = groups;
    renderGroupList();
}

// Group Management Functions
function openGroupManagement(group, members, cfg) {
    config = cfg;
    currentGroup = group;
    currentMembers = members;
    
    document.getElementById('groupMenu').style.display = 'flex';
    document.getElementById('groupBrowser').style.display = 'none';
    document.getElementById('groupManagement').style.display = 'block';
    
    renderGroupManagement();
}

function renderGroupManagement() {
    if (!currentGroup) return;
    
    // Update group info
    document.getElementById('groupTitle').textContent = `Group: ${currentGroup.name}`;
    document.getElementById('groupName').textContent = currentGroup.name;
    document.getElementById('memberCount').textContent = `${currentGroup.memberCount}/${config.Groups.management.maxMembers} Members`;
    document.getElementById('lockStatus').textContent = currentGroup.locked ? 'Locked' : 'Unlocked';
    
    // Show/hide leader controls
    const leaderControls = document.getElementById('leaderControls');
    const memberControls = document.getElementById('memberControls');
    
    if (currentGroup.isLeader) {
        leaderControls.style.display = 'flex';
        memberControls.style.display = 'none';
        
        // Update lock toggle button
        const lockToggle = document.getElementById('lockToggle');
        lockToggle.textContent = currentGroup.locked ? 'Unlock Group' : 'Lock Group';
        lockToggle.onclick = () => toggleLock();
    } else {
        leaderControls.style.display = 'none';
        memberControls.style.display = 'flex';
    }
    
    renderMemberList();
}

function renderMemberList() {
    const memberList = document.getElementById('memberList');
    memberList.innerHTML = '';
    
    currentMembers.forEach(member => {
        const memberItem = document.createElement('div');
        memberItem.className = 'member-item';
        
        memberItem.innerHTML = `
            <div class="member-info">
                <div class="member-name">
                    ${member.name}
                    ${member.isLeader ? '<span class="leader-badge">LEADER</span>' : ''}
                </div>
                <div class="member-rank">${member.rank}</div>
            </div>
            <div class="member-actions">
                ${currentGroup.isLeader && !member.isLeader ? 
                    `<button class="btn btn-danger btn-small" onclick="kickMember(${member.id})">Kick</button>` : 
                    ''
                }
            </div>
        `;
        
        memberList.appendChild(memberItem);
    });
}

function updateGroupMembers(members, group) {
    currentMembers = members;
    currentGroup = group;
    renderGroupManagement();
}

// Dialog Functions
function openCreateGroupDialog(cfg) {
    config = cfg;
    document.getElementById('createGroupDialog').style.display = 'flex';
    document.getElementById('groupNameInput').value = '';
    document.getElementById('groupPasswordInput').value = '';
    document.getElementById('groupNameInput').focus();
}

function openJoinGroupDialog(group, cfg) {
    config = cfg;
    selectedGroupId = group.id;
    
    document.getElementById('joinGroupDialog').style.display = 'flex';
    document.getElementById('joinGroupTitle').textContent = `Join Group: ${group.name}`;
    document.getElementById('joinPasswordInput').value = '';
    
    // Show/hide password field based on lock status
    const passwordGroup = document.getElementById('passwordGroup');
    if (group.locked) {
        passwordGroup.style.display = 'block';
        document.getElementById('joinPasswordInput').focus();
    } else {
        passwordGroup.style.display = 'none';
    }
}

// Action Functions
function createGroup() {
    const name = document.getElementById('groupNameInput').value.trim();
    const password = document.getElementById('groupPasswordInput').value;
    
    if (!name) {
        alert('Please enter a group name');
        return;
    }
    
    fetch(`https://${GetParentResourceName()}/createGroup`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: name, password: password })
    });
    
    closeCreateGroup();
}

function joinGroup() {
    const password = document.getElementById('joinPasswordInput').value;
    
    if (!selectedGroupId) return;
    
    fetch(`https://${GetParentResourceName()}/joinGroup`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ groupId: selectedGroupId, password: password })
    });
    
    closeJoinGroup();
}

function leaveGroup() {
    if (confirm('Are you sure you want to leave the group?')) {
        fetch(`https://${GetParentResourceName()}/leaveGroup`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
    }
}

function kickMember(targetId) {
    if (confirm('Are you sure you want to kick this member?')) {
        fetch(`https://${GetParentResourceName()}/kickMember`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ targetId: targetId })
        });
    }
}

function toggleLock() {
    if (currentGroup.locked) {
        fetch(`https://${GetParentResourceName()}/unlockGroup`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
    } else {
        fetch(`https://${GetParentResourceName()}/lockGroup`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
    }
}

function refreshGroupList() {
    fetch(`https://${GetParentResourceName()}/refreshGroupList`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
    });
}

// Close Functions
function closeMenu() {
    document.getElementById('groupMenu').style.display = 'none';
    
    fetch(`https://${GetParentResourceName()}/closeMenu`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
    });
}

function closeCreateGroup() {
    document.getElementById('createGroupDialog').style.display = 'none';
}

function closeJoinGroup() {
    document.getElementById('joinGroupDialog').style.display = 'none';
}

function openCreateGroup() {
    openCreateGroupDialog(config);
}

// Keyboard Events
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        if (document.getElementById('createGroupDialog').style.display === 'flex') {
            closeCreateGroup();
        } else if (document.getElementById('joinGroupDialog').style.display === 'flex') {
            closeJoinGroup();
        } else {
            closeMenu();
        }
    }
});

// Form Submit Events
document.getElementById('groupNameInput').addEventListener('keydown', function(event) {
    if (event.key === 'Enter') {
        createGroup();
    }
});

document.getElementById('groupPasswordInput').addEventListener('keydown', function(event) {
    if (event.key === 'Enter') {
        createGroup();
    }
});

document.getElementById('joinPasswordInput').addEventListener('keydown', function(event) {
    if (event.key === 'Enter') {
        joinGroup();
    }
});

// Utility Functions
function GetParentResourceName() {
    return 'olympus-groups';
}
