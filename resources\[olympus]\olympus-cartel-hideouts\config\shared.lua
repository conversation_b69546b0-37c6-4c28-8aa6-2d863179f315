Config = {}

-- Cartel & Hideout System Settings
Config.CartelHideouts = {
    -- Hideout Capture Settings
    hideoutCapture = {
        enabled = true,
        captureTime = 120000, -- 2 minutes (120 seconds)
        captureRate = 0.0075, -- Rate for neutral hideouts
        contestedRate = 0.0045, -- Rate for contested hideouts
        maxDistance = 25.0, -- Distance to hideout building
        requiredGang = true, -- Must be in a gang to capture
        notificationCooldown = 300 -- 5 minutes between notifications
    },
    
    -- Black Market Capture Settings
    blackMarketCapture = {
        enabled = true,
        captureTime = 100000, -- ~100 seconds (matching original)
        captureRate = 0.01, -- Capture rate per tick
        maxDistance = 12.0, -- Distance to black market sign
        requiredGang = true, -- Must be in a gang to capture
        notificationCooldown = 300, -- 5 minutes between notifications
        randomDelay = {5, 10} -- Random delay after capture (5-10 seconds)
    },
    
    -- Processing Discounts
    processingDiscounts = {
        blackMarket = 0.15, -- 15% discount at owned black markets
        hideout = 0.10 -- 10% discount for hideout owners
    }
}

-- Hideout Locations (based on original Olympus)
Config.Hideouts = {
    {
        id = 1,
        name = "North Hideout",
        coords = vector3(2892.73, 4631.02, 48.55),
        buildingModel = "prop_mil_hangar_02", -- FiveM equivalent
        flagCoords = vector3(2892.73, 4631.02, 50.55),
        markerType = "hd_warning",
        blipSprite = 437,
        blipColor = 1,
        blipScale = 0.8
    },
    {
        id = 2,
        name = "South Hideout", 
        coords = vector3(1396.32, -2080.44, 52.00),
        buildingModel = "prop_mil_hangar_02",
        flagCoords = vector3(1396.32, -2080.44, 54.00),
        markerType = "hd_warning",
        blipSprite = 437,
        blipColor = 1,
        blipScale = 0.8
    },
    {
        id = 3,
        name = "East Hideout",
        coords = vector3(2447.89, 1576.32, 32.68),
        buildingModel = "prop_mil_hangar_02",
        flagCoords = vector3(2447.89, 1576.32, 34.68),
        markerType = "hd_warning",
        blipSprite = 437,
        blipColor = 1,
        blipScale = 0.8
    },
    {
        id = 4,
        name = "West Hideout",
        coords = vector3(-1518.77, 2725.43, 18.80),
        buildingModel = "prop_mil_hangar_02",
        flagCoords = vector3(-1518.77, 2725.43, 20.80),
        markerType = "hd_warning",
        blipSprite = 437,
        blipColor = 1,
        blipScale = 0.8
    }
}

-- Black Market Locations (based on original Olympus)
Config.BlackMarkets = {
    {
        id = "bmOne",
        name = "Cocaine Black Market",
        drugType = "cocaine",
        coords = vector3(2434.12, 4969.12, 42.35),
        signCoords = vector3(2434.12, 4969.12, 42.35),
        signModel = "prop_forsale_sign_02", -- FiveM equivalent
        markerType = "mil_marker",
        blipSprite = 500,
        blipColor = 1,
        blipScale = 0.7,
        processingDiscount = 0.15
    },
    {
        id = "bmTwo", 
        name = "Weed Black Market",
        drugType = "weed",
        coords = vector3(2892.73, 4631.02, 48.55),
        signCoords = vector3(2892.73, 4631.02, 48.55),
        signModel = "prop_forsale_sign_02",
        markerType = "mil_marker",
        blipSprite = 500,
        blipColor = 2,
        blipScale = 0.7,
        processingDiscount = 0.15
    },
    {
        id = "bmThree",
        name = "Heroin Black Market", 
        drugType = "heroin",
        coords = vector3(1396.32, -2080.44, 52.00),
        signCoords = vector3(1396.32, -2080.44, 52.00),
        signModel = "prop_forsale_sign_02",
        markerType = "mil_marker",
        blipSprite = 500,
        blipColor = 3,
        blipScale = 0.7,
        processingDiscount = 0.15
    },
    {
        id = "bmFour",
        name = "Mushroom Black Market",
        drugType = "mushroom", 
        coords = vector3(2447.89, 1576.32, 32.68),
        signCoords = vector3(2447.89, 1576.32, 32.68),
        signModel = "prop_forsale_sign_02",
        markerType = "mil_marker",
        blipSprite = 500,
        blipColor = 5,
        blipScale = 0.7,
        processingDiscount = 0.15
    }
}

-- Flag Textures (random selection for hideouts)
Config.FlagTextures = {
    "prop_flag_uk",
    "prop_flag_us",
    "prop_flag_canada",
    "prop_flag_france",
    "prop_flag_germany",
    "prop_flag_italy",
    "prop_flag_japan",
    "prop_flag_russia"
}

-- Animation Settings
Config.Animations = {
    capturing = {
        dict = "anim@gangops@facility@servers@bodysearch@",
        anim = "player_search",
        flag = 49
    }
}

-- Notification Settings
Config.Notifications = {
    hideout = {
        mustBeInGang = "You must be in a gang to capture hideouts",
        alreadyOwned = "Your gang already controls this hideout",
        alreadyCapturing = "This hideout is already being captured",
        confirmCapture = "This hideout is controlled by %s. Do you want to capture it?",
        capturing = "Capturing hideout...",
        captureSuccess = "Hideout has been captured",
        captureCancel = "Hideout capture cancelled",
        broadcastCapture = "%s has captured the %s for the %s gang",
        tooFar = "You must stay near the hideout to capture it"
    },
    blackMarket = {
        mustBeInGang = "You must be in a gang to capture the Black Market",
        alreadyOwned = "Your gang already owns the Black Market",
        alreadyCapturing = "You are already performing another action",
        capturing = "Capturing Black Market",
        captureSuccess = "Your gang now owns this Black Market and can use it for processing %s",
        stayNear = "You need to remain within 12m of the sign to continue capturing",
        tooFar = "You must stay near the sign to capture",
        broadcastCapture = "The %s Black Market has been captured by the %s",
        gangNotification = "Another gang is capturing your Black Market used for %s processing",
        gangCaptured = "Another gang has captured your Black Market used for %s processing"
    }
}
