-- Olympus Suicide Vest System - Server Main
-- Based on original fn_suicideBomb.sqf from Olympus Altis Life

local OlympusSuicideVest = {}

-- Database initialization
CreateThread(function()
    -- Wait for database to be ready
    while not exports['olympus-core']:IsDBReady() do
        Wait(100)
    end
    
    -- Initialize suicide vest logs table
    exports['olympus-core']:ExecuteQuery([[
        CREATE TABLE IF NOT EXISTS suicide_vest_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            player_id VARCHAR(50) NOT NULL,
            player_name VARCHAR(100) NOT NULL,
            action_type ENUM('detonation','attempt','jammer_block') NOT NULL,
            location VARCHAR(255) NOT NULL,
            jammer_zone VARCHAR(100) DEFAULT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            server VARCHAR(50) DEFAULT 'olympus-1'
        )
    ]], {})
    
    -- Initialize suicide vest stats table
    exports['olympus-core']:ExecuteQuery([[
        CREATE TABLE IF NOT EXISTS suicide_vest_stats (
            id INT AUTO_INCREMENT PRIMARY KEY,
            player_id VARCHAR(50) NOT NULL,
            total_detonations INT DEFAULT 0,
            total_attempts INT DEFAULT 0,
            jammer_blocks INT DEFAULT 0,
            last_detonation TIMESTAMP NULL DEFAULT NULL,
            UNIQUE KEY player_id (player_id)
        )
    ]], {})
    
    print("[Olympus Suicide Vest] Database initialized")
end)

-- Utility Functions
function OlympusSuicideVest.GetPlayerData(src)
    local success, result = pcall(exports['olympus-core'].GetPlayerData, src)
    if success and result then
        return result
    end
    return nil
end

function OlympusSuicideVest.Notify(src, message, type)
    TriggerClientEvent('olympus-core:client:notify', src, message, type or 'info')
end

function OlympusSuicideVest.GetPlayerCoords(src)
    local ped = GetPlayerPed(src)
    return GetEntityCoords(ped)
end

function OlympusSuicideVest.IsPlayerInVehicle(src)
    local ped = GetPlayerPed(src)
    return GetVehiclePedIsIn(ped, false) ~= 0
end

function OlympusSuicideVest.BroadcastMessage(message)
    TriggerClientEvent('chat:addMessage', -1, {
        color = {255, 0, 0},
        multiline = true,
        args = {"[SUICIDE VEST]", message}
    })
end

-- Jammer Zone Checking (Exact Olympus Implementation)
function OlympusSuicideVest.IsInJammerZone(coords)
    for _, zone in ipairs(Config.SuicideVest.jammerZones) do
        if zone.enabled ~= false then -- Check if zone is enabled (default true)
            local distance = #(coords - zone.coords)
            if distance < zone.radius then
                return true, zone
            end
        end
    end
    return false, nil
end

-- Vest Validation (Exact Olympus Implementation)
function OlympusSuicideVest.CanDetonate(src)
    local playerData = OlympusSuicideVest.GetPlayerData(src)
    if not playerData then
        return false, "Player data not found"
    end
    
    -- Check if player is alive
    if Config.SuicideVest.validation.requireAlive then
        local ped = GetPlayerPed(src)
        if GetEntityHealth(ped) <= 0 then
            return false, Config.SuicideVest.messages.notAlive
        end
    end
    
    -- Check if player is in vehicle (exact Olympus check)
    if Config.SuicideVest.validation.preventInVehicle then
        if OlympusSuicideVest.IsPlayerInVehicle(src) then
            return false, Config.SuicideVest.messages.inVehicle
        end
    end
    
    -- Check jammer zones (exact Olympus implementation)
    if Config.SuicideVest.validation.checkJammerZones then
        local coords = OlympusSuicideVest.GetPlayerCoords(src)
        local inJammer, jammerZone = OlympusSuicideVest.IsInJammerZone(coords)
        if inJammer then
            return false, jammerZone.message
        end
    end
    
    return true, "Can detonate"
end

-- Suicide Vest Detonation (Exact Olympus Implementation)
function OlympusSuicideVest.DetonateVest(src)
    local playerData = OlympusSuicideVest.GetPlayerData(src)
    if not playerData then return end
    
    local canDetonate, reason = OlympusSuicideVest.CanDetonate(src)
    if not canDetonate then
        OlympusSuicideVest.Notify(src, reason, 'error')
        
        -- Log failed attempt
        if Config.SuicideVest.logging.logAttempts then
            OlympusSuicideVest.LogSuicideVest(src, 'attempt', reason)
        end
        
        -- Check if it was blocked by jammer
        local coords = OlympusSuicideVest.GetPlayerCoords(src)
        local inJammer, jammerZone = OlympusSuicideVest.IsInJammerZone(coords)
        if inJammer and Config.SuicideVest.logging.logJammerBlocks then
            OlympusSuicideVest.LogSuicideVest(src, 'jammer_block', jammerZone.name)
            OlympusSuicideVest.UpdatePlayerStats(src, 'jammer_block')
        end
        
        return
    end
    
    -- Get player coordinates for explosion
    local coords = OlympusSuicideVest.GetPlayerCoords(src)
    local playerName = GetPlayerName(src)
    
    -- Create explosion at player location (exact Olympus implementation)
    TriggerClientEvent('olympus-suicide-vest:client:createExplosion', -1, coords, Config.SuicideVest.explosionType, Config.SuicideVest.explosionDamage, Config.SuicideVest.explosionRadius)
    
    -- Kill the player (exact Olympus implementation)
    local ped = GetPlayerPed(src)
    SetEntityHealth(ped, 0)
    
    -- Remove vest from player
    TriggerClientEvent('olympus-suicide-vest:client:removeVest', src)
    
    -- Broadcast message (exact Olympus implementation)
    local message = string.format(Config.SuicideVest.messages.detonationSuccess, playerName)
    OlympusSuicideVest.BroadcastMessage(message)
    
    -- Log detonation
    if Config.SuicideVest.logging.logDetonations then
        OlympusSuicideVest.LogSuicideVest(src, 'detonation', string.format("Coordinates: %s", coords))
    end
    
    -- Update player statistics
    if Config.SuicideVest.statistics.enabled then
        OlympusSuicideVest.UpdatePlayerStats(src, 'detonation')
    end
    
    print(string.format("[Olympus Suicide Vest] %s detonated suicide vest at %s", playerName, coords))
end

-- Database Functions
function OlympusSuicideVest.LogSuicideVest(src, actionType, details)
    local playerData = OlympusSuicideVest.GetPlayerData(src)
    if not playerData then return end
    
    local coords = OlympusSuicideVest.GetPlayerCoords(src)
    local location = string.format("%.2f, %.2f, %.2f", coords.x, coords.y, coords.z)
    
    exports['olympus-core']:ExecuteQuery([[
        INSERT INTO suicide_vest_logs (player_id, player_name, action_type, location, jammer_zone)
        VALUES (?, ?, ?, ?, ?)
    ]], {
        playerData.citizenid,
        GetPlayerName(src),
        actionType,
        location,
        details
    })
end

function OlympusSuicideVest.UpdatePlayerStats(src, statType)
    local playerData = OlympusSuicideVest.GetPlayerData(src)
    if not playerData then return end
    
    local updateField = ""
    if statType == 'detonation' then
        updateField = "total_detonations = total_detonations + 1, last_detonation = NOW()"
    elseif statType == 'attempt' then
        updateField = "total_attempts = total_attempts + 1"
    elseif statType == 'jammer_block' then
        updateField = "jammer_blocks = jammer_blocks + 1"
    end
    
    if updateField ~= "" then
        exports['olympus-core']:ExecuteQuery(string.format([[
            INSERT INTO suicide_vest_stats (player_id, %s)
            VALUES (?, 1)
            ON DUPLICATE KEY UPDATE %s
        ]], updateField:gsub("= [^,]+", "= 1"), updateField), {
            playerData.citizenid
        })
    end
end

-- Event Handlers
RegisterNetEvent('olympus-suicide-vest:server:detonateVest', function()
    local src = source
    OlympusSuicideVest.DetonateVest(src)
end)

RegisterNetEvent('olympus-suicide-vest:server:checkCanDetonate', function()
    local src = source
    local canDetonate, reason = OlympusSuicideVest.CanDetonate(src)
    TriggerClientEvent('olympus-suicide-vest:client:canDetonateResult', src, canDetonate, reason)
end)

-- Export Functions
exports('CanDetonate', function(src)
    return OlympusSuicideVest.CanDetonate(src)
end)

exports('DetonateVest', function(src)
    return OlympusSuicideVest.DetonateVest(src)
end)

exports('IsInJammerZone', function(coords)
    return OlympusSuicideVest.IsInJammerZone(coords)
end)

exports('LogSuicideVest', function(src, actionType, details)
    return OlympusSuicideVest.LogSuicideVest(src, actionType, details)
end)

exports('GetSuicideVestStats', function(src)
    local playerData = OlympusSuicideVest.GetPlayerData(src)
    if not playerData then return nil end
    
    local result = exports['olympus-core']:ExecuteQuery([[
        SELECT * FROM suicide_vest_stats WHERE player_id = ?
    ]], {playerData.citizenid})
    
    return result[1] or nil
end)

-- Player disconnect cleanup
AddEventHandler('playerDropped', function(reason)
    local src = source
    -- Clean up any pending suicide vest operations
    -- No specific cleanup needed for suicide vest system
end)

print("[Olympus Suicide Vest] Server module loaded - Based on original fn_suicideBomb.sqf")
