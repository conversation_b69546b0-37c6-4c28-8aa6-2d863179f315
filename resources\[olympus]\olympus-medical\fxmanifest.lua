fx_version 'cerulean'
game 'gta5'

name 'Olympus Medical System'
description 'Rescue & Recovery (R&R) medical system for Olympus Framework'
author 'Olympus Development Team'
version '1.0.0'

dependencies {
    'olympus-core'
}

shared_scripts {
    'config/shared.lua'
}

server_scripts {
    'server/main.lua'
}

client_scripts {
    'client/main.lua'
}

-- No UI files (UI handled by olympus-ui)

exports {
    'IsMedic',
    'GetMedicRank',
    'IsOnDuty',
    'IsDead',
    'GetDeathTime',
    'RequestMedic',
    'RevivePlayer',
    'GiveDopamine'
}

server_exports {
    'IsPlayerMedic',
    'GetMedicRank',
    'GetOnlineMedics',
    'GetOnlineMedicCount',
    'IsPlayerOnDuty',
    'RevivePlayer',
    'GiveDopamine',
    'SendMedicRequest'
}
