-- Olympus Player Betting System - Configuration
-- Based on original fn_betMoney.sqf from Olympus Altis Life

Config = {}

-- Betting System Settings (Exact Olympus Implementation)
Config.BettingSystem = {
    enabled = true,

    -- System Description
    description = "Players have the option to 'coinflip' their money",

    -- Betting Requirements (Exact Olympus Values)
    requirements = {
        minimumPlaytime = 1800, -- 30 minutes minimum playtime (original value)
        enabledInYMenu = true, -- Must be enabled in Y-Menu settings
        proximityDistance = 20, -- Must be within 20 meters of target

        -- Wager Limits (Exact Olympus Values)
        wagerLimits = {
            minimum = 1000, -- $1,000 minimum bet
            maximum = 5000000 -- $5,000,000 maximum bet
        },

        -- Cooldown System
        cooldown = {
            betCooldown = 180, -- 3 minutes between bets (180 seconds)
            enabled = true
        }
    },

    -- Tax System (Exact Olympus Implementation)
    taxSystem = {
        enabled = true,
        winnerTax = 0.01, -- 1% tax on winnings (0.99 multiplier)
        description = "1% tax applied to winnings"
    }
}

-- Faction Restrictions (Exact Olympus Implementation)
Config.FactionRestrictions = {
    enabled = true,

    -- APD Restrictions
    apd = {
        enabled = true,
        onDutyRestriction = true, -- Cannot bet while on duty
        offDutyAllowed = true, -- Can bet when off duty
        restrictionMessage = "Cops cannot bet!"
    },

    -- R&R Restrictions
    rnr = {
        enabled = true,
        alwaysRestricted = true, -- Cannot bet at all
        restrictionMessage = "Medics cannot bet!"
    },

    -- Admin Restrictions
    admin = {
        enabled = true,
        alwaysRestricted = true, -- Staff cannot bet
        restrictionMessage = "Staff can not bet!"
    },

    -- Civilian Permissions
    civilian = {
        enabled = true,
        fullAccess = true, -- No restrictions for civilians
        onlyPlaytimeRestriction = true -- Only playtime requirement applies
    }
}

-- Coinflip Mechanics (Exact Olympus Implementation)
Config.CoinflipSystem = {
    enabled = true,

    -- Betting Process
    process = {
        -- Random Number Generation (Exact Olympus Method)
        randomGeneration = {
            method = "math.random", -- Use math.random(0, 1)
            range = {0, 1}, -- 0 or 1 for 50/50 chance
            fair = true -- Fair 50/50 odds
        },

        -- Win/Loss Determination
        winCondition = {
            winValue = 1, -- If random = 1, target wins
            loseValue = 0, -- If random = 0, sender wins
            probability = 0.5 -- 50% chance for each outcome
        }
    },
    
    -- Money Transfer System (Exact Olympus Implementation)
    moneyTransfer = {
        -- Winner receives money to bank account (like original)
        winnerAccount = "bank", -- Money goes to bank
        winnerTax = 0.99, -- 1% tax (0.99 multiplier)

        -- Loser money deducted from cash or bank
        loserPriority = {"cash", "bank"}, -- Try cash first, then bank

        -- Instant transfer
        instantTransfer = true
    },

    -- Notification System (Exact Olympus Messages)
    notifications = {
        -- Winner Messages
        winner = {
            message = "Congrats you won the bet worth %s!",
            type = "success"
        },

        -- Loser Messages
        loser = {
            message = "You lost the bet. Better luck next time!",
            type = "error"
        },

        -- Broadcast Message (Exact Olympus Format)
        broadcast = {
            enabled = true,
            message = "%s won a bet worth %s against %s!",
            color = {255, 255, 0} -- Yellow color like original
        },

        -- Disconnect Messages
        disconnect = {
            message = "The person who bet against you left the game... what a loser..",
            type = "info"
        }
    }
}

-- Y-Menu Integration (Exact Olympus Implementation)
Config.YMenuIntegration = {
    enabled = true,

    -- Settings Location
    settingsLocation = {
        menu = "Y-Menu", -- Y-Menu
        section = "Settings", -- Settings section
        option = "Enable Betting", -- "Enable Betting" option

        -- Default State
        defaultState = {
            enabled = false, -- Disabled by default
            requiresManualEnable = true -- Player must manually enable
        }
    },

    -- Setting Validation
    validation = {
        enabled = true,
        checkBeforeBetting = true,
        errorMessage = "You currently have betting disabled!",
        redirectToSettings = true
    }
}

-- Validation Messages (Exact Olympus Implementation)
Config.ValidationMessages = {
    -- Cooldown Messages
    cooldown = "You have already bet money recently. Please try again later.",

    -- Admin Restriction
    adminRestriction = "Staff can not bet!",

    -- Faction Restrictions
    medicRestriction = "Medics cannot bet!",
    copRestriction = "Cops cannot bet!",

    -- State Restrictions
    alreadyInBet = "You are already in a bet!",
    bettingDisabled = "You currently have betting disabled!",

    -- Playtime Restriction
    playtimeRestriction = "You must have at least 30 hours on the server to bet!",

    -- Amount Validation
    minimumBet = "You have to bet at least $1000.",
    maximumBet = "You may not bet more than $5,000,000 at a time.",
    insufficientFunds = "You do not have that much money to bet!",

    -- Target Validation
    invalidTarget = "Invalid target player!",
    targetRestricted = "The other player is on restrictions and cannot be bet against.",
    targetMedic = "You cannot bet medics!",
    playerRestricted = "You are on restrictions and cannot bet.",
    tooFarAway = "The other player is too far away! Try again.",

    -- Confirmation Messages
    targetInUse = "The other player is currently busy.",
    targetNoMoney = "The other player does not have enough money.",
    targetInBet = "The other player is already in a bet.",
    targetCooldown = "The other player has already bet recently.",
    targetDisabled = "The other player has betting disabled.",
    targetPlaytime = "The other player does not have enough playtime.",

    -- Decline Messages
    betDeclined = "The other player declined your bet.",
    betTimeout = "The bet request timed out."
}

-- Error Codes (Exact Olympus Implementation)
Config.ErrorCodes = {
    failU = "targetInUse",      -- Target in use
    failM = "targetNoMoney",    -- Target no money
    failB = "targetInBet",      -- Target in bet
    failC = "targetCooldown",   -- Target cooldown
    failE = "targetDisabled",   -- Target disabled
    failT = "targetPlaytime",   -- Target playtime
    failD = "insufficientFunds", -- Insufficient funds for payout
    no = "betDeclined",         -- Bet declined
    timeout = "betTimeout"      -- Bet timeout
}

return Config
