fx_version 'cerulean'
game 'gta5'

name 'Olympus Legal System'
description 'Complete legal and justice system with lawyers and pardons'
author 'Olympus Development Team'
version '1.0.0'

-- Dependencies
dependencies {
    'olympus-core',
    'olympus-ui',
    'olympus-apd',
    'oxmysql'
}

-- Shared scripts
shared_scripts {
    'config/shared.lua'
}

-- Client scripts
client_scripts {
    'client/main.lua',
    'client/lawyer_system.lua',
    'client/ticket_system.lua',
    'client/pardon_system.lua'
}

-- Server scripts
server_scripts {
    '@oxmysql/lib/MySQL.lua',
    'server/main.lua',
    'server/lawyer_system.lua',
    'server/ticket_processing.lua',
    'server/pardon_system.lua',
    'server/doj_integration.lua'
}

-- Exports
exports {
    'IsPlayerLawyer',
    'ProcessTicket',
    'RequestPardon'
}

server_exports {
    'RegisterLawyer',
    'IssueTicket',
    'ProcessPardon',
    'GetPlayerCharges'
}
