-- Olympus Gang System - Complete Implementation
-- Based on exact Olympus Altis Life gang system with all mechanics

Config = {}

-- Gang Creation Settings (Exact Olympus Implementation)
Config.GangCreation = {
    enabled = true,
    cost = 50000, -- Exactly $50,000 deducted from bank account
    maxGangName = 25, -- Maximum 25 characters for gang name
    uniqueNames = true, -- Gang names must be unique

    -- Gang creation process (exact Olympus steps)
    process = {
        openPlayerMenu = 'Y', -- Press Y to open player interface
        gangIcon = 'second_row_second_icon', -- Person wearing ski mask
        clickCreate = true,
        enterName = true,
        confirmPayment = true -- $50k deducted upon confirmation
    }
}

-- Gang Tags System (Exact Olympus Implementation)
Config.GangTags = {
    enabled = true,
    maxCharacters = 12, -- Maximum 12 characters for tag
    forcedDelimiters = true, -- Must use delimiters like [TAG], |TAG|, etc.
    defaultTag = '[]', -- Default tag if none set

    -- Tag display locations
    displayIn = {
        messageList = true,
        wantedList = true,
        giveList = true,
        mapPlayerList = false -- Not shown on map player list
    },

    -- Forced tags for factions
    factionTags = {
        apd = 'Police |', -- APD members automatically tagged
        rnr = '[Medic]' -- R&R members automatically tagged
    },

    -- Tag validation
    validation = {
        noEmptySpaces = true, -- Cannot be all empty spaces
        staffForced = true, -- Staff can force tags if invalid
        namingPolicy = true -- Must comply with naming policy
    }
}

-- Gang Rank System (Exact Olympus Implementation)
Config.GangRanks = {
    [0] = { -- Member
        name = 'Member',
        permissions = {
            depositFunds = true,
            viewLedger = true,
            retrievePersonalVehicles = true,
            repairShed = true
        }
    },
    [1] = { -- Rank 1
        name = 'Trusted Member',
        permissions = {
            depositFunds = true,
            viewLedger = true,
            retrievePersonalVehicles = true,
            repairShed = true,
            accessShedStorage = true
        }
    },
    [2] = { -- Rank 2
        name = 'Lieutenant',
        permissions = {
            depositFunds = true,
            viewLedger = true,
            retrievePersonalVehicles = true,
            repairShed = true,
            accessShedStorage = true,
            retrieveGangVehicles = true,
            manageGangVehicles = true
        }
    },
    [3] = { -- Rank 3
        name = 'Captain',
        permissions = {
            depositFunds = true,
            viewLedger = true,
            retrievePersonalVehicles = true,
            repairShed = true,
            accessShedStorage = true,
            retrieveGangVehicles = true,
            manageGangVehicles = true,
            inviteKickMembers = true,
            manageGangWars = true,
            payShedRent = true,
            viewFullLedger = true -- On stats page
        }
    },
    [4] = { -- Rank 4 (Co-Leader)
        name = 'Co-Leader',
        permissions = {
            depositFunds = true,
            viewLedger = true,
            retrievePersonalVehicles = true,
            repairShed = true,
            accessShedStorage = true,
            retrieveGangVehicles = true,
            manageGangVehicles = true,
            inviteKickMembers = true,
            manageGangWars = true,
            payShedRent = true,
            viewFullLedger = true,
            transferFundsOffline = true, -- 8% tax rate
            withdrawFunds = true,
            upgradeShed = true,
            manageGangExperience = true
        }
    },
    [5] = { -- Rank 5 (Leader)
        name = 'Leader',
        permissions = {
            depositFunds = true,
            viewLedger = true,
            retrievePersonalVehicles = true,
            repairShed = true,
            accessShedStorage = true,
            retrieveGangVehicles = true,
            manageGangVehicles = true,
            inviteKickMembers = true,
            manageGangWars = true,
            payShedRent = true,
            viewFullLedger = true,
            transferFundsOffline = true,
            withdrawFunds = true,
            upgradeShed = true,
            manageGangExperience = true,
            purchaseSellShed = true,
            disbandGang = true,
            promoteToRank5 = true -- Only leader can promote to rank 5
        }
    }
}

-- Gang Shed System (Exact Olympus Implementation)
Config.GangShed = {
    enabled = true,

    -- Purchase Requirements (Exact Olympus)
    purchaseRequirements = {
        minMembers = 8, -- Must have 8 members on roster
        minFunds = 20000000, -- $20 million in gang funds
        minActiveMembers = 5, -- At least 5 active members
        minCombinedHours = 10 -- 10 hours combined between active members
    },

    -- Pricing (Exact Olympus)
    pricing = {
        initialPurchase = 20000000, -- $20 million
        sellPrice = 15000000, -- $15 million (75% return)

        -- Monthly rent calculation
        rentFormula = 'virtualStorage * 50', -- $50 per virtual storage item

        -- Upgrade costs
        virtualUpgrade = {
            cost = 1500000, -- $1.5M per upgrade
            increment = 1000, -- +1000 virtual storage per upgrade
            sellPrice = 375000 -- $375k return per upgrade
        },
        physicalUpgrade = {
            cost = 1500000, -- $1.5M per upgrade
            increment = 150, -- +150 physical storage per upgrade
            sellPrice = 375000 -- $375k return per upgrade
        },
        oilUpgrade = {
            cost = 500000, -- $500k for oil storage capability
            sellPrice = 125000 -- $125k return
        },

        -- Repair cost
        repairCost = 1000000 -- $1M to repair when destroyed
    },

    -- Storage Limits (Exact Olympus)
    storage = {
        virtual = {
            base = 1000, -- Base virtual storage
            max = 10000 -- Maximum with all upgrades
        },
        physical = {
            base = 150, -- Base physical storage
            max = 900 -- Maximum with all upgrades
        }
    },

    -- Access Control (Exact Olympus)
    access = {
        garage = 'all_members', -- All gang members can use garage
        virtualStorage = 'rank_1_plus', -- Rank 1+ can access virtual storage
        physicalStorage = 'rank_1_plus', -- Rank 1+ can access physical storage
        lostIfBelow8Members = true -- Access limited if below 8 members
    },

    -- Wardrobe System
    wardrobe = {
        enabled = true,
        skinChanging = true, -- Can change clothing skins
        doubleClickToSelect = true
    },

    -- APD Raid System (Exact Olympus)
    apdRaids = {
        enabled = true,
        raidWarrant = {
            rankRequired = 'patrol_officer_plus', -- PO+ can issue raid warrant
            purpose = 'capture_criminal_or_rescue_hostage',
            storageAccess = false -- Cannot search storage with raid warrant
        },
        searchWarrant = {
            rankRequired = 'sergeant_plus', -- SGT+ can issue search warrant
            probableCause = {
                'bounty_while_interacting',
                'illegal_weapon_while_interacting',
                'evading_arrest_while_interacting',
                'visual_from_illegal_area_to_shed'
            },
            storageAccess = true -- Can search virtual and physical storage
        }
    }
}

-- Gang Vehicle System (Exact Olympus Implementation)
Config.GangVehicles = {
    enabled = true,
    maxVehicles = 75, -- Maximum 75 vehicles in gang garage

    -- Vehicle Management (Exact Olympus)
    management = {
        rankRequired = 2, -- Rank 2+ can manage gang vehicles
        addPersonalVehicle = {
            retrieveAtShed = true, -- Must retrieve vehicle at gang shed
            windowsKeyMenu = true, -- Use windows key menu to add
            transferOwnership = true -- Vehicle ownership transfers to gang
        },
        retrieveGangVehicle = {
            rankRequired = 2, -- Rank 2+ can retrieve gang vehicles
            gangGarageMenu = true -- Use "Gang Garage" instead of "Personal"
        }
    }
}

-- Gang Wars System (Exact Olympus Implementation)
Config.GangWars = {
    enabled = true,

    -- War Management (Exact Olympus)
    management = {
        rankRequired = 3, -- Rank 3+ can manage gang wars
        acceptDenyInvites = true, -- Can accept/deny war invites
        startWars = true
    },

    -- War Mechanics (Exact Olympus)
    mechanics = {
        killOnSight = true, -- Enemy gang members are KOS
        redNames = true, -- Enemy gang members have red names
        noEngagementRequired = true, -- Can kill without engagement
        titanAgainstAircraft = true, -- Can use titan against enemy aircraft

        -- War Points System
        warPoints = {
            enabled = true,
            earnedOnKill = true,
            gearDependent = true, -- Points based on gear value
            cheapVsExpensive = 'more_points', -- Cheap loadout vs expensive = more points
            shopLocation = 'rebel_outpost'
        },

        -- Group Wars (Exact Olympus)
        groupWars = {
            enabled = true,
            temporaryWar = true, -- Group members temporarily at war
            lighterRedNames = true, -- Slightly lighter red than regular wars
            warPointsEarnable = true -- Can earn war points through group wars
        }
    }
}

-- Gang Funds System (Exact Olympus Implementation)
Config.GangFunds = {
    enabled = true,

    -- Deposit/Withdrawal (Exact Olympus)
    deposits = {
        anyMember = true -- Any gang member can deposit money
    },
    withdrawals = {
        rankRequired = 4 -- Only rank 4+ can withdraw money
    },

    -- Offline Transfers (Exact Olympus)
    offlineTransfers = {
        enabled = true,
        rankRequired = 3, -- Rank 3+ can transfer to offline players
        taxRate = 0.08, -- 8% tax rate for offline transfers
        destination = 'deposit_box' -- Money goes to deposit box
    },

    -- Cartel Income (Exact Olympus)
    cartelIncome = {
        enabled = true,
        taxRate = 0.15, -- 15% of sales go to gang funds
        applicableCartels = {'cocaine', 'heroin', 'weed', 'meth'} -- When gang controls cartel
    }
}

-- Gang Creation System (Exact Olympus Implementation)
Config.GangCreation = {
    enabled = true,

    -- Creation Requirements
    requirements = {
        minMembers = 3,
        maxNameLength = 20,
        minNameLength = 3,
        allowedCharacters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789 ',
        bannedWords = {'admin', 'police', 'apd', 'medic', 'rnr', 'staff'},
        cooldownAfterLeaving = 86400 -- 24 hours
    }
}

-- Gang Territories
Config.Territories = {
    ['gang_base_1'] = {
        name = 'North Side Territory',
        coords = vector3(1392.3, 1141.9, 114.3),
        radius = 200.0,
        captureTime = 300, -- 5 minutes
        income = 5000, -- Per hour
        maxDefenders = 8,
        maxAttackers = 8,
        cooldown = 3600 -- 1 hour between captures
    },
    ['gang_base_2'] = {
        name = 'South Side Territory',
        coords = vector3(-1172.4, -1572.1, 4.7),
        radius = 200.0,
        captureTime = 300,
        income = 5000,
        maxDefenders = 8,
        maxAttackers = 8,
        cooldown = 3600
    },
    ['gang_base_3'] = {
        name = 'East Side Territory',
        coords = vector3(2447.9, 1576.9, 33.0),
        radius = 200.0,
        captureTime = 300,
        income = 5000,
        maxDefenders = 8,
        maxAttackers = 8,
        cooldown = 3600
    }
}

-- Gang Wars
Config.GangWars = {
    enabled = true,
    minMembersOnline = 3, -- Per gang
    maxDuration = 1800, -- 30 minutes
    cooldownBetweenWars = 7200, -- 2 hours
    winConditions = {
        kills = 10,
        territory_control = 600, -- 10 minutes
        objective_capture = true
    },
    rewards = {
        winner = 50000,
        loser = 10000,
        mvp = 15000
    }
}

-- Gang Hideouts
Config.GangHideouts = {
    ['hideout_1'] = {
        name = 'Abandoned Warehouse',
        coords = vector3(1392.3, 1141.9, 114.3),
        price = 250000,
        features = {
            'spawn_point',
            'vehicle_garage',
            'weapon_storage',
            'drug_processing',
            'money_laundering'
        },
        capacity = {
            vehicles = 10,
            weapons = 50,
            items = 1000
        }
    },
    ['hideout_2'] = {
        name = 'Underground Bunker',
        coords = vector3(-1172.4, -1572.1, 4.7),
        price = 500000,
        features = {
            'spawn_point',
            'vehicle_garage',
            'weapon_storage',
            'drug_processing',
            'money_laundering',
            'security_system'
        },
        capacity = {
            vehicles = 15,
            weapons = 100,
            items = 2000
        }
    }
}

-- Gang Vehicles
Config.GangVehicles = {
    ['gang_car'] = {
        name = 'Gang Vehicle',
        model = 'sultan',
        price = 50000,
        rankRequired = 2,
        modifications = {
            armor = 3,
            engine = 3,
            brakes = 2,
            transmission = 2
        }
    },
    ['gang_bike'] = {
        name = 'Gang Motorcycle',
        model = 'bati',
        price = 25000,
        rankRequired = 1,
        modifications = {
            engine = 2,
            brakes = 1,
            transmission = 1
        }
    },
    ['gang_van'] = {
        name = 'Gang Van',
        model = 'rumpo',
        price = 75000,
        rankRequired = 3,
        modifications = {
            armor = 4,
            engine = 2,
            brakes = 2,
            transmission = 2
        }
    }
}

-- Gang Weapons
Config.GangWeapons = {
    [1] = { -- Member
        'weapon_knife',
        'weapon_bat',
        'weapon_pistol'
    },
    [2] = { -- Trusted
        'weapon_knife',
        'weapon_bat',
        'weapon_pistol',
        'weapon_smg'
    },
    [3] = { -- Lieutenant
        'weapon_knife',
        'weapon_bat',
        'weapon_pistol',
        'weapon_smg',
        'weapon_assaultrifle'
    },
    [4] = { -- Captain
        'weapon_knife',
        'weapon_bat',
        'weapon_pistol',
        'weapon_smg',
        'weapon_assaultrifle',
        'weapon_sniperrifle'
    },
    [5] = { -- Leader
        'weapon_knife',
        'weapon_bat',
        'weapon_pistol',
        'weapon_smg',
        'weapon_assaultrifle',
        'weapon_sniperrifle',
        'weapon_rpg'
    }
}

-- Gang Activities
Config.GangActivities = {
    ['drug_trafficking'] = {
        name = 'Drug Trafficking',
        minRank = 2,
        income = {min = 1000, max = 5000},
        risk = 0.3, -- 30% chance of police attention
        cooldown = 1800 -- 30 minutes
    },
    ['weapon_smuggling'] = {
        name = 'Weapon Smuggling',
        minRank = 3,
        income = {min = 2000, max = 8000},
        risk = 0.4,
        cooldown = 3600 -- 1 hour
    },
    ['money_laundering'] = {
        name = 'Money Laundering',
        minRank = 4,
        income = {min = 5000, max = 15000},
        risk = 0.2,
        cooldown = 7200 -- 2 hours
    },
    ['territory_protection'] = {
        name = 'Territory Protection',
        minRank = 2,
        income = {min = 500, max = 2000},
        risk = 0.1,
        cooldown = 900 -- 15 minutes
    }
}

-- Gang Alliances
Config.GangAlliances = {
    enabled = true,
    maxAllies = 3,
    allianceDuration = 604800, -- 1 week
    benefits = {
        shared_territories = true,
        mutual_defense = true,
        shared_resources = false,
        joint_operations = true
    },
    restrictions = {
        no_friendly_fire = true,
        shared_hideout_access = false,
        alliance_chat = true
    }
}

-- Gang Reputation System
Config.ReputationSystem = {
    enabled = true,
    factors = {
        territory_control = 10,
        successful_operations = 5,
        member_count = 2,
        money_earned = 0.001, -- Per dollar
        wars_won = 25,
        wars_lost = -15
    },
    benefits = {
        [100] = {name = 'Respected', bonus_income = 0.1},
        [250] = {name = 'Feared', bonus_income = 0.2, reduced_police_response = 0.1},
        [500] = {name = 'Notorious', bonus_income = 0.3, reduced_police_response = 0.2},
        [1000] = {name = 'Legendary', bonus_income = 0.5, reduced_police_response = 0.3}
    }
}

-- Gang Communication
Config.Communication = {
    gangChat = {
        enabled = true,
        color = '#00FF00',
        prefix = '[GANG]'
    },
    allianceChat = {
        enabled = true,
        color = '#FFFF00',
        prefix = '[ALLIANCE]'
    },
    leaderChat = {
        enabled = true,
        color = '#FF0000',
        prefix = '[LEADERS]',
        minRank = 5
    }
}

-- Gang Events
Config.GangEvents = {
    ['turf_war'] = {
        name = 'Turf War',
        duration = 1800, -- 30 minutes
        minParticipants = 6,
        rewards = {
            winner = 100000,
            participant = 10000
        }
    },
    ['drug_shipment'] = {
        name = 'Drug Shipment',
        duration = 900, -- 15 minutes
        minParticipants = 3,
        rewards = {
            success = 50000,
            participant = 5000
        }
    },
    ['gang_meeting'] = {
        name = 'Gang Meeting',
        duration = 600, -- 10 minutes
        minParticipants = 5,
        benefits = {
            reputation = 10,
            unity_bonus = 0.1
        }
    }
}

-- Integration Settings
Config.Integration = {
    apd = true, -- Police can investigate gang activities
    economy = true, -- Gang activities affect server economy
    properties = true, -- Gangs can own properties
    vehicles = true, -- Gang vehicle system
    weapons = true -- Gang weapon system
}
