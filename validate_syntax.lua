-- Simple Lua syntax validation script
-- This will help check if our Lua files have correct syntax

local function validateFile(filepath)
    local file = io.open(filepath, "r")
    if not file then
        print("❌ Could not open file: " .. filepath)
        return false
    end
    
    local content = file:read("*all")
    file:close()
    
    local func, err = load(content, filepath)
    if func then
        print("✅ " .. filepath .. " - Syntax OK")
        return true
    else
        print("❌ " .. filepath .. " - Syntax Error:")
        print("   " .. err)
        return false
    end
end

-- Files to validate
local files = {
    "resources/[olympus]/olympus-events/config/federal.lua",
    "resources/[olympus]/olympus-events/config/shared.lua",
    "resources/[olympus]/olympus-core/config/shared.lua",
    "resources/[olympus]/olympus-ui/config/shared.lua"
}

print("🔍 Validating Lua syntax...")
print("")

local allValid = true
for _, file in ipairs(files) do
    if not validateFile(file) then
        allValid = false
    end
end

print("")
if allValid then
    print("🎉 All files have valid syntax!")
else
    print("⚠️  Some files have syntax errors - please fix them before starting the server")
end
