<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Olympus UI Framework</title>
    <style>
        /* ========================================
           OLYMPUS UI FRAMEWORK - AUTHENTIC STYLES
           Based on Original Olympus Altis Life
           ======================================== */

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PuristaMedium', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: transparent;
            color: #ffffff;
            overflow: hidden;
            user-select: none;
        }

        /* ========================================
           HUD CONTAINER - RIGHT SIDE POSITIONING
           ======================================== */

        .hud-container {
            position: fixed;
            top: 0;
            right: 0;
            width: 22vw;
            height: 28vh;
            background: rgba(0, 0, 0, 0);
            z-index: 1000;
            pointer-events: none;
        }

        /* ========================================
           STATUS BARS - AUTHENTIC OLYMPUS LAYOUT
           ======================================== */

        .status-bars {
            position: absolute;
            right: 0;
            bottom: 0;
            width: 100%;
        }

        .status-bar {
            display: flex;
            align-items: center;
            margin-bottom: 1vh;
            height: 4vh;
        }

        .status-icon {
            width: 3vw;
            height: 4vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1vw;
        }

        .status-icon img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
        }

        .status-progress {
            width: 16vw;
            height: 4vh;
            background: rgba(0, 0, 0, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg,
                rgba(58, 123, 213, 0.8) 0%,
                rgba(58, 180, 226, 0.8) 100%);
            transition: width 0.3s ease;
            width: 100%;
        }

        .progress-fill.health-fill {
            background: linear-gradient(90deg, #ff4444 0%, #ff6666 100%);
        }

        .progress-fill.food-fill {
            background: linear-gradient(90deg, #44ff44 0%, #66ff66 100%);
        }

        .progress-fill.water-fill {
            background: linear-gradient(90deg, #4444ff 0%, #6666ff 100%);
        }

        .progress-fill.stamina-fill {
            background: linear-gradient(90deg, #ffff44 0%, #ffff66 100%);
        }

        .progress-fill.wanted-fill {
            background: linear-gradient(90deg, #ff0000 0%, #ff3333 100%);
        }

        .status-text {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.2vh;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            color: #ffffff;
            z-index: 2;
        }

        /* ========================================
           INFO DISPLAY - CASH, BANK, LOCATION, TIME
           ======================================== */

        .info-display {
            position: absolute;
            right: 0;
            bottom: 25vh;
            width: 100%;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5vh;
            padding: 0.5vh 1vw;
            background: rgba(0, 0, 0, 0.4);
            border-left: 3px solid rgba(58, 123, 213, 0.8);
        }

        .info-label {
            font-size: 1.4vh;
            font-weight: bold;
            color: rgba(58, 123, 213, 1);
        }

        .info-value {
            font-size: 1.4vh;
            font-weight: bold;
            color: #ffffff;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        /* ========================================
           ADMIN HUD - TOP RIGHT TOGGLES
           ======================================== */

        .admin-hud {
            position: absolute;
            right: 0;
            top: 0;
            width: 100%;
        }

        .admin-toggle {
            margin-bottom: 0.5vh;
            padding: 0.5vh 1vw;
            background: rgba(255, 0, 0, 0.6);
            border-left: 3px solid #ff0000;
            font-size: 1.2vh;
            font-weight: bold;
            color: #ffffff;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        /* ========================================
           EARPLUGS ICON
           ======================================== */

        .earplugs-icon {
            position: absolute;
            right: 5vw;
            top: -5vh;
            width: 3vw;
            height: 4vh;
        }

        .earplugs-icon img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
        }

        /* ========================================
           NOTIFICATION SYSTEM
           ======================================== */

        .notification-container {
            position: fixed;
            top: 2vh;
            right: 2vw;
            width: 25vw;
            z-index: 2000;
            pointer-events: none;
        }

        .notification {
            background: rgba(0, 0, 0, 0.8);
            border-left: 4px solid #00ccff;
            padding: 1vh 1.5vw;
            margin-bottom: 1vh;
            border-radius: 0 4px 4px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            animation: slideInRight 0.3s ease-out;
            max-width: 100%;
            word-wrap: break-word;
        }

        .notification.success {
            border-left-color: #00ff00;
        }

        .notification.error {
            border-left-color: #ff0000;
        }

        .notification.warning {
            border-left-color: #ffff00;
        }

        .notification.admin {
            border-left-color: #ff00ff;
        }

        .notification.police {
            border-left-color: #0066ff;
        }

        .notification.medical {
            border-left-color: #ff6600;
        }

        .notification-text {
            font-size: 1.4vh;
            color: #ffffff;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* ========================================
           PROGRESS BAR SYSTEM
           ======================================== */

        .progressbar-container {
            position: fixed;
            bottom: 15vh;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1500;
        }

        .progressbar {
            width: 300px;
            height: 20px;
            background: rgba(0, 0, 0, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .progressbar-fill {
            height: 100%;
            background: linear-gradient(90deg,
                rgba(58, 123, 213, 0.8) 0%,
                rgba(58, 180, 226, 0.8) 100%);
            transition: width 0.1s linear;
            width: 0%;
        }

        .progressbar-text {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            font-size: 12px;
            font-weight: bold;
            color: #ffffff;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            z-index: 2;
        }

        /* ========================================
           TITLE TEXT SYSTEM
           ======================================== */

        .titletext-container {
            position: fixed;
            top: 30vh;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1800;
            text-align: center;
            max-width: 80vw;
        }

        .titletext-content {
            font-size: 3vh;
            font-weight: bold;
            color: #ffffff;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        /* ========================================
           Y MENU SYSTEM - AUTHENTIC OLYMPUS STYLE
           ======================================== */

        .ymenu-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 3000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .ymenu-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(2px);
        }

        .ymenu-content {
            position: relative;
            width: 80vw;
            height: 80vh;
            background: linear-gradient(135deg,
                rgba(20, 20, 30, 0.95) 0%,
                rgba(30, 30, 45, 0.95) 100%);
            border: 2px solid rgba(58, 123, 213, 0.8);
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
            animation: scaleIn 0.3s ease-out;
        }

        @keyframes scaleIn {
            from {
                transform: scale(0.8);
                opacity: 0;
            }
            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        .ymenu-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 2vh 2vw;
            background: rgba(58, 123, 213, 0.2);
            border-bottom: 1px solid rgba(58, 123, 213, 0.5);
        }

        .ymenu-header h1 {
            font-size: 2.5vh;
            font-weight: bold;
            color: #ffffff;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .ymenu-close {
            font-size: 3vh;
            font-weight: bold;
            color: #ff4444;
            cursor: pointer;
            padding: 0.5vh 1vw;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        }

        .ymenu-close:hover {
            background: rgba(255, 68, 68, 0.2);
        }

        .ymenu-body {
            display: flex;
            height: calc(100% - 8vh);
        }

        .ymenu-tabs {
            width: 20%;
            background: rgba(0, 0, 0, 0.3);
            border-right: 1px solid rgba(58, 123, 213, 0.5);
            overflow-y: auto;
        }

        .ymenu-tab {
            display: flex;
            align-items: center;
            padding: 1.5vh 1vw;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .ymenu-tab:hover {
            background: rgba(58, 123, 213, 0.2);
        }

        .ymenu-tab.active {
            background: rgba(58, 123, 213, 0.4);
            border-left: 3px solid rgba(58, 123, 213, 1);
        }

        .ymenu-tab-icon {
            width: 2vw;
            height: 2vw;
            margin-right: 1vw;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .ymenu-tab-text {
            font-size: 1.4vh;
            font-weight: bold;
            color: #ffffff;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .ymenu-content-area {
            width: 80%;
            padding: 2vh 2vw;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.1);
        }

        .ymenu-content-area h2 {
            font-size: 2vh;
            font-weight: bold;
            color: rgba(58, 123, 213, 1);
            margin-bottom: 2vh;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .ymenu-content-area p {
            font-size: 1.4vh;
            color: #ffffff;
            line-height: 1.6;
            margin-bottom: 1vh;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }
    </style>
</head>
<body>
    <!-- ========================================
         OLYMPUS HUD CONTAINER
         ======================================== -->
    <div id="hud-container" class="hud-container">
        <!-- Status Bars -->
        <div class="status-bars">
            <div class="status-bar" id="health-bar">
                <div class="status-icon">
                    <img src="images/health.png" alt="Health">
                </div>
                <div class="status-progress">
                    <div class="progress-fill health-fill" id="health-fill"></div>
                    <div class="status-text" id="health-text">100%</div>
                </div>
            </div>
            
            <div class="status-bar" id="food-bar">
                <div class="status-icon">
                    <img src="images/food.png" alt="Food">
                </div>
                <div class="status-progress">
                    <div class="progress-fill food-fill" id="food-fill"></div>
                    <div class="status-text" id="food-text">100%</div>
                </div>
            </div>
            
            <div class="status-bar" id="water-bar">
                <div class="status-icon">
                    <img src="images/water.png" alt="Water">
                </div>
                <div class="status-progress">
                    <div class="progress-fill water-fill" id="water-fill"></div>
                    <div class="status-text" id="water-text">100%</div>
                </div>
            </div>
            
            <div class="status-bar" id="stamina-bar">
                <div class="status-icon">
                    <img src="images/stamina.png" alt="Stamina">
                </div>
                <div class="status-progress">
                    <div class="progress-fill stamina-fill" id="stamina-fill"></div>
                    <div class="status-text" id="stamina-text">100%</div>
                </div>
            </div>
            
            <div class="status-bar" id="wanted-bar" style="display: none;">
                <div class="status-icon">
                    <img src="images/wanted.png" alt="Wanted">
                </div>
                <div class="status-progress">
                    <div class="progress-fill wanted-fill" id="wanted-fill"></div>
                    <div class="status-text" id="wanted-text">Wanted</div>
                </div>
            </div>
        </div>
        
        <!-- Info Display -->
        <div class="info-display">
            <div class="info-item" id="cash-display">
                <span class="info-label">Cash:</span>
                <span class="info-value" id="cash-value">$0</span>
            </div>
            <div class="info-item" id="bank-display">
                <span class="info-label">Bank:</span>
                <span class="info-value" id="bank-value">$0</span>
            </div>
            <div class="info-item" id="duty-display" style="display: none;">
                <span class="info-label">Duty:</span>
                <span class="info-value" id="duty-value">Off Duty</span>
            </div>
            <div class="info-item" id="location-display">
                <span class="info-label">Location:</span>
                <span class="info-value" id="location-value">Unknown</span>
            </div>
            <div class="info-item" id="time-display">
                <span class="info-label">Time:</span>
                <span class="info-value" id="time-value">00:00</span>
            </div>
        </div>
        
        <!-- Admin HUD -->
        <div class="admin-hud" id="admin-hud" style="display: none;">
            <div class="admin-toggle" id="godmode-toggle">Godmode: OFF</div>
            <div class="admin-toggle" id="invisible-toggle">Invisible: OFF</div>
            <div class="admin-toggle" id="esp-toggle">ESP: OFF</div>
            <div class="admin-toggle" id="stase-toggle">Stase: OFF</div>
            <div class="admin-toggle" id="streamer-toggle">Streamer: OFF</div>
            <div class="admin-toggle" id="fly-toggle">Fly: OFF</div>
        </div>
        
        <!-- Earplugs Icon -->
        <div class="earplugs-icon" id="earplugs-icon" style="display: none;">
            <img src="images/earplugs.png" alt="Earplugs">
        </div>
    </div>

    <!-- ========================================
         NOTIFICATION CONTAINER
         ======================================== -->
    <div id="notification-container" class="notification-container"></div>

    <!-- ========================================
         PROGRESS BAR CONTAINER
         ======================================== -->
    <div id="progressbar-container" class="progressbar-container" style="display: none;">
        <div class="progressbar">
            <div class="progressbar-fill" id="progressbar-fill"></div>
            <div class="progressbar-text" id="progressbar-text">Processing...</div>
        </div>
    </div>

    <!-- ========================================
         TITLE TEXT CONTAINER
         ======================================== -->
    <div id="titletext-container" class="titletext-container" style="display: none;">
        <div class="titletext-content" id="titletext-content"></div>
    </div>

    <!-- ========================================
         Y MENU CONTAINER
         ======================================== -->
    <div id="ymenu-container" class="ymenu-container" style="display: none;">
        <div class="ymenu-background"></div>
        <div class="ymenu-content">
            <div class="ymenu-header">
                <h1>Olympus Menu</h1>
                <div class="ymenu-close" id="ymenu-close">×</div>
            </div>
            <div class="ymenu-body">
                <div class="ymenu-tabs" id="ymenu-tabs">
                    <!-- Tabs will be dynamically generated -->
                </div>
                <div class="ymenu-content-area" id="ymenu-content-area">
                    <!-- Content will be dynamically loaded -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/script.js"></script>
</body>
</html>
