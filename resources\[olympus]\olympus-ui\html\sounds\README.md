# Olympus UI Sound Assets

This directory contains all the sound files for the Olympus UI Framework notification system.

## Required Sound Files

### Notification Sounds (OGG format recommended)
- `notification_info.ogg` - General information notification sound
- `notification_success.ogg` - Success notification sound
- `notification_error.ogg` - Error notification sound
- `notification_warning.ogg` - Warning notification sound
- `notification_admin.ogg` - Admin notification sound
- `notification_police.ogg` - Police/APD notification sound
- `notification_medical.ogg` - Medical/R&R notification sound

## Sound Format Requirements

- Format: OGG Vorbis (recommended) or MP3
- Quality: 44.1kHz, 16-bit
- Duration: 1-3 seconds maximum
- Volume: Normalized to prevent ear damage

## Sound Design Guidelines

- Keep sounds short and non-intrusive
- Use distinct tones for different notification types:
  - Info: Neutral beep or chime
  - Success: Pleasant, upward tone
  - Error: Lower, attention-grabbing tone
  - Warning: Urgent but not alarming
  - Admin: Authoritative tone
  - Police: Radio-style beep
  - Medical: Medical equipment beep

## Notes

These are placeholder entries. You should add actual sound files that match the original Olympus Altis Life notification sounds or create new ones that fit the server's aesthetic. The original sounds may be found in the Olympus dump files or you can create custom sounds that match the theme.
