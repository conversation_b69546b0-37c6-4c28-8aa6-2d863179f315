-- ============================================
-- OLYMPUS CIVILIAN JOBS SYSTEM - CLIENT
-- Based on original fn_gather.sqf and fn_processAction.sqf
-- ============================================

local OlympusCivJobs = {}

-- ============================================
-- INITIALIZATION
-- ============================================

local isGathering = false
local isProcessing = false
local gatheringZone = nil
local processingType = nil
local jobBlips = {}
local jobMarkers = {}

-- Resource zone positions (matching server)
local ResourceZones = {
    -- Legal Resources
    apple_1 = {type = "apple", legal = true, position = vector3(2100.5, 1850.2, 35.0), radius = 300, blipSprite = 1, blipColor = 2},
    apple_2 = {type = "apple", legal = true, position = vector3(2150.8, 1900.5, 35.0), radius = 300, blipSprite = 1, blipColor = 2},
    peaches_1 = {type = "peach", legal = true, position = vector3(2200.3, 1950.7, 35.0), radius = 300, blipSprite = 1, blipColor = 3},
    peaches_2 = {type = "peach", legal = true, position = vector3(2250.1, 2000.4, 35.0), radius = 300, blipSprite = 1, blipColor = 3},
    salt_1 = {type = "salt", legal = true, position = vector3(3500.2, 2100.8, 35.0), radius = 300, blipSprite = 78, blipColor = 4, mineable = true},
    iron_1 = {type = "iron", legal = true, position = vector3(3600.5, 2200.3, 35.0), radius = 300, blipSprite = 78, blipColor = 5, mineable = true},
    sand_1 = {type = "sand", legal = true, position = vector3(3700.8, 2300.6, 35.0), radius = 300, blipSprite = 78, blipColor = 6, mineable = true},
    diamond_1 = {type = "diamond", legal = true, position = vector3(3800.1, 2400.9, 35.0), radius = 300, blipSprite = 78, blipColor = 7, mineable = true},
    oil_1 = {type = "oil", legal = true, position = vector3(3900.4, 2500.2, 35.0), radius = 300, blipSprite = 78, blipColor = 8, mineable = true},

    -- Illegal Resources
    heroin_1 = {type = "heroin", legal = false, position = vector3(4000.7, 2600.5, 35.0), radius = 300, blipSprite = 51, blipColor = 1},
    cocaine_1 = {type = "cocaine", legal = false, position = vector3(4100.0, 2700.8, 35.0), radius = 300, blipSprite = 51, blipColor = 1},
    weed_1 = {type = "weed", legal = false, position = vector3(4200.3, 2800.1, 35.0), radius = 300, blipSprite = 51, blipColor = 2},
    phosphorous_1 = {type = "phosphorous", legal = false, position = vector3(4300.6, 2900.4, 35.0), radius = 300, blipSprite = 51, blipColor = 3},
    ephedra_1 = {type = "ephedra", legal = false, position = vector3(4400.9, 3000.7, 35.0), radius = 300, blipSprite = 51, blipColor = 4},
    sugar_1 = {type = "sugar", legal = false, position = vector3(4500.2, 3100.0, 35.0), radius = 300, blipSprite = 51, blipColor = 5},
    corn_1 = {type = "corn", legal = false, position = vector3(4600.5, 3200.3, 35.0), radius = 300, blipSprite = 51, blipColor = 6},
    frog_1 = {type = "frog", legal = false, position = vector3(4700.8, 3300.6, 35.0), radius = 300, blipSprite = 51, blipColor = 7},
    mushroom_1 = {type = "mushroom", legal = false, position = vector3(4800.1, 3400.9, 35.0), radius = 300, blipSprite = 51, blipColor = 8}
}

-- Processing locations
local ProcessingLocations = {
    -- Legal Processing
    legal_processor = {position = vector3(1500.0, 1500.0, 35.0), blipSprite = 478, blipColor = 2},

    -- Illegal Processing
    illegal_processor_1 = {position = vector3(5000.0, 5000.0, 35.0), blipSprite = 478, blipColor = 1},
    illegal_processor_2 = {position = vector3(5100.0, 5100.0, 35.0), blipSprite = 478, blipColor = 1},
    illegal_processor_3 = {position = vector3(5200.0, 5200.0, 35.0), blipSprite = 478, blipColor = 1}
}

-- ============================================
-- UTILITY FUNCTIONS
-- ============================================

function OlympusCivJobs.ShowNotification(message, type)
    local color = "~w~"
    if type == "success" then color = "~g~"
    elseif type == "error" then color = "~r~"
    elseif type == "warning" then color = "~y~"
    end

    SetNotificationTextEntry("STRING")
    AddTextComponentString(color .. message)
    DrawNotification(false, false)
end

function OlympusCivJobs.IsPlayerInVehicle()
    return IsPedInAnyVehicle(PlayerPedId(), false)
end

function OlympusCivJobs.GetClosestResourceZone()
    local playerPos = GetEntityCoords(PlayerPedId())
    local closestZone = nil
    local closestDistance = math.huge

    for zoneName, zoneData in pairs(ResourceZones) do
        local distance = #(playerPos - zoneData.position)
        if distance <= zoneData.radius and distance < closestDistance then
            closestDistance = distance
            closestZone = {name = zoneName, data = zoneData, distance = distance}
        end
    end

    return closestZone
end

function OlympusCivJobs.GetClosestProcessingLocation()
    local playerPos = GetEntityCoords(PlayerPedId())
    local closestLocation = nil
    local closestDistance = math.huge

    for locationName, locationData in pairs(ProcessingLocations) do
        local distance = #(playerPos - locationData.position)
        if distance <= 10.0 and distance < closestDistance then
            closestDistance = distance
            closestLocation = {name = locationName, data = locationData, distance = distance}
        end
    end

    return closestLocation
end

-- ============================================
-- BLIP MANAGEMENT
-- ============================================

function OlympusCivJobs.CreateJobBlips()
    -- Create resource zone blips
    for zoneName, zoneData in pairs(ResourceZones) do
        local blip = AddBlipForCoord(zoneData.position.x, zoneData.position.y, zoneData.position.z)
        SetBlipSprite(blip, zoneData.blipSprite)
        SetBlipColour(blip, zoneData.blipColor)
        SetBlipScale(blip, 0.8)
        SetBlipAsShortRange(blip, true)

        local blipName = string.format("%s %s", zoneData.type:gsub("^%l", string.upper), zoneData.legal and "(Legal)" or "(Illegal)")
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(blipName)
        EndTextCommandSetBlipName(blip)

        jobBlips[zoneName] = blip
    end

    -- Create processing location blips
    for locationName, locationData in pairs(ProcessingLocations) do
        local blip = AddBlipForCoord(locationData.position.x, locationData.position.y, locationData.position.z)
        SetBlipSprite(blip, locationData.blipSprite)
        SetBlipColour(blip, locationData.blipColor)
        SetBlipScale(blip, 0.8)
        SetBlipAsShortRange(blip, true)

        local blipName = locationName:find("legal") and "Legal Processor" or "Illegal Processor"
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(blipName)
        EndTextCommandSetBlipName(blip)

        jobBlips[locationName] = blip
    end
end

function OlympusCivJobs.RemoveJobBlips()
    for _, blip in pairs(jobBlips) do
        if DoesBlipExist(blip) then
            RemoveBlip(blip)
        end
    end
    jobBlips = {}
end

-- ============================================
-- INITIALIZATION
-- ============================================

CreateThread(function()
    -- Wait for core system
    while not exports['olympus-core'] do
        Wait(100)
    end

    -- Create job blips
    OlympusCivJobs.CreateJobBlips()

    print("^2[Olympus Civilian Jobs]^7 Client system initialized")
end)

-- ============================================
-- GATHERING SYSTEM
-- ============================================

function OlympusCivJobs.StartGathering(resourceType, zoneName)
    if isGathering then return end

    isGathering = true
    gatheringZone = zoneName

    OlympusCivJobs.ShowNotification(string.format("Started gathering %s. Press [E] to gather, [X] to stop.", resourceType), "success")

    -- Start gathering loop
    CreateThread(function()
        while isGathering do
            Wait(0)

            if IsControlJustPressed(0, 38) then -- E key
                OlympusCivJobs.GatherResource()
            elseif IsControlJustPressed(0, 73) then -- X key
                OlympusCivJobs.StopGathering()
                break
            end

            -- Check if player moved too far from zone
            local zone = OlympusCivJobs.GetClosestResourceZone()
            if not zone or zone.name ~= gatheringZone then
                OlympusCivJobs.ShowNotification("You moved too far from the resource zone!", "error")
                OlympusCivJobs.StopGathering()
                break
            end

            -- Check if player entered vehicle
            if OlympusCivJobs.IsPlayerInVehicle() then
                OlympusCivJobs.ShowNotification("You cannot gather while in a vehicle!", "error")
                OlympusCivJobs.StopGathering()
                break
            end
        end
    end)
end

function OlympusCivJobs.GatherResource()
    if not isGathering then return end

    -- Play gathering animation
    local playerPed = PlayerPedId()
    TaskStartScenarioInPlace(playerPed, "WORLD_HUMAN_GARDENER_PLANT", 0, true)

    -- Wait for animation
    Wait(2500)

    -- Clear animation
    ClearPedTasks(playerPed)

    -- Trigger server gathering
    TriggerServerEvent('olympus-civilian-jobs:gather', 1)
end

function OlympusCivJobs.StopGathering()
    if not isGathering then return end

    isGathering = false
    gatheringZone = nil

    -- Clear any animations
    ClearPedTasks(PlayerPedId())

    -- Notify server
    TriggerServerEvent('olympus-civilian-jobs:stopGather')

    OlympusCivJobs.ShowNotification("Stopped gathering.", "warning")
end

-- ============================================
-- PROCESSING SYSTEM
-- ============================================

function OlympusCivJobs.StartProcessing(description, duration)
    if isProcessing then return end

    isProcessing = true

    OlympusCivJobs.ShowNotification(string.format("Started %s. Duration: %.1fs", description, duration), "success")

    -- Show progress bar (simplified)
    local startTime = GetGameTimer()
    local endTime = startTime + (duration * 1000)

    CreateThread(function()
        while isProcessing and GetGameTimer() < endTime do
            Wait(100)

            local progress = (GetGameTimer() - startTime) / (duration * 1000)
            local progressText = string.format("%s (%.0f%%)", description, progress * 100)

            -- Draw progress text
            SetTextFont(4)
            SetTextScale(0.5, 0.5)
            SetTextColour(255, 255, 255, 255)
            SetTextEntry("STRING")
            AddTextComponentString(progressText)
            DrawText(0.5, 0.9)

            -- Check if player moved too far or entered vehicle
            local location = OlympusCivJobs.GetClosestProcessingLocation()
            if not location then
                OlympusCivJobs.ShowNotification("You moved too far from the processor!", "error")
                OlympusCivJobs.CancelProcessing()
                break
            end

            if OlympusCivJobs.IsPlayerInVehicle() then
                OlympusCivJobs.ShowNotification("You cannot process while in a vehicle!", "error")
                OlympusCivJobs.CancelProcessing()
                break
            end
        end

        if isProcessing then
            -- Processing completed
            TriggerServerEvent('olympus-civilian-jobs:completeProcess')
            isProcessing = false
        end
    end)
end

function OlympusCivJobs.CancelProcessing()
    if not isProcessing then return end

    isProcessing = false

    -- Notify server
    TriggerServerEvent('olympus-civilian-jobs:cancelProcess')

    OlympusCivJobs.ShowNotification("Processing cancelled!", "warning")
end

-- ============================================
-- INTERACTION SYSTEM
-- ============================================

-- Main interaction loop
CreateThread(function()
    while true do
        Wait(0)

        local playerPos = GetEntityCoords(PlayerPedId())
        local inResourceZone = false
        local inProcessingZone = false

        -- Check resource zones
        local closestZone = OlympusCivJobs.GetClosestResourceZone()
        if closestZone then
            inResourceZone = true

            -- Draw zone marker
            DrawMarker(1, closestZone.data.position.x, closestZone.data.position.y, closestZone.data.position.z - 1.0,
                0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                closestZone.data.radius * 2, closestZone.data.radius * 2, 2.0,
                0, 255, 0, 50, false, true, 2, false, nil, nil, false)

            -- Show interaction text
            if not isGathering and not isProcessing then
                SetTextFont(4)
                SetTextScale(0.5, 0.5)
                SetTextColour(255, 255, 255, 255)
                SetTextEntry("STRING")
                AddTextComponentString(string.format("Press [G] to start gathering %s", closestZone.data.type))
                DrawText(0.5, 0.85)

                if IsControlJustPressed(0, 47) then -- G key
                    TriggerServerEvent('olympus-civilian-jobs:startGather', closestZone.name)
                end
            end
        end

        -- Check processing zones
        local closestProcessor = OlympusCivJobs.GetClosestProcessingLocation()
        if closestProcessor then
            inProcessingZone = true

            -- Draw processor marker
            DrawMarker(1, closestProcessor.data.position.x, closestProcessor.data.position.y, closestProcessor.data.position.z - 1.0,
                0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                5.0, 5.0, 2.0,
                255, 0, 0, 100, false, true, 2, false, nil, nil, false)

            -- Show interaction text
            if not isGathering and not isProcessing then
                SetTextFont(4)
                SetTextScale(0.5, 0.5)
                SetTextColour(255, 255, 255, 255)
                SetTextEntry("STRING")
                AddTextComponentString("Press [H] to open processing menu")
                DrawText(0.5, 0.85)

                if IsControlJustPressed(0, 74) then -- H key
                    OlympusCivJobs.OpenProcessingMenu()
                end
            end
        end

        -- If not in any zone, sleep longer
        if not inResourceZone and not inProcessingZone then
            Wait(500)
        end
    end
end)

-- ============================================
-- PROCESSING MENU
-- ============================================

function OlympusCivJobs.OpenProcessingMenu()
    local processingOptions = {
        -- Legal Processing
        {label = "Salt → Refined Salt", value = "salt", legal = true},
        {label = "Rock → Cement", value = "cement", legal = true},
        {label = "Sand → Glass", value = "sand", legal = true},
        {label = "Iron Ore → Refined Iron", value = "iron", legal = true},
        {label = "Copper Ore → Refined Copper", value = "copper", legal = true},
        {label = "Unrefined Oil → Processed Oil", value = "oil", legal = true},
        {label = "Diamond → Cut Diamond", value = "diamond", legal = true},

        -- Illegal Processing
        {label = "Cannabis → Marijuana", value = "marijuana", legal = false},
        {label = "Heroin (Unprocessed) → Heroin", value = "heroin", legal = false},
        {label = "Cocaine → Processed Cocaine", value = "cocaine", legal = false},
        {label = "Sugar + Yeast + Corn → Moonshine", value = "moonshine", legal = false},
        {label = "Lithium + Phosphorous + Ephedra → Crystal Meth", value = "crystalmeth", legal = false},

        -- Double Processing
        {label = "Marijuana → Hash", value = "hash", legal = false},
        {label = "Processed Cocaine → Crack", value = "crack", legal = false}
    }

    -- Simple menu implementation (would be replaced with proper NUI)
    local menuText = "Processing Menu:\n"
    for i, option in ipairs(processingOptions) do
        menuText = menuText .. string.format("%d. %s %s\n", i, option.label, option.legal and "(Legal)" or "(Illegal)")
    end
    menuText = menuText .. "\nPress number key to select, ESC to cancel"

    -- Show menu notification
    OlympusCivJobs.ShowNotification("Processing menu opened. Check chat for options.", "success")
    TriggerEvent('chat:addMessage', {
        color = {255, 255, 255},
        multiline = true,
        args = {"Processing", menuText}
    })

    -- Handle menu input
    CreateThread(function()
        local menuActive = true
        local startTime = GetGameTimer()

        while menuActive and (GetGameTimer() - startTime) < 30000 do -- 30 second timeout
            Wait(0)

            -- Check for number key presses
            for i = 1, #processingOptions do
                local key = 48 + i -- Number keys 1-9
                if i <= 9 and IsControlJustPressed(0, key) then
                    local selectedOption = processingOptions[i]
                    TriggerServerEvent('olympus-civilian-jobs:startProcess', selectedOption.value)
                    menuActive = false
                    break
                end
            end

            -- Check for ESC key
            if IsControlJustPressed(0, 322) then -- ESC
                OlympusCivJobs.ShowNotification("Processing menu cancelled.", "warning")
                menuActive = false
            end
        end

        if menuActive then
            OlympusCivJobs.ShowNotification("Processing menu timed out.", "warning")
        end
    end)
end

-- ============================================
-- EVENT HANDLERS
-- ============================================

-- Notification handler
RegisterNetEvent('olympus-civilian-jobs:notify', function(message, type)
    OlympusCivJobs.ShowNotification(message, type)
end)

-- Start gathering
RegisterNetEvent('olympus-civilian-jobs:startGathering', function(resourceType, zoneName)
    OlympusCivJobs.StartGathering(resourceType, zoneName)
end)

-- Stop gathering
RegisterNetEvent('olympus-civilian-jobs:stopGathering', function()
    OlympusCivJobs.StopGathering()
end)

-- Start processing
RegisterNetEvent('olympus-civilian-jobs:startProcessing', function(description, duration)
    OlympusCivJobs.StartProcessing(description, duration)
end)

-- Cancel processing
RegisterNetEvent('olympus-civilian-jobs:cancelProcessing', function()
    OlympusCivJobs.CancelProcessing()
end)

-- ============================================
-- EXPORT FUNCTIONS
-- ============================================

exports('GetPlayerJob', function()
    if isGathering then
        return {type = "gathering", zone = gatheringZone}
    elseif isProcessing then
        return {type = "processing", process = processingType}
    end
    return nil
end)

exports('StartJob', function(jobName)
    print(string.format("^3[Olympus Civilian Jobs]^7 Starting job: %s", jobName))

    -- Find matching resource zone
    for zoneName, zoneData in pairs(ResourceZones) do
        if zoneData.type == jobName then
            TriggerServerEvent('olympus-civilian-jobs:startGather', zoneName)
            return true
        end
    end

    return false
end)

exports('EndJob', function()
    if isGathering then
        OlympusCivJobs.StopGathering()
        return true
    elseif isProcessing then
        OlympusCivJobs.CancelProcessing()
        return true
    end
    return false
end)

exports('GetJobProgress', function()
    if isGathering then
        return {type = "gathering", zone = gatheringZone, active = true}
    elseif isProcessing then
        return {type = "processing", active = true}
    end
    return {active = false}
end)

exports('IsPlayerWorking', function()
    return isGathering or isProcessing
end)

exports('GetResourceZones', function()
    return ResourceZones
end)

exports('GetProcessingLocations', function()
    return ProcessingLocations
end)

print("^2[Olympus Civilian Jobs]^7 Client system loaded successfully")
