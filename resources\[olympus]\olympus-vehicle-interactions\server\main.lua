-- ========================================
-- OLYMPUS VEHICLE INTERACTIONS SERVER
-- Based on Original Olympus Functions
-- ========================================

local OlympusVehicleInteractions = {}

-- ========================================
-- DATABASE INITIALIZATION
-- ========================================
function OlympusVehicleInteractions.InitDatabase()
    -- Create vehicle interaction logs table
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS `vehicle_interaction_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `player_id` varchar(50) NOT NULL,
            `player_name` varchar(100) NOT NULL,
            `vehicle_plate` varchar(20) NOT NULL,
            `vehicle_model` varchar(50) NOT NULL,
            `interaction_type` enum('flip','push','search','impound','repair') NOT NULL,
            `reward_amount` int(11) DEFAULT 0,
            `position` varchar(100) NOT NULL,
            `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
            PRIMARY KEY (`id`),
            KEY `player_id` (`player_id`),
            KEY `interaction_type` (`interaction_type`),
            KEY `timestamp` (`timestamp`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ]])
end

-- ========================================
-- UTILITY FUNCTIONS
-- ========================================
function OlympusVehicleInteractions.GetPlayerData(src)
    local success, result = pcall(function()
        return exports['olympus-core']:GetPlayerData(src)
    end)
    return success and result or nil
end

function OlympusVehicleInteractions.GetPlayerFaction(src)
    local playerData = OlympusVehicleInteractions.GetPlayerData(src)
    if not playerData then return 'civilian' end
    return playerData.faction or 'civilian'
end

function OlympusVehicleInteractions.AddPlayerMoney(src, account, amount)
    local success, result = pcall(function()
        return exports['olympus-core']:AddMoney(src, account, amount)
    end)
    return success and result
end

function OlympusVehicleInteractions.HasItem(src, item)
    local success, result = pcall(function()
        return exports['olympus-core']:HasItem(src, item)
    end)
    return success and result or false
end

function OlympusVehicleInteractions.RemoveItem(src, item, amount)
    local success, result = pcall(function()
        return exports['olympus-core']:RemoveItem(src, item, amount or 1)
    end)
    return success and result
end

function OlympusVehicleInteractions.HasLicense(src, license)
    local success, result = pcall(function()
        return exports['olympus-licenses']:HasLicense(src, license)
    end)
    return success and result or false
end

function OlympusVehicleInteractions.GetVehicleOwners(vehicle)
    local success, result = pcall(function()
        return exports['olympus-vehicles']:GetVehicleOwners(vehicle)
    end)
    return success and result or {}
end

function OlympusVehicleInteractions.IsVehicleStolen(vehicle)
    local success, result = pcall(function()
        return exports['olympus-vehicles']:IsVehicleStolen(vehicle)
    end)
    return success and result or false
end

function OlympusVehicleInteractions.GetVehicleValue(model)
    local success, result = pcall(function()
        return exports['olympus-vehicles']:GetVehicleValue(model)
    end)
    return success and result or 50000
end

function OlympusVehicleInteractions.LogInteraction(playerId, playerName, vehiclePlate, vehicleModel, interactionType, rewardAmount, position)
    exports.oxmysql:execute('INSERT INTO vehicle_interaction_logs (player_id, player_name, vehicle_plate, vehicle_model, interaction_type, reward_amount, position) VALUES (?, ?, ?, ?, ?, ?, ?)', {
        playerId, playerName, vehiclePlate, vehicleModel, interactionType, rewardAmount or 0, json.encode(position)
    })
end

function OlympusVehicleInteractions.IsInCity(position)
    for cityName, polygon in pairs(Config.CityPolygons) do
        -- Simple polygon check (would need proper implementation)
        -- For now, just return false
        -- TODO: Implement proper polygon checking
    end
    return false
end

-- ========================================
-- VEHICLE FLIPPING SYSTEM
-- Based on fn_flipAction.sqf
-- ========================================
function OlympusVehicleInteractions.FlipVehicle(src, vehicleNetId)
    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    if not vehicle or vehicle == 0 then
        TriggerClientEvent('olympus-vehicle-interactions:notify', src, Config.Notifications.flipping.tooFar, 'error')
        return false
    end
    
    local playerFaction = OlympusVehicleInteractions.GetPlayerFaction(src)
    local vehicleModel = GetEntityModel(vehicle)
    local vehicleName = GetDisplayNameFromVehicleModel(vehicleModel)
    
    -- Check if player has keys or is authorized faction
    if Config.VehicleInteractions.flipping.requireKeys then
        local hasKeys = false
        
        -- Check if player is authorized faction
        if table.contains(Config.VehicleInteractions.flipping.allowedFactions, playerFaction) then
            hasKeys = true
        else
            -- Check if player has vehicle keys
            local owners = OlympusVehicleInteractions.GetVehicleOwners(vehicle)
            local playerIdentifier = GetPlayerIdentifier(src, 0)
            
            for _, owner in ipairs(owners) do
                if owner.identifier == playerIdentifier then
                    hasKeys = true
                    break
                end
            end
        end
        
        if not hasKeys then
            TriggerClientEvent('olympus-vehicle-interactions:notify', src, Config.Notifications.flipping.noKeys, 'error')
            return false
        end
    end
    
    -- Check vehicle damage
    local damage = GetVehicleEngineHealth(vehicle) / 1000.0
    if damage < 0.15 then
        TriggerClientEvent('olympus-vehicle-interactions:notify', src, Config.Notifications.flipping.tooRekt, 'error')
        return false
    end
    
    -- Check if vehicle has occupants
    local occupants = GetVehicleNumberOfPassengers(vehicle)
    if occupants > 0 then
        TriggerClientEvent('olympus-vehicle-interactions:notify', src, Config.Notifications.flipping.occupied, 'error')
        return false
    end
    
    -- Flip the vehicle
    local coords = GetEntityCoords(vehicle)
    local heading = GetEntityHeading(vehicle)
    
    SetEntityCoords(vehicle, coords.x, coords.y, coords.z + 1.0, false, false, false, true)
    SetEntityRotation(vehicle, 0.0, 0.0, heading, 2, true)
    SetVehicleOnGroundProperly(vehicle)
    
    -- Log interaction
    OlympusVehicleInteractions.LogInteraction(
        GetPlayerIdentifier(src, 0), GetPlayerName(src),
        GetVehicleNumberPlateText(vehicle), vehicleName,
        'flip', 0, GetEntityCoords(GetPlayerPed(src))
    )
    
    TriggerClientEvent('olympus-vehicle-interactions:notify', src, Config.Notifications.flipping.success, 'success')
    return true
end

-- ========================================
-- VEHICLE PUSHING SYSTEM
-- Based on fn_pushVehicle.sqf
-- ========================================
function OlympusVehicleInteractions.PushVehicle(src, vehicleNetId, direction)
    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    if not vehicle or vehicle == 0 then
        TriggerClientEvent('olympus-vehicle-interactions:notify', src, Config.Notifications.pushing.tooFar, 'error')
        return false
    end
    
    local playerPed = GetPlayerPed(src)
    local playerCoords = GetEntityCoords(playerPed)
    local vehicleCoords = GetEntityCoords(vehicle)
    local distance = #(playerCoords - vehicleCoords)
    
    if distance > Config.VehicleInteractions.pushing.maxDistance then
        TriggerClientEvent('olympus-vehicle-interactions:notify', src, Config.Notifications.pushing.tooFar, 'error')
        return false
    end
    
    -- Apply push force
    local pushForce = Config.VehicleInteractions.pushing.pushForce
    local forwardVector = GetEntityForwardVector(playerPed)
    
    SetEntityVelocity(vehicle, 
        forwardVector.x * pushForce,
        forwardVector.y * pushForce,
        0.0
    )
    
    -- Log interaction
    local vehicleModel = GetEntityModel(vehicle)
    local vehicleName = GetDisplayNameFromVehicleModel(vehicleModel)
    
    OlympusVehicleInteractions.LogInteraction(
        GetPlayerIdentifier(src, 0), GetPlayerName(src),
        GetVehicleNumberPlateText(vehicle), vehicleName,
        'push', 0, GetEntityCoords(playerPed)
    )
    
    TriggerClientEvent('olympus-vehicle-interactions:notify', src, Config.Notifications.pushing.success, 'success')
    return true
end

-- ========================================
-- VEHICLE SEARCHING SYSTEM
-- Based on fn_searchVehAction.sqf
-- ========================================
function OlympusVehicleInteractions.SearchVehicle(src, vehicleNetId)
    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    if not vehicle or vehicle == 0 then
        TriggerClientEvent('olympus-vehicle-interactions:notify', src, Config.Notifications.searching.tooFar, 'error')
        return false
    end

    local playerFaction = OlympusVehicleInteractions.GetPlayerFaction(src)

    -- Check if player has driver's license (if civilian)
    if playerFaction == 'civilian' and Config.VehicleInteractions.searching.requireLicense then
        if not OlympusVehicleInteractions.HasLicense(src, 'driver') then
            TriggerClientEvent('olympus-vehicle-interactions:notify', src, Config.Notifications.searching.noLicense, 'error')
            return false
        end
    end

    -- Get vehicle information
    local owners = OlympusVehicleInteractions.GetVehicleOwners(vehicle)
    local isStolen = OlympusVehicleInteractions.IsVehicleStolen(vehicle)
    local vehicleModel = GetEntityModel(vehicle)
    local vehicleName = GetDisplayNameFromVehicleModel(vehicleModel)

    -- Format owner information
    local ownerText = ""
    if #owners > 0 then
        for i, owner in ipairs(owners) do
            ownerText = ownerText .. owner.name
            if i < #owners then
                ownerText = ownerText .. ", "
            end
        end
    else
        ownerText = "No owners, impound it"
    end

    -- Add stolen status
    if isStolen then
        ownerText = ownerText .. "\n" .. Config.Notifications.searching.stolen
    end

    -- Send search result to client
    TriggerClientEvent('olympus-vehicle-interactions:searchResult', src, {
        owners = ownerText,
        vehicleName = vehicleName,
        isStolen = isStolen
    })

    -- Log interaction
    OlympusVehicleInteractions.LogInteraction(
        GetPlayerIdentifier(src, 0), GetPlayerName(src),
        GetVehicleNumberPlateText(vehicle), vehicleName,
        'search', 0, GetEntityCoords(GetPlayerPed(src))
    )

    return true
end

-- ========================================
-- VEHICLE IMPOUNDING SYSTEM
-- Based on fn_impoundAction.sqf
-- ========================================
function OlympusVehicleInteractions.ImpoundVehicle(src, vehicleNetId)
    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    if not vehicle or vehicle == 0 then
        TriggerClientEvent('olympus-vehicle-interactions:notify', src, Config.Notifications.impounding.tooFar, 'error')
        return false
    end

    local playerFaction = OlympusVehicleInteractions.GetPlayerFaction(src)

    -- Check if player can impound
    if not table.contains(Config.VehicleInteractions.impounding.allowedFactions, playerFaction) then
        TriggerClientEvent('olympus-vehicle-interactions:notify', src, Config.Notifications.impounding.noPermission, 'error')
        return false
    end

    -- Check vehicle damage
    local damage = GetVehicleEngineHealth(vehicle) / 1000.0
    if damage < 0.15 then
        TriggerClientEvent('olympus-vehicle-interactions:notify', src, Config.Notifications.impounding.tooRekt, 'error')
        return false
    end

    -- Check if vehicle has occupants
    local occupants = GetVehicleNumberOfPassengers(vehicle)
    if occupants > 0 then
        TriggerClientEvent('olympus-vehicle-interactions:notify', src, Config.Notifications.impounding.occupied, 'error')
        return false
    end

    -- Get vehicle information
    local vehicleModel = GetEntityModel(vehicle)
    local vehicleName = GetDisplayNameFromVehicleModel(vehicleModel)
    local vehicleClass = GetVehicleClass(vehicle)
    local owners = OlympusVehicleInteractions.GetVehicleOwners(vehicle)
    local isStolen = OlympusVehicleInteractions.IsVehicleStolen(vehicle)

    -- Calculate reward
    local reward = 0
    if vehicleClass == 0 or vehicleClass == 1 or vehicleClass == 2 or vehicleClass == 3 or vehicleClass == 4 or vehicleClass == 5 or vehicleClass == 6 or vehicleClass == 7 or vehicleClass == 9 or vehicleClass == 10 or vehicleClass == 11 or vehicleClass == 12 then
        reward = Config.VehicleInteractions.impounding.rewards.car
    elseif vehicleClass == 14 then
        reward = Config.VehicleInteractions.impounding.rewards.boat
    elseif vehicleClass == 15 or vehicleClass == 16 then
        reward = Config.VehicleInteractions.impounding.rewards.air
    end

    -- Check for city bonus
    local playerPos = GetEntityCoords(GetPlayerPed(src))
    local cityBonus = 0
    if OlympusVehicleInteractions.IsInCity(playerPos) then
        cityBonus = Config.VehicleInteractions.impounding.cityBonus
        reward = reward + cityBonus
    end

    -- Check for stolen vehicle reward
    local stolenReward = 0
    if isStolen then
        local vehicleValue = OlympusVehicleInteractions.GetVehicleValue(vehicleModel)
        stolenReward = math.floor(vehicleValue * Config.VehicleInteractions.impounding.stolenReward)
        reward = reward + stolenReward
    end

    -- Remove vehicle from world
    local success, result = pcall(function()
        return exports['olympus-vehicles']:ImpoundVehicle(vehicle, src)
    end)

    if success and result then
        -- Give reward to player
        if reward > 0 then
            OlympusVehicleInteractions.AddPlayerMoney(src, 'bank', reward)
        end

        -- Send notification
        local message = string.format("Vehicle impounded successfully. Reward: $%d", reward)
        if cityBonus > 0 then
            message = message .. string.format(" (City bonus: $%d)", cityBonus)
        end
        if stolenReward > 0 then
            message = message .. string.format(" (Stolen reward: $%d)", stolenReward)
        end

        TriggerClientEvent('olympus-vehicle-interactions:notify', src, message, 'success')

        -- Log interaction
        OlympusVehicleInteractions.LogInteraction(
            GetPlayerIdentifier(src, 0), GetPlayerName(src),
            GetVehicleNumberPlateText(vehicle), vehicleName,
            'impound', reward, playerPos
        )

        return true
    else
        TriggerClientEvent('olympus-vehicle-interactions:notify', src, "Failed to impound vehicle", 'error')
        return false
    end
end

-- ========================================
-- VEHICLE REPAIR SYSTEM
-- Based on fn_repairTruck.sqf
-- ========================================
function OlympusVehicleInteractions.RepairVehicle(src, vehicleNetId)
    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    if not vehicle or vehicle == 0 then
        TriggerClientEvent('olympus-vehicle-interactions:notify', src, Config.Notifications.repair.tooFar, 'error')
        return false
    end

    local playerFaction = OlympusVehicleInteractions.GetPlayerFaction(src)
    local hasToolkit = OlympusVehicleInteractions.HasItem(src, Config.VehicleInteractions.repair.toolkitItem)
    local vehicleModel = GetEntityModel(vehicle)
    local vehicleName = GetDisplayNameFromVehicleModel(vehicleModel)
    local vehicleHash = GetHashKey(GetEntityModel(vehicle))

    -- Get current damage
    local currentDamage = 1.0 - (GetVehicleEngineHealth(vehicle) / 1000.0)

    -- Check if vehicle is too damaged
    if currentDamage > 0.97 then
        TriggerClientEvent('olympus-vehicle-interactions:notify', src, Config.Notifications.repair.tooRekt, 'error')
        return false
    end

    local repairLevel = 0.0 -- Full repair by default
    local cost = 0

    if playerFaction == 'medical' then
        -- R&R can always repair fully
        if not hasToolkit and Config.VehicleInteractions.repair.medicalFree then
            cost = Config.VehicleInteractions.repair.medicalCost
            TriggerClientEvent('olympus-vehicle-interactions:notify', src, Config.Notifications.repair.medicalCost, 'info')
        end
        repairLevel = 0.0 -- Full repair
    else
        -- Civilians and APD
        if hasToolkit then
            repairLevel = 0.0 -- Full repair with toolkit
            OlympusVehicleInteractions.RemoveItem(src, Config.VehicleInteractions.repair.toolkitItem, 1)
        else
            -- Partial repair without toolkit
            if currentDamage > 0.62 and not table.contains(Config.VehicleInteractions.repair.fullRepairVehicles, vehicleName:lower()) then
                repairLevel = 0.6 -- Partial repair
                TriggerClientEvent('olympus-vehicle-interactions:notify', src, Config.Notifications.repair.partial, 'warning')
            else
                repairLevel = 0.0 -- Full repair for special vehicles
            end
            TriggerClientEvent('olympus-vehicle-interactions:notify', src, Config.Notifications.repair.noToolkit, 'warning')
        end
    end

    -- Apply repair
    SetVehicleEngineHealth(vehicle, (1.0 - repairLevel) * 1000.0)
    SetVehicleBodyHealth(vehicle, (1.0 - repairLevel) * 1000.0)
    SetVehiclePetrolTankHealth(vehicle, (1.0 - repairLevel) * 1000.0)

    -- Deduct cost if applicable
    if cost > 0 then
        local success, result = pcall(function()
            return exports['olympus-core']:RemoveMoney(src, 'bank', cost)
        end)
        if not success or not result then
            TriggerClientEvent('olympus-vehicle-interactions:notify', src, "Insufficient funds for repair", 'error')
            return false
        end
    end

    -- Log interaction
    OlympusVehicleInteractions.LogInteraction(
        GetPlayerIdentifier(src, 0), GetPlayerName(src),
        GetVehicleNumberPlateText(vehicle), vehicleName,
        'repair', cost, GetEntityCoords(GetPlayerPed(src))
    )

    TriggerClientEvent('olympus-vehicle-interactions:notify', src, Config.Notifications.repair.success, 'success')
    return true
end

-- ========================================
-- UTILITY HELPER FUNCTIONS
-- ========================================
function table.contains(table, element)
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

-- ========================================
-- EVENT HANDLERS
-- ========================================
RegisterNetEvent('olympus-vehicle-interactions:flipVehicle', function(vehicleNetId)
    local src = source
    OlympusVehicleInteractions.FlipVehicle(src, vehicleNetId)
end)

RegisterNetEvent('olympus-vehicle-interactions:pushVehicle', function(vehicleNetId, direction)
    local src = source
    OlympusVehicleInteractions.PushVehicle(src, vehicleNetId, direction)
end)

RegisterNetEvent('olympus-vehicle-interactions:searchVehicle', function(vehicleNetId)
    local src = source
    OlympusVehicleInteractions.SearchVehicle(src, vehicleNetId)
end)

RegisterNetEvent('olympus-vehicle-interactions:impoundVehicle', function(vehicleNetId)
    local src = source
    OlympusVehicleInteractions.ImpoundVehicle(src, vehicleNetId)
end)

RegisterNetEvent('olympus-vehicle-interactions:repairVehicle', function(vehicleNetId)
    local src = source
    OlympusVehicleInteractions.RepairVehicle(src, vehicleNetId)
end)

-- ========================================
-- EXPORT FUNCTIONS
-- ========================================
exports('FlipVehicle', function(src, vehicleNetId)
    return OlympusVehicleInteractions.FlipVehicle(src, vehicleNetId)
end)

exports('PushVehicle', function(src, vehicleNetId, direction)
    return OlympusVehicleInteractions.PushVehicle(src, vehicleNetId, direction)
end)

exports('SearchVehicle', function(src, vehicleNetId)
    return OlympusVehicleInteractions.SearchVehicle(src, vehicleNetId)
end)

exports('ImpoundVehicle', function(src, vehicleNetId)
    return OlympusVehicleInteractions.ImpoundVehicle(src, vehicleNetId)
end)

exports('RepairVehicle', function(src, vehicleNetId)
    return OlympusVehicleInteractions.RepairVehicle(src, vehicleNetId)
end)

exports('CanInteractWithVehicle', function(src, vehicleNetId)
    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    if not vehicle or vehicle == 0 then return false end

    local playerPed = GetPlayerPed(src)
    local playerCoords = GetEntityCoords(playerPed)
    local vehicleCoords = GetEntityCoords(vehicle)
    local distance = #(playerCoords - vehicleCoords)

    return distance <= Config.VehicleInteractions.flipping.maxDistance
end)

exports('GetVehicleOwners', function(vehicleNetId)
    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    return OlympusVehicleInteractions.GetVehicleOwners(vehicle)
end)

exports('IsVehicleStolen', function(vehicleNetId)
    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    return OlympusVehicleInteractions.IsVehicleStolen(vehicle)
end)

-- ========================================
-- INITIALIZATION
-- ========================================
CreateThread(function()
    OlympusVehicleInteractions.InitDatabase()
    print("^2[Olympus Vehicle Interactions]^7 Server system initialized")
end)
