-- ============================================
-- OLYMPUS CIVILIAN JOBS SYSTEM - SERVER
-- Based on original fn_gather.sqf and fn_processAction.sqf
-- ============================================

local OlympusCivJobs = {}

-- ============================================
-- INITIALIZATION
-- ============================================

-- Database initialization
CreateThread(function()
    -- Job system tables would be added to database schema if needed
    print("^2[Olympus Civilian Jobs]^7 Server system initialized")
end)

-- ============================================
-- JOB STATE MANAGEMENT
-- ============================================

-- Player job states
local PlayerJobs = {}
local ProcessingStates = {}
local GatheringStates = {}

-- Resource zones and configurations
local ResourceZones = {
    -- Legal Resources
    apple_1 = {type = "apple", legal = true, position = vector3(2100.5, 1850.2, 35.0), radius = 300},
    apple_2 = {type = "apple", legal = true, position = vector3(2150.8, 1900.5, 35.0), radius = 300},
    peaches_1 = {type = "peach", legal = true, position = vector3(2200.3, 1950.7, 35.0), radius = 300},
    peaches_2 = {type = "peach", legal = true, position = vector3(2250.1, 2000.4, 35.0), radius = 300},
    salt_1 = {type = "salt", legal = true, position = vector3(3500.2, 2100.8, 35.0), radius = 300, mineable = true},
    iron_1 = {type = "iron", legal = true, position = vector3(3600.5, 2200.3, 35.0), radius = 300, mineable = true},
    sand_1 = {type = "sand", legal = true, position = vector3(3700.8, 2300.6, 35.0), radius = 300, mineable = true},
    diamond_1 = {type = "diamond", legal = true, position = vector3(3800.1, 2400.9, 35.0), radius = 300, mineable = true},
    oil_1 = {type = "oil", legal = true, position = vector3(3900.4, 2500.2, 35.0), radius = 300, mineable = true},

    -- Illegal Resources
    heroin_1 = {type = "heroin", legal = false, position = vector3(4000.7, 2600.5, 35.0), radius = 300},
    cocaine_1 = {type = "cocaine", legal = false, position = vector3(4100.0, 2700.8, 35.0), radius = 300},
    weed_1 = {type = "weed", legal = false, position = vector3(4200.3, 2800.1, 35.0), radius = 300},
    phosphorous_1 = {type = "phosphorous", legal = false, position = vector3(4300.6, 2900.4, 35.0), radius = 300},
    ephedra_1 = {type = "ephedra", legal = false, position = vector3(4400.9, 3000.7, 35.0), radius = 300},
    sugar_1 = {type = "sugar", legal = false, position = vector3(4500.2, 3100.0, 35.0), radius = 300},
    corn_1 = {type = "corn", legal = false, position = vector3(4600.5, 3200.3, 35.0), radius = 300},
    frog_1 = {type = "frog", legal = false, position = vector3(4700.8, 3300.6, 35.0), radius = 300},
    mushroom_1 = {type = "mushroom", legal = false, position = vector3(4800.1, 3400.9, 35.0), radius = 300}
}

-- Processing configurations based on original fn_processAction.sqf
local ProcessingConfig = {
    -- Legal Processing
    salt = {
        input = {"salt"},
        output = "saltr",
        cost = 350,
        time = 3.0,
        legal = true,
        description = "Processing Salt"
    },
    cement = {
        input = {"rock"},
        output = "cement",
        cost = 450,
        time = 3.0,
        legal = true,
        description = "Mixing Cement"
    },
    sand = {
        input = {"sand"},
        output = "glass",
        cost = 650,
        time = 3.0,
        legal = true,
        description = "Processing Sand"
    },
    iron = {
        input = {"ironore"},
        output = "ironr",
        cost = 750,
        time = 3.0,
        legal = true,
        description = "Processing Iron"
    },
    copper = {
        input = {"copperore"},
        output = "copperr",
        cost = 875,
        time = 3.0,
        legal = true,
        description = "Processing Copper"
    },
    oil = {
        input = {"oilu"},
        output = "oilp",
        cost = 1130,
        time = 3.0,
        legal = true,
        description = "Processing Oil"
    },
    diamond = {
        input = {"diamond"},
        output = "diamondc",
        cost = 1200,
        time = 3.0,
        legal = true,
        description = "Processing Diamond"
    },

    -- Illegal Processing
    marijuana = {
        input = {"cannabis"},
        output = "marijuana",
        cost = 500,
        time = 3.0,
        legal = false,
        description = "Processing Marijuana"
    },
    heroin = {
        input = {"heroinu"},
        output = "heroinp",
        cost = 900,
        time = 4.0,
        legal = false,
        description = "Processing Heroin"
    },
    cocaine = {
        input = {"cocaine"},
        output = "cocainep",
        cost = 1100,
        time = 3.5,
        legal = false,
        description = "Processing Cocaine"
    },
    moonshine = {
        input = {"sugar", "yeast", "corn"},
        output = "moonshine",
        cost = 2200,
        time = 6.5,
        legal = false,
        description = "Processing Moonshine"
    },
    crystalmeth = {
        input = {"lithium", "phosphorous", "ephedra"},
        output = "crystalmeth",
        cost = 2400,
        time = 7.0,
        legal = false,
        description = "Processing Meth"
    },

    -- Double Processing
    hash = {
        input = {"marijuana"},
        output = "hash",
        cost = 0,
        time = 1.5,
        legal = false,
        description = "Processing Hash"
    },
    crack = {
        input = {"cocainep"},
        output = "crack",
        cost = 0,
        time = 2.25,
        legal = false,
        description = "Processing Crack"
    }
}

-- ============================================
-- UTILITY FUNCTIONS
-- ============================================

function OlympusCivJobs.GetPlayerData(src)
    local success, result = pcall(function()
        return exports['olympus-core']:GetPlayerData(src)
    end)

    if success and result then
        return result
    end
    return nil
end

function OlympusCivJobs.HasLicense(src, licenseType)
    local playerData = OlympusCivJobs.GetPlayerData(src)
    if not playerData then return false end

    local success, result = pcall(function()
        return exports['olympus-licenses']:HasLicense(src, licenseType)
    end)

    return success and result or false
end

function OlympusCivJobs.GetPlayerInventory(src)
    local playerData = OlympusCivJobs.GetPlayerData(src)
    if not playerData then return {} end

    return playerData.inventory or {}
end

function OlympusCivJobs.AddItem(src, item, amount)
    local success, result = pcall(function()
        return exports['olympus-core']:AddItem(src, item, amount)
    end)

    return success and result or false
end

function OlympusCivJobs.RemoveItem(src, item, amount)
    local success, result = pcall(function()
        return exports['olympus-core']:RemoveItem(src, item, amount)
    end)

    return success and result or false
end

function OlympusCivJobs.GetItemCount(src, item)
    local inventory = OlympusCivJobs.GetPlayerInventory(src)
    if inventory[item] then
        return inventory[item].amount or 0
    end
    return 0
end

function OlympusCivJobs.GetPlayerMoney(src)
    local playerData = OlympusCivJobs.GetPlayerData(src)
    if not playerData then return 0 end

    return playerData.money or 0
end

function OlympusCivJobs.RemoveMoney(src, amount)
    local success, result = pcall(function()
        return exports['olympus-core']:RemoveMoney(src, amount)
    end)

    return success and result or false
end

function OlympusCivJobs.IsPlayerInZone(src, zoneName)
    local zone = ResourceZones[zoneName]
    if not zone then return false end

    local playerPed = GetPlayerPed(src)
    local playerPos = GetEntityCoords(playerPed)
    local distance = #(playerPos - zone.position)

    return distance <= zone.radius
end

-- ============================================
-- GATHERING SYSTEM
-- ============================================

function OlympusCivJobs.StartGathering(src, zoneName)
    if GatheringStates[src] then
        TriggerClientEvent('olympus-civilian-jobs:notify', src, "You are already gathering!", "error")
        return false
    end

    local zone = ResourceZones[zoneName]
    if not zone then
        TriggerClientEvent('olympus-civilian-jobs:notify', src, "Invalid resource zone!", "error")
        return false
    end

    -- Check if player is in zone
    if not OlympusCivJobs.IsPlayerInZone(src, zoneName) then
        TriggerClientEvent('olympus-civilian-jobs:notify', src, "You're too far from the resource zone!", "error")
        return false
    end

    -- Check if player is in vehicle
    local playerPed = GetPlayerPed(src)
    if GetVehiclePedIsIn(playerPed, false) ~= 0 then
        TriggerClientEvent('olympus-civilian-jobs:notify', src, "You cannot gather while in a vehicle!", "error")
        return false
    end

    -- Check for pickaxe requirement for mineable resources
    if zone.mineable and OlympusCivJobs.GetItemCount(src, "pickaxe") < 1 then
        TriggerClientEvent('olympus-civilian-jobs:notify', src, "You need a pickaxe to gather this resource!", "error")
        return false
    end

    -- Check WPL license restriction for illegal resources
    if not zone.legal and OlympusCivJobs.HasLicense(src, "wpl") then
        TriggerClientEvent('olympus-civilian-jobs:notify', src, "Your Workers Protection License prevents you from harvesting this!", "error")
        return false
    end

    -- Start gathering process
    GatheringStates[src] = {
        zone = zoneName,
        startTime = os.time(),
        gathered = 0
    }

    TriggerClientEvent('olympus-civilian-jobs:startGathering', src, zone.type, zoneName)

    return true
end

function OlympusCivJobs.ProcessGathering(src, amount)
    local gatherState = GatheringStates[src]
    if not gatherState then return false end

    local zone = ResourceZones[gatherState.zone]
    if not zone then return false end

    -- Determine what item to gather based on zone type
    local gatherItem = zone.type
    if zone.type == "apple" then gatherItem = "apple"
    elseif zone.type == "peach" then gatherItem = "peach"
    elseif zone.type == "salt" then gatherItem = "salt"
    elseif zone.type == "iron" then gatherItem = "ironore"
    elseif zone.type == "sand" then gatherItem = "sand"
    elseif zone.type == "diamond" then gatherItem = "diamond"
    elseif zone.type == "oil" then gatherItem = "oilu"
    elseif zone.type == "heroin" then gatherItem = "heroinu"
    elseif zone.type == "cocaine" then gatherItem = "cocaine"
    elseif zone.type == "weed" then gatherItem = "cannabis"
    elseif zone.type == "phosphorous" then gatherItem = "phosphorous"
    elseif zone.type == "ephedra" then gatherItem = "ephedra"
    elseif zone.type == "sugar" then gatherItem = "sugar"
    elseif zone.type == "corn" then gatherItem = "corn"
    elseif zone.type == "frog" then gatherItem = "frog"
    elseif zone.type == "mushroom" then gatherItem = "mushroom"
    end

    -- Try to add items to inventory
    if OlympusCivJobs.AddItem(src, gatherItem, amount) then
        gatherState.gathered = gatherState.gathered + amount
        TriggerClientEvent('olympus-civilian-jobs:notify', src,
            string.format("Gathered %d %s (Total: %d)", amount, gatherItem, gatherState.gathered), "success")
        return true
    else
        TriggerClientEvent('olympus-civilian-jobs:notify', src, "Inventory full!", "error")
        return false
    end
end

function OlympusCivJobs.StopGathering(src)
    if GatheringStates[src] then
        local gathered = GatheringStates[src].gathered
        GatheringStates[src] = nil

        TriggerClientEvent('olympus-civilian-jobs:stopGathering', src)

        if gathered > 0 then
            TriggerClientEvent('olympus-civilian-jobs:notify', src,
                string.format("Gathering complete! Total gathered: %d", gathered), "success")
        end
    end
end

-- ============================================
-- PROCESSING SYSTEM
-- ============================================

function OlympusCivJobs.StartProcessing(src, processType)
    if ProcessingStates[src] then
        TriggerClientEvent('olympus-civilian-jobs:notify', src, "You are already processing!", "error")
        return false
    end

    local config = ProcessingConfig[processType]
    if not config then
        TriggerClientEvent('olympus-civilian-jobs:notify', src, "Invalid processing type!", "error")
        return false
    end

    -- Check if player is in vehicle
    local playerPed = GetPlayerPed(src)
    if GetVehiclePedIsIn(playerPed, false) ~= 0 then
        TriggerClientEvent('olympus-civilian-jobs:notify', src, "You cannot process while in a vehicle!", "error")
        return false
    end

    -- Check input items
    local inputAmounts = {}
    local minAmount = math.huge

    for _, inputItem in ipairs(config.input) do
        local amount = OlympusCivJobs.GetItemCount(src, inputItem)
        if amount <= 0 then
            TriggerClientEvent('olympus-civilian-jobs:notify', src,
                string.format("You need %s to process!", inputItem), "error")
            return false
        end
        inputAmounts[inputItem] = amount
        minAmount = math.min(minAmount, amount)
    end

    -- Check license requirement
    local hasLicense = true
    if config.legal then
        hasLicense = OlympusCivJobs.HasLicense(src, "processing")
    end

    -- Check money for processing cost if no license
    local totalCost = config.cost * minAmount
    if not hasLicense and totalCost > 0 then
        if OlympusCivJobs.GetPlayerMoney(src) < totalCost then
            TriggerClientEvent('olympus-civilian-jobs:notify', src,
                string.format("You need $%s to process without a license!", totalCost), "error")
            return false
        end
    end

    -- Start processing
    ProcessingStates[src] = {
        type = processType,
        config = config,
        amount = minAmount,
        hasLicense = hasLicense,
        cost = totalCost,
        startTime = os.time()
    }

    TriggerClientEvent('olympus-civilian-jobs:startProcessing', src, config.description, config.time * minAmount)

    return true
end

function OlympusCivJobs.CompleteProcessing(src)
    local processState = ProcessingStates[src]
    if not processState then return false end

    local config = processState.config
    local amount = processState.amount

    -- Remove input items
    for _, inputItem in ipairs(config.input) do
        if not OlympusCivJobs.RemoveItem(src, inputItem, amount) then
            TriggerClientEvent('olympus-civilian-jobs:notify', src, "Processing failed - missing ingredients!", "error")
            ProcessingStates[src] = nil
            return false
        end
    end

    -- Add output item
    if not OlympusCivJobs.AddItem(src, config.output, amount) then
        TriggerClientEvent('olympus-civilian-jobs:notify', src, "Processing failed - inventory full!", "error")
        -- Return input items
        for _, inputItem in ipairs(config.input) do
            OlympusCivJobs.AddItem(src, inputItem, amount)
        end
        ProcessingStates[src] = nil
        return false
    end

    -- Charge processing cost if no license
    if not processState.hasLicense and processState.cost > 0 then
        if not OlympusCivJobs.RemoveMoney(src, processState.cost) then
            TriggerClientEvent('olympus-civilian-jobs:notify', src, "Processing failed - insufficient funds!", "error")
            ProcessingStates[src] = nil
            return false
        end

        TriggerClientEvent('olympus-civilian-jobs:notify', src,
            string.format("Processed %d %s for $%s", amount, config.output, processState.cost), "success")
    else
        TriggerClientEvent('olympus-civilian-jobs:notify', src,
            string.format("Processed %d %s", amount, config.output), "success")
    end

    ProcessingStates[src] = nil
    return true
end

function OlympusCivJobs.CancelProcessing(src)
    if ProcessingStates[src] then
        ProcessingStates[src] = nil
        TriggerClientEvent('olympus-civilian-jobs:cancelProcessing', src)
        TriggerClientEvent('olympus-civilian-jobs:notify', src, "Processing cancelled!", "warning")
    end
end

-- ============================================
-- EVENT HANDLERS
-- ============================================

-- Player starts gathering
RegisterNetEvent('olympus-civilian-jobs:startGather', function(zoneName)
    local src = source
    OlympusCivJobs.StartGathering(src, zoneName)
end)

-- Player gathers items
RegisterNetEvent('olympus-civilian-jobs:gather', function(amount)
    local src = source
    OlympusCivJobs.ProcessGathering(src, amount or 1)
end)

-- Player stops gathering
RegisterNetEvent('olympus-civilian-jobs:stopGather', function()
    local src = source
    OlympusCivJobs.StopGathering(src)
end)

-- Player starts processing
RegisterNetEvent('olympus-civilian-jobs:startProcess', function(processType)
    local src = source
    OlympusCivJobs.StartProcessing(src, processType)
end)

-- Player completes processing
RegisterNetEvent('olympus-civilian-jobs:completeProcess', function()
    local src = source
    OlympusCivJobs.CompleteProcessing(src)
end)

-- Player cancels processing
RegisterNetEvent('olympus-civilian-jobs:cancelProcess', function()
    local src = source
    OlympusCivJobs.CancelProcessing(src)
end)

-- Player disconnection cleanup
AddEventHandler('playerDropped', function()
    local src = source
    if GatheringStates[src] then
        GatheringStates[src] = nil
    end
    if ProcessingStates[src] then
        ProcessingStates[src] = nil
    end
end)

-- ============================================
-- EXPORT FUNCTIONS
-- ============================================

exports('GetPlayerJob', function(src)
    if GatheringStates[src] then
        return {type = "gathering", zone = GatheringStates[src].zone}
    elseif ProcessingStates[src] then
        return {type = "processing", process = ProcessingStates[src].type}
    end
    return nil
end)

exports('IsPlayerWorking', function(src)
    return GatheringStates[src] ~= nil or ProcessingStates[src] ~= nil
end)

exports('GetResourceZones', function()
    return ResourceZones
end)

exports('GetProcessingConfig', function()
    return ProcessingConfig
end)

exports('RegisterJob', function(jobData)
    print(string.format("^3[Olympus Civilian Jobs]^7 Registering job: %s", jobData.name))
    -- Job registration logic would go here
    return true
end)

exports('ProcessJobCompletion', function(src, jobName)
    print(string.format("^3[Olympus Civilian Jobs]^7 Processing job completion: %s for player %s", jobName, src))
    -- Job completion logic would go here
    return true
end)

exports('GetJobStatistics', function(src)
    local stats = {
        gathering = GatheringStates[src] and {
            zone = GatheringStates[src].zone,
            gathered = GatheringStates[src].gathered,
            duration = os.time() - GatheringStates[src].startTime
        } or nil,
        processing = ProcessingStates[src] and {
            type = ProcessingStates[src].type,
            duration = os.time() - ProcessingStates[src].startTime
        } or nil
    }
    return stats
end)

print("^2[Olympus Civilian Jobs]^7 Server system loaded successfully")
