fx_version 'cerulean'
game 'gta5'

name 'Olympus Housing System'
description 'Complete housing system with ownership, storage, and real estate'
author 'Olympus Development Team'
version '1.0.0'

-- Dependencies
dependencies {
    'olympus-core',
    'olympus-ui',
    'olympus-economy',
    'oxmysql'
}

-- Shared scripts
shared_scripts {
    'config/shared.lua'
}

-- Client scripts
client_scripts {
    'client/main.lua'
}

-- Server scripts
server_scripts {
    'server/main.lua'
}

-- UI files
ui_page 'html/index.html'

files {
    'html/index.html',
    'html/css/*.css',
    'html/js/*.js',
    'html/images/*.png'
}

-- Exports
exports {
    'GetPlayerHouses',
    'IsPlayerNearHouse',
    'GetNearestHouse',
    'OpenHouseMenu',
    'GetHouseData'
}

server_exports {
    'PurchaseHouse',
    'SellHouse',
    'GetHouseInfo',
    'GetAllHouses',
    'InitializeHousingSystem'
}
