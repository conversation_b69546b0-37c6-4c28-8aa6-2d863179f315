-- Olympus Group/Party System - Client Main
-- Based on original Olympus group functions (fn_groupMenu.sqf, fn_groupManagement.sqf, etc.)

local OlympusGroups = {}

-- Client state
local currentGroup = nil
local groupList = {}
local groupMembers = {}
local isInGroup = false
local groupMenuOpen = false

-- Initialize client system
CreateThread(function()
    while not exports['olympus-core'] do
        Wait(100)
    end
    
    print("[Olympus Groups] Client initialized - Based on original Olympus group functions")
    
    -- Request initial group list
    TriggerServerEvent('olympus-groups:server:requestGroupList')
end)

-- Utility Functions
function OlympusGroups.Notify(message, type)
    TriggerEvent('olympus-core:client:notify', message, type or 'info')
end

function OlympusGroups.OpenGroupMenu()
    if groupMenuOpen then return end
    
    groupMenuOpen = true
    
    if isInGroup then
        -- Open group management menu (existing group)
        OlympusGroups.OpenGroupManagement()
    else
        -- Open group browser (join/create)
        OlympusGroups.OpenGroupBrowser()
    end
end

function OlympusGroups.CloseGroupMenu()
    groupMenuOpen = false
    SetNuiFocus(false, false)
    SendNUIMessage({
        action = "closeMenu"
    })
end

function OlympusGroups.OpenGroupBrowser()
    -- Send group list to NUI
    SendNUIMessage({
        action = "openGroupBrowser",
        groups = groupList,
        config = Config.Groups
    })
    
    SetNuiFocus(true, true)
end

function OlympusGroups.OpenGroupManagement()
    if not currentGroup then return end
    
    -- Send group data to NUI
    SendNUIMessage({
        action = "openGroupManagement",
        group = currentGroup,
        members = groupMembers,
        config = Config.Groups
    })
    
    SetNuiFocus(true, true)
end

function OlympusGroups.OpenCreateGroupDialog()
    SendNUIMessage({
        action = "openCreateGroup",
        config = Config.Groups
    })
    
    SetNuiFocus(true, true)
end

function OlympusGroups.OpenJoinGroupDialog(groupId)
    local group = nil
    for _, g in ipairs(groupList) do
        if g.id == groupId then
            group = g
            break
        end
    end
    
    if not group then return end
    
    SendNUIMessage({
        action = "openJoinGroup",
        group = group,
        config = Config.Groups
    })
    
    SetNuiFocus(true, true)
end

-- Group Management Functions
function OlympusGroups.CreateGroup(groupName, password)
    TriggerServerEvent('olympus-groups:server:createGroup', groupName, password)
end

function OlympusGroups.JoinGroup(groupId, password)
    TriggerServerEvent('olympus-groups:server:joinGroup', groupId, password)
end

function OlympusGroups.LeaveGroup()
    TriggerServerEvent('olympus-groups:server:leaveGroup')
end

function OlympusGroups.KickMember(targetId)
    TriggerServerEvent('olympus-groups:server:kickMember', targetId)
end

function OlympusGroups.LockGroup()
    TriggerServerEvent('olympus-groups:server:lockGroup')
end

function OlympusGroups.UnlockGroup()
    TriggerServerEvent('olympus-groups:server:unlockGroup')
end

-- Event Handlers
RegisterNetEvent('olympus-groups:client:updateGroupList', function(groups)
    groupList = groups
    
    -- Update NUI if group browser is open
    if groupMenuOpen and not isInGroup then
        SendNUIMessage({
            action = "updateGroupList",
            groups = groupList
        })
    end
end)

RegisterNetEvent('olympus-groups:client:updateGroupMembers', function(members, group)
    groupMembers = members
    currentGroup = group
    isInGroup = true
    
    -- Update NUI if group management is open
    if groupMenuOpen and isInGroup then
        SendNUIMessage({
            action = "updateGroupMembers",
            members = groupMembers,
            group = currentGroup
        })
    end
end)

RegisterNetEvent('olympus-groups:client:createGroupResult', function(success, result)
    if success then
        isInGroup = true
        OlympusGroups.CloseGroupMenu()
        TriggerServerEvent('olympus-groups:server:requestGroupData')
    else
        OlympusGroups.Notify(result, 'error')
    end
end)

RegisterNetEvent('olympus-groups:client:joinGroupResult', function(success, result)
    if success then
        isInGroup = true
        OlympusGroups.CloseGroupMenu()
        TriggerServerEvent('olympus-groups:server:requestGroupData')
    else
        OlympusGroups.Notify(result, 'error')
    end
end)

RegisterNetEvent('olympus-groups:client:leaveGroupResult', function(success, result)
    if success then
        isInGroup = false
        currentGroup = nil
        groupMembers = {}
        OlympusGroups.CloseGroupMenu()
    else
        OlympusGroups.Notify(result, 'error')
    end
end)

RegisterNetEvent('olympus-groups:client:kickMemberResult', function(success, result)
    if not success then
        OlympusGroups.Notify(result, 'error')
    end
end)

RegisterNetEvent('olympus-groups:client:lockGroupResult', function(success, result)
    if not success then
        OlympusGroups.Notify(result, 'error')
    end
end)

RegisterNetEvent('olympus-groups:client:unlockGroupResult', function(success, result)
    if not success then
        OlympusGroups.Notify(result, 'error')
    end
end)

-- NUI Callbacks
RegisterNUICallback('closeMenu', function(data, cb)
    OlympusGroups.CloseGroupMenu()
    cb('ok')
end)

RegisterNUICallback('createGroup', function(data, cb)
    OlympusGroups.CreateGroup(data.name, data.password)
    cb('ok')
end)

RegisterNUICallback('joinGroup', function(data, cb)
    OlympusGroups.JoinGroup(data.groupId, data.password)
    cb('ok')
end)

RegisterNUICallback('leaveGroup', function(data, cb)
    OlympusGroups.LeaveGroup()
    cb('ok')
end)

RegisterNUICallback('kickMember', function(data, cb)
    OlympusGroups.KickMember(data.targetId)
    cb('ok')
end)

RegisterNUICallback('lockGroup', function(data, cb)
    OlympusGroups.LockGroup()
    cb('ok')
end)

RegisterNUICallback('unlockGroup', function(data, cb)
    OlympusGroups.UnlockGroup()
    cb('ok')
end)

RegisterNUICallback('openCreateGroup', function(data, cb)
    OlympusGroups.OpenCreateGroupDialog()
    cb('ok')
end)

RegisterNUICallback('openJoinGroup', function(data, cb)
    OlympusGroups.OpenJoinGroupDialog(data.groupId)
    cb('ok')
end)

RegisterNUICallback('refreshGroupList', function(data, cb)
    TriggerServerEvent('olympus-groups:server:requestGroupList')
    cb('ok')
end)

-- Commands
RegisterCommand('group', function()
    OlympusGroups.OpenGroupMenu()
end, false)

RegisterCommand('groupmenu', function()
    OlympusGroups.OpenGroupMenu()
end, false)

RegisterCommand('leavegroup', function()
    if isInGroup then
        OlympusGroups.LeaveGroup()
    else
        OlympusGroups.Notify("You are not in a group", 'error')
    end
end, false)

-- Export Functions
exports('OpenGroupMenu', function()
    OlympusGroups.OpenGroupMenu()
end)

exports('CreateGroup', function(name, password)
    OlympusGroups.CreateGroup(name, password)
end)

exports('JoinGroup', function(groupId, password)
    OlympusGroups.JoinGroup(groupId, password)
end)

exports('LeaveGroup', function()
    OlympusGroups.LeaveGroup()
end)

exports('IsInGroup', function()
    return isInGroup
end)

exports('GetGroupData', function()
    return currentGroup
end)

exports('GetGroupMembers', function()
    return groupMembers
end)

print("[Olympus Groups] Client module loaded")
