# Olympus Altis Life Framework - Installation Guide

This guide will walk you through the complete installation and setup of the Olympus Altis Life Framework for FiveM.

## 📋 Prerequisites

### System Requirements
- **Operating System**: Windows Server 2016+ or Linux (Ubuntu 18.04+)
- **RAM**: Minimum 8GB, Recommended 16GB+
- **CPU**: Minimum 4 cores, Recommended 8+ cores
- **Storage**: Minimum 50GB free space
- **Network**: Stable internet connection with adequate bandwidth

### Software Requirements
- **FiveM Server** (Build 2944 or higher)
- **MySQL Database** (8.0+ recommended)
- **Node.js** (16.0+ for optional tools)

## 🗄️ Database Setup

### 1. Create Database
```sql
CREATE DATABASE olympus_altis_life CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. Create Database User
```sql
CREATE USER 'olympus_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON olympus_altis_life.* TO 'olympus_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. Configure Connection
The framework will automatically create all required tables on first startup.

## 📁 File Installation

### 1. Download Framework
- Extract all framework files to your FiveM server directory
- Ensure the `resources/[olympus]/` folder contains all framework resources

### 2. Required Dependencies
Install this dependency in your `resources` folder:
```
oxmysql
```

**Download oxmysql:**
- Go to: https://github.com/overextended/oxmysql/releases
- Download the latest release (oxmysql.zip)
- Extract to `resources/oxmysql/`

### 3. File Structure
Your server should have this structure:
```
server-data/
├── resources/
│   ├── [olympus]/
│   │   ├── olympus-loader/
│   │   ├── olympus-core/
│   │   ├── olympus-apd/
│   │   ├── olympus-medical/
│   │   ├── olympus-gangs/
│   │   ├── olympus-economy/
│   │   ├── olympus-vehicles/
│   │   ├── olympus-events/
│   │   ├── olympus-ui/
│   │   ├── olympus-civilian/
│   │   └── olympus-admin/
│   └── oxmysql/
├── server.cfg
└── cache/
```

## ⚙️ Configuration

### 1. Server Configuration
Edit `server.cfg` with your details:

```cfg
# Database Connection
set mysql_connection_string "mysql://olympus_user:your_secure_password@localhost/olympus_altis_life?charset=utf8mb4"

# Server Information
sv_hostname "Your Olympus Altis Life Server"
sv_licenseKey "your_license_key_here"
set steam_webApiKey "your_steam_web_api_key"

# Admin Setup
add_principal identifier.steam:YOUR_STEAM_ID group.admin
```

### 2. Framework Configuration
Edit configuration files in each resource's `config/` folder:

#### Core Settings (`olympus-core/config/shared.lua`)
```lua
Config.StartingMoney = 5000
Config.MaxPlayers = 200
Config.RespawnTime = 300
Config.NewLifeRuleTime = 900
```

#### APD Settings (`olympus-apd/config/shared.lua`)
```lua
Config.MinAPDForEvents = 4
Config.ProcessingTime = 900
Config.MaxTicketAmount = 500000
```

#### Economy Settings (`olympus-economy/config/shared.lua`)
```lua
Config.BankInterestRate = 0.001
Config.TaxRate = 0.05
Config.StartingMoney = 5000
```

### 3. Discord Integration (Optional)
For Discord logging and integration:
```cfg
set discord_webhook "your_webhook_url"
set discord_bot_token "your_bot_token"
```

## 🚀 Starting the Server

### 1. Resource Load Order
Ensure resources load in this order in `server.cfg`:
```cfg
# Core Dependencies
ensure oxmysql

# Framework Loader
ensure olympus-loader

# Core Framework
ensure olympus-core

# Faction Systems
ensure olympus-apd
ensure olympus-medical

# Additional Systems
ensure olympus-gangs
ensure olympus-economy
ensure olympus-vehicles
ensure olympus-events
ensure olympus-ui
ensure olympus-civilian
ensure olympus-admin
```

### 2. First Startup
1. Start your FiveM server
2. Watch console for initialization messages
3. Check for any errors in the console
4. Verify database tables are created automatically

### 3. Verification
Check that the framework loaded correctly:
- Console shows "Framework successfully initialized!"
- Database contains all required tables
- No error messages in console

## 👤 Admin Setup

### 1. Set Admin Level
Connect to your server and run in console:
```
olympus setadmin [player_id] [level]
```

Admin levels:
- 1: Moderator
- 2: Administrator  
- 3: Senior Admin
- 4: Head Admin
- 5: Owner

### 2. Admin Commands
Test admin functionality:
- `/admin` - Open admin menu
- `/noclip` - Toggle noclip
- `/godmode` - Toggle god mode
- `/spectate [id]` - Spectate player

## 🔧 Troubleshooting

### Common Issues

#### Database Connection Failed
- Verify MySQL is running
- Check connection string in server.cfg
- Ensure database user has proper permissions
- Test connection manually

#### Resources Not Loading
- Check resource dependencies
- Verify file permissions
- Check for syntax errors in config files
- Ensure proper load order

#### Players Can't Connect
- Check server.cfg for correct endpoints
- Verify license key is valid
- Check firewall settings
- Ensure ports are open (30120 default)

#### Framework Not Initializing
- Check console for error messages
- Verify all required resources are present
- Check database connectivity
- Ensure proper file structure

### Debug Mode
Enable debug mode for detailed logging:
```cfg
set olympus_debug "true"
```

### Log Files
Check these locations for logs:
- FiveM console output
- `logs/` folder (if configured)
- Database `olympus_logs` table

## 📊 Performance Optimization

### 1. Database Optimization
```sql
-- Add indexes for better performance
ALTER TABLE olympus_players ADD INDEX idx_identifier (identifier);
ALTER TABLE olympus_vehicles ADD INDEX idx_owner (owner_id);
ALTER TABLE olympus_gangs ADD INDEX idx_leader (leader_id);
```

### 2. Server Settings
```cfg
# Performance Settings
set onesync on
set onesync_enableInfinity 1
set onesync_enableBeyond 1
set sv_scriptHookAllowed 0
```

### 3. Resource Monitoring
Monitor resource usage:
```
resmon
```

## 🔒 Security

### 1. Database Security
- Use strong passwords
- Limit database user permissions
- Enable SSL connections
- Regular backups

### 2. Server Security
- Keep FiveM updated
- Use strong admin passwords
- Enable anti-cheat
- Monitor logs regularly

### 3. Framework Security
- Regular security updates
- Monitor admin actions
- Use proper permissions
- Enable logging

## 📈 Monitoring

### 1. Performance Monitoring
The framework includes built-in monitoring:
- Server performance metrics
- Player activity tracking
- Resource usage monitoring
- Error reporting

### 2. Discord Integration
Set up Discord webhooks for:
- Admin action logs
- Player reports
- Security alerts
- Server status updates

## 🆘 Support

### Getting Help
1. Check this documentation first
2. Review console logs for errors
3. Check the troubleshooting section
4. Contact support with detailed information

### Reporting Issues
When reporting issues, include:
- Server console logs
- Framework version
- Steps to reproduce
- Expected vs actual behavior
- Server specifications

### Community Resources
- Documentation: [Framework Docs]
- Discord: [Community Discord]
- Forums: [Support Forums]
- GitHub: [Issue Tracker]

## 📝 Post-Installation

### 1. Server Testing
- Test all major systems
- Verify admin commands work
- Check player registration
- Test faction systems
- Verify economy functions

### 2. Configuration Tuning
- Adjust spawn locations
- Configure job payments
- Set up gang territories
- Configure event schedules
- Tune economy settings

### 3. Content Addition
- Add custom vehicles
- Configure additional jobs
- Set up custom events
- Add server-specific features
- Configure whitelist (if needed)

## ✅ Installation Checklist

- [ ] MySQL database created and configured
- [ ] All framework files extracted
- [ ] Dependencies installed (mysql-async, oxmysql)
- [ ] server.cfg configured with database connection
- [ ] Admin permissions set up
- [ ] Server started successfully
- [ ] Framework initialization confirmed
- [ ] Database tables created automatically
- [ ] Admin commands tested
- [ ] Player registration tested
- [ ] All major systems verified
- [ ] Performance monitoring enabled
- [ ] Security measures implemented
- [ ] Backup system configured

---

**Congratulations!** Your Olympus Altis Life Framework is now installed and ready for players. Remember to regularly update the framework and monitor server performance for the best experience.
