-- Olympus Licensing System - Complete Implementation
-- Based on exact Olympus Altis Life licensing system with all mechanics

Config = {}

-- Licensing System Settings (Exact Olympus Implementation)
Config.Licensing = {
    enabled = true,

    -- DMV Locations (FiveM coordinates)
    dmvLocations = {
        vector3(240.0, -1379.0, 33.7), -- Los Santos DMV
        vector3(1855.0, 3678.0, 33.0), -- Sandy Shores DMV
        vector3(-551.0, -191.0, 38.2)  -- Rockford Hills DMV
    },

    -- Gun Store Locations (For WPL and Firearm License)
    gunStoreLocations = {
        vector3(21.0, -1107.0, 29.8), -- Ammunation Downtown
        vector3(810.0, -2157.0, 29.6), -- Ammunation South
        vector3(1693.0, 3759.0, 34.7)  -- Ammunation Sandy Shores
    },

    -- License Revocation System
    revocation = {
        enabled = true,
        apdAuthority = true, -- APD can revoke licenses
        reasons = {
            'criminal_activity',
            'misuse_of_license',
            'court_order',
            'administrative_action'
        }
    }
}

-- All Licenses (Exact Olympus Implementation with Costs)
Config.Licenses = {
    -- Basic Civilian Licenses
    ['driver'] = {
        name = 'Driver License',
        displayName = 'Driver License',
        cost = 500, -- $500 at DMV
        location = 'dmv',
        description = 'Required to legally drive vehicles',
        requirements = {
            age = 16, -- Minimum age
            test = false -- No test required
        },
        revocable = true
    },
    
    ['pilot'] = {
        name = 'Pilot License',
        displayName = 'Pilot License',
        cost = 25000, -- $25,000 at DMV
        location = 'dmv',
        description = 'Required to legally fly aircraft',
        requirements = {
            age = 18,
            test = true, -- Flight test required
            instructor = true -- Requires instructor
        },
        revocable = true
    },
    
    ['truck'] = {
        name = 'Truck License',
        displayName = 'Truck License',
        cost = 10000, -- $10,000 at DMV
        location = 'dmv',
        description = 'Required to drive large trucks',
        requirements = {
            age = 18,
            test = false,
            driverLicense = true -- Must have driver license first
        },
        revocable = true
    },
    
    ['boat'] = {
        name = 'Boat License',
        displayName = 'Boat License',
        cost = 2000, -- $2,000 at DMV
        location = 'dmv',
        description = 'Required to operate boats',
        requirements = {
            age = 16,
            test = false
        },
        revocable = true
    },
    
    ['fishing'] = {
        name = 'Fishing License',
        displayName = 'Fishing License',
        cost = 2500, -- $2,500 at DMV
        location = 'dmv',
        description = 'Required for commercial fishing',
        requirements = {
            age = 16,
            test = false
        },
        revocable = true
    },
    
    ['homeowner'] = {
        name = 'Homeowner License',
        displayName = 'Homeowner License',
        cost = 100000, -- $100,000 at DMV
        location = 'dmv',
        description = 'Required to purchase houses',
        requirements = {
            age = 18,
            test = false
        },
        revocable = false -- Cannot be revoked
    },
    
    -- Weapon Licenses
    ['firearm'] = {
        name = 'Firearm License',
        displayName = 'Firearm License',
        cost = 50000, -- $50,000 at gun store
        location = 'gun_store',
        description = 'Required to purchase and carry firearms',
        requirements = {
            age = 21,
            test = false,
            backgroundCheck = true
        },
        revocable = true
    },
    
    ['workers_protection'] = {
        name = 'Worker\'s Protection License',
        displayName = 'Worker\'s Protection License',
        cost = 75000, -- $75,000 at gun store
        location = 'gun_store',
        description = 'Allows purchase of WPL weapons and 15% legal run bonus',
        requirements = {
            age = 21,
            test = false,
            firearmLicense = true -- Must have firearm license first
        },
        revocable = true,
        benefits = {
            legalRunBonus = 0.15, -- 15% bonus on legal runs
            weaponAccess = true, -- Access to WPL weapons
            processingBonus = true -- Faster processing
        }
    },
    
    ['rebel'] = {
        name = 'Rebel License',
        displayName = 'Rebel License',
        cost = 500000, -- $500,000 at rebel outpost
        location = 'rebel_outpost',
        description = 'Allows purchase of rebel weapons and equipment',
        requirements = {
            age = 21,
            test = false,
            firearmLicense = true
        },
        revocable = true,
        illegal = true -- Illegal license
    },
    
    -- Processing Licenses (Legal Runs)
    ['salt_processing'] = {
        name = 'Salt Processing License',
        displayName = 'Salt Processing License',
        cost = 12000, -- $12,000 at DMV
        location = 'dmv',
        description = 'Required to process salt',
        requirements = {
            age = 18,
            test = false
        },
        revocable = true
    },
    
    ['copper_processing'] = {
        name = 'Copper Processing License',
        displayName = 'Copper Processing License',
        cost = 8000, -- $8,000 at DMV
        location = 'dmv',
        description = 'Required to process copper',
        requirements = {
            age = 18,
            test = false
        },
        revocable = true
    },
    
    ['iron_processing'] = {
        name = 'Iron Processing License',
        displayName = 'Iron Processing License',
        cost = 9500, -- $9,500 at DMV
        location = 'dmv',
        description = 'Required to process iron',
        requirements = {
            age = 18,
            test = false
        },
        revocable = true
    },
    
    ['oil_processing'] = {
        name = 'Oil Processing License',
        displayName = 'Oil Processing License',
        cost = 10000, -- $10,000 at DMV
        location = 'dmv',
        description = 'Required to process oil',
        requirements = {
            age = 18,
            test = false
        },
        revocable = true
    },
    
    ['diamond_processing'] = {
        name = 'Diamond Processing License',
        displayName = 'Diamond Processing License',
        cost = 35000, -- $35,000 at DMV
        location = 'dmv',
        description = 'Required to process diamonds',
        requirements = {
            age = 18,
            test = false
        },
        revocable = true
    },
    
    ['silver_processing'] = {
        name = 'Silver Processing License',
        displayName = 'Silver Processing License',
        cost = 9000, -- $9,000 at DMV
        location = 'dmv',
        description = 'Required to process silver',
        requirements = {
            age = 18,
            test = false
        },
        revocable = true
    },
    
    ['platinum_processing'] = {
        name = 'Platinum Processing License',
        displayName = 'Platinum Processing License',
        cost = 10000, -- $10,000 at DMV
        location = 'dmv',
        description = 'Required to process platinum',
        requirements = {
            age = 18,
            test = false
        },
        revocable = true
    },
    
    ['sand_processing'] = {
        name = 'Sand Processing License',
        displayName = 'Sand Processing License',
        cost = 14500, -- $14,500 at DMV
        location = 'dmv',
        description = 'Required to process sand into glass',
        requirements = {
            age = 18,
            test = false
        },
        revocable = true
    },
    
    ['cement_mixing'] = {
        name = 'Cement Mixing License',
        displayName = 'Cement Mixing License',
        cost = 6500, -- $6,500 at DMV
        location = 'dmv',
        description = 'Required to mix cement',
        requirements = {
            age = 18,
            test = false
        },
        revocable = true
    },
    
    -- Drug Processing Licenses (Illegal)
    ['cocaine_processing'] = {
        name = 'Cocaine Processing License',
        displayName = 'Cocaine Processing License',
        cost = 30000, -- $30,000 at processor
        location = 'cocaine_processor',
        description = 'Required to process cocaine',
        requirements = {
            age = 18,
            test = false
        },
        revocable = true,
        illegal = true
    },
    
    ['heroin_processing'] = {
        name = 'Heroin Processing License',
        displayName = 'Heroin Processing License',
        cost = 25000, -- $25,000 at processor
        location = 'heroin_processor',
        description = 'Required to process heroin',
        requirements = {
            age = 18,
            test = false
        },
        revocable = true,
        illegal = true
    },
    
    ['weed_processing'] = {
        name = 'Weed Processing License',
        displayName = 'Weed Processing License',
        cost = 20000, -- $20,000 at processor
        location = 'weed_processor',
        description = 'Required to process weed',
        requirements = {
            age = 18,
            test = false
        },
        revocable = true,
        illegal = true
    },
    
    ['meth_processing'] = {
        name = 'Meth Processing License',
        displayName = 'Meth Processing License',
        cost = 35000, -- $35,000 at processor
        location = 'meth_processor',
        description = 'Required to process meth',
        requirements = {
            age = 18,
            test = false
        },
        revocable = true,
        illegal = true
    }
}

-- License Dependencies (Exact Olympus Requirements)
Config.LicenseDependencies = {
    ['truck'] = {'driver'}, -- Truck license requires driver license
    ['workers_protection'] = {'firearm'}, -- WPL requires firearm license
    ['rebel'] = {'firearm'} -- Rebel license requires firearm license
}

-- License Testing System
Config.LicenseTesting = {
    ['pilot'] = {
        enabled = true,
        testType = 'flight_test',
        instructor = 'apd_or_rnr', -- APD or R&R can conduct test
        passingScore = 80, -- 80% to pass
        testFee = 5000 -- $5,000 test fee
    }
}

-- License Revocation Reasons (Exact Olympus)
Config.RevocationReasons = {
    ['criminal_activity'] = 'Criminal activity while using license',
    ['misuse_of_license'] = 'Misuse of licensed privileges',
    ['court_order'] = 'Court-ordered revocation',
    ['administrative_action'] = 'Administrative action by authorities'
}

return Config
