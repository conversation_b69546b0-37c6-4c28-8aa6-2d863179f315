fx_version 'cerulean'
game 'gta5'

name 'Olympus Admin System'
description 'Complete administration and moderation tools for Olympus Framework'
author 'Olympus Development Team'
version '1.0.0'

dependencies {
    'olympus-core'
}

shared_scripts {
    'config/shared.lua'
}

server_scripts {
    'server/main.lua'
}

client_scripts {
    'client/main.lua'
}

ui_page 'html/index.html'

files {
    'html/index.html',
    'html/css/*.css',
    'html/js/*.js',
    'html/img/**/*'
}

exports {
    'IsPlayerAdmin',
    'GetAdminLevel',
    'HasPermission',
    'LogAdminAction',
    'SendAdminMessage'
}

server_exports {
    'BanPlayer',
    'KickPlayer',
    'WarnPlayer',
    'FreezePlayer',
    'TeleportPlayer',
    'SpectatePlayer',
    'GetPlayerInfo',
    'GetServerStats'
}
