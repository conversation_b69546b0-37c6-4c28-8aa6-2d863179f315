-- Olympus Lottery System - Client Main
-- Based on original fn_buyLotteryTicket.sqf

local OlympusLottery = {}
local lotteryBlips = {}
local isInLotteryMenu = false

-- Initialize lottery system
CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end

    print("[Olympus Lottery] Client initialized")

    -- Create lottery location blips
    OlympusLottery.CreateLotteryBlips()

    -- Start interaction thread
    CreateThread(OlympusLottery.InteractionThread)
end)

-- Utility Functions
function OlympusLottery.Notify(message, type)
    local success = pcall(function()
        exports['olympus-core']:Notify(message, type)
    end)

    if not success then
        -- Fallback notification
        SetNotificationTextEntry("STRING")
        AddTextComponentString(message)
        DrawNotification(false, false)
    end
end

function OlympusLottery.PlayAnimation(dict, anim, duration)
    RequestAnimDict(dict)
    while not HasAnimDictLoaded(dict) do
        Wait(10)
    end

    local playerPed = PlayerPedId()
    TaskPlayAnim(playerPed, dict, anim, 8.0, -8.0, duration or -1, 1, 0, false, false, false)
end

-- Blip Management
function OlympusLottery.CreateLotteryBlips()
    for i, location in pairs(Config.Lottery.locations) do
        local blip = AddBlipForCoord(location.coords.x, location.coords.y, location.coords.z)
        SetBlipSprite(blip, location.blip.sprite)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, location.blip.scale)
        SetBlipColour(blip, location.blip.color)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(location.blip.name)
        EndTextCommandSetBlipName(blip)

        lotteryBlips[i] = blip
    end
end

-- Lottery System Functions
function OlympusLottery.OpenLotteryMenu()
    if isInLotteryMenu then return end

    -- Check player status (matching original fn_buyLotteryTicket.sqf checks)
    local playerPed = PlayerPedId()
    if GetVehiclePedIsIn(playerPed, false) ~= 0 then
        OlympusLottery.Notify("You are not able to buy a lottery ticket while in a vehicle!", "error")
        return
    end

    -- Request lottery status from server
    TriggerServerEvent('olympus-lottery:server:checkPlayer')
end

function OlympusLottery.ShowTicketPurchaseMenu(lotteryData)
    if not lotteryData.lotteryActive then
        if lotteryData.cooldown then
            OlympusLottery.Notify("The lottery system is on cooldown, you are not able to buy lottery tickets at this time!", "error")
        else
            OlympusLottery.Notify("No lottery is currently active!", "error")
        end
        return
    end

    if lotteryData.inLottery then
        OlympusLottery.Notify("You have already bought tickets in the current lottery!", "error")
        return
    end

    -- Calculate jackpot amount and next draw time
    local jackpotAmount = 0
    local nextDrawTime = "--:--"

    if lotteryData.drawTime then
        local timeLeft = lotteryData.drawTime - os.time()
        if timeLeft > 0 then
            local minutes = math.floor(timeLeft / 60)
            local seconds = timeLeft % 60
            nextDrawTime = string.format("%02d:%02d", minutes, seconds)
        end
    end

    -- Open the authentic Olympus lottery UI
    isInLotteryMenu = true
    TriggerEvent('olympus-lottery:client:openUI', {
        jackpot = jackpotAmount,
        nextDraw = nextDrawTime,
        cooldown = lotteryData.cooldown or false
    })
end

-- Interaction System
function OlympusLottery.InteractionThread()
    while true do
        Wait(0)

        local playerPed = PlayerPedId()
        local coords = GetEntityCoords(playerPed)
        local inRange = false

        -- Check for lottery location proximity
        for _, location in pairs(Config.Lottery.locations) do
            local distance = #(coords - location.coords)
            if distance <= 3.0 then
                inRange = true

                -- Show interaction prompt
                DrawText3D(location.coords.x, location.coords.y, location.coords.z + 1.0, "[E] Buy Lottery Tickets")

                if IsControlJustReleased(0, 38) then -- E key
                    OlympusLottery.OpenLotteryMenu()
                end
                break
            end
        end

        if not inRange then
            Wait(500) -- Reduce frequency when not near anything
        end
    end
end

function DrawText3D(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())

    if onScreen then
        SetTextScale(0.35, 0.35)
        SetTextFont(4)
        SetTextProportional(1)
        SetTextColour(255, 255, 255, 215)
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x, _y)

        local factor = (string.len(text)) / 370
        DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 41, 11, 41, 68)
    end
end

-- Event Handlers
RegisterNetEvent('olympus-lottery:client:notify', function(message, type)
    OlympusLottery.Notify(message, type)
end)

RegisterNetEvent('olympus-lottery:client:receivePlayerCheck', function(data)
    OlympusLottery.ShowTicketPurchaseMenu(data)
end)

RegisterNetEvent('olympus-lottery:client:receiveLotteryStatus', function(status)
    if status.active then
        local timeRemaining = math.floor(status.timeRemaining / 60)
        OlympusLottery.Notify(string.format("Lottery Status: %d tickets sold, $%s prize pool, %d minutes remaining",
            status.totalTickets, status.prizePool, timeRemaining), "primary")
    elseif status.cooldown then
        local cooldownRemaining = math.floor(status.cooldownRemaining / 60)
        OlympusLottery.Notify(string.format("Lottery on cooldown for %d more minutes", cooldownRemaining), "warning")
    else
        OlympusLottery.Notify("No lottery currently active", "error")
    end
end)

-- Commands
RegisterCommand('lottery', function()
    OlympusLottery.OpenLotteryMenu()
end, false)

RegisterCommand('lotterystatus', function()
    TriggerServerEvent('olympus-lottery:server:requestLotteryStatus')
end, false)

-- Event handler for lottery UI close
RegisterNetEvent('olympus-lottery:client:closeLottery', function()
    isInLotteryMenu = false
end)

-- Export Functions
exports('IsPlayerInLottery', function()
    -- This would need to be tracked client-side or requested from server
    return false
end)

exports('GetLotteryStatus', function()
    TriggerServerEvent('olympus-lottery:server:requestLotteryStatus')
    return true
end)

exports('BuyLotteryTicket', function(ticketCount)
    if ticketCount and ticketCount > 0 and ticketCount <= Config.Lottery.maxTicketsPerPlayer then
        TriggerServerEvent('olympus-lottery:server:buyTickets', ticketCount)
        return true
    end
    return false
end)

print("[Olympus Lottery] Client module loaded")
