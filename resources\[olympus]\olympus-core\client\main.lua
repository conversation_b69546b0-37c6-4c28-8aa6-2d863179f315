-- Olympus Core Framework - Main Client Script
-- This is the primary client-side entry point for the framework

local OlympusCore = {}
OlympusCore.PlayerData = {}
OlympusCore.PlayerLoaded = false
OlympusCore.ServerCallbacks = {}
OlympusCore.CurrentRequestId = 0

-- Framework initialization
CreateThread(function()
    while not NetworkIsSessionStarted() do
        Wait(0)
    end
    
    print("^2[Olympus Core]^7 Client framework initializing...")
    
    -- Request player data from server
    TriggerServerEvent('olympus:server:playerJoined', GetPlayerServerId(PlayerId()))
end)

-- Player loaded event
RegisterNetEvent('olympus:client:playerLoaded')
AddEventHandler('olympus:client:playerLoaded', function(playerData)
    OlympusCore.PlayerData = playerData
    OlympusCore.PlayerLoaded = true
    
    print("^2[Olympus Core]^7 Player data loaded successfully")
    
    -- Initialize UI
    TriggerEvent('olympus:client:initUI')

    -- Initialize HUD
    TriggerEvent('olympus:client:initHUD')

    -- Send initial player data to UI
    exports['olympus-ui']:UpdateHUD({
        money = playerData.money or 0,
        bank = playerData.bank or 0,
        faction = playerData.faction or 'civilian',
        faction_rank = playerData.faction_rank or 1,
        gang_id = playerData.gang_id,
        gang_name = playerData.gang_name,
        gang_rank = playerData.gang_rank or 1
    })

    -- Spawn player
    TriggerEvent('olympus:client:spawnPlayer')

    -- Start player loops
    TriggerEvent('olympus:client:startPlayerLoops')
end)

-- Update player data
RegisterNetEvent('olympus:client:updatePlayerData')
AddEventHandler('olympus:client:updatePlayerData', function(key, value)
    if OlympusCore.PlayerData then
        OlympusCore.PlayerData[key] = value

        -- Update UI with new data
        if key == 'money' or key == 'bank' then
            exports['olympus-ui']:UpdateHUD({
                [key] = value
            })
        end

        -- Trigger update event
        TriggerEvent('olympus:client:playerDataUpdated', key, value)
    end
end)

-- Spawn player
RegisterNetEvent('olympus:client:spawnPlayer')
AddEventHandler('olympus:client:spawnPlayer', function()
    if OlympusCore.PlayerData and OlympusCore.PlayerData.position then
        local pos = OlympusCore.PlayerData.position
        
        -- Set player position
        SetEntityCoords(PlayerPedId(), pos.x, pos.y, pos.z, false, false, false, true)
        
        -- Set player health
        SetEntityHealth(PlayerPedId(), 200)
        
        -- Remove weapons
        RemoveAllPedWeapons(PlayerPedId(), true)
        
        -- Load inventory
        TriggerEvent('olympus:client:loadInventory')
        
        -- Load loadout
        TriggerEvent('olympus:client:loadLoadout')
        
        print("^2[Olympus Core]^7 Player spawned successfully")
    end
end)

-- Player loops
RegisterNetEvent('olympus:client:startPlayerLoops')
AddEventHandler('olympus:client:startPlayerLoops', function()
    -- Position saving loop
    CreateThread(function()
        while OlympusCore.PlayerLoaded do
            Wait(30000) -- Save position every 30 seconds
            
            if OlympusCore.PlayerData then
                local ped = PlayerPedId()
                local coords = GetEntityCoords(ped)
                
                OlympusCore.PlayerData.position = {
                    x = coords.x,
                    y = coords.y,
                    z = coords.z
                }
                
                -- Update server
                TriggerServerEvent('olympus:server:updatePlayerPosition', OlympusCore.PlayerData.position)
            end
        end
    end)
    
    -- Health and armor monitoring
    CreateThread(function()
        while OlympusCore.PlayerLoaded do
            Wait(1000)
            
            local ped = PlayerPedId()
            local health = GetEntityHealth(ped)
            local armor = GetPedArmour(ped)
            
            -- Check if player died
            if health <= 0 and not OlympusCore.PlayerData.isDead then
                TriggerEvent('olympus:client:playerDied')
            end
            
            -- Update HUD with Olympus style
            local healthPercent = math.max(0, ((health - 100) / 100) * 100)
            exports['olympus-ui']:UpdateHUD({
                health = healthPercent,
                armor = armor
            })
        end
    end)
    
    -- Vehicle monitoring
    CreateThread(function()
        while OlympusCore.PlayerLoaded do
            Wait(1000)
            
            local ped = PlayerPedId()
            local vehicle = GetVehiclePedIsIn(ped, false)
            
            if vehicle ~= 0 then
                local speed = GetEntitySpeed(vehicle) * 2.236936 -- Convert to MPH
                local fuel = GetVehicleFuelLevel(vehicle)
                local engine = GetVehicleEngineHealth(vehicle)
                local body = GetVehicleBodyHealth(vehicle)
                
                -- Update HUD
                TriggerEvent('olympus:client:updateVehicleHUD', {
                    speed = speed,
                    fuel = fuel,
                    engine = engine,
                    body = body,
                    inVehicle = true
                })
            else
                TriggerEvent('olympus:client:updateVehicleHUD', {
                    inVehicle = false
                })
            end
        end
    end)
end)

-- Player death handling
RegisterNetEvent('olympus:client:playerDied')
AddEventHandler('olympus:client:playerDied', function()
    OlympusCore.PlayerData.isDead = true
    
    -- Trigger death screen
    TriggerEvent('olympus:client:showDeathScreen')
    
    -- Notify server
    TriggerServerEvent('olympus:server:playerDied')
    
    print("^1[Olympus Core]^7 Player died")
end)

-- Player revive
RegisterNetEvent('olympus:client:revivePlayer')
AddEventHandler('olympus:client:revivePlayer', function()
    local ped = PlayerPedId()
    
    -- Revive player
    SetEntityHealth(ped, 200)
    ClearPedBloodDamage(ped)
    
    -- Clear death state
    OlympusCore.PlayerData.isDead = false
    
    -- Hide death screen
    TriggerEvent('olympus:client:hideDeathScreen')
    
    print("^2[Olympus Core]^7 Player revived")
end)

-- Server callbacks
function TriggerServerCallback(name, cb, ...)
    OlympusCore.ServerCallbacks[OlympusCore.CurrentRequestId] = cb
    
    TriggerServerEvent('olympus:server:triggerCallback', name, OlympusCore.CurrentRequestId, ...)
    
    if OlympusCore.CurrentRequestId < 65535 then
        OlympusCore.CurrentRequestId = OlympusCore.CurrentRequestId + 1
    else
        OlympusCore.CurrentRequestId = 0
    end
end

RegisterNetEvent('olympus:client:serverCallback')
AddEventHandler('olympus:client:serverCallback', function(requestId, ...)
    if OlympusCore.ServerCallbacks[requestId] then
        OlympusCore.ServerCallbacks[requestId](...)
        OlympusCore.ServerCallbacks[requestId] = nil
    end
end)

-- Notification system
RegisterNetEvent('olympus:client:notify')
AddEventHandler('olympus:client:notify', function(data)
    SendNUIMessage({
        type = 'notification',
        data = data
    })
end)

-- Progress bar system
RegisterNetEvent('olympus:client:showProgressBar')
AddEventHandler('olympus:client:showProgressBar', function(data)
    SendNUIMessage({
        type = 'progressBar',
        data = data
    })
end)

RegisterNetEvent('olympus:client:hideProgressBar')
AddEventHandler('olympus:client:hideProgressBar', function()
    SendNUIMessage({
        type = 'hideProgressBar'
    })
end)

-- Menu system
RegisterNetEvent('olympus:client:openMenu')
AddEventHandler('olympus:client:openMenu', function(menuData)
    SetNuiFocus(true, true)
    SendNUIMessage({
        type = 'openMenu',
        data = menuData
    })
end)

RegisterNetEvent('olympus:client:closeMenu')
AddEventHandler('olympus:client:closeMenu', function()
    SetNuiFocus(false, false)
    SendNUIMessage({
        type = 'closeMenu'
    })
end)

-- NUI Callbacks
RegisterNUICallback('closeMenu', function(data, cb)
    TriggerEvent('olympus:client:closeMenu')
    cb('ok')
end)

RegisterNUICallback('menuAction', function(data, cb)
    TriggerEvent('olympus:client:menuAction', data)
    cb('ok')
end)

-- Key bindings (authentic Olympus)
RegisterKeyMapping('olympus_ymenu', 'Y Menu', 'keyboard', 'Y')
RegisterKeyMapping('olympus_vehicle_menu', 'Vehicle Menu', 'keyboard', 'F2')
RegisterKeyMapping('olympus_interaction', 'Interaction Menu', 'keyboard', 'E')

-- Command handlers
-- Y Menu command (authentic Olympus)
RegisterCommand('olympus_ymenu', function()
    if OlympusCore.PlayerLoaded then
        TriggerEvent('olympus:client:openYMenu')
    end
end, false)

RegisterCommand('olympus_vehicle_menu', function()
    if OlympusCore.PlayerLoaded then
        TriggerEvent('olympus:client:openVehicleMenu')
    end
end, false)

RegisterCommand('olympus_interaction', function()
    if OlympusCore.PlayerLoaded then
        TriggerEvent('olympus:client:openInteractionMenu')
    end
end, false)

-- Y Menu event handler (authentic Olympus)
RegisterNetEvent('olympus:client:openYMenu')
AddEventHandler('olympus:client:openYMenu', function()
    if exports['olympus-ui'] then
        exports['olympus-ui']:OpenYMenu({
            playerData = OlympusCore.PlayerData,
            faction = OlympusCore.PlayerData.faction or 'civilian',
            gang = OlympusCore.PlayerData.gang or nil,
            licenses = OlympusCore.PlayerData.licenses or {},
            inventory = OlympusCore.PlayerData.inventory or {},
            keys = OlympusCore.PlayerData.keys or {},
            contacts = OlympusCore.PlayerData.contacts or {},
            callHistory = OlympusCore.PlayerData.call_history or {},
            wantedList = {},
            settings = OlympusCore.PlayerData.settings or {}
        })
    end
end)

RegisterNetEvent('olympus:client:openVehicleMenu')
AddEventHandler('olympus:client:openVehicleMenu', function()
    if exports['olympus-ui'] then
        exports['olympus-ui']:OpenMenu('vehicle', {
            vehicles = OlympusCore.PlayerData.vehicles or {}
        })
    end
end)

RegisterNetEvent('olympus:client:openInteractionMenu')
AddEventHandler('olympus:client:openInteractionMenu', function()
    if exports['olympus-ui'] then
        exports['olympus-ui']:OpenMenu('interaction', {})
    end
end)

-- Utility functions
function GetPlayerData()
    return OlympusCore.PlayerData
end

function IsPlayerLoaded()
    return OlympusCore.PlayerLoaded
end

function GetPlayerMoney()
    if OlympusCore.PlayerData then
        return OlympusCore.PlayerData.money or 0
    end
    return 0
end

function GetPlayerBank()
    if OlympusCore.PlayerData then
        return OlympusCore.PlayerData.bank or 0
    end
    return 0
end

function GetPlayerJob()
    if OlympusCore.PlayerData then
        return OlympusCore.PlayerData.job or 'unemployed'
    end
    return 'unemployed'
end

function GetPlayerFaction()
    if OlympusCore.PlayerData then
        return OlympusCore.PlayerData.faction or 'civilian'
    end
    return 'civilian'
end

function GetPlayerGang()
    if OlympusCore.PlayerData then
        return OlympusCore.PlayerData.gang_id
    end
    return nil
end

-- Initialize UI event handler
RegisterNetEvent('olympus:client:initUI')
AddEventHandler('olympus:client:initUI', function()
    -- Wait for UI to be ready
    CreateThread(function()
        while not exports['olympus-ui'] or not exports['olympus-ui']:IsLoaded() do
            Wait(100)
        end

        print("^2[Olympus Core]^7 UI initialized")
    end)
end)

-- Initialize HUD event handler
RegisterNetEvent('olympus:client:initHUD')
AddEventHandler('olympus:client:initHUD', function()
    -- Wait for UI to be ready
    CreateThread(function()
        while not exports['olympus-ui'] or not exports['olympus-ui']:IsLoaded() do
            Wait(100)
        end

        -- Initialize HUD with current player data
        if OlympusCore.PlayerData then
            local playerPed = PlayerPedId()
            local health = GetEntityHealth(playerPed)
            local armor = GetPedArmour(playerPed)

            -- Convert health from 0-200 to 0-100 percentage
            local healthPercent = math.max(0, ((health - 100) / 100) * 100)

            exports['olympus-ui']:UpdateHUD({
                money = OlympusCore.PlayerData.money or 0,
                bank = OlympusCore.PlayerData.bank or 0,
                faction = OlympusCore.PlayerData.faction or 'civilian',
                faction_rank = OlympusCore.PlayerData.faction_rank or 1,
                gang_id = OlympusCore.PlayerData.gang_id,
                gang_name = OlympusCore.PlayerData.gang_name,
                gang_rank = OlympusCore.PlayerData.gang_rank or 1,
                health = healthPercent,
                armor = armor,
                duty_status = OlympusCore.PlayerData.duty_status or false
            })
        end

        print("^2[Olympus Core]^7 HUD initialized")
    end)
end)

-- Location Update Thread (Less Frequent for Performance)
CreateThread(function()
    local lastStreet = ""
    local lastArea = ""

    while true do
        if OlympusCore.PlayerData then
            local playerPed = PlayerPedId()
            local coords = GetEntityCoords(playerPed)

            -- Get street and area names
            local streetHash, crossingHash = GetStreetNameAtCoord(coords.x, coords.y, coords.z)
            local streetName = GetStreetNameFromHashKey(streetHash)
            local zoneName = GetNameOfZone(coords.x, coords.y, coords.z)
            local areaName = GetLabelText(zoneName)

            -- Clean up names
            local cleanStreet = streetName ~= "" and streetName or "Unknown Street"
            local cleanArea = areaName ~= "DUMMY_DEFAULT_TEXT" and areaName or "Los Santos"

            -- Only update if location changed (performance optimization)
            if cleanStreet ~= lastStreet or cleanArea ~= lastArea then
                exports['olympus-ui']:UpdateHUD({
                    location = {
                        street = cleanStreet,
                        area = cleanArea
                    }
                })

                lastStreet = cleanStreet
                lastArea = cleanArea
            end
        end

        Wait(5000) -- Update every 5 seconds (better for performance)
    end
end)

-- Time Update Thread (More Frequent for Accuracy)
CreateThread(function()
    while true do
        if OlympusCore.PlayerData then
            -- Get in-game time
            local hour = GetClockHours()
            local minute = GetClockMinutes()
            local timeString = string.format("%02d:%02d", hour, minute)

            -- Get in-game day of week
            local dayOfWeek = GetClockDayOfWeek()
            local days = {"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"}
            local dayName = days[dayOfWeek + 1] or "Sunday"

            -- Update UI with time
            exports['olympus-ui']:UpdateHUD({
                time = {
                    current = timeString,
                    day = dayName
                }
            })
        end

        Wait(30000) -- Update every 30 seconds (in-game time changes slowly)
    end
end)

-- Exports
exports('GetPlayerData', GetPlayerData)
exports('IsPlayerLoaded', IsPlayerLoaded)
exports('GetPlayerMoney', GetPlayerMoney)
exports('GetPlayerBank', GetPlayerBank)
exports('GetPlayerJob', GetPlayerJob)
exports('GetPlayerFaction', GetPlayerFaction)
exports('GetPlayerGang', GetPlayerGang)
exports('TriggerServerCallback', TriggerServerCallback)

print("^2[Olympus Core]^7 Client module loaded")
