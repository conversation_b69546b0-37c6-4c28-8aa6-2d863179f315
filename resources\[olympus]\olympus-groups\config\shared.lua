-- Olympus Group/Party System Configuration
-- Based on original Olympus group functions (fn_createGroup.sqf, fn_joinGroup.sqf, etc.)

Config = {}

-- Group System Configuration
Config.Groups = {
    enabled = true,
    description = "Complete group/party system separate from gangs",
    
    -- Group Creation Settings (Exact Olympus Implementation)
    creation = {
        enabled = true,
        cost = 0, -- Groups are free to make (exact Olympus)
        maxNameLength = 32, -- Maximum character limit for group name
        maxPasswordLength = 16, -- Maximum character limit for password
        allowedCharacters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_ ", -- Exact Olympus allowed chars
        requirePassword = false, -- Password is optional
        autoLock = true, -- Groups start locked by default
        leaderRank = "COLONEL" -- Leader rank in group (exact Olympus)
    },
    
    -- Group Management Settings
    management = {
        maxMembers = 8, -- Maximum group size (typical Arma group size)
        allowKicking = true, -- Leaders can kick members
        allowLocking = true, -- Leaders can lock/unlock groups
        allowPasswordChange = true, -- Leaders can change passwords
        leadershipTransfer = true, -- Leadership transfers when leader leaves
        autoDisband = true, -- Disband when last member leaves
        persistentGroups = false -- Groups don't persist across server restarts
    },
    
    -- Group Joining Settings
    joining = {
        requirePassword = true, -- Password required for locked groups
        allowPublicJoin = true, -- Allow joining unlocked groups without password
        maxJoinAttempts = 3, -- Maximum password attempts
        joinCooldown = 5000, -- 5 second cooldown between join attempts
        notifyMembers = true, -- Notify existing members when someone joins
        autoAccept = true -- Auto-accept join requests for unlocked groups
    },
    
    -- Group Features
    features = {
        groupChat = true, -- Enable group chat functionality
        groupMarkers = true, -- Show group members on map
        groupVoice = false, -- Group voice chat (not implemented in base)
        groupInvites = true, -- Allow group invitations
        groupBroadcast = true, -- Leaders can broadcast to group
        lastGroupMemory = true, -- Remember last group for rejoining
        groupStats = true -- Track group statistics
    },
    
    -- Validation Messages (Exact Olympus)
    messages = {
        nameRequired = "You must choose a name.",
        nameInvalid = "Invalid character in name, characters allowed are A-z,0-9, and _",
        nameTooLong = "The maximum character limit for a group name is 32.",
        nameTaken = "That group name is already taken!",
        passwordInvalid = "Invalid character in password, characters allowed are A-z,0-9, and _",
        passwordTooLong = "The maximum character limit for a password is 16.",
        passwordIncorrect = "Incorrect password.",
        groupNotValid = "Group is not valid",
        groupFull = "Group is full",
        alreadyInGroup = "You are already in a group",
        notInGroup = "You are not in a group",
        notLeader = "You are not the group leader",
        cannotKickSelf = "You cannot kick yourself from the group",
        memberNotFound = "Member not found in group",
        joinSuccess = "You have joined the group: %s",
        createSuccess = "Group '%s' created successfully",
        leaveSuccess = "You have left the group",
        kickSuccess = "%s has been kicked from the group",
        groupLocked = "Group has been locked",
        groupUnlocked = "Group has been unlocked",
        leadershipTransferred = "Group leadership transferred to %s"
    },
    
    -- Group Colors and Display
    display = {
        groupColor = {255, 255, 0}, -- Yellow for group members
        leaderColor = {255, 165, 0}, -- Orange for group leader
        showGroupTags = true, -- Show [GROUP] tags above players
        showMemberCount = true, -- Show member count in group list
        showLockStatus = true, -- Show lock status in group list
        refreshInterval = 5000 -- 5 second refresh interval for group list
    },
    
    -- Database Settings
    database = {
        enabled = true,
        logGroupActions = true, -- Log group creation, joining, leaving, etc.
        trackStatistics = true, -- Track group usage statistics
        cleanupInterval = 3600000, -- 1 hour cleanup interval for old logs
        maxLogAge = 2592000 -- 30 days maximum log age
    }
}

-- Group Actions for Logging
Config.GroupActions = {
    CREATE = "create",
    JOIN = "join",
    LEAVE = "leave",
    KICK = "kick",
    LOCK = "lock",
    UNLOCK = "unlock",
    DISBAND = "disband",
    TRANSFER_LEADERSHIP = "transfer_leadership",
    PASSWORD_CHANGE = "password_change"
}

-- Group Permissions
Config.Permissions = {
    CREATE_GROUP = "group.create",
    JOIN_GROUP = "group.join",
    KICK_MEMBERS = "group.kick", -- Leader only
    LOCK_GROUP = "group.lock", -- Leader only
    CHANGE_PASSWORD = "group.password", -- Leader only
    TRANSFER_LEADERSHIP = "group.transfer" -- Leader only
}
