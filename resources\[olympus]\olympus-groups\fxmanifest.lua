fx_version 'cerulean'
game 'gta5'

name 'Olympus Group/Party System'
description 'Complete group system based on original Olympus group functions - separate from gangs'
author 'Olympus Development Team'
version '1.0.0'

-- Dependencies
dependencies {
    'olympus-core'
}

-- Shared scripts
shared_scripts {
    'config/shared.lua'
}

-- Client scripts
client_scripts {
    'client/main.lua'
}

-- Server scripts
server_scripts {
    'server/main.lua'
}

-- UI files
ui_page 'html/index.html'

files {
    'html/index.html',
    'html/style.css',
    'html/script.js'
}

-- Exports
exports {
    'CreateGroup',
    'JoinGroup',
    'LeaveGroup',
    'IsInGroup',
    'GetGroupData',
    'GetGroupMembers'
}

server_exports {
    'CreateGroup',
    'JoinGroup',
    'LeaveGroup',
    'KickFromGroup',
    'LockGroup',
    'UnlockGroup',
    'GetGroupList',
    'GetGroupData'
}
