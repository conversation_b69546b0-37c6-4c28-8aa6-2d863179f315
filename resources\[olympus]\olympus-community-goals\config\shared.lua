-- Olympus Community Goals System - Complete Implementation
-- Based on Olympus Altis Life community goals mechanics with exact specifications

Config = {}

-- Community Goals System Settings
Config.CommunityGoalsSystem = {
    enabled = true,
    
    -- System Description
    description = "Community goals are like quests but are completed with the entire community",
    
    -- Goal Management
    goalManagement = {
        staffControlled = true, -- Staff sets community goals
        uniquePerFaction = true, -- Unique goals for each faction
        oneActivePerFaction = true, -- Only one goal per faction at a time
        
        -- Goal Duration
        duration = {
            default = 604800, -- 1 week (7 days) from creation
            customizable = true, -- Staff can set custom duration
            
            -- Expiration Handling
            expiration = {
                autoComplete = false, -- Goals don't auto-complete
                rewardDistribution = true, -- Distribute rewards on completion
                progressReset = true -- Reset progress after completion/expiration
            }
        }
    },
    
    -- Access Method
    access = {
        yMenu = {
            enabled = true,
            location = 'below_bank_symbol', -- Below bank symbol in Y-menu
            button = 'Quest Menu', -- Green "Quest Menu" button
            
            -- Menu Structure
            menuStructure = {
                questMenu = {
                    name = 'Quest Menu',
                    color = 'green',
                    
                    -- Sub-menu
                    subMenu = {
                        communityGoals = {
                            name = 'Community Goals',
                            
                            -- Information Displayed
                            information = {
                                currentGoals = true, -- Current community goals
                                contributionMethods = true, -- How to contribute
                                progress = true, -- Goal progress
                                maxReward = true, -- Highest possible reward
                                playerContribution = true -- Player's current contribution
                            }
                        }
                    }
                }
            }
        }
    }
}

-- Reward System (Exact Olympus Implementation)
Config.RewardSystem = {
    enabled = true,
    
    -- Tier System
    tierSystem = {
        enabled = true,
        
        -- Tier Rewards (Percentage of Max Reward)
        tiers = {
            S = {
                name = 'S Tier',
                rewardPercentage = 1.00, -- 100% of max reward
                color = '#FFD700' -- Gold
            },
            A = {
                name = 'A Tier',
                rewardPercentage = 0.80, -- 80% of max reward
                color = '#C0C0C0' -- Silver
            },
            B = {
                name = 'B Tier',
                rewardPercentage = 0.60, -- 60% of max reward
                color = '#CD7F32' -- Bronze
            },
            C = {
                name = 'C Tier',
                rewardPercentage = 0.40, -- 40% of max reward
                color = '#4169E1' -- Royal Blue
            },
            D = {
                name = 'D Tier',
                rewardPercentage = 0.20, -- 20% of max reward
                color = '#32CD32' -- Lime Green
            },
            E = {
                name = 'E Tier',
                rewardPercentage = 0.10, -- 10% of max reward
                color = '#FFA500' -- Orange
            },
            F = {
                name = 'F Tier',
                rewardPercentage = 0.025, -- 2.5% of max reward
                color = '#FF4500' -- Red Orange
            }
        }
    },
    
    -- Contribution Ranking System
    contributionRanking = {
        enabled = true,
        
        -- Ranking Categories (Percentage of Tier Reward)
        rankings = {
            top6Contributors = {
                name = 'Top 6 Contributors',
                requirement = 'top_6_absolute',
                rewardPercentage = 1.00, -- 100% of tier reward
                priority = 1
            },
            
            top10Percent = {
                name = 'Top 10% of All Contributors',
                requirement = 'top_10_percent',
                rewardPercentage = 0.85, -- 85% of tier reward
                priority = 2
            },
            
            top25Percent = {
                name = 'Top 25% of All Contributors',
                requirement = 'top_25_percent',
                rewardPercentage = 0.75, -- 75% of tier reward
                priority = 3
            },
            
            top50Percent = {
                name = 'Top 50% of All Contributors',
                requirement = 'top_50_percent',
                rewardPercentage = 0.50, -- 50% of tier reward
                priority = 4
            },
            
            top75Percent = {
                name = 'Top 75% of All Contributors',
                requirement = 'top_75_percent',
                rewardPercentage = 0.25, -- 25% of tier reward
                priority = 5
            },
            
            top100Percent = {
                name = 'Top 100% of All Contributors',
                requirement = 'any_contribution',
                rewardPercentage = 0.10, -- 10% of tier reward
                priority = 6
            },
            
            noContribution = {
                name = 'No Contribution',
                requirement = 'no_contribution',
                rewardPercentage = 0.00, -- No reward
                priority = 7
            }
        }
    },
    
    -- Reward Calculation
    rewardCalculation = {
        enabled = true,
        
        -- Calculation Formula
        formula = {
            finalReward = 'max_reward * tier_percentage * ranking_percentage',
            
            -- Example Calculation
            example = {
                maxReward = 100000, -- $100,000 max reward
                tierS = 1.00, -- S tier (100%)
                top6Contributors = 1.00, -- Top 6 (100%)
                finalReward = 100000 -- $100,000 final reward
            }
        }
    }
}

-- Civilian Goals (Exact Olympus Implementation)
Config.CivilianGoals = {
    enabled = true,
    faction = 'civilian',
    
    -- Possible Goal Types
    goalTypes = {
        sellingIllegalGoods = {
            name = 'Selling Illegal Goods',
            description = 'Sell illegal drugs and contraband',
            
            -- Contribution Methods
            contributions = {
                drugSales = {
                    cocaine = 1, -- 1 contribution per cocaine sale
                    heroin = 1, -- 1 contribution per heroin sale
                    weed = 1, -- 1 contribution per weed sale
                    meth = 3, -- 3 contributions per meth sale (special)
                    moonshine = 3 -- 3 contributions per moonshine sale (special)
                }
            }
        },
        
        sellingLegalGoods = {
            name = 'Selling Legal Goods',
            description = 'Sell legal processed materials',
            
            contributions = {
                legalSales = {
                    salt = 1,
                    copper = 1,
                    iron = 1,
                    oil = 1,
                    diamond = 1,
                    silver = 1,
                    platinum = 1
                }
            }
        },
        
        useBlastingCharges = {
            name = 'Use Blasting Charges',
            description = 'Detonate blasting charges',
            
            contributions = {
                blastingCharges = 1 -- 1 contribution per charge used
            }
        },
        
        captureCartels = {
            name = 'Capture Cartels',
            description = 'Capture and control drug cartels',
            
            contributions = {
                cartelCapture = 5, -- 5 contributions per cartel captured
                cartelControl = 1 -- 1 contribution per 10 minutes controlled
            }
        },
        
        killCops = {
            name = 'Kill Cops',
            description = 'Eliminate APD officers',
            
            contributions = {
                apdKills = 2 -- 2 contributions per APD kill
            }
        },
        
        stealPharmaceuticals = {
            name = 'Steal Pharmaceuticals',
            description = 'Steal pharmaceutical supplies',
            
            contributions = {
                pharmaceuticalTheft = 3 -- 3 contributions per theft
            }
        },
        
        unsealBlackMarkets = {
            name = 'Unseal Black Markets',
            description = 'Unseal black market locations',
            
            contributions = {
                blackMarketUnseal = 10 -- 10 contributions per unseal
            }
        },
        
        robPlayers = {
            name = 'Rob Players',
            description = 'Successfully rob other players',
            
            contributions = {
                playerRobbery = 1 -- 1 contribution per successful robbery
            }
        }
    }
}

-- APD Goals (Exact Olympus Implementation)
Config.APDGoals = {
    enabled = true,
    faction = 'apd',
    
    -- Possible Goal Types
    goalTypes = {
        playTime = {
            name = 'Play Time',
            description = 'Accumulate time on duty',
            
            contributions = {
                onDutyTime = 1 -- 1 contribution per hour on duty
            }
        },
        
        defuseBombs = {
            name = 'Defuse Bombs',
            description = 'Successfully defuse explosive devices',
            
            contributions = {
                bombDefusal = 5 -- 5 contributions per bomb defused
            }
        },
        
        impoundVehicles = {
            name = 'Impound Vehicles',
            description = 'Impound civilian vehicles',
            
            contributions = {
                vehicleImpound = 1 -- 1 contribution per vehicle impounded
            }
        },
        
        terminatePharmaceuticals = {
            name = 'Terminate Pharmaceuticals',
            description = 'Destroy pharmaceutical operations',
            
            contributions = {
                pharmaceuticalTermination = 3 -- 3 contributions per termination
            }
        },
        
        seizePharmaceuticals = {
            name = 'Seize Pharmaceuticals',
            description = 'Seize pharmaceutical supplies',
            
            contributions = {
                pharmaceuticalSeizure = 2 -- 2 contributions per seizure
            }
        },
        
        completeEscorts = {
            name = 'Complete Escorts',
            description = 'Successfully complete prisoner escorts',
            
            contributions = {
                escortCompletion = 3 -- 3 contributions per escort completed
            }
        },
        
        impoundStolenVehicles = {
            name = 'Impound Stolen Vehicles',
            description = 'Impound vehicles reported as stolen',
            
            contributions = {
                stolenVehicleImpound = 2 -- 2 contributions per stolen vehicle
            }
        },
        
        resolveDispatches = {
            name = 'Resolve Dispatches',
            description = 'Respond to and resolve dispatch calls',
            
            contributions = {
                dispatchResolution = 1 -- 1 contribution per dispatch resolved
            }
        },
        
        driveDistance = {
            name = 'Drive Distance',
            description = 'Accumulate driving distance on duty',
            
            contributions = {
                distanceDriven = 1 -- 1 contribution per 10km driven
            }
        },
        
        arrestPlayers = {
            name = 'Arrest X Players',
            description = 'Make arrests of criminal players',
            
            contributions = {
                playerArrest = 2 -- 2 contributions per arrest
            }
        },
        
        tasePlayers = {
            name = 'Tase X Players',
            description = 'Successfully tase players',
            
            contributions = {
                playerTase = 1 -- 1 contribution per tase
            }
        },
        
        lethalPlayers = {
            name = 'Lethal X Players',
            description = 'Use lethal force when authorized',
            
            contributions = {
                lethalForce = 3 -- 3 contributions per lethal action
            }
        },
        
        pardonPlayers = {
            name = 'Pardon X Players',
            description = 'Issue pardons to players',
            
            contributions = {
                playerPardon = 2 -- 2 contributions per pardon
            }
        },
        
        seizeVehicles = {
            name = 'Seize X Vehicles',
            description = 'Seize vehicles from criminals',
            
            contributions = {
                vehicleSeizure = 2 -- 2 contributions per seizure
            }
        },
        
        seizeGearValue = {
            name = 'Seize X Worth of Gear',
            description = 'Seize valuable equipment and gear',
            
            contributions = {
                gearSeizure = 1 -- 1 contribution per $10,000 seized
            }
        }
    }
}

-- R&R Goals (Exact Olympus Implementation)
Config.RnRGoals = {
    enabled = true,
    faction = 'rnr',
    
    -- Possible Goal Types
    goalTypes = {
        vehiclesImpounded = {
            name = 'Vehicles Impounded',
            description = 'Impound abandoned or illegally parked vehicles',
            
            contributions = {
                vehicleImpound = 1 -- 1 contribution per vehicle impounded
            }
        },
        
        playersRevived = {
            name = 'Players Revived',
            description = 'Successfully revive downed players',
            
            contributions = {
                playerRevive = 2 -- 2 contributions per revive
            }
        },
        
        lollipopsGiven = {
            name = 'Lollipops Given',
            description = 'Distribute lollipops to players',
            
            contributions = {
                lollipopDistribution = 1 -- 1 contribution per lollipop given
            }
        },
        
        playTime = {
            name = 'Play Time',
            description = 'Accumulate time on duty as R&R',
            
            contributions = {
                onDutyTime = 1 -- 1 contribution per hour on duty
            }
        }
    }
}

-- Progress Tracking System
Config.ProgressTracking = {
    enabled = true,
    
    -- Tracking Mechanics
    trackingMechanics = {
        realTime = true, -- Track progress in real-time
        persistent = true, -- Progress persists between sessions
        
        -- Progress Display
        progressDisplay = {
            enabled = true,
            
            -- Display Elements
            elements = {
                currentProgress = true, -- Current progress amount
                targetGoal = true, -- Target goal amount
                progressPercentage = true, -- Percentage completed
                timeRemaining = true, -- Time remaining
                playerContribution = true, -- Player's contribution
                playerRank = true -- Player's contribution rank
            }
        }
    },
    
    -- Leaderboard System
    leaderboard = {
        enabled = true,
        
        -- Leaderboard Features
        features = {
            topContributors = true, -- Show top contributors
            playerRanking = true, -- Show player's rank
            contributionAmount = true, -- Show contribution amounts
            
            -- Update Frequency
            updateFrequency = 300, -- Update every 5 minutes
            realTimeUpdates = false -- Not real-time to reduce server load
        }
    }
}

-- Integration Systems
Config.IntegrationSystems = {
    -- Staff Management Integration
    staffManagement = {
        enabled = true,
        
        -- Staff Controls
        staffControls = {
            createGoals = true, -- Staff can create new goals
            modifyGoals = true, -- Staff can modify existing goals
            endGoals = true, -- Staff can end goals early
            setRewards = true, -- Staff can set reward amounts
            
            -- Goal Templates
            goalTemplates = {
                enabled = true,
                predefinedGoals = true, -- Pre-defined goal templates
                customGoals = true -- Staff can create custom goals
            }
        }
    },
    
    -- Faction Integration
    factionIntegration = {
        enabled = true,
        
        -- Faction-Specific Features
        factionFeatures = {
            separateGoals = true, -- Each faction has separate goals
            factionProgress = true, -- Track faction-specific progress
            crossFactionGoals = false, -- No cross-faction goals
            
            -- Faction Rewards
            factionRewards = {
                customRewards = true, -- Faction-specific rewards
                scaledRewards = true -- Rewards scale with faction size
            }
        }
    },
    
    -- Statistics Integration
    statisticsIntegration = {
        enabled = true,
        
        -- Statistics Tracking
        statisticsTracking = {
            goalCompletion = true, -- Track goal completion rates
            playerParticipation = true, -- Track player participation
            rewardDistribution = true, -- Track reward distribution
            
            -- Historical Data
            historicalData = {
                enabled = true,
                retentionPeriod = 7776000, -- 90 days
                goalArchive = true -- Archive completed goals
            }
        }
    }
}
