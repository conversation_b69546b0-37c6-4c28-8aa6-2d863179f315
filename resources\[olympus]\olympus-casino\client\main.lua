-- Olympus Casino System - Client Main

local OlympusCasino = {}
local isCasinoUIOpen = false
local currentCasino = nil
local casinoBlips = {}

-- Initialize casino system
CreateThread(function()
    Wait(1000) -- Wait for game to be ready

    print("[Olympus Casino] Client initialized")

    -- Create casino blips
    CreateCasinoBlips()

    -- Create casino NPCs and interactions
    CreateCasinoNPCs()
    CreateCasinoInteractions()
end)

-- Create casino blips
function CreateCasinoBlips()
    local casinos = {
        {
            name = 'Kavala Casino',
            coords = Config.CasinoSystem.locations.kavala.ageVerification.coords,
            sprite = 679, -- Casino chip icon
            color = 1, -- Red
            scale = 1.0
        },
        {
            name = 'Pyrgos Casino',
            coords = Config.CasinoSystem.locations.pyrgos.ageVerification.coords,
            sprite = 679, -- Casino chip icon
            color = 1, -- Red
            scale = 1.0
        }
    }

    for _, casino in pairs(casinos) do
        local blip = AddBlipForCoord(casino.coords.x, casino.coords.y, casino.coords.z)
        SetBlipSprite(blip, casino.sprite)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, casino.scale)
        SetBlipColour(blip, casino.color)
        SetBlipAsShortRange(blip, true)

        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(casino.name)
        EndTextCommandSetBlipName(blip)

        table.insert(casinoBlips, blip)
    end

    print(string.format("^2[Olympus Casino]^7 Created %d casino blips", #casinoBlips))
end

-- Create casino NPCs
function CreateCasinoNPCs()
    local casinos = {
        Config.CasinoSystem.locations.kavala,
        Config.CasinoSystem.locations.pyrgos
    }
    
    for _, casino in pairs(casinos) do
        -- Create age verification NPC
        local npcHash = GetHashKey('a_m_m_business_01')
        RequestModel(npcHash)
        while not HasModelLoaded(npcHash) do
            Wait(1)
        end
        
        local npc = CreatePed(4, npcHash, casino.ageVerification.coords.x, casino.ageVerification.coords.y, casino.ageVerification.coords.z - 1.0, casino.ageVerification.heading, false, true)
        SetEntityInvincible(npc, true)
        SetBlockingOfNonTemporaryEvents(npc, true)
        FreezeEntityPosition(npc, true)
        
        -- Add interaction for age verification
        exports['olympus-ui']:AddInteraction({
            coords = casino.ageVerification.coords,
            distance = 2.0,
            text = 'Press [E] to verify age for casino access',
            action = function()
                TriggerServerEvent('olympus-casino:server:verifyAge')
            end
        })
    end
end

-- Create casino game interactions
function CreateCasinoInteractions()
    local casinos = {
        {name = 'kavala', data = Config.CasinoSystem.locations.kavala},
        {name = 'pyrgos', data = Config.CasinoSystem.locations.pyrgos}
    }
    
    for _, casino in pairs(casinos) do
        -- Slot machines
        for i, slotCoords in pairs(casino.data.slotMachines) do
            exports['olympus-ui']:AddInteraction({
                coords = slotCoords,
                distance = 1.5,
                text = 'Press [E] to play slots',
                action = function()
                    OpenSlotMachine(casino.name, i)
                end
            })
        end
        
        -- Roulette tables
        for i, rouletteCoords in pairs(casino.data.rouletteTables) do
            exports['olympus-ui']:AddInteraction({
                coords = rouletteCoords,
                distance = 2.0,
                text = 'Press [E] to play roulette',
                action = function()
                    OpenRoulette(casino.name, i)
                end
            })
        end
        
        -- Blackjack tables
        for i, blackjackCoords in pairs(casino.data.blackjackTables) do
            exports['olympus-ui']:AddInteraction({
                coords = blackjackCoords,
                distance = 2.0,
                text = 'Press [E] to play blackjack',
                action = function()
                    OpenBlackjack(casino.name, i)
                end
            })
        end
    end
end

-- Open slot machine
function OpenSlotMachine(casino, machineId)
    TriggerServerEvent('olympus-casino:server:checkAccess', 'slots')
end

-- Open roulette
function OpenRoulette(casino, tableId)
    TriggerServerEvent('olympus-casino:server:checkAccess', 'roulette')
end

-- Open blackjack
function OpenBlackjack(casino, tableId)
    TriggerServerEvent('olympus-casino:server:checkAccess', 'blackjack')
end

-- Handle casino access response
RegisterNetEvent('olympus-casino:client:accessGranted', function(gameType, data)
    currentCasino = {type = gameType, data = data}
    isCasinoUIOpen = true
    SetNuiFocus(true, true)
    
    SendNUIMessage({
        type = 'openCasino',
        gameType = gameType,
        data = data
    })
end)

-- Handle casino access denied
RegisterNetEvent('olympus-casino:client:accessDenied', function(reason)
    OlympusCore.Functions.Notify(reason, 'error', 5000)
end)

-- Handle age verification result
RegisterNetEvent('olympus-casino:client:ageVerified', function(verified)
    if verified then
        OlympusCore.Functions.Notify('Age verified! You can now access casino games.', 'success')
    else
        OlympusCore.Functions.Notify('You need at least 10 hours of playtime to access the casino.', 'error')
    end
end)

-- Handle gambling results
RegisterNetEvent('olympus-casino:client:gamblingResult', function(result)
    SendNUIMessage({
        type = 'gamblingResult',
        result = result
    })
    
    if result.won then
        PlaySoundFrontend(-1, 'CHECKPOINT_PERFECT', 'HUD_MINI_GAME_SOUNDSET', 1)
        OlympusCore.Functions.Notify(string.format('You won $%s!', OlympusCore.Shared.CommaValue(result.winAmount)), 'success')
    else
        PlaySoundFrontend(-1, 'CHECKPOINT_MISSED', 'HUD_MINI_GAME_SOUNDSET', 1)
        OlympusCore.Functions.Notify(string.format('You lost $%s', OlympusCore.Shared.CommaValue(result.betAmount)), 'error')
    end
end)

-- Handle self-exclusion
RegisterNetEvent('olympus-casino:client:selfExcluded', function()
    OlympusCore.Functions.Notify('You have been permanently excluded from all gambling activities.', 'error', 10000)
    
    if isCasinoUIOpen then
        CloseCasino()
    end
end)

-- Close casino UI
function CloseCasino()
    isCasinoUIOpen = false
    SetNuiFocus(false, false)
    currentCasino = nil
    
    SendNUIMessage({
        type = 'closeCasino'
    })
end

-- NUI Callbacks
RegisterNUICallback('closeCasino', function(data, cb)
    CloseCasino()
    cb('ok')
end)

RegisterNUICallback('placeBet', function(data, cb)
    TriggerServerEvent('olympus-casino:server:placeBet', currentCasino.type, data)
    cb('ok')
end)

RegisterNUICallback('requestSelfExclusion', function(data, cb)
    TriggerServerEvent('olympus-casino:server:requestSelfExclusion')
    cb('ok')
end)

-- Exports
exports('CanPlayerGamble', function()
    -- This would check if player meets age requirements and isn't self-excluded
    return true -- Placeholder
end)

exports('OpenCasino', function(gameType)
    if gameType == 'slots' then
        OpenSlotMachine('kavala', 1)
    elseif gameType == 'roulette' then
        OpenRoulette('kavala', 1)
    elseif gameType == 'blackjack' then
        OpenBlackjack('kavala', 1)
    end
end)

print("[Olympus Casino] Client module loaded")
