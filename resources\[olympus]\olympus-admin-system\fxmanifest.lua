fx_version 'cerulean'
game 'gta5'

name 'Olympus Admin System'
description 'Complete admin and staff management system for Olympus Altis Life'
author 'Olympus Development Team'
version '1.0.0'

-- Dependencies
dependencies {
    'olympus-core',
    'olympus-ui',
    'oxmysql'
}

-- Shared scripts
shared_scripts {
    'config/shared.lua'
}

-- Client scripts
client_scripts {
    'client/main.lua',
    'client/admin_panel.lua',
    'client/spectate.lua',
    'client/vanish.lua',
    'client/reports.lua'
}

-- Server scripts
server_scripts {
    '@oxmysql/lib/MySQL.lua',
    'server/main.lua',
    'server/admin_commands.lua',
    'server/player_management.lua',
    'server/punishment_system.lua',
    'server/audit_system.lua',
    'server/quota_tracking.lua'
}

-- UI files
ui_page 'html/index.html'

files {
    'html/index.html',
    'html/css/*.css',
    'html/js/*.js',
    'html/images/*.png',
    'html/images/*.jpg'
}

-- Exports
exports {
    'IsPlayerAdmin',
    'GetPlayerAdminRank',
    'LogAdminAction',
    'SendAdminNotification'
}

server_exports {
    'RegisterAdminCommand',
    'BanPlayer',
    'KickPlayer',
    'GetPlayerInfractions',
    'AddInfraction'
}
