-- Olympus Auction House System - Complete Implementation
-- Based on Olympus Altis Life auction house mechanics with exact specifications

Config = {}

-- Auction House System Settings (Exact Olympus Implementation)
Config.AuctionHouseSystem = {
    enabled = true,

    -- System Description
    description = "The most secure and reliable method to sell and buy almost any vehicle, equipment, and virtual item",

    -- Auction Duration (24 hours in seconds)
    auctionDuration = 86400,

    -- Minimum bid increment
    minimumBidIncrement = 1000,

    -- Fees (exact Olympus rates)
    fees = {
        listingFee = 0.05, -- 5% of listing price
        sellerFee = 0.10, -- 10% of final sale price
        buyerFee = 0.00 -- No buyer fee
    },

    -- Access Control
    accessControl = {
        excludedFactions = {'apd', 'rnr'},
        restrictionMessage = 'APD and R&R members cannot access the auction house'
    },
    
    -- Access Locations (Black Markets - Second Floor)
    locations = {
        blackMarket1 = {
            name = 'Black Market Auction House',
            entrance = vector3(1500.0, 2800.0, 35.0), -- Black market location
            floor = 2, -- Second floor
            
            -- NPC Positions
            npcs = {
                seller = vector3(1502.0, 2802.0, 40.0), -- Right NPC (selling)
                buyer = vector3(1498.0, 2802.0, 40.0) -- Left NPC (buying)
            }
        },
        
        blackMarket2 = {
            name = 'Black Market Auction House 2',
            entrance = vector3(2100.0, 1850.0, 35.0), -- Second black market
            floor = 2,
            
            npcs = {
                seller = vector3(2102.0, 1852.0, 40.0),
                buyer = vector3(2098.0, 1852.0, 40.0)
            }
        }
    },
    
    -- External Integration
    externalIntegration = {
        enabled = true,
        statsPageUrl = 'https://lc3.olympus-entertainment.com/#/stats/auction',
        webAccess = true -- Can view listings on stats page
    },

    -- Access Control
    accessControl = {
        enabled = true,
        -- Allow access to anyone except APD and R&R
        excludedFactions = {'apd', 'rnr'},
        restrictionMessage = 'APD and R&R members cannot access the auction house.'
    }
}

-- Selling System (Exact Olympus Implementation)
Config.SellingSystem = {
    enabled = true,
    
    -- Item Requirements
    itemRequirements = {
        -- Storage Requirements
        storage = {
            vehicleRequired = true, -- Items must be stored in vehicle
            proximityRange = 25, -- Vehicle must be within 25 meters
            
            -- Exception for Vehicles
            vehicleSelling = {
                noStorageRequired = true, -- Vehicle itself doesn't need to be in storage
                directSale = true -- Can sell vehicle directly
            }
        }
    },
    
    -- Listing Categories
    listingCategories = {
        vehicles = {
            name = 'Vehicles',
            enabled = true,
            subcategories = {
                'civilian_cars',
                'sports_cars',
                'trucks',
                'helicopters',
                'planes',
                'boats'
            }
        },
        
        equipment = {
            name = 'Equipment',
            enabled = true,
            subcategories = {
                'weapons',
                'attachments',
                'clothing',
                'gear'
            }
        },
        
        virtualItems = {
            name = 'Virtual Items',
            enabled = true,
            subcategories = {
                'processed_materials',
                'raw_materials',
                'drugs',
                'consumables'
            }
        }
    },
    
    -- Listing Process
    listingProcess = {
        -- Pricing
        pricing = {
            playerSet = true, -- Player inputs price
            minimumPrice = 1000, -- $1,000 minimum
            maximumPrice = 100000000, -- $100M maximum
            
            -- Listing Fee
            listingFee = {
                percentage = 0.025, -- 2.5% listing fee
                calculation = 'price * 0.025',
                paymentRequired = true, -- Must pay upfront
                nonRefundable = true -- Fee not refunded if unsold
            }
        },
        
        -- Listing Duration
        duration = {
            initial = 2678400, -- 31 days (31 * 24 * 60 * 60)
            
            -- Expiration Handling
            expiration = {
                returnToStorage = true, -- Return to auction house storage
                storageExpiry = 604800, -- 1 week in storage before deletion
                notifyPlayer = true -- Notify player of expiration
            }
        },
        
        -- Sale Completion
        saleCompletion = {
            notification = {
                enabled = true,
                method = 'in_game_notification',
                message = "Your auction listing has been sold!"
            },
            
            -- Payment
            payment = {
                method = 'deposit_box', -- Money goes to deposit box
                immediate = true, -- Immediate payment on sale
                
                -- No Additional Fees
                additionalFees = {
                    saleFee = 0, -- No additional sale fee
                    taxFee = 0 -- No tax on sale
                }
            }
        }
    }
}

-- Buying System (Exact Olympus Implementation)
Config.BuyingSystem = {
    enabled = true,
    
    -- Market Interface
    marketInterface = {
        npc = 'left', -- Left auction house NPC
        
        -- Search System
        searchSystem = {
            enabled = true,
            
            -- Search Methods
            methods = {
                category = {
                    enabled = true,
                    selectCategory = true, -- Select category first
                    clickItem = true, -- Click on item
                    checkAvailability = true -- Press "Check Availability"
                },
                
                cheapestFirst = {
                    enabled = true,
                    findCheapest = true, -- Finds cheapest listing automatically
                    sortByPrice = true
                }
            }
        },
        
        -- Listing Details
        listingDetails = {
            enabled = true,
            
            -- Information Displayed
            information = {
                price = true, -- Item price
                quantity = true, -- Available quantity
                seller = true, -- Seller name
                expirationTime = true, -- Time until expiration
                
                -- Price History
                priceHistory = {
                    enabled = true,
                    lastSales = 5, -- Show last 5 sales
                    comparison = true -- Compare with current prices
                }
            }
        }
    },
    
    -- Purchase Process
    purchaseProcess = {
        -- Quantity Selection
        quantitySelection = {
            enabled = true,
            
            -- Selection Methods
            methods = {
                typeAmount = {
                    enabled = true,
                    inputBox = 'bottom_right', -- Input box location
                    validation = true -- Validate quantity available
                },
                
                selectAll = {
                    enabled = true,
                    method = 'double_click', -- Double click listing
                    maxQuantity = true -- Select all available
                }
            }
        },
        
        -- Purchase Confirmation
        confirmation = {
            enabled = true,
            button = 'Purchase Listing',
            
            -- Validation
            validation = {
                sufficientFunds = true, -- Check player has enough money
                itemAvailable = true, -- Check item still available
                quantityValid = true -- Check quantity is valid
            }
        },
        
        -- Purchase Completion
        completion = {
            storage = {
                location = 'auction_house_storage', -- Items go to auction storage
                category = 'Purchased Listings',
                
                -- Retrieval Requirements
                retrieval = {
                    vehicleRequired = true, -- Need vehicle within 25m
                    proximityRange = 25, -- 25 meter range
                    
                    -- Storage Expiry
                    expiry = {
                        duration = 604800, -- 7 days from purchase
                        warning = true, -- Warn before expiry
                        deletion = true -- Delete if not retrieved
                    }
                }
            }
        }
    }
}

-- Storage System (Exact Olympus Implementation)
Config.StorageSystem = {
    enabled = true,
    
    -- Storage Categories
    categories = {
        purchasedListings = {
            name = 'Purchased Listings',
            description = 'Items you have purchased from the auction house',
            expiry = 604800, -- 7 days
            
            -- Retrieval
            retrieval = {
                vehicleRequired = true,
                proximityRange = 25
            }
        },
        
        failedRetractedListings = {
            name = 'Failed/Retracted Listings',
            description = 'Items that failed to sell or were retracted',
            expiry = 604800, -- 7 days
            
            -- Retrieval & Relisting
            actions = {
                retrieve = true, -- Can retrieve items
                relist = true -- Can relist items
            }
        },
        
        expiredListings = {
            name = 'Expired Listings',
            description = 'Items that expired after 31 days',
            expiry = 604800, -- 7 days in storage
            
            actions = {
                retrieve = true,
                relist = true
            }
        }
    },
    
    -- Storage Management
    management = {
        -- Access Requirements
        access = {
            vehicleRequired = true, -- Must have vehicle within 25m
            proximityRange = 25, -- 25 meter range
            
            -- Vehicle Validation
            vehicleValidation = {
                playerOwned = true, -- Must be player's vehicle
                accessible = true, -- Must be accessible
                notDestroyed = true -- Must not be destroyed
            }
        },
        
        -- Expiry System
        expirySystem = {
            enabled = true,
            
            -- Warning System
            warnings = {
                enabled = true,
                schedule = {
                    3600, -- 1 hour before expiry
                    86400, -- 1 day before expiry
                    259200 -- 3 days before expiry
                },
                message = "Warning: Items in your auction house storage will expire in %s!"
            },
            
            -- Deletion
            deletion = {
                automatic = true, -- Automatically delete expired items
                notification = true, -- Notify player of deletion
                irreversible = true -- Cannot recover deleted items
            }
        }
    }
}

-- Listing Management System (Exact Olympus Implementation)
Config.ListingManagement = {
    enabled = true,
    
    -- My Listings Interface
    myListings = {
        enabled = true,
        category = 'My Listings', -- Middle column category
        
        -- Available Actions
        actions = {
            retract = {
                enabled = true,
                button = 'Retract Listing',
                
                -- Retraction Process
                process = {
                    immediate = true, -- Immediate retraction
                    returnToStorage = true, -- Return to storage
                    storageCategory = 'Failed/Retracted Listings',
                    
                    -- No Refund
                    refund = {
                        listingFee = false, -- No refund of listing fee
                        explanation = "Listing fee is non-refundable"
                    }
                }
            }
        }
    },
    
    -- Relisting System
    relisting = {
        enabled = true,
        
        -- Relisting Process
        process = {
            source = 'Failed/Retracted Listings', -- From storage category
            
            -- Relisting Options
            options = {
                samePrice = {
                    enabled = true,
                    description = "Relist for the same price you originally listed it for",
                    button = 'Re-List Listing'
                },
                
                newPrice = {
                    enabled = true,
                    description = "Relist with a new price",
                    priceInput = true
                }
            },
            
            -- Relisting Fee
            fee = {
                percentage = 0.01, -- 1% listing fee for relisting
                calculation = 'original_price * 0.01',
                paymentRequired = true
            }
        }
    },
    
    -- Extension System
    extension = {
        enabled = true,
        
        -- Extension Options
        options = {
            duration = 2592000, -- Extend for 30 days
            
            -- Extension Fee
            fee = {
                percentage = 0.01, -- 1% extension fee
                calculation = 'listing_price * 0.01',
                paymentRequired = true
            }
        },
        
        -- Extension Process
        process = {
            button = 'Extend Listing',
            confirmation = true, -- Require confirmation
            immediate = true -- Immediate extension
        }
    }
}

-- Integration Systems
Config.IntegrationSystems = {
    -- Stats Page Integration
    statsPage = {
        enabled = true,
        url = 'https://lc3.olympus-entertainment.com/#/stats/auction',
        
        -- Features
        features = {
            viewListings = true, -- View all current listings
            searchListings = true, -- Search functionality
            priceHistory = true, -- View price history
            playerStats = true -- View player auction statistics
        }
    },
    
    -- Notification System
    notifications = {
        enabled = true,
        
        -- Notification Types
        types = {
            itemSold = {
                enabled = true,
                message = "Your auction listing '%s' has been sold for $%s!",
                sound = true
            },
            
            itemExpired = {
                enabled = true,
                message = "Your auction listing '%s' has expired and returned to storage.",
                sound = false
            },
            
            storageExpiring = {
                enabled = true,
                message = "Items in your auction house storage will expire in %s!",
                sound = true
            },
            
            purchaseComplete = {
                enabled = true,
                message = "You have successfully purchased '%s' for $%s!",
                sound = true
            }
        }
    },
    
    -- Security System
    security = {
        enabled = true,
        
        -- Anti-Fraud Measures
        antiFraud = {
            priceValidation = true, -- Validate reasonable prices
            duplicateListingPrevention = true, -- Prevent duplicate listings
            rapidListingPrevention = true, -- Prevent rapid listing spam
            
            -- Monitoring
            monitoring = {
                suspiciousActivity = true, -- Monitor for suspicious activity
                priceManipulation = true, -- Detect price manipulation
                logging = true -- Log all transactions
            }
        }
    }
}
