-- ========================================
-- OLYMPUS FEDERAL EVENTS SYSTEM - SERVER MAIN
-- Complete recreation based on original federal event functions
-- Handles federal reserve, blackwater, evidence lockup, and jail break events
-- ========================================

local OlympusEvents = {}
OlympusEvents.ActiveEvents = {} -- Currently active federal events
OlympusEvents.EventCooldowns = {} -- Event cooldown tracking
OlympusEvents.EventHistory = {} -- Event history for logging
OlympusEvents.ParticipantData = {} -- Player participation tracking

-- Configuration is loaded as shared script in fxmanifest.lua
-- local Config = require('config/federal')

-- Event state tracking
local EVENT_STATES = {
    INACTIVE = 0,
    STARTING = 1,
    ACTIVE = 2,
    COMPLETED = 3,
    FAILED = 4,
    COOLDOWN = 5
}

-- Initialize federal events system
function InitializeFederalEventsSystem()
    print("^2[Olympus Events]^7 Initializing federal events system...")

    -- Load event cooldowns from database
    LoadEventCooldowns()

    -- Start event monitoring system
    StartEventMonitoring()

    -- Start cooldown management
    StartCooldownManager()

    -- Initialize event zones
    InitializeEventZones()

    print("^2[Olympus Events]^7 Federal events system initialized!")
end

-- Load event cooldowns from database
function LoadEventCooldowns()
    local query = "SELECT event_name, cooldown_end FROM fed_results WHERE cooldown_end > NOW()"
    exports['olympus-core']:FetchQuery(query, {}, function(result)
        if result and #result > 0 then
            for _, row in ipairs(result) do
                OlympusEvents.EventCooldowns[row.event_name] = row.cooldown_end
            end
            print(string.format("^3[Olympus Events]^7 Loaded %d event cooldowns", #result))
        end
    end)
end

-- Start event monitoring system
function StartEventMonitoring()
    CreateThread(function()
        while true do
            Wait(30000) -- Check every 30 seconds

            -- Check if any events can be started
            CheckEventAvailability()

            -- Update active events
            UpdateActiveEvents()

            -- Clean up expired events
            CleanupExpiredEvents()
        end
    end)
end

-- Start cooldown manager
function StartCooldownManager()
    CreateThread(function()
        while true do
            Wait(60000) -- Check every minute

            local currentTime = os.time()
            for eventName, cooldownEnd in pairs(OlympusEvents.EventCooldowns) do
                if currentTime >= cooldownEnd then
                    OlympusEvents.EventCooldowns[eventName] = nil
                    print(string.format("^3[Olympus Events]^7 %s cooldown expired", eventName))
                end
            end
        end
    end)
end

-- Initialize event zones
function InitializeEventZones()
    -- Create federal reserve zones
    CreateEventZone('federal_reserve', Config.FederalReserve.location, 150.0, 'Federal Reserve')

    -- Create blackwater zones
    CreateEventZone('blackwater_armory', Config.BlackwaterArmory.location, 100.0, 'Blackwater Armory')

    -- Create evidence lockup zones
    CreateEventZone('evidence_lockup', Config.EvidenceLockup.location, 75.0, 'Evidence Lockup')

    -- Create jail break zones
    CreateEventZone('jail_break', Config.JailBreak.location, 100.0, 'Altis Penitentiary')
end

-- Create event zone
function CreateEventZone(eventName, coords, radius, displayName)
    -- This would create the actual zone in the game world
    -- For now, we'll just store the zone data
    if not OlympusEvents.EventZones then
        OlympusEvents.EventZones = {}
    end

    OlympusEvents.EventZones[eventName] = {
        coords = coords,
        radius = radius,
        displayName = displayName,
        active = false
    }
end

-- Check event availability (matches original event checking)
function CheckEventAvailability()
    -- Check Federal Reserve availability
    if CanStartEvent('federal_reserve') then
        CheckFederalReserveRequirements()
    end

    -- Check Blackwater availability
    if CanStartEvent('blackwater_armory') then
        CheckBlackwaterRequirements()
    end

    -- Check Evidence Lockup availability
    if CanStartEvent('evidence_lockup') then
        CheckEvidenceLockupRequirements()
    end

    -- Check Jail Break availability
    if CanStartEvent('jail_break') then
        CheckJailBreakRequirements()
    end
end

-- Check if event can be started
function CanStartEvent(eventName)
    -- Check if event is already active
    if OlympusEvents.ActiveEvents[eventName] then
        return false
    end

    -- Check cooldown
    if OlympusEvents.EventCooldowns[eventName] then
        return false
    end

    -- Check global cooldown
    local lastEventTime = GetLastEventTime()
    if lastEventTime and (os.time() - lastEventTime) < Config.GlobalSettings.globalCooldown then
        return false
    end

    return true
end

-- Check Federal Reserve requirements (matches original requirements)
function CheckFederalReserveRequirements()
    local apdCount = GetOnlineAPDCount()

    if apdCount >= Config.FederalReserve.requirements.minAPD then
        -- Federal Reserve can be started
        TriggerClientEvent('olympus:client:federalEventAvailable', -1, {
            eventName = 'federal_reserve',
            displayName = 'Federal Reserve Bank',
            location = Config.FederalReserve.location,
            requirements = Config.FederalReserve.requirements,
            apdOnline = apdCount
        })
    end
end

-- Check Blackwater requirements
function CheckBlackwaterRequirements()
    local apdCount = GetOnlineAPDCount()

    if apdCount >= Config.BlackwaterArmory.requirements.minAPD then
        TriggerClientEvent('olympus:client:federalEventAvailable', -1, {
            eventName = 'blackwater_armory',
            displayName = 'Blackwater Armory',
            location = Config.BlackwaterArmory.location,
            requirements = Config.BlackwaterArmory.requirements,
            apdOnline = apdCount
        })
    end
end

-- Check Evidence Lockup requirements
function CheckEvidenceLockupRequirements()
    local apdCount = GetOnlineAPDCount()

    if apdCount >= Config.EvidenceLockup.requirements.minAPD then
        TriggerClientEvent('olympus:client:federalEventAvailable', -1, {
            eventName = 'evidence_lockup',
            displayName = 'Evidence Lockup',
            location = Config.EvidenceLockup.location,
            requirements = Config.EvidenceLockup.requirements,
            apdOnline = apdCount
        })
    end
end

-- Check Jail Break requirements
function CheckJailBreakRequirements()
    local apdCount = GetOnlineAPDCount()

    if apdCount >= Config.JailBreak.requirements.minAPD then
        -- Also check if there are gang members in prison
        local query = "SELECT COUNT(*) as count FROM players WHERE jail_time > 0"
        exports['olympus-core']:FetchQuery(query, {}, function(result)
            if result and result[1] and result[1].count > 0 then
                TriggerClientEvent('olympus:client:federalEventAvailable', -1, {
                    eventName = 'jail_break',
                    displayName = 'Jail Break',
                    location = Config.JailBreak.location,
                    requirements = Config.JailBreak.requirements,
                    apdOnline = apdCount,
                    prisonersCount = result[1].count
                })
            end
        end)
    end
end

-- Get online APD count
function GetOnlineAPDCount()
    local count = 0
    local players = GetPlayers()

    for _, playerId in ipairs(players) do
        local playerData = exports['olympus-core']:GetPlayerData(tonumber(playerId))
        if playerData and playerData.job == 'cop' then
            count = count + 1
        end
    end

    return count
end

-- Get last event time
function GetLastEventTime()
    -- This would query the database for the last federal event
    -- For now, return nil to allow events
    return nil
end

-- Start Federal Reserve event (matches original federal reserve mechanics)
RegisterServerEvent('olympus:server:startFederalReserve')
AddEventHandler('olympus:server:startFederalReserve', function()
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData then return end

    -- Validate requirements
    if not ValidateFederalReserveStart(source, playerData) then
        return
    end

    -- Start the event
    local eventData = {
        eventName = 'federal_reserve',
        startTime = os.time(),
        bombTimer = Config.FederalReserve.timers.bombTimer,
        participants = {[playerData.identifier] = {name = playerData.name, gang = playerData.gang}},
        phase = 'planting',
        apdOnline = GetOnlineAPDCount(),
        goldBars = CalculateGoldBars(GetOnlineAPDCount()),
        antiAirActive = true,
        state = EVENT_STATES.STARTING
    }

    OlympusEvents.ActiveEvents['federal_reserve'] = eventData

    -- Notify all players
    TriggerClientEvent('olympus:client:federalEventStarted', -1, {
        eventName = 'federal_reserve',
        displayName = 'Federal Reserve Bank',
        startedBy = playerData.name,
        gang = playerData.gang and playerData.gang.name or 'Unknown',
        bombTimer = eventData.bombTimer,
        goldBars = eventData.goldBars,
        location = Config.FederalReserve.location
    })

    -- Notify APD specifically
    NotifyAPDOfFederalEvent('federal_reserve', eventData)

    -- Start bomb timer
    StartBombTimer('federal_reserve', eventData.bombTimer)

    -- Log event start
    LogFederalEvent('federal_reserve', 'started', playerData, eventData)

    print(string.format("^2[Olympus Events]^7 Federal Reserve started by %s (%s)",
        playerData.name, playerData.gang and playerData.gang.name or 'No Gang'))
end)

-- Validate Federal Reserve start requirements
function ValidateFederalReserveStart(source, playerData)
    -- Check if player is in a gang
    if not playerData.gang or playerData.gang.id == 0 then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Federal Events',
            message = 'You must be in a gang to start federal events'
        })
        return false
    end

    -- Check if player has required items
    if not HasRequiredItems(source, Config.FederalReserve.requirements.items) then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Federal Events',
            message = 'You need a blasting charge to start the Federal Reserve'
        })
        return false
    end

    -- Check APD count
    local apdCount = GetOnlineAPDCount()
    if apdCount < Config.FederalReserve.requirements.minAPD then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Federal Events',
            message = string.format('Need at least %d APD online (currently %d)',
                Config.FederalReserve.requirements.minAPD, apdCount)
        })
        return false
    end

    -- Check if event can be started
    if not CanStartEvent('federal_reserve') then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Federal Events',
            message = 'Federal Reserve is on cooldown or already active'
        })
        return false
    end

    return true
end

-- Calculate gold bars based on APD count (matches original scaling)
function CalculateGoldBars(apdCount)
    local scaling = Config.FederalReserve.rewards.goldBars.scaling

    if scaling[apdCount] then
        return scaling[apdCount]
    elseif apdCount >= 15 then
        return Config.FederalReserve.rewards.goldBars.maxBars
    else
        return 150 -- Minimum bars
    end
end

-- Check if player has required items
function HasRequiredItems(source, requiredItems)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData or not playerData.inventory then return false end

    for _, item in ipairs(requiredItems) do
        local hasItem = false
        for _, invItem in ipairs(playerData.inventory) do
            if invItem.name == item and invItem.amount > 0 then
                hasItem = true
                break
            end
        end
        if not hasItem then
            return false
        end
    end

    return true
end

-- Notify APD of federal event
function NotifyAPDOfFederalEvent(eventName, eventData)
    local players = GetPlayers()

    for _, playerId in ipairs(players) do
        local playerData = exports['olympus-core']:GetPlayerData(tonumber(playerId))
        if playerData and playerData.job == 'cop' then
            TriggerClientEvent('olympus:client:apdFederalAlert', tonumber(playerId), {
                eventName = eventName,
                location = Config.FederalReserve.location,
                priority = 1,
                message = 'FEDERAL RESERVE BANK ROBBERY IN PROGRESS',
                participants = eventData.participants,
                bombTimer = eventData.bombTimer
            })
        end
    end
end

-- Start bomb timer
function StartBombTimer(eventName, duration)
    CreateThread(function()
        local timeLeft = duration

        while timeLeft > 0 and OlympusEvents.ActiveEvents[eventName] do
            Wait(1000)
            timeLeft = timeLeft - 1

            -- Update clients with timer
            TriggerClientEvent('olympus:client:updateBombTimer', -1, {
                eventName = eventName,
                timeLeft = timeLeft
            })

            -- Check for defusal attempts
            if OlympusEvents.ActiveEvents[eventName].state == EVENT_STATES.FAILED then
                break
            end
        end

        -- Timer expired - event success
        if timeLeft <= 0 and OlympusEvents.ActiveEvents[eventName] then
            CompleteFederalEvent(eventName, true)
        end
    end)
end

-- Complete federal event
function CompleteFederalEvent(eventName, success)
    local eventData = OlympusEvents.ActiveEvents[eventName]
    if not eventData then return end

    eventData.state = success and EVENT_STATES.COMPLETED or EVENT_STATES.FAILED
    eventData.endTime = os.time()

    if success then
        -- Spawn rewards
        SpawnFederalRewards(eventName, eventData)

        -- Set success cooldown
        local cooldownTime = Config.FederalReserve.timers.cooldownSuccess
        OlympusEvents.EventCooldowns[eventName] = os.time() + cooldownTime

        -- Notify success
        TriggerClientEvent('olympus:client:federalEventCompleted', -1, {
            eventName = eventName,
            success = true,
            rewards = eventData.goldBars,
            participants = eventData.participants
        })
    else
        -- Set failure cooldown
        local cooldownTime = Config.FederalReserve.timers.cooldownFailure
        OlympusEvents.EventCooldowns[eventName] = os.time() + cooldownTime

        -- Notify failure
        TriggerClientEvent('olympus:client:federalEventCompleted', -1, {
            eventName = eventName,
            success = false,
            reason = 'Bomb defused by APD'
        })
    end

    -- Log event completion
    LogFederalEvent(eventName, success and 'completed' or 'failed', nil, eventData)

    -- Clean up event
    CleanupFederalEvent(eventName)

    print(string.format("^2[Olympus Events]^7 %s %s",
        eventName, success and 'completed successfully' or 'failed'))
end

-- Spawn federal rewards
function SpawnFederalRewards(eventName, eventData)
    if eventName == 'federal_reserve' then
        -- Spawn gold bars at federal reserve
        local spawnLocation = Config.FederalReserve.location

        for i = 1, eventData.goldBars do
            -- This would spawn actual gold bar items in the world
            -- For now, we'll just track them
            TriggerClientEvent('olympus:client:spawnGoldBar', -1, {
                location = vector3(
                    spawnLocation.x + math.random(-10, 10),
                    spawnLocation.y + math.random(-10, 10),
                    spawnLocation.z
                ),
                value = Config.FederalReserve.rewards.goldBars.baseValue
            })
        end
    end
end

-- Log federal event
function LogFederalEvent(eventName, action, playerData, eventData)
    local query = [[
        INSERT INTO fed_results (event_name, action, player_id, player_name, gang_id, gang_name,
                                participants, rewards, timestamp, cooldown_end)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), DATE_ADD(NOW(), INTERVAL ? SECOND))
    ]]

    local cooldownSeconds = action == 'completed' and
        Config.FederalReserve.timers.cooldownSuccess or
        Config.FederalReserve.timers.cooldownFailure

    exports['olympus-core']:ExecuteQuery(query, {
        eventName,
        action,
        playerData and playerData.identifier or nil,
        playerData and playerData.name or nil,
        playerData and playerData.gang and playerData.gang.id or nil,
        playerData and playerData.gang and playerData.gang.name or nil,
        json.encode(eventData.participants or {}),
        json.encode(eventData.goldBars or 0),
        cooldownSeconds
    })
end

-- Cleanup federal event
function CleanupFederalEvent(eventName)
    -- Remove from active events
    OlympusEvents.ActiveEvents[eventName] = nil

    -- Clean up any spawned objects/entities
    TriggerClientEvent('olympus:client:cleanupFederalEvent', -1, eventName)
end

-- Update active events
function UpdateActiveEvents()
    for eventName, eventData in pairs(OlympusEvents.ActiveEvents) do
        -- Update event timers and states
        if eventData.state == EVENT_STATES.ACTIVE then
            local timeElapsed = os.time() - eventData.startTime

            -- Check if event should timeout
            if timeElapsed > (eventData.bombTimer + 300) then -- 5 minute grace period
                CompleteFederalEvent(eventName, false)
            end
        end
    end
end

-- Clean up expired events
function CleanupExpiredEvents()
    local currentTime = os.time()

    for eventName, eventData in pairs(OlympusEvents.ActiveEvents) do
        if eventData.endTime and (currentTime - eventData.endTime) > 300 then -- 5 minutes after end
            CleanupFederalEvent(eventName)
        end
    end
end

-- APD Defuse Bomb Event
RegisterServerEvent('olympus:server:defuseBomb')
AddEventHandler('olympus:server:defuseBomb', function(eventName)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData or playerData.job ~= 'cop' then
        return
    end

    local eventData = OlympusEvents.ActiveEvents[eventName]
    if not eventData then
        return
    end

    -- Check if player has defusal kit
    if not HasRequiredItems(source, {'defusal_kit'}) then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Federal Events',
            message = 'You need a defusal kit to defuse the bomb'
        })
        return
    end

    -- Start defusal process
    TriggerClientEvent('olympus:client:startDefusal', source, {
        eventName = eventName,
        defuseTime = Config.FederalReserve.timers.defuseTime
    })

    -- Set timer for defusal
    CreateThread(function()
        Wait(Config.FederalReserve.timers.defuseTime * 1000)

        -- Check if event is still active and player is still defusing
        if OlympusEvents.ActiveEvents[eventName] and
           OlympusEvents.ActiveEvents[eventName].state ~= EVENT_STATES.FAILED then

            -- Bomb defused successfully
            CompleteFederalEvent(eventName, false)

            TriggerClientEvent('olympus:client:notify', source, {
                type = 'success',
                title = 'Federal Events',
                message = 'Bomb defused successfully!'
            })
        end
    end)
end)

-- Anti-Air System Events
RegisterServerEvent('olympus:server:hackAntiAir')
AddEventHandler('olympus:server:hackAntiAir', function()
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData then return end

    -- Check if federal reserve is active
    local eventData = OlympusEvents.ActiveEvents['federal_reserve']
    if not eventData then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Anti-Air System',
            message = 'No active federal event'
        })
        return
    end

    -- Check if player has hacking device
    if not HasRequiredItems(source, Config.FederalReserve.antiAir.hackingTerminal.items) then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Anti-Air System',
            message = 'You need a hacking device'
        })
        return
    end

    -- Start hacking process
    TriggerClientEvent('olympus:client:startHacking', source, {
        hackTime = Config.FederalReserve.antiAir.hackingTerminal.hackTime,
        location = Config.FederalReserve.antiAir.hackingTerminal.location
    })

    -- Disable anti-air after hack time
    CreateThread(function()
        Wait(Config.FederalReserve.antiAir.hackingTerminal.hackTime * 1000)

        -- Disable anti-air system
        eventData.antiAirActive = false
        eventData.antiAirDisabledUntil = os.time() + Config.FederalReserve.antiAir.hackingTerminal.disableTime

        TriggerClientEvent('olympus:client:antiAirDisabled', -1, {
            disableTime = Config.FederalReserve.antiAir.hackingTerminal.disableTime
        })

        TriggerClientEvent('olympus:client:notify', source, {
            type = 'success',
            title = 'Anti-Air System',
            message = 'Anti-air system disabled for 5 minutes!'
        })

        -- Re-enable after disable time
        Wait(Config.FederalReserve.antiAir.hackingTerminal.disableTime * 1000)
        if OlympusEvents.ActiveEvents['federal_reserve'] then
            eventData.antiAirActive = true
            TriggerClientEvent('olympus:client:antiAirEnabled', -1)
        end
    end)
end)

-- Export functions for other resources
exports('GetActiveEvents', function()
    return OlympusEvents.ActiveEvents
end)

exports('IsEventActive', function(eventName)
    return OlympusEvents.ActiveEvents[eventName] ~= nil
end)

exports('GetEventCooldowns', function()
    return OlympusEvents.EventCooldowns
end)

exports('CanStartEvent', function(eventName)
    return CanStartEvent(eventName)
end)

exports('GetOnlineAPDCount', function()
    return GetOnlineAPDCount()
end)

-- Initialize system when resource starts
CreateThread(function()
    Wait(2000) -- Wait for other resources to load
    InitializeFederalEventsSystem()
end)
