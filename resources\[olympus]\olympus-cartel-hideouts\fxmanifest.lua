fx_version 'cerulean'
game 'gta5'

author 'Olympus Development'
description 'Olympus Cartel & Hideout System - Complete cartel capture and hideout control mechanics'
version '1.0.0'

dependencies {
    'olympus-core',
    'olympus-gangs'
}

shared_scripts {
    'config/shared.lua'
}

client_scripts {
    'client/main.lua'
}

server_scripts {
    'server/main.lua'
}

exports {
    'CaptureHideout',
    'CaptureBlackMarket',
    'GetHideoutOwner',
    'GetBlackMarketOwner',
    'IsHideoutCaptured',
    'IsBlackMarketCaptured',
    'GetCartelDiscounts',
    'CanUseBlackMarket'
}
