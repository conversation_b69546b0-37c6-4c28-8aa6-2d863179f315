fx_version 'cerulean'
game 'gta5'

name 'Olympus Casino System'
description 'Complete casino system with slots, roulette, and blackjack'
author 'Olympus Development Team'
version '1.0.0'

-- Dependencies
dependencies {
    'olympus-core',
    'olympus-ui',
    'oxmysql'
}

-- Shared scripts
shared_scripts {
    'config/shared.lua'
}

-- Client scripts
client_scripts {
    'client/main.lua',
    'client/casino_ui.lua',
    'client/slot_machines.lua',
    'client/roulette.lua',
    'client/blackjack.lua',
    'client/casino_npcs.lua'
}

-- Server scripts
server_scripts {
    '@oxmysql/lib/MySQL.lua',
    'server/main.lua',
    'server/casino_system.lua',
    'server/gambling_logic.lua',
    'server/self_exclusion.lua',
    'server/statistics.lua'
}

-- UI files
ui_page 'html/index.html'

files {
    'html/index.html',
    'html/css/*.css',
    'html/js/*.js',
    'html/images/*.png',
    'html/sounds/*.ogg'
}

-- Exports
exports {
    'CanPlayerGamble',
    'OpenCasino',
    'GetGamblingStats'
}

server_exports {
    'ProcessGamble',
    'SetSelfExclusion',
    'GetCasinoStats'
}
