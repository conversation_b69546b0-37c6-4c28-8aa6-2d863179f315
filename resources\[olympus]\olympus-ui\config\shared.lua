-- ========================================
-- OLYMPUS UI FRAMEWORK - SHARED CONFIG
-- Based on Original Olympus Altis Life
-- ========================================

OlympusUI = {}

-- ========================================
-- HUD CONFIGURATION
-- ========================================
OlympusUI.HUD = {
    -- HUD Position and Size
    position = {
        x = 0.78, -- Right side of screen
        y = 0.0,  -- Top of screen
        width = 0.22, -- 22% of screen width
        height = 0.28 -- 28% of screen height
    },
    
    -- Status Bars Configuration
    statusBars = {
        health = {
            enabled = true,
            icon = 'health.png',
            color = {255, 68, 68}, -- Red
            position = 1
        },
        food = {
            enabled = true,
            icon = 'food.png',
            color = {68, 255, 68}, -- Green
            position = 2
        },
        water = {
            enabled = true,
            icon = 'water.png',
            color = {68, 68, 255}, -- Blue
            position = 3
        },
        stamina = {
            enabled = true,
            icon = 'stamina.png',
            color = {255, 255, 68}, -- Yellow
            position = 4
        },
        wanted = {
            enabled = true,
            icon = 'wanted.png',
            color = {255, 0, 0}, -- Bright Red
            position = 5,
            showOnlyWhenWanted = true
        }
    },
    
    -- Info Display Configuration
    infoDisplay = {
        cash = {
            enabled = true,
            label = 'Cash:',
            format = '$%s',
            position = 1
        },
        bank = {
            enabled = true,
            label = 'Bank:',
            format = '$%s',
            position = 2
        },
        duty = {
            enabled = true,
            label = 'Duty:',
            format = '%s',
            position = 3,
            showOnlyOnDuty = true
        },
        location = {
            enabled = true,
            label = 'Location:',
            format = '%s',
            position = 4
        },
        time = {
            enabled = true,
            label = 'Time:',
            format = '%s',
            position = 5
        }
    },
    
    -- Admin HUD Configuration
    adminHUD = {
        enabled = true,
        toggles = {
            'godmode',
            'invisible',
            'esp',
            'stase',
            'streamer',
            'fly'
        }
    },
    
    -- Update Intervals (milliseconds)
    updateIntervals = {
        hud = 1000,      -- 1 second
        location = 2000, -- 2 seconds
        time = 1000,     -- 1 second
        admin = 500      -- 0.5 seconds
    }
}

-- ========================================
-- NOTIFICATION CONFIGURATION
-- ========================================
OlympusUI.Notifications = {
    -- Default Settings
    defaultDuration = 5000, -- 5 seconds
    maxNotifications = 5,
    
    -- Notification Types (Based on Original Olympus)
    types = {
        success = {
            color = '#00ff00',
            sound = 'notification_success.ogg'
        },
        error = {
            color = '#ff0000',
            sound = 'notification_error.ogg'
        },
        warning = {
            color = '#ffff00',
            sound = 'notification_warning.ogg'
        },
        info = {
            color = '#00ccff',
            sound = 'notification_info.ogg'
        },
        admin = {
            color = '#ff00ff',
            sound = 'notification_admin.ogg'
        },
        police = {
            color = '#0066ff',
            sound = 'notification_police.ogg'
        },
        medical = {
            color = '#ff6600',
            sound = 'notification_medical.ogg'
        }
    },
    
    -- Sound Settings
    sounds = {
        enabled = true,
        volume = 0.5
    }
}

-- ========================================
-- Y MENU CONFIGURATION
-- ========================================
OlympusUI.YMenu = {
    -- Menu Structure (Based on Original Olympus P Menu)
    tabs = {
        {
            id = 'licenses',
            name = 'Licenses',
            icon = 'license.png',
            enabled = true
        },
        {
            id = 'keychain',
            name = 'Key Chain',
            icon = 'keys.png',
            enabled = true
        },
        {
            id = 'gang',
            name = 'Gang',
            icon = 'gang.png',
            enabled = true
        },
        {
            id = 'cell',
            name = 'Cell Phone',
            icon = 'phone.png',
            enabled = true
        },
        {
            id = 'wanted',
            name = 'Wanted List',
            icon = 'wanted.png',
            enabled = true,
            factionOnly = {'police'}
        },
        {
            id = 'admin',
            name = 'Admin Menu',
            icon = 'admin.png',
            enabled = true,
            adminOnly = true
        },
        {
            id = 'settings',
            name = 'Settings',
            icon = 'settings.png',
            enabled = true
        },
        {
            id = 'market',
            name = 'Market',
            icon = 'market.png',
            enabled = true
        },
        {
            id = 'stats',
            name = 'Statistics',
            icon = 'stats.png',
            enabled = true
        },
        {
            id = 'smartphone',
            name = 'Smart Phone',
            icon = 'smartphone.png',
            enabled = true
        },
        {
            id = 'gps',
            name = 'GPS',
            icon = 'gps.png',
            enabled = true
        },
        {
            id = 'sync',
            name = 'Sync Data',
            icon = 'sync.png',
            enabled = true
        }
    },
    
    -- Animation Settings
    animations = {
        openDuration = 300,
        closeDuration = 200,
        tabSwitchDuration = 150
    }
}

-- ========================================
-- PROGRESS BAR CONFIGURATION
-- ========================================
OlympusUI.ProgressBar = {
    -- Default Settings
    defaultDuration = 5000,
    
    -- Styling
    style = {
        width = 300,
        height = 20,
        backgroundColor = 'rgba(0, 0, 0, 0.6)',
        progressColor = 'rgba(58, 123, 213, 0.8)',
        textColor = '#ffffff',
        borderColor = 'rgba(255, 255, 255, 0.3)'
    },
    
    -- Position
    position = {
        x = 0.5, -- Center horizontally
        y = 0.8  -- Bottom of screen
    }
}

-- ========================================
-- TITLE TEXT CONFIGURATION
-- ========================================
OlympusUI.TitleText = {
    -- Default Settings
    defaultDuration = 5000,
    fadeOutDuration = 1000,
    
    -- Styling
    style = {
        fontSize = '3vh',
        fontWeight = 'bold',
        textAlign = 'center',
        color = '#ffffff',
        textShadow = '2px 2px 4px rgba(0, 0, 0, 0.8)'
    },
    
    -- Position
    position = {
        x = 0.5, -- Center horizontally
        y = 0.3  -- Upper portion of screen
    }
}

return OlympusUI
