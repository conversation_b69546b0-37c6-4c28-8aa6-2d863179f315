-- Olympus Quest System - Server Main

CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end
    
    print("[Olympus Quests] Server initialized")
end)

exports('CreateQuest', function(questData)
    print("[Olympus Quests] Creating quest:", questData.name)
end)

exports('CompleteQuestStep', function(source, questId, stepId)
    print("[Olympus Quests] Completing quest step:", stepId)
end)

exports('CompleteQuest', function(source, questId)
    print("[Olympus Quests] Completing quest:", questId)
end)

exports('GetPlayerQuests', function(source)
    return {}
end)

print("[Olympus Quests] Server module loaded")
