-- Olympus Core Framework - Shared Configuration
-- This file contains configuration that is shared between client and server

Config = {}

-- Framework Information
Config.FrameworkName = "Olympus"
Config.Version = "1.0.0"
Config.Debug = true

-- Server Settings
Config.MaxPlayers = 200
Config.DefaultSpawn = vector3(-1037.0, -2737.0, 20.0) -- Los Santos Airport
Config.RespawnTime = 300 -- 5 minutes in seconds
Config.NewLifeRuleTime = 900 -- 15 minutes in seconds

-- Economy Settings
Config.StartingMoney = 5000
Config.MaxMoney = *********
Config.BankInterestRate = 0.001 -- 0.1% per hour
Config.TaxRate = 0.05 -- 5% tax on transactions

-- Player Settings
Config.MaxInventorySlots = 50
Config.MaxInventoryWeight = 100
Config.MaxHouses = 5
Config.MaxVehicles = 20

-- Faction Settings
Config.Factions = {
    ['civilian'] = {
        name = 'Civilian',
        color = '#FFFFFF',
        spawn = vector3(-1037.0, -2737.0, 20.0),
        ranks = {
            [1] = 'Citizen'
        }
    },
    ['apd'] = {
        name = 'Altis Police Department',
        color = '#0066CC',
        spawn = vector3(425.1, -979.5, 30.7),
        ranks = {
            [1] = 'Deputy',
            [2] = 'Patrol Officer',
            [3] = 'Corporal',
            [4] = 'Sergeant',
            [5] = 'Lieutenant',
            [6] = 'Captain',
            [7] = 'Deputy Chief',
            [8] = 'Chief'
        }
    },
    ['rnr'] = {
        name = 'Rescue & Recovery',
        color = '#FF6600',
        spawn = vector3(298.6, -1448.1, 29.9),
        ranks = {
            [1] = 'EMT',
            [2] = 'Paramedic',
            [3] = 'Senior Paramedic',
            [4] = 'Supervisor',
            [5] = 'Chief of Medicine'
        }
    }
}

-- Gang Settings
Config.MaxGangMembers = 15
Config.GangCreationCost = 100000
Config.GangRankNames = {
    [1] = 'Member',
    [2] = 'Trusted',
    [3] = 'Lieutenant',
    [4] = 'Captain',
    [5] = 'Leader'
}

-- Vehicle Settings
Config.VehicleDespawnTime = 1800 -- 30 minutes
Config.ImpoundCost = 1000
Config.VehicleKeys = true

-- Interaction Settings
Config.InteractionDistance = 3.0
Config.VehicleInteractionDistance = 5.0

-- Notification Settings
Config.NotificationDuration = 5000 -- 5 seconds
Config.NotificationPosition = 'top-right'

-- Progress Bar Settings
Config.ProgressBarPosition = 'bottom'
Config.ProgressBarColor = '#3498db'

-- Chat Settings
Config.ChatSuggestions = true
Config.ChatCommands = true

-- Admin Settings
Config.AdminLevels = {
    [1] = 'Moderator',
    [2] = 'Administrator',
    [3] = 'Senior Admin',
    [4] = 'Head Admin',
    [5] = 'Owner'
}

-- Event Settings
Config.Events = {
    ['federal_reserve'] = {
        name = 'Federal Reserve',
        location = vector3(243.2, 225.5, 106.3),
        reward = 500000,
        cooldown = 3600 -- 1 hour
    },
    ['bank'] = {
        name = 'Bank of Altis',
        location = vector3(147.0, -1045.2, 29.4),
        reward = 250000,
        cooldown = 1800 -- 30 minutes
    },
    ['blackwater'] = {
        name = 'Blackwater Armory',
        location = vector3(2447.9, 1576.9, 33.0),
        reward = 750000,
        cooldown = 7200 -- 2 hours
    }
}

-- Zone Settings
Config.SafeZones = {
    {
        name = 'Spawn Area',
        coords = vector3(-1037.0, -2737.0, 20.0),
        radius = 100.0
    },
    {
        name = 'Hospital',
        coords = vector3(298.6, -1448.1, 29.9),
        radius = 50.0
    }
}

Config.RedZones = {
    {
        name = 'Warzone',
        coords = vector3(2447.9, 1576.9, 33.0),
        radius = 500.0
    }
}

-- License Settings
Config.Licenses = {
    ['drivers'] = {
        name = 'Driver\'s License',
        cost = 500,
        required = false
    },
    ['weapon'] = {
        name = 'Weapon License',
        cost = 5000,
        required = true
    },
    ['pilot'] = {
        name = 'Pilot License',
        cost = 25000,
        required = true
    },
    ['boat'] = {
        name = 'Boat License',
        cost = 2500,
        required = true
    },
    ['cdl'] = {
        name = 'Commercial Driver\'s License',
        cost = 10000,
        required = true
    }
}

-- Job Settings
Config.Jobs = {
    ['unemployed'] = {
        name = 'Unemployed',
        payment = 0
    },
    ['taxi'] = {
        name = 'Taxi Driver',
        payment = 50
    },
    ['trucker'] = {
        name = 'Truck Driver',
        payment = 100
    },
    ['miner'] = {
        name = 'Miner',
        payment = 75
    }
}

-- Keybinds
Config.Keybinds = {
    ['inventory'] = 'TAB',
    ['phone'] = 'F1',
    ['vehicle_menu'] = 'F2',
    ['interaction'] = 'E',
    ['hands_up'] = 'X',
    ['crouch'] = 'LCTRL',
    ['prone'] = 'Z'
}

-- UI Settings
Config.UI = {
    ['hud'] = true,
    ['minimap'] = true,
    ['speedometer'] = true,
    ['compass'] = true,
    ['street_names'] = true
}
