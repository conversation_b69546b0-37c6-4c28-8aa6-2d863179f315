-- ========================================
-- OLYMPUS ECONOMY SYSTEM - SERVER MAIN
-- Complete recreation based on original Olympus market functions
-- Handles dynamic market pricing, item trading, and economic events
-- ========================================

local OlympusEconomy = {}
OlympusEconomy.MarketData = {} -- Current market prices and adjustments
OlympusEconomy.MarketStorage = {} -- Market storage data [variableName, currentPrice]
OlympusEconomy.MarketConfig = {} -- Market configuration [itemName, lowest, highest, flag, decrease%, increase%]
OlympusEconomy.MarketVarNames = {} -- Variable names that match market data
OlympusEconomy.MarketUpdate = {} -- Pending market updates
OlympusEconomy.MarketCache = false -- Market cache status
OlympusEconomy.EconomicEvents = {} -- Active economic events

-- Configuration is loaded as shared script in fxmanifest.lua
-- local Config = require('config/shared')

-- Initialize Olympus Economy System (matches original fn_initMarket.sqf)
function InitializeEconomySystem()
    print("^2[Olympus Economy]^7 Initializing economy system...")

    -- Load market data from database
    LoadMarketData()

    -- Initialize market configuration (exact Olympus structure)
    InitializeMarketConfig()

    -- Start market update system
    StartMarketUpdateSystem()

    -- Start economic events system
    StartEconomicEventsSystem()

    -- Start bank interest system
    StartBankInterestSystem()

    print("^2[Olympus Economy]^7 Economy system initialized!")
end

-- Load market data from database (matches original market query)
function LoadMarketData()
    local query = "SELECT market_data, reset FROM market WHERE id = ?"
    exports['olympus-core']:FetchQuery(query, {1}, function(result)
        if result and #result > 0 then
            local marketArray = result[1].market_data
            local resetRequested = result[1].reset == 1

            if resetRequested then
                -- Reset market to default values
                ResetMarketToDefaults()
            else
                -- Load existing market data
                if marketArray then
                    local priceArray = json.decode(marketArray)
                    if priceArray then
                        LoadMarketPrices(priceArray)
                    else
                        ResetMarketToDefaults()
                    end
                else
                    ResetMarketToDefaults()
                end
            end
        else
            -- No market data exists, create default
            CreateDefaultMarketData()
        end
    end)
end

-- Initialize market configuration (exact Olympus market config)
function InitializeMarketConfig()
    -- Market variable names (exact Olympus order)
    OlympusEconomy.MarketVarNames = {
        "foodDiv", "apple", "peach", "salema", "ornate", "mackerel", "mullet", "catshark", "tuna",
        "legalDiv", "saltr", "cement", "glass", "ironr", "copperr", "silverr", "platinumr", "oilp", "diamondc",
        "illegalDiv", "marijuana", "frogp", "mmushroom", "heroinp", "cocainep", "turtle", "moonshine", "crystalmeth", "moneybag", "goldbar"
    }

    -- Market configuration (exact Olympus values)
    -- [ItemName, Lowest, Highest, Flag, Decrease%, Increase%]
    OlympusEconomy.MarketConfig = {
        {"foodDiv", 0, 0, -1, 0, 0},
        {"apple", 88, 160, 0, 0.003, 0.001},
        {"peach", 128, 248, 0, 0.003, 0.001},
        {"salema", 96, 183, 0, 0.003, 0.001},
        {"ornate", 96, 183, 0, 0.003, 0.001},
        {"mackerel", 303, 564, 0, 0.003, 0.001},
        {"mullet", 336, 624, 0, 0.003, 0.001},
        {"catshark", 972, 1809, 0, 0.003, 0.001},
        {"tuna", 651, 1209, 0, 0.002, 0.001},
        {"legalDiv", 0, 0, -1, 0, 0},
        {"saltr", 860, 1915, 1, 0.003, 0.001},
        {"cement", 1206, 2690, 1, 0.003, 0.001},
        {"glass", 896, 1993, 1, 0.003, 0.001},
        {"ironr", 944, 1752, 1, 0.003, 0.001},
        {"copperr", 896, 1996, 1, 0.003, 0.001},
        {"silverr", 918, 2047, 1, 0.003, 0.001},
        {"platinumr", 1008, 1872, 1, 0.003, 0.001},
        {"oilp", 1586, 2948, 1, 0.003, 0.001},
        {"diamondc", 1140, 2118, 1, 0.0008, 0.0012},
        {"illegalDiv", 0, 0, -1, 0, 0},
        {"marijuana", 1382, 2568, 2, 0.0015, 0.001},
        {"frogp", 1984, 3388, 2, 0.0015, 0.001},
        {"mmushroom", 1806, 3354, 2, 0.0019, 0.001},
        {"heroinp", 1680, 3048, 2, 0.0015, 0.001},
        {"cocainep", 1778, 3230, 2, 0.0008, 0.001},
        {"turtle", 5586, 10374, 2, 0.0006, 0.0012},
        {"moonshine", 5884, 10926, 2, 0.0006, 0.0004},
        {"crystalmeth", 6174, 12466, 2, 0.0008, 0.0006},
        {"moneybag", 30000, 30000, 2, 0, 0},
        {"goldbar", 69063, 69063, 3, 0, 0}
    }
end

-- Reset market to default values
function ResetMarketToDefaults()
    OlympusEconomy.MarketData = {}
    OlympusEconomy.MarketStorage = {}

    for i, config in ipairs(OlympusEconomy.MarketConfig) do
        local itemName = config[1]
        local lowest = config[2]
        local highest = config[3]
        local basePrice = math.floor((lowest + highest) / 2) -- Calculate base price

        -- Initialize market data [currentPrice, priceAdjustment]
        OlympusEconomy.MarketData[i] = {basePrice, 0}

        -- Initialize market storage [variableName, currentPrice]
        OlympusEconomy.MarketStorage[i] = {itemName, basePrice}
    end

    -- Save to database
    SaveMarketData()

    -- Update reset flag
    local query = "UPDATE market SET reset = 0 WHERE id = 1"
    exports['olympus-core']:ExecuteQuery(query, {})

    print("^3[Olympus Economy]^7 Market reset to default values")
end

-- Load market prices from array
function LoadMarketPrices(priceArray)
    OlympusEconomy.MarketData = {}
    OlympusEconomy.MarketStorage = {}

    for i, price in ipairs(priceArray) do
        if OlympusEconomy.MarketConfig[i] then
            local itemName = OlympusEconomy.MarketConfig[i][1]

            -- Initialize market data [currentPrice, priceAdjustment]
            OlympusEconomy.MarketData[i] = {price, 0}

            -- Initialize market storage [variableName, currentPrice]
            OlympusEconomy.MarketStorage[i] = {itemName, price}
        end
    end

    print("^3[Olympus Economy]^7 Market data loaded from database")
end

-- Create default market data in database
function CreateDefaultMarketData()
    local defaultPrices = {}

    for i, config in ipairs(OlympusEconomy.MarketConfig) do
        local lowest = config[2]
        local highest = config[3]
        local basePrice = math.floor((lowest + highest) / 2)
        table.insert(defaultPrices, basePrice)
    end

    local query = "INSERT INTO market (id, market_data, reset) VALUES (1, ?, 0) ON DUPLICATE KEY UPDATE market_data = ?, reset = 0"
    local marketArrayJson = json.encode(defaultPrices)
    exports['olympus-core']:ExecuteQuery(query, {marketArrayJson, marketArrayJson})

    LoadMarketPrices(defaultPrices)
    print("^3[Olympus Economy]^7 Default market data created")
end

-- Save market data to database
function SaveMarketData()
    local priceArray = {}

    for i, data in ipairs(OlympusEconomy.MarketData) do
        table.insert(priceArray, data[1]) -- Current price
    end

    local query = "UPDATE market SET market_data = ? WHERE id = 1"
    local marketArrayJson = json.encode(priceArray)
    exports['olympus-core']:ExecuteQuery(query, {marketArrayJson})
end

-- Start market update system (matches original fn_marketUpdate.sqf)
function StartMarketUpdateSystem()
    CreateThread(function()
        while true do
            Wait(240000) -- 4 minutes base wait
            Wait(math.random(0, 120000)) -- Random 0-2 minutes additional wait

            if #OlympusEconomy.MarketUpdate > 0 then
                ProcessMarketUpdates()
            end
        end
    end)
end

-- Process market updates (exact Olympus logic from fn_marketUpdate.sqf)
function ProcessMarketUpdates()
    local saveArr = OlympusEconomy.MarketUpdate
    OlympusEconomy.MarketUpdate = {}
    OlympusEconomy.MarketCache = false

    for _, updateData in ipairs(saveArr) do
        local varName = updateData[1]
        local amount = updateData[2]
        local index = GetItemIndex(varName)

        if index then
            local config = OlympusEconomy.MarketConfig[index]
            local currentPrice = OlympusEconomy.MarketStorage[index][2]
            local currentAdjust = OlympusEconomy.MarketData[index][2]

            -- Calculate price decrease (exact Olympus formula)
            local tmpDecrease = math.ceil(currentPrice * config[5] * amount) -- config[5] = decrease%
            local newPrice = math.ceil(currentPrice - tmpDecrease)
            currentAdjust = currentAdjust - tmpDecrease

            -- Ensure price doesn't go below minimum
            if newPrice < config[2] then -- config[2] = lowest price
                newPrice = config[2]
                currentAdjust = (GetBasePrice(index) - config[2]) * -1
            end

            -- Update market data
            OlympusEconomy.MarketData[index][1] = newPrice
            OlympusEconomy.MarketData[index][2] = currentAdjust
            OlympusEconomy.MarketStorage[index][2] = newPrice

            -- Process price increases for other items (if enabled)
            ProcessMarketInfluence(index, amount, config[3]) -- config[3] = flag
        end
    end

    -- Save updated market data
    SaveMarketData()

    -- Notify all clients of price changes
    TriggerClientEvent('olympus-economy:client:updateMarketPrices', -1, GetCurrentMarketPrices())

    print("^3[Olympus Economy]^7 Market prices updated")
end

-- Process market influence on other items
function ProcessMarketInfluence(soldItemIndex, amount, flag)
    -- Only process if market influence is enabled
    if not Config.MarketInfluenceEnabled then return end

    for i, config in ipairs(OlympusEconomy.MarketConfig) do
        if i ~= soldItemIndex and config[4] ~= -1 then -- Skip dividers and the sold item
            local currentPrice = OlympusEconomy.MarketStorage[i][2]
            local currentAdjust = OlympusEconomy.MarketData[i][2]

            -- Calculate price increase (exact Olympus formula)
            local tmpIncrease = math.ceil(currentPrice * config[6] * amount) -- config[6] = increase%
            local newPrice = math.ceil(currentPrice + tmpIncrease)
            currentAdjust = currentAdjust + tmpIncrease

            -- Ensure price doesn't go above maximum
            if newPrice > config[3] then -- config[3] = highest price
                newPrice = config[3]
                currentAdjust = config[3] - GetBasePrice(i)
            end

            -- Update market data
            OlympusEconomy.MarketData[i][1] = newPrice
            OlympusEconomy.MarketData[i][2] = currentAdjust
            OlympusEconomy.MarketStorage[i][2] = newPrice
        end
    end
end

-- Get item index by variable name
function GetItemIndex(varName)
    for i, name in ipairs(OlympusEconomy.MarketVarNames) do
        if name == varName then
            return i
        end
    end
    return nil
end

-- Get base price for item
function GetBasePrice(index)
    local config = OlympusEconomy.MarketConfig[index]
    return math.floor((config[2] + config[3]) / 2) -- (lowest + highest) / 2
end

-- Get current market prices
function GetCurrentMarketPrices()
    local prices = {}

    for i, storage in ipairs(OlympusEconomy.MarketStorage) do
        prices[storage[1]] = storage[2] -- [itemName] = currentPrice
    end

    return prices
end

-- Add item sale to market update queue (matches original fn_marketCache.sqf)
function AddMarketSale(itemName, amount)
    if not OlympusEconomy.MarketCache then
        OlympusEconomy.MarketCache = true

        -- Start cache timer (4 minutes)
        CreateThread(function()
            Wait(240000) -- 4 minutes
            OlympusEconomy.MarketCache = false
        end)
    end

    -- Add to update queue
    local found = false
    for i, update in ipairs(OlympusEconomy.MarketUpdate) do
        if update[1] == itemName then
            update[2] = update[2] + amount
            found = true
            break
        end
    end

    if not found then
        table.insert(OlympusEconomy.MarketUpdate, {itemName, amount})
    end
end

-- Start economic events system
function StartEconomicEventsSystem()
    CreateThread(function()
        while true do
            Wait(3600000) -- Check every hour

            for eventName, eventData in pairs(Config.EconomicEvents) do
                if math.random() <= eventData.probability then
                    StartEconomicEvent(eventName, eventData)
                end
            end
        end
    end)
end

-- Start economic event
function StartEconomicEvent(eventName, eventData)
    if OlympusEconomy.EconomicEvents[eventName] then return end -- Already active

    OlympusEconomy.EconomicEvents[eventName] = {
        startTime = os.time(),
        duration = eventData.duration,
        effects = eventData.effects
    }

    -- Notify all players
    TriggerClientEvent('olympus:client:notify', -1, {
        type = 'warning',
        title = 'Economic Event',
        message = eventData.name .. ' is now active for ' .. math.floor(eventData.duration / 60) .. ' minutes!',
        duration = 10000
    })

    -- End event after duration
    CreateThread(function()
        Wait(eventData.duration * 1000)
        EndEconomicEvent(eventName)
    end)

    print("^3[Olympus Economy]^7 Economic event started: " .. eventName)
end

-- End economic event
function EndEconomicEvent(eventName)
    OlympusEconomy.EconomicEvents[eventName] = nil

    -- Notify all players
    TriggerClientEvent('olympus:client:notify', -1, {
        type = 'info',
        title = 'Economic Event',
        message = 'Economic event has ended.',
        duration = 5000
    })

    print("^3[Olympus Economy]^7 Economic event ended: " .. eventName)
end

-- Start bank interest system
function StartBankInterestSystem()
    CreateThread(function()
        while true do
            Wait(3600000) -- Every hour
            ProcessBankInterest()
        end
    end)
end

-- Process bank interest for all players
function ProcessBankInterest()
    local query = "SELECT player_id, bank FROM players WHERE bank > 0"
    exports['olympus-core']:FetchQuery(query, {}, function(result)
        if result then
            for _, player in ipairs(result) do
                local interestRate = Config.BankInterestRate

                -- Apply economic event modifiers
                for eventName, eventData in pairs(OlympusEconomy.EconomicEvents) do
                    if eventData.effects.bank_interest then
                        interestRate = interestRate * (1 + eventData.effects.bank_interest)
                    end
                end

                local interest = math.floor(player.bank * interestRate)
                if interest > 0 then
                    local newBalance = player.bank + interest

                    -- Update database
                    local updateQuery = "UPDATE players SET bank = ? WHERE player_id = ?"
                    exports['olympus-core']:ExecuteQuery(updateQuery, {newBalance, player.player_id})

                    -- Notify player if online
                    local source = exports['olympus-core']:GetPlayerByIdentifier(player.player_id)
                    if source then
                        TriggerClientEvent('olympus:client:notify', source, {
                            type = 'success',
                            title = 'Bank Interest',
                            message = 'You earned $' .. interest .. ' in bank interest',
                            duration = 5000
                        })
                    end
                end
            end
        end
    end)
end

-- Handle item selling
RegisterServerEvent('olympus-economy:server:sellItem')
AddEventHandler('olympus-economy:server:sellItem', function(itemName, amount, shopType)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData then return end

    -- Check if player has the item
    local playerItem = exports['olympus-core']:GetPlayerItem(source, itemName)
    if not playerItem or playerItem.amount < amount then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Insufficient Items',
            message = 'You don\'t have enough ' .. itemName .. ' to sell',
            duration = 5000
        })
        return
    end

    -- Get current market price
    local currentPrice = GetItemPrice(itemName)
    if not currentPrice then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Invalid Item',
            message = 'This item cannot be sold',
            duration = 5000
        })
        return
    end

    -- Apply economic event modifiers
    local finalPrice = currentPrice
    for eventName, eventData in pairs(OlympusEconomy.EconomicEvents) do
        if eventData.effects.all_prices then
            finalPrice = finalPrice * (1 + eventData.effects.all_prices)
        end

        -- Check for specific item type modifiers
        local itemIndex = GetItemIndex(itemName)
        if itemIndex then
            local config = OlympusEconomy.MarketConfig[itemIndex]
            if config[4] == 2 and eventData.effects.drug_prices then -- Illegal drugs
                finalPrice = finalPrice * (1 + eventData.effects.drug_prices)
            end
        end
    end

    finalPrice = math.floor(finalPrice)
    local totalValue = finalPrice * amount

    -- Apply tax
    local tax = math.floor(totalValue * Config.TaxRate)
    local finalPayout = totalValue - tax

    -- Remove item and add money
    exports['olympus-core']:RemovePlayerItem(source, itemName, amount)
    exports['olympus-core']:AddPlayerMoney(source, finalPayout)

    -- Add to market update queue
    AddMarketSale(itemName, amount)

    -- Notify player
    TriggerClientEvent('olympus:client:notify', source, {
        type = 'success',
        title = 'Item Sold',
        message = string.format('Sold %dx %s for $%s (Tax: $%s)', amount, itemName, finalPayout, tax),
        duration = 5000
    })

    print(string.format("^3[Olympus Economy]^7 %s sold %dx %s for $%s", GetPlayerName(source), amount, itemName, finalPayout))
end)

-- Get item price
function GetItemPrice(itemName)
    local index = GetItemIndex(itemName)
    if index and OlympusEconomy.MarketStorage[index] then
        return OlympusEconomy.MarketStorage[index][2]
    end
    return nil
end

-- Handle blackmarket access
RegisterServerEvent('olympus-economy:server:requestBlackmarketAccess')
AddEventHandler('olympus-economy:server:requestBlackmarketAccess', function()
    local source = source
    local canAccess, reason = CanAccessBlackmarket(source)

    if canAccess then
        TriggerClientEvent('olympus-economy:client:openBlackmarket', source, Config.Shops.black_market)
    else
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Access Denied',
            message = reason,
            duration = 5000
        })
    end
end)

-- Handle blackmarket purchase
RegisterServerEvent('olympus-economy:server:purchaseBlackmarketItem')
AddEventHandler('olympus-economy:server:purchaseBlackmarketItem', function(itemName, price)
    local source = source
    local canAccess, reason = CanAccessBlackmarket(source)

    if not canAccess then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Access Denied',
            message = reason,
            duration = 5000
        })
        return
    end

    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData then return end

    -- Check if player has enough money
    if playerData.money < price then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Insufficient Funds',
            message = 'You need $' .. price .. ' to purchase this item',
            duration = 5000
        })
        return
    end

    -- Remove money and give item
    exports['olympus-core']:RemovePlayerMoney(source, price)
    exports['olympus-core']:AddPlayerItem(source, itemName, 1)

    TriggerClientEvent('olympus:client:notify', source, {
        type = 'success',
        title = 'Purchase Complete',
        message = 'You purchased ' .. itemName .. ' for $' .. price,
        duration = 5000
    })

    print(string.format("^3[Olympus Economy]^7 %s purchased %s for $%s", GetPlayerName(source), itemName, price))
end)

-- Check if player can access blackmarket
function CanAccessBlackmarket(source)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData then
        return false, 'Player data not found'
    end

    -- Check if player is in excluded factions (APD or R&R)
    local excludedFactions = Config.Shops.black_market.requirements.excludedFactions
    for _, faction in pairs(excludedFactions) do
        if playerData.faction == faction then
            return false, 'APD and R&R members cannot access the black market'
        end
    end

    return true
end

-- Initialize system when server starts
CreateThread(function()
    -- Wait for core system
    while not exports['olympus-core'] do
        Wait(100)
    end

    Wait(2000) -- Additional wait for database
    InitializeEconomySystem()
end)

-- Export functions
exports('GetMarketPrice', GetItemPrice)
exports('GetMarketPrices', GetCurrentMarketPrices)
exports('AddMarketSale', AddMarketSale)
exports('IsEconomicEventActive', function(eventName)
    return OlympusEconomy.EconomicEvents[eventName] ~= nil
end)
exports('GetActiveEconomicEvents', function()
    return OlympusEconomy.EconomicEvents
end)
exports('CanAccessBlackmarket', CanAccessBlackmarket)
