-- Olympus Items - Medical Items Client
-- Based on original Olympus Altis Life medical item functions

local isUsingMedicalItem = false
local bloodBagActive = false
local dopeEffectActive = false

-- Use blood bag function
function UseBloodBag()
    if isUsingMedicalItem then
        exports['olympus-items']:ShowNotification("You are already using a medical item!", "error")
        return false
    end
    
    if bloodBagActive then
        exports['olympus-items']:ShowNotification("You already have a blood bag active!", "error")
        return false
    end
    
    local ped = PlayerPedId()
    local health = GetEntityHealth(ped)
    local maxHealth = GetEntityMaxHealth(ped)
    
    if health >= maxHealth then
        exports['olympus-items']:ShowNotification("You are already at full health!", "error")
        return false
    end
    
    -- Start blood bag process
    return StartBloodBagProcess()
end

-- Start blood bag process
function StartBloodBagProcess()
    isUsingMedicalItem = true
    
    local ped = PlayerPedId()
    
    -- Play medical animation
    exports['olympus-items']:PlayAnimation(Config.Animations.medical)
    
    -- Show progress bar
    local progressConfig = {
        label = "Applying blood bag...",
        duration = 3000,
        useWhileDead = false,
        canCancel = true
    }
    
    exports['olympus-items']:ShowProgressBar(progressConfig, function()
        -- Progress completed
        CompleteBloodBagApplication()
    end, function()
        -- Progress cancelled
        CancelMedicalItem()
    end)
    
    return true
end

-- Complete blood bag application
function CompleteBloodBagApplication()
    isUsingMedicalItem = false
    bloodBagActive = true
    
    -- Clear animation
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    -- Start healing over time
    StartBloodBagHealing()
    
    -- Consume item
    TriggerServerEvent('olympus-items:server:consumeItem', 'bloodbag', true)
    
    exports['olympus-items']:ShowNotification("Blood bag applied! Healing over time...", "success")
end

-- Start blood bag healing
function StartBloodBagHealing()
    local ped = PlayerPedId()
    local healAmount = Config.Items.bloodbag.healAmount
    local duration = Config.Items.bloodbag.duration * 1000
    local tickRate = 1000 -- Heal every second
    local totalTicks = duration / tickRate
    local healPerTick = healAmount / totalTicks
    
    CreateThread(function()
        local startTime = GetGameTimer()
        
        while GetGameTimer() - startTime < duration and bloodBagActive do
            local currentHealth = GetEntityHealth(ped)
            local maxHealth = GetEntityMaxHealth(ped)
            
            if currentHealth < maxHealth then
                local newHealth = math.min(maxHealth, currentHealth + math.floor(maxHealth * healPerTick))
                SetEntityHealth(ped, newHealth)
            end
            
            Wait(tickRate)
        end
        
        bloodBagActive = false
        exports['olympus-items']:ShowNotification("Blood bag effect has worn off.", "info")
    end)
end

-- Use EpiPen function
function UseEpiPen(target)
    if isUsingMedicalItem then
        exports['olympus-items']:ShowNotification("You are already using a medical item!", "error")
        return false
    end
    
    local ped = PlayerPedId()
    local targetPed = target
    
    -- If no target specified, try to find nearby downed player
    if not targetPed then
        targetPed = GetNearbyDownedPlayer()
        if not targetPed then
            exports['olympus-items']:ShowNotification("No downed player nearby!", "error")
            return false
        end
    end
    
    -- Check if target is downed
    if not IsPlayerDowned(targetPed) then
        exports['olympus-items']:ShowNotification("This player is not downed!", "error")
        return false
    end
    
    -- Check distance
    local playerPos = GetEntityCoords(ped)
    local targetPos = GetEntityCoords(targetPed)
    local distance = #(playerPos - targetPos)
    
    if distance > 3.0 then
        exports['olympus-items']:ShowNotification("You are too far from the player!", "error")
        return false
    end
    
    -- Start EpiPen process
    return StartEpiPenProcess(targetPed)
end

-- Get nearby downed player
function GetNearbyDownedPlayer()
    local ped = PlayerPedId()
    local playerPos = GetEntityCoords(ped)
    local players = GetActivePlayers()
    
    for _, player in ipairs(players) do
        if player ~= PlayerId() then
            local playerPed = GetPlayerPed(player)
            local targetPos = GetEntityCoords(playerPed)
            local distance = #(playerPos - targetPos)
            
            if distance <= 3.0 and IsPlayerDowned(playerPed) then
                return playerPed
            end
        end
    end
    
    return nil
end

-- Check if player is downed
function IsPlayerDowned(ped)
    -- This would check the player's downed state from the medical system
    -- For now, we'll use a simple health check
    return GetEntityHealth(ped) <= 100
end

-- Start EpiPen process
function StartEpiPenProcess(targetPed)
    isUsingMedicalItem = true
    
    local ped = PlayerPedId()
    
    -- Play medical animation
    exports['olympus-items']:PlayAnimation(Config.Animations.medical)
    
    -- Show progress bar
    local progressConfig = {
        label = "Administering EpiPen...",
        duration = 5000,
        useWhileDead = false,
        canCancel = true
    }
    
    exports['olympus-items']:ShowProgressBar(progressConfig, function()
        -- Progress completed
        CompleteEpiPenApplication(targetPed)
    end, function()
        -- Progress cancelled
        CancelMedicalItem()
    end)
    
    return true
end

-- Complete EpiPen application
function CompleteEpiPenApplication(targetPed)
    isUsingMedicalItem = false
    
    -- Clear animation
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    -- Revive target player
    local reviveHealth = Config.Items.epipen.reviveHealth
    local maxHealth = GetEntityMaxHealth(targetPed)
    local newHealth = math.floor(maxHealth * (reviveHealth / 100))
    
    SetEntityHealth(targetPed, newHealth)
    
    -- Notify target player
    local targetPlayerId = NetworkGetPlayerIndexFromPed(targetPed)
    if targetPlayerId ~= -1 then
        local targetServerId = GetPlayerServerId(targetPlayerId)
        TriggerServerEvent('olympus-items:server:playerRevived', targetServerId)
    end
    
    -- Consume item
    TriggerServerEvent('olympus-items:server:consumeItem', 'epipen', true)
    
    exports['olympus-items']:ShowNotification("Player revived successfully!", "success")
end

-- Use Dope Shot function
function UseDopeShot()
    if isUsingMedicalItem then
        exports['olympus-items']:ShowNotification("You are already using a medical item!", "error")
        return false
    end
    
    if dopeEffectActive then
        exports['olympus-items']:ShowNotification("You already have a dope shot active!", "error")
        return false
    end
    
    -- Start dope shot process
    return StartDopeShotProcess()
end

-- Start dope shot process
function StartDopeShotProcess()
    isUsingMedicalItem = true
    
    local ped = PlayerPedId()
    
    -- Play injection animation
    local animDict = "anim@amb@business@weed@weed_inspecting_high_01@"
    local animName = "weed_inspecting_high_01_inspector"
    
    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(10)
    end
    
    TaskPlayAnim(ped, animDict, animName, 8.0, -8.0, 2000, 0, 0, false, false, false)
    
    -- Show progress bar
    local progressConfig = {
        label = "Injecting stimulant...",
        duration = 2000,
        useWhileDead = false,
        canCancel = true
    }
    
    exports['olympus-items']:ShowProgressBar(progressConfig, function()
        -- Progress completed
        CompleteDopeShotApplication()
    end, function()
        -- Progress cancelled
        CancelMedicalItem()
    end)
    
    return true
end

-- Complete dope shot application
function CompleteDopeShotApplication()
    isUsingMedicalItem = false
    dopeEffectActive = true
    
    -- Clear animation
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    -- Apply speed effect
    local speedMultiplier = Config.Items.dopeshot.effects.speed
    local duration = Config.Items.dopeshot.effects.duration * 1000
    
    SetPedMoveRateOverride(ped, speedMultiplier)
    SetRunSprintMultiplierForPlayer(PlayerId(), speedMultiplier)
    
    -- Start effect timer
    CreateThread(function()
        Wait(duration)
        
        -- Remove effects
        SetPedMoveRateOverride(ped, 1.0)
        SetRunSprintMultiplierForPlayer(PlayerId(), 1.0)
        dopeEffectActive = false
        
        exports['olympus-items']:ShowNotification("Stimulant effect has worn off.", "info")
    end)
    
    -- Consume item
    TriggerServerEvent('olympus-items:server:consumeItem', 'dopeshot', true)
    
    exports['olympus-items']:ShowNotification("Stimulant injected! You feel energized!", "success")
end

-- Use Lethal Injector function
function UseLethalInjector(target)
    if isUsingMedicalItem then
        exports['olympus-items']:ShowNotification("You are already using a medical item!", "error")
        return false
    end
    
    -- Check faction access
    if not exports['olympus-items']:HasFactionAccess('lethalinjector') then
        exports['olympus-items']:ShowNotification("You don't have access to this item!", "error")
        return false
    end
    
    local ped = PlayerPedId()
    local targetPed = target
    
    -- If no target specified, try to find nearby restrained player
    if not targetPed then
        targetPed = GetNearbyRestrainedPlayer()
        if not targetPed then
            exports['olympus-items']:ShowNotification("No restrained player nearby!", "error")
            return false
        end
    end
    
    -- Check if target is restrained
    if not IsPlayerRestrained(targetPed) then
        exports['olympus-items']:ShowNotification("This player is not restrained!", "error")
        return false
    end
    
    -- Check distance
    local playerPos = GetEntityCoords(ped)
    local targetPos = GetEntityCoords(targetPed)
    local distance = #(playerPos - targetPos)
    
    if distance > 3.0 then
        exports['olympus-items']:ShowNotification("You are too far from the player!", "error")
        return false
    end
    
    -- Start lethal injection process
    return StartLethalInjectionProcess(targetPed)
end

-- Get nearby restrained player
function GetNearbyRestrainedPlayer()
    local ped = PlayerPedId()
    local playerPos = GetEntityCoords(ped)
    local players = GetActivePlayers()
    
    for _, player in ipairs(players) do
        if player ~= PlayerId() then
            local playerPed = GetPlayerPed(player)
            local targetPos = GetEntityCoords(playerPed)
            local distance = #(playerPos - targetPos)
            
            if distance <= 3.0 and IsPlayerRestrained(playerPed) then
                return playerPed
            end
        end
    end
    
    return nil
end

-- Check if player is restrained
function IsPlayerRestrained(ped)
    -- This would check the player's restrained state
    -- For now, we'll use a simple check
    return IsPedCuffed(ped) or IsPedBeingStunned(ped, 0)
end

-- Start lethal injection process
function StartLethalInjectionProcess(targetPed)
    isUsingMedicalItem = true
    
    local ped = PlayerPedId()
    
    -- Play injection animation
    exports['olympus-items']:PlayAnimation(Config.Animations.medical)
    
    -- Show progress bar
    local progressConfig = {
        label = "Administering lethal injection...",
        duration = 10000, -- Longer duration for lethal injection
        useWhileDead = false,
        canCancel = true
    }
    
    exports['olympus-items']:ShowProgressBar(progressConfig, function()
        -- Progress completed
        CompleteLethalInjection(targetPed)
    end, function()
        -- Progress cancelled
        CancelMedicalItem()
    end)
    
    return true
end

-- Complete lethal injection
function CompleteLethalInjection(targetPed)
    isUsingMedicalItem = false
    
    -- Clear animation
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    -- Kill target player
    SetEntityHealth(targetPed, 0)
    
    -- Notify target player
    local targetPlayerId = NetworkGetPlayerIndexFromPed(targetPed)
    if targetPlayerId ~= -1 then
        local targetServerId = GetPlayerServerId(targetPlayerId)
        TriggerServerEvent('olympus-items:server:playerExecuted', targetServerId)
    end
    
    -- Consume item
    TriggerServerEvent('olympus-items:server:consumeItem', 'lethalinjector', true)
    
    exports['olympus-items']:ShowNotification("Lethal injection administered.", "success")
end

-- Cancel medical item use
function CancelMedicalItem()
    isUsingMedicalItem = false
    
    -- Clear animation
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    exports['olympus-items']:ShowNotification("Medical procedure cancelled!", "info")
end

-- Export functions
exports('UseBloodBag', UseBloodBag)
exports('UseEpiPen', UseEpiPen)
exports('UseDopeShot', UseDopeShot)
exports('UseLethalInjector', UseLethalInjector)

-- Event handlers
RegisterNetEvent('olympus-items:client:medicalItemInterrupted', function()
    if isUsingMedicalItem then
        CancelMedicalItem()
    end
end)

RegisterNetEvent('olympus-items:client:stopBloodBag', function()
    bloodBagActive = false
end)

RegisterNetEvent('olympus-items:client:stopDopeEffect', function()
    if dopeEffectActive then
        local ped = PlayerPedId()
        SetPedMoveRateOverride(ped, 1.0)
        SetRunSprintMultiplierForPlayer(PlayerId(), 1.0)
        dopeEffectActive = false
    end
end)
