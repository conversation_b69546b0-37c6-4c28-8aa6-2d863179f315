-- Olympus Lottery - Gas Station Integration
-- Lottery ticket purchasing at gas stations

local gasStationBlips = {}

-- Gas station locations for lottery tickets
local gasStations = {
    {coords = vector3(49.4187, -1757.514, 29.421), name = "Davis Gas Station"},
    {coords = vector3(263.894, -1261.309, 29.292), name = "Strawberry Gas Station"},
    {coords = vector3(1163.373, -323.801, 69.205), name = "Mirror Park Gas Station"},
    {coords = vector3(1701.314, 6416.028, 32.763), name = "Paleto Bay Gas Station"},
    {coords = vector3(1961.464, 3739.96, 32.343), name = "Sandy Shores Gas Station"},
    {coords = vector3(2557.458, 382.282, 108.622), name = "Palomino Gas Station"},
    {coords = vector3(2679.858, 3263.946, 55.240), name = "Senora Freeway Gas Station"},
    {coords = vector3(1039.958, 2671.134, 39.550), name = "Route 68 Gas Station"},
    {coords = vector3(1207.260, 2660.175, 37.899), name = "Grand Senora Gas Station"},
    {coords = vector3(1181.381, -330.847, 69.316), name = "Mirror Park Gas Station 2"}
}

-- Initialize gas station lottery points
CreateThread(function()
    for i, station in ipairs(gasStations) do
        -- Create blip
        local blip = AddBlipForCoord(station.coords.x, station.coords.y, station.coords.z)
        SetBlipSprite(blip, 361)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, 0.6)
        SetBlipColour(blip, 2)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString("Lottery Tickets")
        EndTextCommandSetBlipName(blip)
        
        gasStationBlips[i] = blip
    end
end)

-- Check for nearby gas stations
CreateThread(function()
    while true do
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local sleep = 1000
        
        for _, station in ipairs(gasStations) do
            local distance = #(coords - station.coords)
            
            if distance < 10.0 then
                sleep = 0
                
                if distance < 2.0 then
                    -- Show interaction prompt
                    exports['olympus-ui']:ShowHelpText("Press [E] to buy lottery tickets")
                    
                    if IsControlJustPressed(0, 38) then -- E key
                        OpenLotteryPurchase(station)
                    end
                end
            end
        end
        
        Wait(sleep)
    end
end)

-- Open lottery purchase menu
function OpenLotteryPurchase(station)
    TriggerEvent('olympus-lottery:client:openUI')
end

-- Export functions
exports('OpenLotteryPurchase', OpenLotteryPurchase)
