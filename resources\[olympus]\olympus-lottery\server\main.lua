-- Olympus Lottery System - Server Main
-- Based on original fn_buyLotteryTicket.sqf and OES_fnc_handleLottery

local OlympusLottery = {}
local currentLottery = {
    active = false,
    participants = {},
    totalTickets = 0,
    prizePool = 0,
    drawTime = nil,
    cooldown = false
}

-- Initialize lottery system
CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end

    print("[Olympus Lottery] Server initialized")

    -- Initialize database tables
    OlympusLottery.InitializeDatabase()

    -- Start lottery management thread
    CreateThread(OlympusLottery.LotteryManagementThread)
end)

-- Utility Functions
function OlympusLottery.GetPlayerData(source)
    local success, result = pcall(function()
        return exports['olympus-core']:GetPlayerData(source)
    end)
    return success and result or nil
end

function OlympusLottery.GetPlayerMoney(source, type)
    local success, result = pcall(function()
        return exports['olympus-core']:GetMoney(source, type)
    end)
    return success and result or 0
end

function OlympusLottery.RemovePlayerMoney(source, type, amount)
    local success = pcall(function()
        exports['olympus-core']:RemoveMoney(source, type, amount)
    end)
    return success
end

function OlympusLottery.AddPlayerMoney(source, type, amount)
    local success = pcall(function()
        exports['olympus-core']:AddMoney(source, type, amount)
    end)
    return success
end

-- Database Functions
function OlympusLottery.InitializeDatabase()
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS lottery_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            player_id VARCHAR(50) NOT NULL,
            player_name VARCHAR(100) NOT NULL,
            action VARCHAR(50) NOT NULL,
            tickets_bought INT DEFAULT 0,
            amount_spent INT DEFAULT 0,
            amount_won INT DEFAULT 0,
            lottery_id INT DEFAULT 0,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_player_id (player_id),
            INDEX idx_lottery_id (lottery_id),
            INDEX idx_timestamp (timestamp)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ]])

    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS lottery_history (
            id INT AUTO_INCREMENT PRIMARY KEY,
            lottery_id INT NOT NULL,
            total_participants INT DEFAULT 0,
            total_tickets INT DEFAULT 0,
            prize_pool INT DEFAULT 0,
            winner_id VARCHAR(50) DEFAULT NULL,
            winner_name VARCHAR(100) DEFAULT NULL,
            winning_tickets INT DEFAULT 0,
            draw_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_lottery_id (lottery_id),
            INDEX idx_winner_id (winner_id),
            INDEX idx_draw_time (draw_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ]])
end

-- Lottery Management System
function OlympusLottery.LotteryManagementThread()
    while true do
        Wait(60000) -- Check every minute

        -- Check if lottery should start
        if not currentLottery.active and not currentLottery.cooldown then
            OlympusLottery.StartNewLottery()
        end

        -- Check if lottery should end
        if currentLottery.active and currentLottery.drawTime and os.time() >= currentLottery.drawTime then
            OlympusLottery.EndLottery()
        end

        -- Check if cooldown should end
        if currentLottery.cooldown and currentLottery.cooldownEnd and os.time() >= currentLottery.cooldownEnd then
            currentLottery.cooldown = false
            currentLottery.cooldownEnd = nil
            TriggerClientEvent('olympus-lottery:client:notify', -1, 'Lottery system is now available for ticket purchases!', 'success')
        end
    end
end

function OlympusLottery.StartNewLottery()
    currentLottery = {
        active = true,
        participants = {},
        totalTickets = 0,
        prizePool = 0,
        drawTime = os.time() + Config.Lottery.drawInterval,
        cooldown = false,
        lotteryId = os.time() -- Simple lottery ID based on timestamp
    }

    -- Announce new lottery
    TriggerClientEvent('olympus-lottery:client:notify', -1,
        string.format('New lottery started! Draw in %d minutes. Ticket price: $%s',
            math.floor(Config.Lottery.drawInterval / 60),
            Config.Lottery.ticketPrice), 'primary')

    print(string.format("[Olympus Lottery] New lottery started (ID: %d)", currentLottery.lotteryId))
end

function OlympusLottery.EndLottery()
    if not currentLottery.active then return end

    local lotteryId = currentLottery.lotteryId
    local totalParticipants = 0
    local totalTickets = currentLottery.totalTickets
    local prizePool = currentLottery.prizePool

    -- Count participants
    for playerId, data in pairs(currentLottery.participants) do
        totalParticipants = totalParticipants + 1
    end

    if totalParticipants == 0 then
        -- No participants, restart lottery
        TriggerClientEvent('olympus-lottery:client:notify', -1, 'Lottery ended with no participants. Starting new lottery...', 'warning')
        currentLottery.active = false
        return
    end

    -- Select winner based on ticket weight
    local winner = OlympusLottery.SelectWinner()

    if winner then
        local winnerId = winner.playerId
        local winnerName = winner.playerName
        local winnerTickets = winner.tickets
        local winnerSource = winner.source

        -- Calculate prize (80% of prize pool)
        local prize = math.floor(prizePool * Config.Lottery.prizePercentage)

        -- Award prize to winner if online
        if winnerSource and GetPlayerName(winnerSource) then
            OlympusLottery.AddPlayerMoney(winnerSource, "cash", prize)
            TriggerClientEvent('olympus-lottery:client:notify', winnerSource,
                string.format('Congratulations! You won the lottery! Prize: $%s', prize), 'success')
        end

        -- Announce winner
        TriggerClientEvent('olympus-lottery:client:notify', -1,
            string.format('Lottery Winner: %s with %d ticket%s! Prize: $%s',
                winnerName, winnerTickets, winnerTickets > 1 and "s" or "", prize), 'success')

        -- Log lottery result
        OlympusLottery.LogLotteryResult(lotteryId, totalParticipants, totalTickets, prizePool, winnerId, winnerName, winnerTickets)

        print(string.format("[Olympus Lottery] Winner: %s (%s) with %d tickets, Prize: $%s",
            winnerName, winnerId, winnerTickets, prize))
    else
        TriggerClientEvent('olympus-lottery:client:notify', -1, 'Lottery ended with no valid winner.', 'error')
    end

    -- Start cooldown
    currentLottery.active = false
    currentLottery.cooldown = true
    currentLottery.cooldownEnd = os.time() + Config.Lottery.cooldownTime

    TriggerClientEvent('olympus-lottery:client:notify', -1,
        string.format('Lottery system on cooldown for %d minutes.', math.floor(Config.Lottery.cooldownTime / 60)), 'warning')
end

function OlympusLottery.SelectWinner()
    if currentLottery.totalTickets == 0 then return nil end

    -- Create weighted list of all tickets
    local tickets = {}
    for playerId, data in pairs(currentLottery.participants) do
        for i = 1, data.tickets do
            table.insert(tickets, {
                playerId = playerId,
                playerName = data.playerName,
                source = data.source,
                tickets = data.tickets
            })
        end
    end

    if #tickets == 0 then return nil end

    -- Select random ticket
    local winningTicket = math.random(1, #tickets)
    return tickets[winningTicket]
end

function OlympusLottery.LogLotteryResult(lotteryId, participants, totalTickets, prizePool, winnerId, winnerName, winnerTickets)
    exports.oxmysql:execute('INSERT INTO lottery_history (lottery_id, total_participants, total_tickets, prize_pool, winner_id, winner_name, winning_tickets) VALUES (?, ?, ?, ?, ?, ?, ?)', {
        lotteryId, participants, totalTickets, prizePool, winnerId, winnerName, winnerTickets
    })
end

-- Event Handlers based on original fn_buyLotteryTicket.sqf
RegisterNetEvent('olympus-lottery:server:checkPlayer', function()
    local source = source
    local Player = OlympusLottery.GetPlayerData(source)
    if not Player then return end

    local playerId = Player.citizenid or GetPlayerIdentifier(source, 0)

    -- Check if player is already in current lottery
    local inLottery = currentLottery.participants[playerId] ~= nil

    TriggerClientEvent('olympus-lottery:client:receivePlayerCheck', source, {
        inLottery = inLottery,
        lotteryActive = currentLottery.active,
        cooldown = currentLottery.cooldown,
        drawTime = currentLottery.drawTime,
        ticketPrice = Config.Lottery.ticketPrice
    })
end)

RegisterNetEvent('olympus-lottery:server:buyTickets', function(ticketCount)
    local source = source
    local Player = OlympusLottery.GetPlayerData(source)
    if not Player then return end

    local playerId = Player.citizenid or GetPlayerIdentifier(source, 0)
    local playerName = Player.charinfo and (Player.charinfo.firstname .. ' ' .. Player.charinfo.lastname) or GetPlayerName(source)

    -- Validation checks matching original fn_buyLotteryTicket.sqf
    if currentLottery.cooldown then
        TriggerClientEvent('olympus-lottery:client:showError', source, 'The lottery system is on cooldown, you are not able to buy lottery tickets at this time!')
        return
    end

    if not currentLottery.active then
        TriggerClientEvent('olympus-lottery:client:showError', source, 'No lottery is currently active!')
        return
    end

    local playerPed = GetPlayerPed(source)
    if GetVehiclePedIsIn(playerPed, false) ~= 0 then
        TriggerClientEvent('olympus-lottery:client:showError', source, 'You are not able to buy a lottery ticket while in a vehicle!')
        return
    end

    -- Check if player already bought tickets (matching original oev_inLottery check)
    if currentLottery.participants[playerId] then
        TriggerClientEvent('olympus-lottery:client:showError', source, 'You have already bought tickets in the current lottery!')
        return
    end

    -- Validate ticket count (max 10 like original)
    if ticketCount > Config.Lottery.maxTicketsPerPlayer then
        TriggerClientEvent('olympus-lottery:client:showError', source, string.format('You may only buy up to %d lottery tickets.', Config.Lottery.maxTicketsPerPlayer))
        return
    end

    if ticketCount <= 0 then
        TriggerClientEvent('olympus-lottery:client:showError', source, 'Invalid ticket count!')
        return
    end

    local totalCost = ticketCount * Config.Lottery.ticketPrice
    local cashMoney = OlympusLottery.GetPlayerMoney(source, "cash")
    local bankMoney = OlympusLottery.GetPlayerMoney(source, "bank")

    -- Check if player has enough money (cash or bank like original)
    if cashMoney < totalCost and bankMoney < totalCost then
        TriggerClientEvent('olympus-lottery:client:showError', source, 'You do not have enough money to buy that amount of lottery tickets!')
        return
    end

    -- Remove money (prefer cash like original)
    local success = false
    if cashMoney >= totalCost then
        success = OlympusLottery.RemovePlayerMoney(source, "cash", totalCost)
    else
        success = OlympusLottery.RemovePlayerMoney(source, "bank", totalCost)
    end

    if not success then
        TriggerClientEvent('olympus-lottery:client:showError', source, 'Failed to process payment!')
        return
    end

    -- Add player to lottery
    currentLottery.participants[playerId] = {
        playerName = playerName,
        source = source,
        tickets = ticketCount
    }

    currentLottery.totalTickets = currentLottery.totalTickets + ticketCount
    currentLottery.prizePool = currentLottery.prizePool + totalCost

    -- Log purchase
    exports.oxmysql:execute('INSERT INTO lottery_logs (player_id, player_name, action, tickets_bought, amount_spent, lottery_id) VALUES (?, ?, ?, ?, ?, ?)', {
        playerId, playerName, 'purchase', ticketCount, totalCost, currentLottery.lotteryId
    })

    TriggerClientEvent('olympus-lottery:client:showSuccess', source,
        string.format('Thank you for purchasing %d lottery ticket%s for $%s. Good luck!',
            ticketCount, ticketCount > 1 and "s" or "", totalCost))

    print(string.format("[Olympus Lottery] %s bought %d tickets for $%s", playerName, ticketCount, totalCost))
end)

RegisterNetEvent('olympus-lottery:server:requestLotteryStatus', function()
    local source = source

    local timeRemaining = 0
    if currentLottery.active and currentLottery.drawTime then
        timeRemaining = math.max(0, currentLottery.drawTime - os.time())
    end

    local cooldownRemaining = 0
    if currentLottery.cooldown and currentLottery.cooldownEnd then
        cooldownRemaining = math.max(0, currentLottery.cooldownEnd - os.time())
    end

    TriggerClientEvent('olympus-lottery:client:receiveLotteryStatus', source, {
        active = currentLottery.active,
        cooldown = currentLottery.cooldown,
        totalTickets = currentLottery.totalTickets,
        prizePool = currentLottery.prizePool,
        timeRemaining = timeRemaining,
        cooldownRemaining = cooldownRemaining,
        ticketPrice = Config.Lottery.ticketPrice,
        maxTickets = Config.Lottery.maxTicketsPerPlayer
    })
end)

-- Player disconnect cleanup
AddEventHandler('playerDropped', function(reason)
    local source = source
    local Player = OlympusLottery.GetPlayerData(source)
    if not Player then return end

    local playerId = Player.citizenid or GetPlayerIdentifier(source, 0)

    -- Update participant source to nil (they're offline but still in lottery)
    if currentLottery.participants[playerId] then
        currentLottery.participants[playerId].source = nil
    end
end)

-- Export Functions
exports('GetLotteryStatus', function()
    return {
        active = currentLottery.active,
        cooldown = currentLottery.cooldown,
        totalTickets = currentLottery.totalTickets,
        prizePool = currentLottery.prizePool,
        participants = currentLottery.participants
    }
end)

exports('ProcessLotteryPurchase', function(playerId, ticketCount)
    -- This would be called by external systems
    return false -- Not implemented for external use
end)

exports('GetLotteryData', function()
    return currentLottery
end)

exports('StartLotteryDraw', function()
    if currentLottery.active then
        OlympusLottery.EndLottery()
        return true
    end
    return false
end)

exports('EndLotteryDraw', function()
    if currentLottery.active then
        currentLottery.active = false
        return true
    end
    return false
end)

print("[Olympus Lottery] Server module loaded")


