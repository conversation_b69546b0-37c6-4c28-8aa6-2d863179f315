-- Olympus Core Framework - Items Configuration
-- Based on Olympus Altis Life Virtual Items system

Config.Items = {}

-- Consumables
Config.Items['water'] = {
    name = 'Water',
    label = 'Water',
    weight = 1,
    type = 'consumable',
    image = 'water.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Keeps you hydrated.',
    price = 10,
    sellPrice = 5,
    illegal = false,
    effects = {
        thirst = 25
    }
}

Config.Items['tactical_bacon'] = {
    name = 'tactical_bacon',
    label = 'Tactical Bacon',
    weight = 1,
    type = 'consumable',
    image = 'bacon.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Keeps you full.',
    price = 75,
    sellPrice = 25,
    illegal = false,
    effects = {
        hunger = 25
    }
}

Config.Items['redgull'] = {
    name = 'redgull',
    label = 'Redgull',
    weight = 1,
    type = 'consumable',
    image = 'redgull.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = '3 minute stamina buff.',
    price = 1500,
    sellPrice = 650,
    illegal = false,
    effects = {
        stamina = 100,
        duration = 180
    }
}

Config.Items['cupcake'] = {
    name = 'cupcake',
    label = 'Cupcake',
    weight = 1,
    type = 'consumable',
    image = 'cupcake.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = '5 minute stamina buff with increased run speed.',
    price = 2500,
    sellPrice = 1200,
    illegal = false,
    effects = {
        stamina = 100,
        speed = 1.2,
        duration = 300
    }
}

-- Medical Items
Config.Items['first_aid_kit'] = {
    name = 'first_aid_kit',
    label = 'First Aid Kit',
    weight = 1,
    type = 'medical',
    image = 'fak.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Allows you to heal to 75%.',
    price = 500,
    sellPrice = 250,
    illegal = false,
    effects = {
        health = 75
    }
}

Config.Items['bloodbag'] = {
    name = 'bloodbag',
    label = 'Bloodbag',
    weight = 5,
    type = 'medical',
    image = 'bloodbag.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Heals you to 100%, can be used in vehicle.',
    price = 7500,
    sellPrice = 5,
    illegal = false,
    effects = {
        health = 100
    }
}

Config.Items['epipen'] = {
    name = 'epipen',
    label = 'Epipen',
    weight = 10,
    type = 'medical',
    image = 'epipen.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Revives a person without a medic.',
    price = 17500,
    sellPrice = 5000,
    illegal = false,
    effects = {
        revive = true
    }
}

Config.Items['dopamine_shot'] = {
    name = 'dopamine_shot',
    label = 'Dopamine Shot',
    weight = 10,
    type = 'medical',
    image = 'dopeshot.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Give dopamine in field. 30 minute cooldown.',
    price = 100000,
    sellPrice = 40000,
    illegal = false,
    effects = {
        dopamine = true,
        cooldown = 1800
    }
}

-- Tools
Config.Items['lockpick'] = {
    name = 'lockpick',
    label = 'Lockpick',
    weight = 1,
    type = 'tool',
    image = 'lockpick.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Get keys to vehicles you don\'t own.',
    price = 150,
    sellPrice = 60,
    illegal = true,
    seizeAmount = 75
}

Config.Items['bolt_cutter'] = {
    name = 'bolt_cutter',
    label = 'Bolt Cutter',
    weight = 5,
    type = 'tool',
    image = 'boltcutter.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Multi use item. Unrestrain, break doors, cut spike strips.',
    price = 2500,
    sellPrice = 1500,
    illegal = true,
    seizeAmount = 1250
}

Config.Items['ziptie'] = {
    name = 'ziptie',
    label = 'Ziptie',
    weight = 1,
    type = 'tool',
    image = 'ziptie.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Restrain people with hands up or tased.',
    price = 500,
    sellPrice = 0,
    illegal = false
}

Config.Items['pickaxe'] = {
    name = 'pickaxe',
    label = 'Pickaxe',
    weight = 2,
    type = 'tool',
    image = 'pickaxe.png',
    unique = false,
    useable = false,
    shouldClose = false,
    combinable = nil,
    description = 'Required to gather most raw materials.',
    price = 1200,
    sellPrice = 600,
    illegal = false
}

-- Vehicle Items
Config.Items['fuel_can_full'] = {
    name = 'fuel_can_full',
    label = 'Fuel Can (Full)',
    weight = 5,
    type = 'vehicle',
    image = 'fuel.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Refuel a vehicle.',
    price = 850,
    sellPrice = 100,
    illegal = false
}

Config.Items['toolkit'] = {
    name = 'toolkit',
    label = 'Toolkit',
    weight = 10,
    type = 'vehicle',
    image = 'toolkit.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Repair Vehicle.',
    price = 1000,
    sellPrice = 500,
    illegal = false
}

Config.Items['gps_tracker'] = {
    name = 'gps_tracker',
    label = 'GPS Tracker',
    weight = 2,
    type = 'vehicle',
    image = 'gps1.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Tracks a vehicle for you.',
    price = 15000,
    sellPrice = 6000,
    illegal = false
}

-- Drugs
Config.Items['marijuana'] = {
    name = 'marijuana',
    label = 'Marijuana',
    weight = 1,
    type = 'drug',
    image = 'marijuana.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Illegal drug.',
    price = 2568,
    sellPrice = 1382,
    illegal = true,
    seizeAmount = 1975,
    fluctuates = true
}

Config.Items['cocaine'] = {
    name = 'cocaine',
    label = 'Cocaine',
    weight = 1,
    type = 'drug',
    image = 'cocaine2.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Illegal drug.',
    price = 3230,
    sellPrice = 1778,
    illegal = true,
    seizeAmount = 2504,
    fluctuates = true
}

Config.Items['heroin'] = {
    name = 'heroin',
    label = 'Pure Heroin',
    weight = 1,
    type = 'drug',
    image = 'heroin_processed.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Fixes broken legs. Illegal drug.',
    price = 3810,
    sellPrice = 2100,
    illegal = true,
    seizeAmount = 2955,
    fluctuates = true,
    effects = {
        heal_legs = true
    }
}

Config.Items['meth'] = {
    name = 'meth',
    label = 'Crystal Meth',
    weight = 3,
    type = 'drug',
    image = 'meth.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Damage Reduction by 60% for 1-3 minutes.',
    price = 12466,
    sellPrice = 6174,
    illegal = true,
    seizeAmount = 9320,
    fluctuates = true,
    effects = {
        damage_reduction = 0.6,
        duration = 180
    }
}

-- Raw Materials
Config.Items['copper_ore'] = {
    name = 'copper_ore',
    label = 'Copper Ore',
    weight = 2,
    type = 'resource',
    image = 'copper1.png',
    unique = false,
    useable = false,
    shouldClose = false,
    combinable = nil,
    description = 'Legal resource that can be processed.',
    price = 42,
    sellPrice = 0,
    illegal = false
}

Config.Items['copper_ingot'] = {
    name = 'copper_ingot',
    label = 'Copper Ingot',
    weight = 1,
    type = 'resource',
    image = 'copper2.png',
    unique = false,
    useable = false,
    shouldClose = false,
    combinable = nil,
    description = 'Processed legal good.',
    price = 1996,
    sellPrice = 896,
    illegal = false,
    fluctuates = true
}

-- Federal Event Items
Config.Items['gold_bar'] = {
    name = 'gold_bar',
    label = 'Gold Bar',
    weight = 6,
    type = 'resource',
    image = 'goldbar.png',
    unique = false,
    useable = false,
    shouldClose = false,
    combinable = nil,
    description = 'Reward for Federal Reserve robbery.',
    price = 0,
    sellPrice = 69063,
    illegal = true,
    seizeAmount = 34531,
    fluctuates = true
}

Config.Items['money_bag'] = {
    name = 'money_bag',
    label = 'Money Bag',
    weight = 6,
    type = 'resource',
    image = 'moneybag.png',
    unique = false,
    useable = false,
    shouldClose = false,
    combinable = nil,
    description = 'Reward for bank robbery.',
    price = 0,
    sellPrice = 30000,
    illegal = true,
    seizeAmount = 15000,
    fluctuates = true
}

-- Explosives
Config.Items['blasting_charge'] = {
    name = 'blasting_charge',
    label = 'Blasting Charge',
    weight = 15,
    type = 'tool',
    image = 'blastingcharge.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Used to break into federal events.',
    price = 25000,
    sellPrice = 6000,
    illegal = true,
    seizeAmount = 7500
}

Config.Items['hacking_terminal'] = {
    name = 'hacking_terminal',
    label = 'Hacking Terminal',
    weight = 7,
    type = 'tool',
    image = 'hackingterminal.png',
    unique = false,
    useable = true,
    shouldClose = true,
    combinable = nil,
    description = 'Used to corrupt anti-air systems.',
    price = 7000,
    sellPrice = 3500,
    illegal = true,
    seizeAmount = 3500
}
