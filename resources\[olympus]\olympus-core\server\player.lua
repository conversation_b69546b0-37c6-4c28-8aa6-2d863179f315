-- Olympus Core Framework - Player Management
-- Handles player data, progression, and character management

-- Player data management
RegisterServerEvent('olympus:server:updatePlayerPosition')
AddEventHandler('olympus:server:updatePlayerPosition', function(position)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)
    
    if playerData then
        playerData.position = position
        exports['olympus-core']:UpdatePlayerData(source, 'position', position)
    end
end)

-- Player death handling
RegisterServerEvent('olympus:server:playerDied')
AddEventHandler('olympus:server:playerDied', function()
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)
    
    if playerData then
        -- Log death
        exports['olympus-core']:LogAction(source, 'player_death', {
            location = playerData.position,
            timestamp = os.time()
        })
        
        -- Start respawn timer
        playerData.deathTime = os.time()
        playerData.canRespawn = false
        
        exports['olympus-core']:UpdatePlayerData(source, 'deathTime', playerData.deathTime)
        exports['olympus-core']:UpdatePlayerData(source, 'canRespawn', false)
        
        -- Set respawn timer
        SetTimeout(Config.RespawnTime * 1000, function()
            local currentPlayerData = exports['olympus-core']:GetPlayerData(source)
            if currentPlayerData and currentPlayerData.isDead then
                currentPlayerData.canRespawn = true
                exports['olympus-core']:UpdatePlayerData(source, 'canRespawn', true)
                TriggerClientEvent('olympus:client:canRespawn', source)
            end
        end)
        
        print("^1[Olympus Core]^7 Player " .. playerData.name .. " died")
    end
end)

-- Player respawn
RegisterServerEvent('olympus:server:respawnPlayer')
AddEventHandler('olympus:server:respawnPlayer', function()
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)
    
    if playerData and playerData.canRespawn then
        -- Clear death state
        playerData.isDead = false
        playerData.canRespawn = false
        playerData.deathTime = nil
        
        -- Set new life rule
        playerData.newLifeRule = os.time() + Config.NewLifeRuleTime
        
        -- Update player data
        exports['olympus-core']:UpdatePlayerData(source, 'isDead', false)
        exports['olympus-core']:UpdatePlayerData(source, 'canRespawn', false)
        exports['olympus-core']:UpdatePlayerData(source, 'deathTime', nil)
        exports['olympus-core']:UpdatePlayerData(source, 'newLifeRule', playerData.newLifeRule)
        
        -- Revive player
        TriggerClientEvent('olympus:client:revivePlayer', source)
        
        -- Log respawn
        exports['olympus-core']:LogAction(source, 'player_respawn', {
            location = playerData.position,
            timestamp = os.time()
        })
        
        print("^2[Olympus Core]^7 Player " .. playerData.name .. " respawned")
    end
end)

-- Money management
RegisterServerEvent('olympus:server:addMoney')
AddEventHandler('olympus:server:addMoney', function(amount, reason)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)
    
    if playerData and amount > 0 then
        local newAmount = playerData.money + amount
        if newAmount <= Config.MaxMoney then
            playerData.money = newAmount
            exports['olympus-core']:UpdatePlayerData(source, 'money', newAmount)
            
            -- Log transaction
            exports['olympus-core']:LogAction(source, 'money_add', {
                amount = amount,
                reason = reason or 'Unknown',
                newBalance = newAmount
            })
            
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'success',
                title = 'Money Received',
                message = 'You received $' .. amount
            })
        end
    end
end)

RegisterServerEvent('olympus:server:removeMoney')
AddEventHandler('olympus:server:removeMoney', function(amount, reason)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)
    
    if playerData and amount > 0 and playerData.money >= amount then
        local newAmount = playerData.money - amount
        playerData.money = newAmount
        exports['olympus-core']:UpdatePlayerData(source, 'money', newAmount)
        
        -- Log transaction
        exports['olympus-core']:LogAction(source, 'money_remove', {
            amount = amount,
            reason = reason or 'Unknown',
            newBalance = newAmount
        })
        
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'warning',
            title = 'Money Deducted',
            message = 'You paid $' .. amount
        })
    end
end)

-- Bank money management
RegisterServerEvent('olympus:server:addBankMoney')
AddEventHandler('olympus:server:addBankMoney', function(amount, reason)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)
    
    if playerData and amount > 0 then
        local newAmount = playerData.bank + amount
        if newAmount <= Config.MaxMoney then
            playerData.bank = newAmount
            exports['olympus-core']:UpdatePlayerData(source, 'bank', newAmount)
            
            -- Log transaction
            exports['olympus-core']:LogAction(source, 'bank_add', {
                amount = amount,
                reason = reason or 'Unknown',
                newBalance = newAmount
            })
            
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'success',
                title = 'Bank Deposit',
                message = 'Your bank received $' .. amount
            })
        end
    end
end)

RegisterServerEvent('olympus:server:removeBankMoney')
AddEventHandler('olympus:server:removeBankMoney', function(amount, reason)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)
    
    if playerData and amount > 0 and playerData.bank >= amount then
        local newAmount = playerData.bank - amount
        playerData.bank = newAmount
        exports['olympus-core']:UpdatePlayerData(source, 'bank', newAmount)
        
        -- Log transaction
        exports['olympus-core']:LogAction(source, 'bank_remove', {
            amount = amount,
            reason = reason or 'Unknown',
            newBalance = newAmount
        })
        
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'warning',
            title = 'Bank Withdrawal',
            message = 'Your bank was charged $' .. amount
        })
    end
end)

-- Transfer money between players
RegisterServerEvent('olympus:server:transferMoney')
AddEventHandler('olympus:server:transferMoney', function(targetId, amount, reason)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)
    local targetData = exports['olympus-core']:GetPlayerData(targetId)
    
    if playerData and targetData and amount > 0 and playerData.money >= amount then
        -- Remove from sender
        playerData.money = playerData.money - amount
        exports['olympus-core']:UpdatePlayerData(source, 'money', playerData.money)
        
        -- Add to receiver
        targetData.money = targetData.money + amount
        exports['olympus-core']:UpdatePlayerData(targetId, 'money', targetData.money)
        
        -- Log transactions
        exports['olympus-core']:LogAction(source, 'money_transfer_send', {
            amount = amount,
            target = targetData.name,
            reason = reason or 'Transfer'
        })
        
        exports['olympus-core']:LogAction(targetId, 'money_transfer_receive', {
            amount = amount,
            sender = playerData.name,
            reason = reason or 'Transfer'
        })
        
        -- Notify both players
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'success',
            title = 'Money Sent',
            message = 'You sent $' .. amount .. ' to ' .. targetData.name
        })
        
        TriggerClientEvent('olympus:client:notify', targetId, {
            type = 'success',
            title = 'Money Received',
            message = 'You received $' .. amount .. ' from ' .. playerData.name
        })
    end
end)

-- Job management
RegisterServerEvent('olympus:server:setJob')
AddEventHandler('olympus:server:setJob', function(job, grade)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)
    
    if playerData and Config.Jobs[job] then
        local oldJob = playerData.job
        playerData.job = job
        playerData.job_grade = grade or 0
        
        exports['olympus-core']:UpdatePlayerData(source, 'job', job)
        exports['olympus-core']:UpdatePlayerData(source, 'job_grade', grade or 0)
        
        -- Log job change
        exports['olympus-core']:LogAction(source, 'job_change', {
            oldJob = oldJob,
            newJob = job,
            grade = grade or 0
        })
        
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'info',
            title = 'Job Changed',
            message = 'Your job is now: ' .. Config.Jobs[job].name
        })
        
        print("^3[Olympus Core]^7 " .. playerData.name .. " job changed to " .. job)
    end
end)

-- Faction management
RegisterServerEvent('olympus:server:setFaction')
AddEventHandler('olympus:server:setFaction', function(faction, rank)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)
    
    if playerData and Config.Factions[faction] then
        local oldFaction = playerData.faction
        playerData.faction = faction
        playerData.faction_rank = rank or 1
        
        exports['olympus-core']:UpdatePlayerData(source, 'faction', faction)
        exports['olympus-core']:UpdatePlayerData(source, 'faction_rank', rank or 1)
        
        -- Log faction change
        exports['olympus-core']:LogAction(source, 'faction_change', {
            oldFaction = oldFaction,
            newFaction = faction,
            rank = rank or 1
        })
        
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'info',
            title = 'Faction Changed',
            message = 'You joined: ' .. Config.Factions[faction].name
        })
        
        -- Trigger faction-specific events
        if faction == 'apd' then
            TriggerEvent('olympus:apd:playerJoined', source)
        elseif faction == 'rnr' then
            TriggerEvent('olympus:medical:playerJoined', source)
        elseif oldFaction == 'apd' then
            TriggerEvent('olympus:apd:playerLeft', source)
        elseif oldFaction == 'rnr' then
            TriggerEvent('olympus:medical:playerLeft', source)
        end
        
        print("^3[Olympus Core]^7 " .. playerData.name .. " faction changed to " .. faction)
    end
end)

-- License management
RegisterServerEvent('olympus:server:addLicense')
AddEventHandler('olympus:server:addLicense', function(license)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)
    
    if playerData and Config.Licenses[license] then
        if not playerData.licenses then
            playerData.licenses = {}
        end
        
        playerData.licenses[license] = true
        exports['olympus-core']:UpdatePlayerData(source, 'licenses', playerData.licenses)
        
        -- Log license addition
        exports['olympus-core']:LogAction(source, 'license_add', {
            license = license,
            name = Config.Licenses[license].name
        })
        
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'success',
            title = 'License Obtained',
            message = 'You obtained: ' .. Config.Licenses[license].name
        })
    end
end)

RegisterServerEvent('olympus:server:removeLicense')
AddEventHandler('olympus:server:removeLicense', function(license)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)
    
    if playerData and playerData.licenses and playerData.licenses[license] then
        playerData.licenses[license] = nil
        exports['olympus-core']:UpdatePlayerData(source, 'licenses', playerData.licenses)
        
        -- Log license removal
        exports['olympus-core']:LogAction(source, 'license_remove', {
            license = license,
            name = Config.Licenses[license] and Config.Licenses[license].name or license
        })
        
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'warning',
            title = 'License Revoked',
            message = 'Your license was revoked: ' .. (Config.Licenses[license] and Config.Licenses[license].name or license)
        })
    end
end)

-- Playtime tracking
CreateThread(function()
    while true do
        Wait(60000) -- Update every minute
        
        for source, player in pairs(exports['olympus-core']:GetAllPlayers()) do
            if player and player.data then
                player.data.playtime = (player.data.playtime or 0) + 1
                exports['olympus-core']:UpdatePlayerData(source, 'playtime', player.data.playtime)
            end
        end
    end
end)

-- Server callbacks for player data
exports['olympus-core']:RegisterServerCallback('olympus:getPlayerData', function(source, cb)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    cb(playerData)
end)

exports['olympus-core']:RegisterServerCallback('olympus:getPlayerMoney', function(source, cb)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    cb(playerData and playerData.money or 0)
end)

exports['olympus-core']:RegisterServerCallback('olympus:getPlayerBank', function(source, cb)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    cb(playerData and playerData.bank or 0)
end)

exports['olympus-core']:RegisterServerCallback('olympus:hasLicense', function(source, cb, license)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    cb(playerData and playerData.licenses and playerData.licenses[license] or false)
end)
