-- Olympus Casino System - Complete Implementation
-- Based on Olympus Altis Life casino mechanics with exact specifications

Config = {}

-- Casino System Settings
Config.CasinoSystem = {
    enabled = true,
    
    -- System Requirements
    requirements = {
        minimumPlaytime = 36000, -- 10 hours minimum playtime (legal gambling age)
        idCheckRequired = true, -- Dealers check ID before allowing play
        
        -- Restrictions
        restrictions = {
            closeBeforeRestart = 60, -- Close 1 minute before server restart
            preventMoneyLoss = true, -- Prevent loss during restart
            
            -- Self-Exclusion System
            selfExclusion = {
                enabled = true,
                durations = {
                    '2_weeks', '1_month', '2_months', '3_months', 
                    '6_months', '1_year', 'permanent'
                },
                confirmationText = 'DEGENERATE', -- Must type this to confirm
                irreversible = true, -- Cannot be reversed
                statsPageIntegration = true -- Managed via stats page
            }
        }
    },
    
    -- Casino Locations (Exact Olympus)
    locations = {
        kavala = {
            name = 'Kavala Square Casino',
            entrance = vector3(1174.0, 2640.0, 38.0), -- Kavala Square
            
            -- Interior Layout
            interior = {
                slots = {
                    floor = 1, -- First floor
                    machines = 8, -- Two rows of slot machines
                    positions = {
                        vector3(1170.0, 2635.0, 38.0),
                        vector3(1172.0, 2635.0, 38.0),
                        vector3(1174.0, 2635.0, 38.0),
                        vector3(1176.0, 2635.0, 38.0),
                        vector3(1170.0, 2645.0, 38.0),
                        vector3(1172.0, 2645.0, 38.0),
                        vector3(1174.0, 2645.0, 38.0),
                        vector3(1176.0, 2645.0, 38.0)
                    }
                },
                
                blackjack = {
                    floor = 1, -- First floor
                    tables = 3, -- Three blackjack tables
                    positions = {
                        vector3(1180.0, 2640.0, 38.0),
                        vector3(1182.0, 2640.0, 38.0),
                        vector3(1184.0, 2640.0, 38.0)
                    }
                },
                
                roulette = {
                    floor = 2, -- Second floor (upstairs)
                    tables = 1, -- One roulette table
                    position = vector3(1177.0, 2640.0, 42.0)
                }
            }
        },
        
        pyrgos = {
            name = 'Pyrgos Boardwalk Casino',
            entrance = vector3(3003.0, 3310.0, 12.0), -- Pyrgos Boardwalk
            
            -- Same interior layout as Kavala
            interior = {
                slots = {
                    floor = 1,
                    machines = 8,
                    positions = {
                        vector3(2998.0, 3305.0, 12.0),
                        vector3(3000.0, 3305.0, 12.0),
                        vector3(3002.0, 3305.0, 12.0),
                        vector3(3004.0, 3305.0, 12.0),
                        vector3(2998.0, 3315.0, 12.0),
                        vector3(3000.0, 3315.0, 12.0),
                        vector3(3002.0, 3315.0, 12.0),
                        vector3(3004.0, 3315.0, 12.0)
                    }
                },
                
                blackjack = {
                    floor = 1,
                    tables = 3,
                    positions = {
                        vector3(3008.0, 3310.0, 12.0),
                        vector3(3010.0, 3310.0, 12.0),
                        vector3(3012.0, 3310.0, 12.0)
                    }
                },
                
                roulette = {
                    floor = 2,
                    tables = 1,
                    position = vector3(3005.0, 3310.0, 16.0)
                }
            }
        }
    },
    
    -- Casino Management NPCs
    management = {
        enabled = true,
        
        -- NPC Positions
        positions = {
            kavala = vector3(1174.0, 2638.0, 38.0), -- Outside Kavala casino
            pyrgos = vector3(3003.0, 3308.0, 12.0) -- Outside Pyrgos casino
        },
        
        -- NPC Functions
        functions = {
            allowEntry = true, -- Allow guests to enter
            assistance = true, -- Assist guests
            idCheck = true, -- Check player ID for age verification
            
            -- Dialogue
            dialogue = {
                welcome = "Welcome to Altis Casino! May I see your ID please?",
                underAge = "Sorry, you must have 10 hours of playtime to gamble.",
                restricted = "I'm sorry, but you've restricted yourself from the casino.",
                assistance = "How may I assist you today?"
            }
        }
    }
}

-- Slot Machine System (Exact Olympus Implementation)
Config.SlotMachines = {
    enabled = true,
    
    -- Betting System
    betting = {
        minBet = 10000, -- $10,000 minimum bet
        maxBet = 100000, -- $100,000 maximum bet
        
        -- Bet Increments
        increments = {10000, 25000, 50000, 75000, 100000},
        
        -- Payment Method
        paymentMethod = 'bank_account' -- Must pay from bank account
    },
    
    -- Game Mechanics
    mechanics = {
        type = 'one_armed_bandit', -- Classic slot machine style
        
        -- Reel System
        reels = {
            count = 3, -- 3 reels
            symbols = {
                'cherry', 'lemon', 'orange', 'plum', 
                'bell', 'bar', 'seven', 'diamond'
            },
            
            -- Symbol Weights (rarity)
            weights = {
                cherry = 20,
                lemon = 18,
                orange = 16,
                plum = 14,
                bell = 12,
                bar = 10,
                seven = 6,
                diamond = 4
            }
        },
        
        -- Win Conditions
        winConditions = {
            threeMatch = {
                requirement = 'all_three_symbols_match',
                payout = 2.0, -- Double the initial wager
                
                -- Special Payouts
                specialPayouts = {
                    three_sevens = 5.0, -- 5x for three sevens
                    three_diamonds = 10.0 -- 10x for three diamonds
                }
            }
        },
        
        -- Animation System
        animation = {
            enabled = true,
            spinDuration = 3000, -- 3 seconds spin animation
            reelStopDelay = 500, -- 0.5 seconds between reel stops
            winCelebration = true -- Special animation for wins
        }
    },
    
    -- User Interface
    interface = {
        style = 'classic', -- Classic slot machine GUI
        
        -- Display Elements
        elements = {
            betAmount = true, -- Show current bet amount
            balance = true, -- Show player balance
            lastWin = true, -- Show last win amount
            payoutTable = true, -- Show payout table
            
            -- Controls
            controls = {
                betUp = 'Increase Bet',
                betDown = 'Decrease Bet',
                maxBet = 'Max Bet',
                spin = 'Pull Lever'
            }
        }
    }
}

-- Roulette System (Exact Olympus Implementation)
Config.RouletteSystem = {
    enabled = true,
    
    -- Betting System
    betting = {
        minBet = 100000, -- $100,000 minimum bet
        maxBet = 10000000, -- $10,000,000 maximum bet
        
        -- Bet Types
        betTypes = {
            red = {
                name = 'Red',
                payout = 2.0, -- Double the initial wager
                probability = 0.47, -- Slightly less than 50% (house edge)
                
                -- Red Numbers
                numbers = {1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36}
            },
            
            black = {
                name = 'Black',
                payout = 2.0, -- Double the initial wager
                probability = 0.47, -- Slightly less than 50% (house edge)
                
                -- Black Numbers
                numbers = {2, 4, 6, 8, 10, 11, 13, 15, 17, 20, 22, 24, 26, 28, 29, 31, 33, 35}
            },
            
            green = {
                name = 'Green',
                payout = 14.0, -- 14x multiplier (exact Olympus)
                probability = 0.06, -- Much lower probability
                
                -- Green Numbers (0 and 00)
                numbers = {0, 37} -- 0 at top, 00 at bottom of wheel
            }
        }
    },
    
    -- Wheel Mechanics
    wheel = {
        type = 'american_roulette', -- American style with 0 and 00
        
        -- Wheel Layout
        layout = {
            totalSlots = 38, -- 36 numbers + 0 + 00
            greenSlots = 2, -- 0 and 00 (top and bottom)
            redSlots = 18, -- 18 red numbers
            blackSlots = 18, -- 18 black numbers
            
            -- Physical Layout
            physicalLayout = {
                greenTop = 0, -- Green 0 at top
                greenBottom = 37, -- Green 00 at bottom (represented as 37)
                evenDistribution = true -- Red and black evenly distributed
            }
        },
        
        -- Spin Animation
        animation = {
            enabled = true,
            spinDuration = 5000, -- 5 seconds spin
            ballBounce = true, -- Ball bounces before settling
            resultDelay = 1000 -- 1 second delay before showing result
        }
    },
    
    -- Game Interface
    interface = {
        style = 'classic_roulette', -- Classic roulette table
        
        -- Betting Interface
        bettingInterface = {
            showWheel = true, -- Show roulette wheel
            showTable = true, -- Show betting table
            
            -- Bet Placement
            betPlacement = {
                redButton = 'Bet on Red (2x payout)',
                blackButton = 'Bet on Black (2x payout)',
                greenButton = 'Bet on Green (14x payout)',
                
                -- Visual Feedback
                visualFeedback = {
                    highlightSelection = true,
                    showPotentialWin = true,
                    confirmBet = true
                }
            }
        },
        
        -- Croupier System
        croupier = {
            enabled = true,
            
            -- Croupier Dialogue
            dialogue = {
                welcome = "Welcome to the roulette table! Place your bets!",
                noMoreBets = "No more bets, please!",
                winner = "We have a winner! %s pays %s!",
                houseWins = "House wins! Better luck next time!",
                warning = "Many have gambled and lost their fortunes at this table."
            }
        }
    }
}

-- Blackjack System (Exact Olympus Implementation)
Config.BlackjackSystem = {
    enabled = true,
    
    -- Betting System
    betting = {
        minBet = 100000, -- $100,000 minimum bet
        maxBet = 10000000, -- $10,000,000 maximum bet
        
        -- Special Bets
        specialBets = {
            doubleDown = {
                enabled = true,
                conditions = {
                    firstHand = true, -- Always available on first hand
                    specificCards = {9, 10, 11}, -- Available when first card is 9, 10, or 11
                    doubleWager = true -- Must double the wager
                }
            }
        }
    },
    
    -- Game Rules (Standard Blackjack)
    rules = {
        objective = 21, -- Get as close to 21 as possible
        
        -- Card Values
        cardValues = {
            ace = {1, 11}, -- Ace can be 1 or 11
            face = 10, -- Jack, Queen, King = 10
            number = 'face_value' -- Number cards = face value
        },
        
        -- Dealer Rules
        dealer = {
            hitOn = 16, -- Dealer hits on 16 or below
            standOn = 17, -- Dealer stands on 17 or above
            
            -- Soft 17 Rule (Exact Olympus)
            soft17 = {
                enabled = true,
                dealerHits = true -- Dealer hits on soft 17 (Ace + 6)
            }
        },
        
        -- Natural Blackjack
        naturalBlackjack = {
            enabled = true,
            requirement = 'two_cards_equal_21', -- First two cards = 21
            payout = 1.5, -- 1.5x the initial wager
            beatsDealer21 = true -- Natural blackjack beats dealer 21
        },
        
        -- House Edge
        houseEdge = {
            enabled = true,
            rigged = true, -- "Generally rigged like an actual casino"
            advantage = 0.05 -- 5% house advantage
        }
    },
    
    -- Game Flow
    gameFlow = {
        -- Initial Deal
        initialDeal = {
            playerCards = 2, -- Player gets 2 cards
            dealerCards = 2, -- Dealer gets 2 cards
            dealerHoleCard = true, -- One dealer card face down
            
            -- Natural Blackjack Check
            naturalCheck = {
                enabled = true,
                immediateWin = true, -- Immediate win if natural blackjack
                dealerPeek = true -- Dealer peeks for blackjack
            }
        },
        
        -- Player Actions
        playerActions = {
            hit = {
                enabled = true,
                unlimited = true, -- Can hit multiple times
                bustAt = 22 -- Bust if over 21
            },
            
            stand = {
                enabled = true,
                endTurn = true -- Ends player turn
            },
            
            doubleDown = {
                enabled = true,
                oneCardOnly = true, -- Only one additional card
                doubleWager = true, -- Double the wager
                endTurn = true -- Ends turn after one card
            },
            
            split = {
                enabled = false -- Not implemented in Olympus
            }
        },
        
        -- Dealer Play
        dealerPlay = {
            revealHoleCard = true, -- Reveal face-down card
            followRules = true, -- Follow dealer hit/stand rules
            
            -- Dealer Bust
            dealerBust = {
                playerWins = true, -- Player wins if dealer busts
                payout = 1.0 -- 1:1 payout
            }
        }
    },
    
    -- User Interface
    interface = {
        style = 'classic_blackjack', -- Classic blackjack table
        
        -- Table Layout
        tableLayout = {
            dealerArea = true, -- Dealer card area
            playerArea = true, -- Player card area
            bettingCircle = true, -- Betting circle
            
            -- Action Buttons
            actionButtons = {
                hit = 'Hit',
                stand = 'Stand',
                doubleDown = 'Double Down',
                newGame = 'New Game'
            }
        },
        
        -- Dealer System
        dealer = {
            enabled = true,
            
            -- Dealer Dialogue
            dialogue = {
                welcome = "Welcome to blackjack! Place your bet to begin.",
                dealCards = "Dealing cards...",
                playerTurn = "Your turn! Hit or stand?",
                dealerTurn = "Dealer's turn...",
                playerWins = "Congratulations! You win!",
                dealerWins = "Dealer wins! Better luck next time!",
                push = "Push! It's a tie!",
                bust = "Bust! You went over 21!"
            }
        }
    }
}
