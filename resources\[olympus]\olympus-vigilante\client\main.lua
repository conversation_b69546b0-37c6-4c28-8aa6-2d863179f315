-- ============================================
-- OLYMPUS VIGILANTE SYSTEM - CLIENT MAIN
-- Based on original vigilante functions and mechanics
-- ============================================

local OlympusVigilante = {}

-- ============================================
-- CLIENT STATE VARIABLES
-- ============================================

local vigilanteArrests = 0
local vigilanteArrestsStored = 0
local vigilanteTier = 1
local vigilanteBuddy = nil
local isVigilante = false

-- ============================================
-- CLIENT INITIALIZATION
-- ============================================

CreateThread(function()
    while not exports['olympus-core'] do
        Wait(100)
    end

    -- Initialize vigilante system
    TriggerServerEvent('olympus-vigilante:playerLoaded')

    -- Check if player has vigilante license
    CreateThread(function()
        while true do
            Wait(5000) -- Check every 5 seconds

            local hasLicense = false
            local success, result = pcall(function()
                return exports['olympus-licenses']:HasLicense('vigilante')
            end)

            if success then
                hasLicense = result
            end
            if hasLicense ~= isVigilante then
                isVigilante = hasLicense

                if isVigilante then
                    OlympusVigilante.ShowVigilanteRules()
                end
            end
        end
    end)

    print("[Olympus Vigilante] Client initialized")
end)

-- ============================================
-- VIGILANTE RULES DISPLAY
-- Based on original fn_vigiNotify.sqf
-- ============================================

function OlympusVigilante.ShowVigilanteRules()
    -- Show vigilante rules notification (simplified version)
    exports['olympus-core']:ShowNotification({
        title = "Vigilante License Activated",
        message = "You now have access to vigilante equipment and can arrest wanted players with $75,000+ bounties. Check the forums for complete rules.",
        type = "success",
        duration = 10000
    })
end

-- ============================================
-- ARREST SYSTEM
-- ============================================

function OlympusVigilante.CanArrestPlayer(targetId)
    if not isVigilante then
        exports['olympus-core']:ShowNotification({
            title = "Access Denied",
            message = "You need a vigilante license to arrest players!",
            type = "error"
        })
        return false
    end

    -- Check if target is wanted
    local targetBounty = exports['olympus-apd']:GetPlayerBounty(targetId)
    if not targetBounty or targetBounty < 75000 then
        exports['olympus-core']:ShowNotification({
            title = "Invalid Target",
            message = "Target must have a bounty of at least $75,000!",
            type = "error"
        })
        return false
    end

    return true
end

function OlympusVigilante.ArrestPlayer(targetId)
    if not OlympusVigilante.CanArrestPlayer(targetId) then
        return false
    end

    local targetBounty = exports['olympus-apd']:GetPlayerBounty(targetId)

    -- Process the arrest
    TriggerServerEvent('olympus-vigilante:processBounty', targetId, targetBounty, 'arrest')

    -- Send target to jail
    TriggerServerEvent('olympus-apd:jailPlayer', targetId, 'vigilante_arrest')

    return true
end

-- ============================================
-- EQUIPMENT SYSTEM
-- ============================================

function OlympusVigilante.GetVigilanteLoadout()
    if not isVigilante then return {} end

    local loadout = {}

    -- Base equipment (Tier 1+)
    table.insert(loadout, {weapon = "WEAPON_PISTOL", ammo = 100}) -- P07

    -- Tier 2+ equipment
    if vigilanteTier >= 2 then
        table.insert(loadout, {weapon = "WEAPON_COMBATPISTOL", ammo = 100}) -- ACP-C2
        table.insert(loadout, {weapon = "WEAPON_STUNGUN", ammo = 10}) -- Sting
    end

    -- Tier 3+ equipment
    if vigilanteTier >= 3 then
        table.insert(loadout, {armor = 50}) -- T3 Vest
    end

    -- Tier 4+ equipment
    if vigilanteTier >= 4 then
        table.insert(loadout, {weapon = "WEAPON_CARBINERIFLE", ammo = 200}) -- SPAR16
        table.insert(loadout, {weapon = "WEAPON_CARBINERIFLE_MK2", ammo = 200}) -- SPAR16-GL
    end

    -- Tier 5+ equipment
    if vigilanteTier >= 5 then
        table.insert(loadout, {weapon = "WEAPON_SPECIALCARBINE", ammo = 200}) -- SPAR16S
    end

    return loadout
end

function OlympusVigilante.GiveVigilanteLoadout()
    if not isVigilante then
        exports['olympus-core']:ShowNotification({
            title = "Access Denied",
            message = "You need a vigilante license to access equipment!",
            type = "error"
        })
        return false
    end

    local loadout = OlympusVigilante.GetVigilanteLoadout()

    -- Remove all weapons first
    RemoveAllPedWeapons(PlayerPedId(), true)

    -- Give weapons and equipment
    for _, item in pairs(loadout) do
        if item.weapon then
            GiveWeaponToPed(PlayerPedId(), GetHashKey(item.weapon), item.ammo, false, true)
        elseif item.armor then
            SetPedArmour(PlayerPedId(), item.armor)
        end
    end

    exports['olympus-core']:ShowNotification({
        title = "Equipment Received",
        message = string.format("Vigilante Tier %d equipment loaded", vigilanteTier),
        type = "success"
    })

    return true
end

-- ============================================
-- BUDDY SYSTEM
-- ============================================

function OlympusVigilante.SendBuddyRequest(targetId)
    if not isVigilante then
        exports['olympus-core']:ShowNotification({
            title = "Access Denied",
            message = "You need a vigilante license to use the buddy system!",
            type = "error"
        })
        return false
    end

    TriggerServerEvent('olympus-vigilante:sendBuddyRequest', targetId)
    return true
end

function OlympusVigilante.EndBuddyAgreement()
    if not vigilanteBuddy then
        exports['olympus-core']:ShowNotification({
            title = "No Buddy",
            message = "You do not have a buddy agreement!",
            type = "error"
        })
        return false
    end

    TriggerServerEvent('olympus-vigilante:endBuddyAgreement')
    return true
end

-- ============================================
-- ARREST STORAGE SYSTEM
-- ============================================

function OlympusVigilante.StoreArrests()
    if vigilanteArrests == 0 then
        exports['olympus-core']:ShowNotification({
            title = "No Arrests",
            message = "You do not have any arrests to store!",
            type = "error"
        })
        return false
    end

    TriggerServerEvent('olympus-vigilante:storeArrests')
    return true
end

function OlympusVigilante.ClaimStoredArrests()
    if vigilanteArrestsStored == 0 then
        exports['olympus-core']:ShowNotification({
            title = "No Stored Arrests",
            message = "You do not have any stored arrests to claim!",
            type = "error"
        })
        return false
    end

    TriggerServerEvent('olympus-vigilante:claimStoredArrests')
    return true
end

-- ============================================
-- EVENT HANDLERS
-- ============================================

-- Arrest count updates
RegisterNetEvent('olympus-vigilante:setArrests')
AddEventHandler('olympus-vigilante:setArrests', function(arrests)
    vigilanteArrests = arrests
    vigilanteTier = math.max(1, math.floor((arrests / 25) + 1))
    if vigilanteTier > 5 then vigilanteTier = 5 end
end)

RegisterNetEvent('olympus-vigilante:setStoredArrests')
AddEventHandler('olympus-vigilante:setStoredArrests', function(arrests)
    vigilanteArrestsStored = arrests
end)

-- Tier progression notification
RegisterNetEvent('olympus-vigilante:tierProgression')
AddEventHandler('olympus-vigilante:tierProgression', function(tier, arrests)
    vigilanteTier = tier

    exports['olympus-core']:ShowNotification({
        title = string.format("Vigilante Tier %d Unlocked!", tier),
        message = string.format("Congratulations! You now have %d arrests and access to Tier %d equipment.", arrests, tier),
        type = "success",
        duration = 10000
    })
end)

-- Buddy system events
RegisterNetEvent('olympus-vigilante:receiveBuddyRequest')
AddEventHandler('olympus-vigilante:receiveBuddyRequest', function(requesterId, requesterName)
    exports['olympus-core']:ShowDialog({
        title = "Vigilante Buddy Request",
        message = string.format("%s has invited you to partner up. Would you like to accept the buddy agreement?", requesterName),
        buttons = {
            {
                text = "Accept",
                action = function()
                    TriggerServerEvent('olympus-vigilante:acceptBuddyRequest', requesterId)
                end
            },
            {
                text = "Decline",
                action = function()
                    TriggerServerEvent('olympus-vigilante:declineBuddyRequest', requesterId)
                end
            }
        }
    })
end)

-- ============================================
-- COMMANDS
-- ============================================

-- Vigilante equipment command
RegisterCommand('vigiequip', function()
    OlympusVigilante.GiveVigilanteLoadout()
end, false)

-- Vigilante stats command
RegisterCommand('vigistats', function()
    if not isVigilante then
        exports['olympus-core']:ShowNotification({
            title = "Access Denied",
            message = "You need a vigilante license to view stats!",
            type = "error"
        })
        return
    end

    exports['olympus-core']:ShowNotification({
        title = "Vigilante Statistics",
        message = string.format("Arrests: %d\nStored Arrests: %d\nTier: %d\nBuddy: %s",
            vigilanteArrests,
            vigilanteArrestsStored,
            vigilanteTier,
            vigilanteBuddy and "Active" or "None"
        ),
        type = "info",
        duration = 8000
    })
end, false)

-- Buddy request command
RegisterCommand('vigibuddy', function(source, args)
    if #args < 1 then
        exports['olympus-core']:ShowNotification({
            title = "Usage",
            message = "/vigibuddy [player_id]",
            type = "info"
        })
        return
    end

    local targetId = tonumber(args[1])
    if not targetId then
        exports['olympus-core']:ShowNotification({
            title = "Invalid ID",
            message = "Please provide a valid player ID",
            type = "error"
        })
        return
    end

    OlympusVigilante.SendBuddyRequest(targetId)
end, false)

-- End buddy agreement command
RegisterCommand('vigiendbuddy', function()
    OlympusVigilante.EndBuddyAgreement()
end, false)

-- Store arrests command
RegisterCommand('vigistore', function()
    OlympusVigilante.StoreArrests()
end, false)

-- Claim stored arrests command
RegisterCommand('vigiclaim', function()
    OlympusVigilante.ClaimStoredArrests()
end, false)

-- ============================================
-- EXPORT FUNCTIONS
-- ============================================

exports('IsPlayerVigilante', function()
    return isVigilante
end)

exports('GetVigilanteRank', function()
    return vigilanteTier
end)

exports('GetVigilanteArrests', function()
    return vigilanteArrests
end)

exports('GetStoredArrests', function()
    return vigilanteArrestsStored
end)

exports('CanArrestPlayer', function(targetId)
    return OlympusVigilante.CanArrestPlayer(targetId)
end)

exports('ArrestPlayer', function(targetId)
    return OlympusVigilante.ArrestPlayer(targetId)
end)

exports('GetVigilanteLoadout', function()
    return OlympusVigilante.GetVigilanteLoadout()
end)

exports('GiveVigilanteLoadout', function()
    return OlympusVigilante.GiveVigilanteLoadout()
end)

exports('SendBuddyRequest', function(targetId)
    return OlympusVigilante.SendBuddyRequest(targetId)
end)

exports('EndBuddyAgreement', function()
    return OlympusVigilante.EndBuddyAgreement()
end)

exports('StoreArrests', function()
    return OlympusVigilante.StoreArrests()
end)

exports('ClaimStoredArrests', function()
    return OlympusVigilante.ClaimStoredArrests()
end)

print("[Olympus Vigilante] Client system loaded - Complete implementation based on original Olympus functions")
