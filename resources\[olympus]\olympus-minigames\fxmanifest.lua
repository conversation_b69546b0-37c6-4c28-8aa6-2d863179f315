fx_version 'cerulean'
game 'gta5'

name 'Olympus Mini-Games'
description 'Skill-based mini-games for lockpicking, hacking, and crafting'
author 'Olympus Development Team'
version '1.0.0'

-- Dependencies
dependencies {
    'olympus-core',
    'olympus-ui'
}

-- Shared scripts
shared_scripts {
    'config/shared.lua'
}

-- Client scripts
client_scripts {
    'client/main.lua',
    'client/lockpicking.lua',
    'client/hacking.lua',
    'client/safe_cracking.lua',
    'client/evidence_system.lua',
    'client/crafting.lua',
    'client/signal_jammers.lua'
}

-- Server scripts
server_scripts {
    'server/main.lua',
    'server/minigame_validation.lua',
    'server/skill_progression.lua',
    'server/crafting_system.lua'
}

-- UI files
ui_page 'html/index.html'

files {
    'html/index.html',
    'html/css/*.css',
    'html/js/*.js',
    'html/sounds/*.ogg'
}

-- Exports
exports {
    'StartLockpicking',
    'StartHacking',
    'StartSafeCracking',
    'StartCrafting'
}

server_exports {
    'ValidateMinigame',
    'UpdateSkillLevel',
    'ProcessCrafting'
}
