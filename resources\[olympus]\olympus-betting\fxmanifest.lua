fx_version 'cerulean'
game 'gta5'

name 'Olympus Player Betting System'
description 'Player betting system based on original fn_betMoney.sqf from Olympus Altis Life'
author 'Olympus Development Team'
version '1.0.0'

-- Dependencies
dependencies {
    'olympus-core'
}

-- Shared scripts
shared_scripts {
    'config/shared.lua'
}

-- Client scripts
client_scripts {
    'client/main.lua'
}

-- Server scripts
server_scripts {
    'server/main.lua'
}

-- UI files
ui_page 'html/index.html'

files {
    'html/index.html',
    'html/style.css',
    'html/script.js'
}

-- Exports
exports {
    'BetMoney',
    'CanPlayerBet',
    'IsPlayerInBet',
    'GetBetCooldown'
}

server_exports {
    'CanPlayerBet',
    'IsPlayerInBet',
    'GetBetCooldown',
    'GetPlayerBettingHistory'
}
