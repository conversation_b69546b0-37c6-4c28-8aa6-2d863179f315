# Olympus Loading Screen

A professional, animated loading screen for the Olympus Altis Life FiveM framework.

## Features

- **Authentic Olympus Branding**: Matches the official Olympus Altis Life aesthetic
- **Real-time Progress Tracking**: Shows actual framework loading progress
- **Animated Elements**: Floating particles, progress bars, and smooth transitions
- **Server Information**: Displays player count, server status, and uptime
- **Rotating Tips**: Helpful server tips that change during loading
- **Responsive Design**: Works on all screen resolutions
- **Integration**: Seamlessly integrates with the Olympus framework loader

## Installation

1. The resource is already included in the Olympus framework
2. Ensure it's added to your `server.cfg`:
   ```
   ensure olympus-loadscreen
   ```
3. The resource should load automatically with the framework

## Customization

### Images
- Replace `html/images/logo.png` with your server logo
- Replace `html/images/background.jpg` with your preferred background
- Optimize images for web use to ensure fast loading

### Colors and Styling
- Edit `html/css/style.css` to modify colors, fonts, and animations
- The color scheme uses CSS variables for easy customization
- Main colors: `#4a90e2` (blue), `#5cb85c` (green), `#1a1a2e` (dark)

### Server Tips
- Modify the `serverTips` array in `html/js/script.js`
- Add your own server-specific tips and information
- Tips rotate every 4 seconds during loading

### Social Links
- Update the social links in `html/index.html`
- Replace placeholder URLs with your actual Discord, website, etc.

## Integration with Framework

The loading screen automatically integrates with the Olympus framework loader:

1. **Progress Updates**: Receives real-time loading progress from `olympus-loader`
2. **Step Tracking**: Shows current loading step (Core, Database, UI, etc.)
3. **Completion**: Automatically hides when framework is fully loaded
4. **Error Handling**: Shows appropriate messages if loading fails

## Technical Details

### Files Structure
```
olympus-loadscreen/
├── fxmanifest.lua          # Resource manifest
├── html/
│   ├── index.html          # Main HTML structure
│   ├── css/style.css       # Styling and animations
│   ├── js/script.js        # JavaScript functionality
│   └── images/             # Logo and background images
└── README.md               # This file
```

### Loading Events
The loading screen listens for standard FiveM loading events:
- `startInitFunctionOrder`
- `initFunctionInvoking`
- `startDataFileEntries`
- `performMapLoadFunction`
- `onLogLine`

### Performance
- Optimized CSS animations using GPU acceleration
- Efficient particle system with automatic cleanup
- Minimal JavaScript footprint
- Compressed assets for faster loading

## Troubleshooting

### Loading Screen Not Showing
1. Check that `olympus-loadscreen` is in your `server.cfg`
2. Verify the resource is starting without errors
3. Check browser console (F12) for JavaScript errors

### Progress Not Updating
1. Ensure `olympus-loader` is running properly
2. Check server console for framework loading errors
3. Verify all Olympus resources are starting correctly

### Styling Issues
1. Clear browser cache (Ctrl+F5)
2. Check CSS file for syntax errors
3. Verify image paths are correct

## Support

For support with the Olympus loading screen:
1. Check the server console for error messages
2. Review the framework documentation
3. Contact the Olympus development team

---

**Note**: This loading screen is designed specifically for the Olympus Altis Life framework and may require modifications for use with other frameworks.
