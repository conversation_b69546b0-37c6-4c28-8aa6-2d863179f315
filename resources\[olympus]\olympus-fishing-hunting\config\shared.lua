Config = {}

-- Fishing System Configuration
Config.Fishing = {
    -- Fishing zones (areas where fish spawn)
    zones = {
        {
            name = "Kavala Bay",
            center = vector3(-2077.0, -1020.0, 8.97),
            radius = 500.0,
            fishTypes = {"salema", "ornate", "mackerel", "tuna", "mullet", "catshark"}
        },
        {
            name = "Pyrgos Coast",
            center = vector3(2500.0, -1500.0, 8.97),
            radius = 400.0,
            fishTypes = {"salema", "mackerel", "tuna", "mullet"}
        },
        {
            name = "Athira Waters",
            center = vector3(1500.0, 2000.0, 8.97),
            radius = 300.0,
            fishTypes = {"ornate", "catshark", "tuna"}
        }
    },
    
    -- Fish types and their properties
    fishTypes = {
        salema = {
            name = "Salema",
            sellPrice = 45,
            rarity = 0.4, -- Higher = more common
            minDepth = 5.0,
            maxDepth = 50.0
        },
        ornate = {
            name = "Ornate",
            sellPrice = 40,
            rarity = 0.35,
            minDepth = 10.0,
            maxDepth = 60.0
        },
        mackerel = {
            name = "<PERSON>ere<PERSON>",
            sellPrice = 175,
            rarity = 0.25,
            minDepth = 15.0,
            maxDepth = 80.0
        },
        tuna = {
            name = "Tuna",
            sellPrice = 700,
            rarity = 0.15,
            minDepth = 20.0,
            maxDepth = 100.0
        },
        mullet = {
            name = "Mullet",
            sellPrice = 250,
            rarity = 0.2,
            minDepth = 8.0,
            maxDepth = 40.0
        },
        catshark = {
            name = "Catshark",
            sellPrice = 300,
            rarity = 0.1,
            minDepth = 25.0,
            maxDepth = 120.0
        }
    },
    
    -- Fishing net configuration
    fishingNet = {
        enabled = true,
        cooldown = 30, -- seconds between net uses
        maxDistance = 20.0, -- max distance to catch fish with net
        netDuration = 5.0, -- seconds to deploy net
        requiredVehicle = "boat" -- must be in boat to use net
    },
    
    -- Fishing requirements
    requirements = {
        minWaterDepth = 5.0,
        maxDistanceFromShore = 1000.0,
        fishingLicense = false, -- No license required for fishing
        requiredItems = {} -- No special items required
    }
}

-- Turtle Poaching Configuration
Config.TurtlePoaching = {
    -- Turtle poaching zones
    zones = {
        {
            name = "turtle_one",
            center = vector3(-1500.0, -800.0, 8.97),
            radius = 230.0,
            blipColor = 1,
            blipSprite = 141
        },
        {
            name = "turtle_two", 
            center = vector3(2200.0, -1200.0, 8.97),
            radius = 230.0,
            blipColor = 1,
            blipSprite = 141
        }
    },
    
    -- Turtle properties
    turtle = {
        name = "Turtle",
        sellPrice = 3000,
        spawnRate = 0.3, -- chance per minute in zone
        maxTurtles = 5, -- max turtles per zone
        despawnTime = 300 -- seconds before turtle despawns
    },
    
    -- Requirements
    requirements = {
        mustBeInZone = true,
        turtleMustBeDead = true,
        maxDistance = 3.5
    }
}

-- Hunting System Configuration  
Config.Hunting = {
    -- Hunting zones
    zones = {
        {
            name = "Forest Hunting Area",
            center = vector3(0.0, 1000.0, 100.0),
            radius = 800.0,
            animalTypes = {"rabbit", "hen", "rooster", "goat", "sheep"}
        },
        {
            name = "Mountain Hunting Area", 
            center = vector3(-1000.0, 2000.0, 200.0),
            radius = 600.0,
            animalTypes = {"goat", "sheep", "snake"}
        }
    },
    
    -- Animal types and their properties
    animalTypes = {
        rabbit = {
            name = "Rabbit",
            rawMeat = "rabbit_raw",
            sellPrice = 65,
            spawnRate = 0.4,
            health = 50,
            model = "a_c_rabbit_01"
        },
        hen = {
            name = "Chicken",
            rawMeat = "hen_raw", 
            sellPrice = 45,
            spawnRate = 0.35,
            health = 30,
            model = "a_c_hen"
        },
        rooster = {
            name = "Rooster",
            rawMeat = "rooster_raw",
            sellPrice = 55,
            spawnRate = 0.3,
            health = 40,
            model = "a_c_hen" -- Same model as hen
        },
        goat = {
            name = "Goat", 
            rawMeat = "goat_raw",
            sellPrice = 150,
            spawnRate = 0.2,
            health = 80,
            model = "a_c_cow"
        },
        sheep = {
            name = "Sheep",
            rawMeat = "sheep_raw", 
            sellPrice = 135,
            spawnRate = 0.25,
            health = 70,
            model = "a_c_cow" -- Using cow model as placeholder
        },
        snake = {
            name = "Snake",
            rawMeat = "snake_raw",
            sellPrice = 200,
            spawnRate = 0.15,
            health = 25,
            model = "a_c_fish"
        }
    },
    
    -- Hunting requirements
    requirements = {
        huntingLicense = false, -- No license required
        requiredWeapons = {"weapon_knife", "weapon_machete", "weapon_hatchet"},
        maxDistance = 3.5, -- max distance to gut animal
        gutTime = 15.0 -- seconds to gut animal
    }
}

-- Processing Configuration
Config.Processing = {
    -- Fish processing locations
    fishProcessing = {
        {
            name = "Kavala Fish Market",
            coords = vector3(-1686.0, -1072.0, 13.0),
            blip = {sprite = 68, color = 3, scale = 0.8},
            sellMultiplier = 1.0
        },
        {
            name = "Pyrgos Fish Market", 
            coords = vector3(2500.0, -1400.0, 13.0),
            blip = {sprite = 68, color = 3, scale = 0.8},
            sellMultiplier = 0.9
        }
    },
    
    -- Meat processing locations
    meatProcessing = {
        {
            name = "Kavala Butcher",
            coords = vector3(-1500.0, -1000.0, 13.0),
            blip = {sprite = 267, color = 1, scale = 0.8},
            sellMultiplier = 1.0
        }
    },
    
    -- Turtle processing (illegal)
    turtleProcessing = {
        {
            name = "Black Market Turtle Dealer",
            coords = vector3(1500.0, 1500.0, 13.0),
            blip = {sprite = 378, color = 1, scale = 0.6},
            sellMultiplier = 1.2,
            hidden = true -- No blip shown
        }
    }
}

-- General Settings
Config.General = {
    -- Animation settings
    animations = {
        fishing = "WORLD_HUMAN_STAND_FISHING",
        gutting = "WORLD_HUMAN_GARDENER_PLANT",
        netFishing = "WORLD_HUMAN_STAND_FISHING"
    },
    
    -- Notification settings
    notifications = {
        showCatchNotifications = true,
        showProcessingNotifications = true,
        showZoneNotifications = true
    },
    
    -- Debug settings
    debug = false
}
