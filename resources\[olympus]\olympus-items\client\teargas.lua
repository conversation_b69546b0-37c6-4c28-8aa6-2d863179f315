-- Olympus Items - Teargas Client
-- Based on original Olympus teargas mechanics

local isThrowing = false
local activeTeargas = {}

-- Use Teargas function
function UseTeargas()
    if isThrowing then
        exports['olympus-items']:ShowNotification("You are already throwing teargas!", "error")
        return false
    end
    
    local ped = PlayerPedId()
    
    -- Check if player is in a vehicle
    if IsPedInAnyVehicle(ped, false) then
        exports['olympus-items']:ShowNotification("You cannot use teargas while in a vehicle!", "error")
        return false
    end
    
    -- Start throwing teargas
    return StartThrowingTeargas()
end

-- Start throwing teargas
function StartThrowingTeargas()
    isThrowing = true
    
    local ped = PlayerPedId()
    
    -- Play throwing animation
    local animDict = "weapons@first_person@aim_rng@generic@projectile@thermal_charge@"
    local animName = "throw_m_fb_stand"
    
    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(10)
    end
    
    TaskPlayAnim(ped, animDict, animName, 8.0, -8.0, 1000, 0, 0, false, false, false)
    
    -- Wait for animation to complete
    Wait(1000)
    
    -- Get throw position and direction
    local playerPos = GetEntityCoords(ped)
    local forward = GetEntityForwardVector(ped)
    local throwPos = playerPos + forward * 10.0
    
    -- Create teargas effect
    CreateTeargasEffect(throwPos)
    
    -- Consume item
    TriggerServerEvent('olympus-items:server:consumeItem', 'teargas', true)
    
    isThrowing = false
    
    exports['olympus-items']:ShowNotification("Teargas deployed!", "success")
    
    return true
end

-- Create teargas effect
function CreateTeargasEffect(position)
    local teargasId = #activeTeargas + 1
    
    activeTeargas[teargasId] = {
        position = position,
        startTime = GetGameTimer(),
        duration = 60000, -- 1 minute
        radius = 15.0
    }
    
    -- Start teargas effects
    CreateThread(function()
        local teargas = activeTeargas[teargasId]
        if not teargas then return end
        
        local endTime = teargas.startTime + teargas.duration
        
        while GetGameTimer() < endTime do
            -- Create smoke effect
            UseParticleFxAssetNextCall("core")
            local particle = StartParticleFxLoopedAtCoord("exp_grd_bzgas_smoke", teargas.position.x, teargas.position.y, teargas.position.z, 0.0, 0.0, 0.0, 3.0, false, false, false, false)
            
            -- Check for players in range
            local players = GetActivePlayers()
            for _, playerId in ipairs(players) do
                local playerPed = GetPlayerPed(playerId)
                local playerPos = GetEntityCoords(playerPed)
                local distance = #(playerPos - teargas.position)
                
                if distance <= teargas.radius then
                    ApplyTeargasEffect(playerId)
                end
            end
            
            Wait(1000)
            
            -- Stop particle effect
            if particle then
                StopParticleFxLooped(particle, false)
            end
        end
        
        -- Remove teargas from active list
        activeTeargas[teargasId] = nil
    end)
end

-- Apply teargas effect to player
function ApplyTeargasEffect(playerId)
    if playerId == PlayerId() then
        -- Apply visual effects
        SetTimecycleModifier("REDMIST_blend")
        SetTimecycleModifierStrength(0.3)
        
        -- Apply coughing and movement penalty
        local ped = PlayerPedId()
        SetPedMovementClipset(ped, "move_injured_generic", 0.25)
        
        -- Remove effects after 5 seconds
        CreateThread(function()
            Wait(5000)
            ClearTimecycleModifier()
            ResetPedMovementClipset(ped, 0.25)
        end)
        
        exports['olympus-items']:ShowNotification("You are affected by teargas!", "warning")
    end
end

-- Export functions
exports('UseTeargas', UseTeargas)

-- Event handlers
RegisterNetEvent('olympus-items:client:useTeargas', function()
    UseTeargas()
end)
