-- ========================================
-- OLYMPUS VEHICLE SYSTEM - SERVER MAIN
-- Complete recreation based on original fn_insertVehicle.sqf and persistent vehicle system
-- Handles vehicle ownership, garage system, modifications, and persistence
-- ========================================

local OlympusVehicles = {}
OlympusVehicles.SpawnedVehicles = {} -- Active vehicles in world
OlympusVehicles.PlayerVehicles = {} -- Player-owned vehicles cache
OlympusVehicles.GangVehicles = {} -- Gang-owned vehicles cache

-- Vehicle configuration (matches original Olympus)
local VEHICLE_CONFIG = {
    maxVehiclesPerPlayer = 5, -- Maximum vehicles per player
    maxGangVehicles = 10, -- Maximum vehicles per gang
    defaultInsurance = false, -- Default insurance status
    persistentSaveInterval = 300000, -- Save persistent data every 5 minutes
    vehicleCleanupTime = 1800000, -- Clean up abandoned vehicles after 30 minutes
    spawnHeight = 1.0, -- Height offset when spawning vehicles
    maxModifications = 8 -- Maximum modification slots (matches original)
}

-- Vehicle types and categories (matches original vehicle config)
local VEHICLE_TYPES = {
    civilian = {
        cars = {"adder", "zentorno", "t20", "osiris", "entityxf"},
        trucks = {"phantom", "hauler", "packer", "benson"},
        motorcycles = {"bati", "akuma", "ruffian", "pcj"},
        boats = {"seashark", "jetmax", "speeder", "tropic"},
        helicopters = {"maverick", "frogger", "buzzard2"},
        planes = {"luxor", "shamal", "velum", "dodo"}
    },
    gang = {
        cars = {"kuruma", "insurgent", "technical", "mesa3"},
        trucks = {"guardian", "dubsta6x6", "sandking", "rebel2"},
        motorcycles = {"bati", "akuma", "vader", "lectro"}
    },
    cop = {
        cars = {"police", "police2", "police3", "sheriff"},
        trucks = {"riot", "fbi2", "policet", "sheriff2"},
        motorcycles = {"policeb", "akuma"},
        helicopters = {"polmav", "buzzard2"},
        boats = {"predator", "seashark2"}
    },
    medical = {
        cars = {"ambulance", "lguard"},
        helicopters = {"polmav"},
        boats = {"seashark"}
    }
}

-- Initialize vehicle system (matches original persistent vehicle loading)
function InitializeVehicleSystem()
    print("^2[Olympus Vehicles]^7 Initializing vehicle system...")

    -- Load persistent vehicles from database
    LoadPersistentVehicles()

    -- Start persistent vehicle save system
    StartPersistentVehicleSaver()

    -- Start vehicle cleanup system
    StartVehicleCleanupSystem()

    print("^2[Olympus Vehicles]^7 Vehicle system initialized!")
end

-- Load persistent vehicles (matches original fn_persistentVehiclesLoad.sqf)
function LoadPersistentVehicles()
    -- Load civilian vehicles
    LoadVehiclesByType('civ')

    -- Load gang vehicles
    LoadGangVehicles()

    -- Load cop vehicles
    LoadVehiclesByType('cop')

    -- Load medical vehicles
    LoadVehiclesByType('med')
end

-- Load vehicles by type (matches original query structure)
function LoadVehiclesByType(side)
    local query = [[
        SELECT CONVERT(id, char) as id, side, classname, type, pid, alive, active, plate,
               color, inventory, insured, modifications, persistentPosition, persistentDirection
        FROM vehicles
        WHERE alive = '1' AND (active = '0' OR active = ?) AND persistentServer = ? AND side = ?
    ]]

    exports['olympus-core']:FetchQuery(query, {GetConvar('sv_servername', 'olympus'), GetConvar('sv_servername', 'olympus'), side}, function(result)
        if result and #result > 0 then
            for _, vehicleData in ipairs(result) do
                ProcessVehicleData(vehicleData)
            end
            print(string.format("^3[Olympus Vehicles]^7 Loaded %d %s vehicles", #result, side))
        end
    end)
end

-- Load gang vehicles (matches original gang vehicle loading)
function LoadGangVehicles()
    local query = [[
        SELECT CONVERT(id, char) as id, side, classname, type, gang_id, alive, active, plate,
               color, inventory, insured, modifications, persistentPosition, persistentDirection
        FROM gangvehicles
        WHERE alive = '1' AND (active = '0' OR active = ?) AND persistentServer = ?
    ]]

    exports['olympus-core']:FetchQuery(query, {GetConvar('sv_servername', 'olympus'), GetConvar('sv_servername', 'olympus')}, function(result)
        if result and #result > 0 then
            for _, vehicleData in ipairs(result) do
                ProcessGangVehicleData(vehicleData)
            end
            print(string.format("^3[Olympus Vehicles]^7 Loaded %d gang vehicles", #result))
        end
    end)
end

-- Process vehicle data (matches original vehicle data parsing)
function ProcessVehicleData(vehicleData)
    local vehicleId = tonumber(vehicleData.id)

    -- Parse color data (matches original color parsing)
    local color = {0, 0}
    if vehicleData.color then
        local success, parsed = pcall(json.decode, vehicleData.color)
        if success and type(parsed) == "table" then
            color = parsed
        end
    end

    -- Parse inventory data
    local inventory = {}
    if vehicleData.inventory then
        local success, parsed = pcall(json.decode, vehicleData.inventory)
        if success then inventory = parsed end
    end

    -- Parse modifications data (matches original mods parsing)
    local modifications = {0,0,0,0,0,0,0,0}
    if vehicleData.modifications then
        local success, parsed = pcall(json.decode, vehicleData.modifications)
        if success and type(parsed) == "table" then
            modifications = parsed
        end
    end

    -- Parse position data
    local position = nil
    local direction = 0.0
    if vehicleData.persistentPosition then
        local success, parsed = pcall(json.decode, vehicleData.persistentPosition)
        if success and type(parsed) == "table" and #parsed >= 3 then
            position = vector3(parsed[1], parsed[2], parsed[3] + VEHICLE_CONFIG.spawnHeight)
        end
    end
    if vehicleData.persistentDirection then
        direction = tonumber(vehicleData.persistentDirection) or 0.0
    end

    -- Create vehicle object (matches original vehicle structure)
    local vehicle = {
        id = vehicleId,
        side = vehicleData.side,
        classname = vehicleData.classname,
        type = vehicleData.type,
        owner_id = vehicleData.pid,
        plate = vehicleData.plate,
        color = color,
        inventory = inventory,
        insured = vehicleData.insured == '1',
        modifications = modifications,
        position = position,
        direction = direction,
        active = vehicleData.active == '1',
        spawned = false,
        entity = nil
    }

    -- Add to appropriate cache
    if not OlympusVehicles.PlayerVehicles[vehicleData.pid] then
        OlympusVehicles.PlayerVehicles[vehicleData.pid] = {}
    end
    OlympusVehicles.PlayerVehicles[vehicleData.pid][vehicleId] = vehicle

    -- Spawn vehicle if it was active and has position
    if vehicle.active and vehicle.position then
        SpawnPersistentVehicle(vehicle)
    end
end

-- Process gang vehicle data (similar to regular vehicles but for gangs)
function ProcessGangVehicleData(vehicleData)
    local vehicleId = tonumber(vehicleData.id)

    -- Parse data similar to regular vehicles
    local color = {0, 0}
    if vehicleData.color then
        local success, parsed = pcall(json.decode, vehicleData.color)
        if success and type(parsed) == "table" then
            color = parsed
        end
    end

    local inventory = {}
    if vehicleData.inventory then
        local success, parsed = pcall(json.decode, vehicleData.inventory)
        if success then inventory = parsed end
    end

    local modifications = {0,0,0,0,0,0,0,0}
    if vehicleData.modifications then
        local success, parsed = pcall(json.decode, vehicleData.modifications)
        if success and type(parsed) == "table" then
            modifications = parsed
        end
    end

    local position = nil
    local direction = 0.0
    if vehicleData.persistentPosition then
        local success, parsed = pcall(json.decode, vehicleData.persistentPosition)
        if success and type(parsed) == "table" and #parsed >= 3 then
            position = vector3(parsed[1], parsed[2], parsed[3] + VEHICLE_CONFIG.spawnHeight)
        end
    end
    if vehicleData.persistentDirection then
        direction = tonumber(vehicleData.persistentDirection) or 0.0
    end

    -- Create gang vehicle object
    local vehicle = {
        id = vehicleId,
        side = vehicleData.side,
        classname = vehicleData.classname,
        type = vehicleData.type,
        gang_id = vehicleData.gang_id,
        plate = vehicleData.plate,
        color = color,
        inventory = inventory,
        insured = vehicleData.insured == '1',
        modifications = modifications,
        position = position,
        direction = direction,
        active = vehicleData.active == '1',
        spawned = false,
        entity = nil
    }

    -- Add to gang vehicles cache
    if not OlympusVehicles.GangVehicles[vehicleData.gang_id] then
        OlympusVehicles.GangVehicles[vehicleData.gang_id] = {}
    end
    OlympusVehicles.GangVehicles[vehicleData.gang_id][vehicleId] = vehicle

    -- Spawn vehicle if it was active and has position
    if vehicle.active and vehicle.position then
        SpawnPersistentVehicle(vehicle)
    end
end

-- Spawn persistent vehicle (matches original vehicle spawning)
function SpawnPersistentVehicle(vehicleData)
    if vehicleData.spawned or not vehicleData.position then return end

    -- Create vehicle entity
    local vehicle = CreateVehicle(GetHashKey(vehicleData.classname), vehicleData.position.x, vehicleData.position.y, vehicleData.position.z, vehicleData.direction, true, false)

    if DoesEntityExist(vehicle) then
        -- Set vehicle properties (matches original vehicle setup)
        SetVehicleNumberPlateText(vehicle, tostring(vehicleData.plate))
        SetVehicleColours(vehicle, vehicleData.color[1] or 0, vehicleData.color[2] or 0)

        -- Apply modifications (matches original modification system)
        if vehicleData.modifications then
            for i, mod in ipairs(vehicleData.modifications) do
                if mod > 0 then
                    -- Apply modification based on index (simplified for FiveM)
                    if i == 1 then -- Turbo
                        ToggleVehicleMod(vehicle, 18, true)
                    elseif i == 2 then -- Engine
                        SetVehicleMod(vehicle, 11, mod - 1, false)
                    elseif i == 3 then -- Brakes
                        SetVehicleMod(vehicle, 12, mod - 1, false)
                    elseif i == 4 then -- Transmission
                        SetVehicleMod(vehicle, 13, mod - 1, false)
                    elseif i == 5 then -- Suspension
                        SetVehicleMod(vehicle, 15, mod - 1, false)
                    elseif i == 6 then -- Armor
                        SetVehicleMod(vehicle, 16, mod - 1, false)
                    end
                end
            end
        end

        -- Set vehicle variables (matches original vehicle variables)
        Entity(vehicle).state.vehicleId = vehicleData.id
        Entity(vehicle).state.owner = vehicleData.owner_id or vehicleData.gang_id
        Entity(vehicle).state.isGangVehicle = vehicleData.gang_id ~= nil
        Entity(vehicle).state.locked = true -- Vehicles start locked
        Entity(vehicle).state.keyPlayers = vehicleData.owner_id and {vehicleData.owner_id} or {}
        Entity(vehicle).state.inventory = vehicleData.inventory
        Entity(vehicle).state.insured = vehicleData.insured

        -- Update vehicle data
        vehicleData.spawned = true
        vehicleData.entity = vehicle
        OlympusVehicles.SpawnedVehicles[vehicle] = vehicleData

        print(string.format("^3[Olympus Vehicles]^7 Spawned vehicle %s (ID: %d) at %s",
            vehicleData.classname, vehicleData.id, vehicleData.position))
    else
        print(string.format("^1[Olympus Vehicles]^7 Failed to spawn vehicle %s (ID: %d)",
            vehicleData.classname, vehicleData.id))
    end
end

-- Start persistent vehicle saver (matches original persistent save system)
function StartPersistentVehicleSaver()
    CreateThread(function()
        while true do
            Wait(VEHICLE_CONFIG.persistentSaveInterval)
            SavePersistentVehicles()
        end
    end)
end

-- Save persistent vehicles (matches original fn_persistentVehiclesSave.sqf)
function SavePersistentVehicles()
    local savedCount = 0

    for vehicle, vehicleData in pairs(OlympusVehicles.SpawnedVehicles) do
        if DoesEntityExist(vehicle) then
            -- Get current position and direction
            local coords = GetEntityCoords(vehicle)
            local heading = GetEntityHeading(vehicle)

            -- Update vehicle data
            vehicleData.position = vector3(coords.x, coords.y, coords.z)
            vehicleData.direction = heading

            -- Save to database
            local positionJson = json.encode({coords.x, coords.y, coords.z})
            local query = [[
                UPDATE vehicles
                SET persistentPosition = ?, persistentDirection = ?, active = '1'
                WHERE id = ?
            ]]

            if vehicleData.gang_id then
                query = [[
                    UPDATE gangvehicles
                    SET persistentPosition = ?, persistentDirection = ?, active = '1'
                    WHERE id = ?
                ]]
            end

            exports['olympus-core']:ExecuteQuery(query, {positionJson, heading, vehicleData.id}, function(success)
                if success then
                    savedCount = savedCount + 1
                end
            end)
        else
            -- Vehicle was deleted, mark as inactive
            local query = "UPDATE vehicles SET active = '0' WHERE id = ?"
            if vehicleData.gang_id then
                query = "UPDATE gangvehicles SET active = '0' WHERE id = ?"
            end

            exports['olympus-core']:ExecuteQuery(query, {vehicleData.id})
            OlympusVehicles.SpawnedVehicles[vehicle] = nil
        end
    end

    if savedCount > 0 then
        print(string.format("^3[Olympus Vehicles]^7 Saved %d persistent vehicles", savedCount))
    end
end

-- Start vehicle cleanup system
function StartVehicleCleanupSystem()
    CreateThread(function()
        while true do
            Wait(VEHICLE_CONFIG.vehicleCleanupTime)
            CleanupAbandonedVehicles()
        end
    end)
end

-- Cleanup abandoned vehicles
function CleanupAbandonedVehicles()
    local cleanedCount = 0

    for vehicle, vehicleData in pairs(OlympusVehicles.SpawnedVehicles) do
        if DoesEntityExist(vehicle) then
            -- Check if vehicle has been abandoned (no players nearby)
            local coords = GetEntityCoords(vehicle)
            local playersNearby = false

            for _, playerId in ipairs(GetPlayers()) do
                local playerCoords = GetEntityCoords(GetPlayerPed(tonumber(playerId)))
                if #(coords - playerCoords) < 100.0 then
                    playersNearby = true
                    break
                end
            end

            -- If no players nearby and vehicle is empty, consider for cleanup
            if not playersNearby and GetVehicleNumberOfPassengers(vehicle) == 0 and IsVehicleSeatFree(vehicle, -1) then
                -- Mark as inactive and delete
                local query = "UPDATE vehicles SET active = '0' WHERE id = ?"
                if vehicleData.gang_id then
                    query = "UPDATE gangvehicles SET active = '0' WHERE id = ?"
                end

                exports['olympus-core']:ExecuteQuery(query, {vehicleData.id})
                DeleteEntity(vehicle)
                OlympusVehicles.SpawnedVehicles[vehicle] = nil
                cleanedCount = cleanedCount + 1
            end
        end
    end

    if cleanedCount > 0 then
        print(string.format("^3[Olympus Vehicles]^7 Cleaned up %d abandoned vehicles", cleanedCount))
    end
end

-- Initialize when loaded
CreateThread(function()
    -- Wait for core to be ready
    while not exports['olympus-core']:IsDBReady() do
        Wait(100)
    end

    Wait(5000) -- Additional delay for core system to load
    InitializeVehicleSystem()
end)

-- Vehicle purchase (matches original fn_insertVehicle.sqf)
RegisterServerEvent('olympus:server:purchaseVehicle')
AddEventHandler('olympus:server:purchaseVehicle', function(vehicleClass, vehicleType, color, spawnInGarage, gangId)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData then return end

    -- Validate parameters (matches original validation)
    if not vehicleClass or vehicleClass == "" or not vehicleType or vehicleType == "" then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Vehicles',
            message = 'Invalid vehicle data'
        })
        return
    end

    -- Check vehicle limits
    local vehicleCount = GetPlayerVehicleCount(playerData.identifier)
    if vehicleCount >= VEHICLE_CONFIG.maxVehiclesPerPlayer then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Vehicles',
            message = string.format('You can only own %d vehicles maximum', VEHICLE_CONFIG.maxVehiclesPerPlayer)
        })
        return
    end

    -- Generate plate number
    local plate = GenerateVehiclePlate()

    -- Prepare modifications array (matches original mods structure)
    local modifications = {0,0,0,0,0,0,0,0}

    -- Prepare color data (matches original color format)
    local colorData = color or {0, 0}
    if type(colorData) == "number" then
        colorData = {colorData, 0}
    end

    -- Insert vehicle into database (matches original fn_insertVehicle.sqf)
    if gangId and gangId > 0 then
        -- Gang vehicle insertion
        local query = [[
            INSERT INTO gangvehicles (side, classname, type, gang_id, alive, active, inventory, color, plate, insured, modifications, persistentServer)
            VALUES (?, ?, ?, ?, '1', '0', '[]', ?, ?, '0', ?, ?)
        ]]

        exports['olympus-core']:ExecuteQuery(query, {
            playerData.job,
            vehicleClass,
            vehicleType,
            gangId,
            json.encode(colorData),
            plate,
            json.encode(modifications),
            GetConvar('sv_servername', 'olympus')
        }, function(success, result)
            if success and result.insertId then
                -- Add to gang vehicles cache
                local vehicleData = {
                    id = result.insertId,
                    side = playerData.job,
                    classname = vehicleClass,
                    type = vehicleType,
                    gang_id = gangId,
                    plate = plate,
                    color = colorData,
                    inventory = {},
                    insured = false,
                    modifications = modifications,
                    position = nil,
                    direction = 0.0,
                    active = false,
                    spawned = false,
                    entity = nil
                }

                if not OlympusVehicles.GangVehicles[gangId] then
                    OlympusVehicles.GangVehicles[gangId] = {}
                end
                OlympusVehicles.GangVehicles[gangId][result.insertId] = vehicleData

                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'success',
                    title = 'Vehicles',
                    message = string.format('Gang vehicle %s purchased! (Plate: %s)', vehicleClass, plate)
                })

                print(string.format("^2[Olympus Vehicles]^7 %s purchased gang vehicle %s (ID: %d, Gang: %d)",
                    playerData.name, vehicleClass, result.insertId, gangId))
            else
                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'error',
                    title = 'Vehicles',
                    message = 'Failed to purchase vehicle'
                })
            end
        end)
    else
        -- Regular vehicle insertion
        local query = [[
            INSERT INTO vehicles (side, classname, type, pid, alive, active, inventory, color, plate, insured, modifications, persistentServer)
            VALUES (?, ?, ?, ?, '1', '0', '[]', ?, ?, '0', ?, ?)
        ]]

        exports['olympus-core']:ExecuteQuery(query, {
            playerData.job,
            vehicleClass,
            vehicleType,
            playerData.identifier,
            json.encode(colorData),
            plate,
            json.encode(modifications),
            GetConvar('sv_servername', 'olympus')
        }, function(success, result)
            if success and result.insertId then
                -- Add to player vehicles cache
                local vehicleData = {
                    id = result.insertId,
                    side = playerData.job,
                    classname = vehicleClass,
                    type = vehicleType,
                    owner_id = playerData.identifier,
                    plate = plate,
                    color = colorData,
                    inventory = {},
                    insured = false,
                    modifications = modifications,
                    position = nil,
                    direction = 0.0,
                    active = false,
                    spawned = false,
                    entity = nil
                }

                if not OlympusVehicles.PlayerVehicles[playerData.identifier] then
                    OlympusVehicles.PlayerVehicles[playerData.identifier] = {}
                end
                OlympusVehicles.PlayerVehicles[playerData.identifier][result.insertId] = vehicleData

                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'success',
                    title = 'Vehicles',
                    message = string.format('Vehicle %s purchased! (Plate: %s)', vehicleClass, plate)
                })

                print(string.format("^2[Olympus Vehicles]^7 %s purchased vehicle %s (ID: %d)",
                    playerData.name, vehicleClass, result.insertId))
            else
                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'error',
                    title = 'Vehicles',
                    message = 'Failed to purchase vehicle'
                })
            end
        end)
    end
end)

-- Helper functions
function GetPlayerVehicleCount(playerIdentifier)
    local count = 0
    if OlympusVehicles.PlayerVehicles[playerIdentifier] then
        for _ in pairs(OlympusVehicles.PlayerVehicles[playerIdentifier]) do
            count = count + 1
        end
    end
    return count
end

function GenerateVehiclePlate()
    local chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    local plate = ""
    for i = 1, 8 do
        local rand = math.random(#chars)
        plate = plate .. string.sub(chars, rand, rand)
    end
    return plate
end

-- Exports (matches original export structure)
exports('CreateVehicle', function(source, vehicleClass, vehicleType, color, spawnInGarage, gangId)
    TriggerEvent('olympus:server:purchaseVehicle', vehicleClass, vehicleType, color, spawnInGarage, gangId)
end)

exports('GetVehicleOwner', function(vehicleId)
    for playerId, vehicles in pairs(OlympusVehicles.PlayerVehicles) do
        if vehicles[vehicleId] then
            return playerId
        end
    end

    for gangId, vehicles in pairs(OlympusVehicles.GangVehicles) do
        if vehicles[vehicleId] then
            return gangId, true -- Return gang ID and flag indicating it's a gang vehicle
        end
    end

    return nil
end)

exports('GetPlayerVehicles', function(playerIdentifier)
    return OlympusVehicles.PlayerVehicles[playerIdentifier] or {}
end)

exports('GetGangVehicles', function(gangId)
    return OlympusVehicles.GangVehicles[gangId] or {}
end)

exports('GetSpawnedVehicles', function()
    return OlympusVehicles.SpawnedVehicles
end)

print("^2[Olympus Vehicles]^7 Server main loaded!")
