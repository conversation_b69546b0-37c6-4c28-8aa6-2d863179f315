-- Olympus Civilian Jobs System - Complete Implementation
-- Based on Olympus Altis Life civilian job mechanics and economy

Config = {}

-- Civilian Jobs System Settings
Config.CivilianJobs = {
    enabled = true,
    
    -- Market Fluctuation System (Exact Olympus Implementation)
    marketFluctuation = {
        enabled = true,
        updateInterval = 300, -- 5 minutes
        baseRange = {min = 0.8, max = 1.2}, -- 80%-120% of base price
        supplyDemandFactor = true, -- Affected by player activity
        
        -- Dynamic pricing based on server activity
        activityModifiers = {
            lowActivity = 1.1, -- 10% bonus during low activity
            normalActivity = 1.0, -- Normal pricing
            highActivity = 0.9 -- 10% penalty during high activity
        }
    },
    
    -- Worker's Protection License Integration
    wplIntegration = {
        enabled = true,
        bonus = 0.15, -- 15% bonus for WPL holders
        processingSpeedBonus = true, -- Faster processing
        equipmentAccess = true -- Access to WPL equipment
    }
}

-- Apple Picking Job (Classic Olympus Civilian Job)
Config.CivilianJobs['apple'] = {
    name = 'Apple Picking',
    displayName = 'Apple Orchard Worker',
    
    -- Job Locations
    locations = {
        orchard = vector3(2100.5, 1850.2, 35.0), -- Apple orchard
        processor = vector3(2150.8, 1900.5, 35.0), -- Apple juice factory
        trader = vector3(2120.2, 1820.8, 35.0) -- Market trader
    },
    
    -- Requirements
    requirements = {
        license = nil, -- No license required
        vehicle = true, -- Need vehicle for transport
        backpack = true, -- Need storage space
        items = {'food', 'water'} -- Recommended items
    },
    
    -- Collection Mechanics
    collection = {
        time = 3, -- 3 seconds per apple
        yield = 1, -- 1 apple per collection
        range = 5, -- Must be within 5m of apple tree
        animation = 'picking', -- Picking animation
        cancellable = true
    },
    
    -- Processing (Optional - Apple Juice)
    processing = {
        enabled = true,
        time = 4, -- 4 seconds per apple
        yield = 1, -- 1 apple juice per apple
        range = 25, -- Must stay within 25m
        wplBonus = true -- Faster with WPL
    },
    
    -- Pricing
    pricing = {
        rawApple = 150, -- $150 per raw apple
        appleJuice = 250, -- $250 per processed apple juice
        wplBonus = 0.15, -- 15% bonus with WPL
        
        fluctuation = {
            enabled = true,
            min = 0.8,
            max = 1.2
        }
    },
    
    -- Estimated Payouts (Box Truck + Bergen)
    payouts = {
        raw = {
            standard = 31500, -- $31,500 for raw apples
            wpl = 36225 -- $36,225 with WPL
        },
        processed = {
            standard = 52500, -- $52,500 for apple juice
            wpl = 60375 -- $60,375 with WPL
        }
    },
    
    runTime = {
        raw = 900, -- 15 minutes for raw
        processed = 1800 -- 30 minutes with processing
    }
}

-- Peach Picking Job (Classic Olympus Civilian Job)
Config.CivilianJobs['peach'] = {
    name = 'Peach Picking',
    displayName = 'Peach Orchard Worker',
    
    locations = {
        orchard = vector3(1800.5, 3200.2, 35.0), -- Peach orchard
        processor = vector3(1750.8, 3150.5, 35.0), -- Peach processing
        trader = vector3(1720.2, 3100.8, 35.0) -- Market trader
    },
    
    requirements = {
        license = nil,
        vehicle = true,
        backpack = true,
        items = {'food', 'water'}
    },
    
    collection = {
        time = 3,
        yield = 1,
        range = 5,
        animation = 'picking',
        cancellable = true
    },
    
    processing = {
        enabled = true,
        time = 4,
        yield = 1, -- Peach to peach preserves
        range = 25,
        wplBonus = true
    },
    
    pricing = {
        rawPeach = 175, -- Slightly higher than apples
        peachPreserves = 275,
        wplBonus = 0.15,
        
        fluctuation = {
            enabled = true,
            min = 0.8,
            max = 1.2
        }
    },
    
    payouts = {
        raw = {
            standard = 36750,
            wpl = 42263
        },
        processed = {
            standard = 57750,
            wpl = 66413
        }
    },
    
    runTime = {
        raw = 900,
        processed = 1800
    }
}

-- Fishing Job (Legal Water Activity)
Config.CivilianJobs['fishing'] = {
    name = 'Commercial Fishing',
    displayName = 'Commercial Fisherman',
    
    locations = {
        fishingSpots = {
            vector3(1200.5, 4800.2, 0.0), -- North coast
            vector3(3500.8, 2150.5, 0.0), -- East coast
            vector3(2800.2, 5200.8, 0.0), -- Northeast bay
            vector3(800.5, 3600.2, 0.0) -- West coast
        },
        processor = vector3(1500.8, 4750.5, 35.0), -- Fish processing plant
        trader = vector3(1450.2, 4700.8, 35.0) -- Fish market
    },
    
    requirements = {
        license = 'fishing', -- $2,500 fishing license
        boat = true, -- Must have boat
        divingGear = true, -- Diving gear required
        items = {'food', 'water'}
    },
    
    collection = {
        time = 5, -- 5 seconds per fish
        yield = 1,
        range = 10, -- Must be in water
        animation = 'fishing',
        cancellable = true,
        underwater = true -- Must be underwater
    },
    
    processing = {
        enabled = true,
        time = 6, -- Longer processing for fish
        yield = 1, -- Fish to processed fish
        range = 25,
        wplBonus = true
    },
    
    pricing = {
        rawFish = 300, -- Higher value due to license requirement
        processedFish = 450,
        wplBonus = 0.15,
        
        fluctuation = {
            enabled = true,
            min = 0.8,
            max = 1.2
        }
    },
    
    payouts = {
        raw = {
            standard = 63000,
            wpl = 72450
        },
        processed = {
            standard = 94500,
            wpl = 108675
        }
    },
    
    runTime = {
        raw = 1200, -- 20 minutes (includes boat travel)
        processed = 2400 -- 40 minutes with processing
    }
}

-- Trucking Job (Commercial Transport)
Config.CivilianJobs['trucking'] = {
    name = 'Commercial Trucking',
    displayName = 'Truck Driver',
    
    -- Multiple delivery routes
    routes = {
        {
            name = 'Kavala to Pyrgos Supply Run',
            pickup = vector3(1500.2, 2800.5, 35.0),
            delivery = vector3(2100.8, 1850.2, 35.0),
            distance = 15000, -- 15km
            payment = 25000, -- $25,000
            cargo = 'general_supplies'
        },
        {
            name = 'Sofia Industrial Transport',
            pickup = vector3(1800.5, 3200.2, 35.0),
            delivery = vector3(2500.8, 2400.5, 35.0),
            distance = 12000, -- 12km
            payment = 20000, -- $20,000
            cargo = 'industrial_equipment'
        },
        {
            name = 'Airport Cargo Run',
            pickup = vector3(1685.0, 3289.0, 41.0),
            delivery = vector3(1174.0, 2640.0, 38.0),
            distance = 8000, -- 8km
            payment = 15000, -- $15,000
            cargo = 'air_freight'
        }
    },
    
    requirements = {
        license = 'truck', -- $10,000 truck license
        truckVehicle = true, -- Must use truck-class vehicle
        cleanRecord = true, -- No recent traffic violations
        items = {'food', 'water', 'toolkit'}
    },
    
    mechanics = {
        timeLimit = 1800, -- 30 minutes to complete delivery
        damageReduction = true, -- Payment reduced for damaged cargo
        speedingPenalty = true, -- Penalty for excessive speeding
        routeDeviation = false, -- Must follow designated route
        
        -- Payment modifiers
        onTimeBonus = 0.1, -- 10% bonus for on-time delivery
        damagePenalty = 0.05, -- 5% penalty per 10% damage
        wplBonus = 0.15 -- 15% bonus with WPL
    },
    
    payouts = {
        shortRoute = {
            standard = 15000,
            wpl = 17250
        },
        mediumRoute = {
            standard = 20000,
            wpl = 23000
        },
        longRoute = {
            standard = 25000,
            wpl = 28750
        }
    },
    
    runTime = {
        shortRoute = 900, -- 15 minutes
        mediumRoute = 1200, -- 20 minutes
        longRoute = 1800 -- 30 minutes
    }
}

-- Taxi Job (Passenger Transport)
Config.CivilianJobs['taxi'] = {
    name = 'Taxi Service',
    displayName = 'Taxi Driver',

    -- Taxi Stands
    stands = {
        vector3(1174.0, 2640.0, 38.0), -- Kavala taxi stand
        vector3(3003.0, 3310.0, 12.0), -- Pyrgos taxi stand
        vector3(1685.0, 3289.0, 41.0), -- Airport taxi stand
        vector3(1800.5, 3200.2, 35.0) -- Sofia taxi stand
    },

    -- Common Destinations
    destinations = {
        {name = 'Kavala Hospital', location = vector3(1200.5, 2700.2, 35.0), fare = 2000},
        {name = 'Pyrgos Market', location = vector3(2150.8, 1900.5, 35.0), fare = 3500},
        {name = 'Airport Terminal', location = vector3(1685.0, 3289.0, 41.0), fare = 4000},
        {name = 'Sofia Industrial', location = vector3(1850.8, 3250.5, 35.0), fare = 2500},
        {name = 'DMV Office', location = vector3(1174.0, 2640.0, 38.0), fare = 1500}
    },

    requirements = {
        license = 'driver', -- $500 driver license
        taxiVehicle = true, -- Must use taxi or civilian car
        cleanRecord = true, -- No recent violations
        items = {'food', 'water'}
    },

    mechanics = {
        passengerPickup = true, -- Pick up NPC passengers
        timedRuns = true, -- Passengers have time limits
        tipSystem = true, -- Random tips for good service

        -- Payment system
        baseFare = 1000, -- $1,000 base fare
        perKmRate = 200, -- $200 per kilometer
        timeBonus = 0.1, -- 10% bonus for fast service
        tipChance = 0.3, -- 30% chance of tip
        tipAmount = {min = 500, max = 2000}, -- $500-$2000 tips
        wplBonus = 0.15
    },

    payouts = {
        shortRide = {
            standard = 1500,
            wpl = 1725
        },
        mediumRide = {
            standard = 3000,
            wpl = 3450
        },
        longRide = {
            standard = 4500,
            wpl = 5175
        }
    },

    runTime = {
        shortRide = 300, -- 5 minutes
        mediumRide = 600, -- 10 minutes
        longRide = 900 -- 15 minutes
    }
}

-- Delivery Job (Package Delivery)
Config.CivilianJobs['delivery'] = {
    name = 'Package Delivery',
    displayName = 'Delivery Driver',

    -- Distribution Centers
    centers = {
        vector3(1500.2, 2800.5, 35.0), -- Kavala distribution center
        vector3(2100.8, 1850.2, 35.0), -- Pyrgos distribution center
        vector3(1800.5, 3200.2, 35.0) -- Sofia distribution center
    },

    -- Delivery Types
    deliveryTypes = {
        {
            name = 'Express Delivery',
            timeLimit = 600, -- 10 minutes
            payment = 5000, -- $5,000
            packages = 5
        },
        {
            name = 'Standard Delivery',
            timeLimit = 1200, -- 20 minutes
            payment = 8000, -- $8,000
            packages = 10
        },
        {
            name = 'Bulk Delivery',
            timeLimit = 1800, -- 30 minutes
            payment = 12000, -- $12,000
            packages = 15
        }
    },

    requirements = {
        license = 'driver', -- $500 driver license
        deliveryVehicle = true, -- Van or truck preferred
        gps = true, -- GPS device required
        items = {'food', 'water', 'toolkit'}
    },

    mechanics = {
        packagePickup = true, -- Pick up packages at center
        multipleStops = true, -- Multiple delivery locations
        signatureRequired = true, -- Must interact at each stop

        -- Payment modifiers
        onTimeBonus = 0.15, -- 15% bonus for on-time delivery
        lateDeliveryPenalty = 0.1, -- 10% penalty per late delivery
        packageDamagePenalty = 0.05, -- 5% penalty per damaged package
        wplBonus = 0.15
    },

    payouts = {
        express = {
            standard = 5000,
            wpl = 5750
        },
        standard = {
            standard = 8000,
            wpl = 9200
        },
        bulk = {
            standard = 12000,
            wpl = 13800
        }
    },

    runTime = {
        express = 600, -- 10 minutes
        standard = 1200, -- 20 minutes
        bulk = 1800 -- 30 minutes
    }
}

-- Coral Diving Job (Underwater Collection)
Config.CivilianJobs['coral'] = {
    name = 'Coral Diving',
    displayName = 'Marine Biologist',

    locations = {
        divingSites = {
            vector3(1000.5, 4500.2, -10.0), -- Coral reef site 1
            vector3(3200.8, 1800.5, -15.0), -- Coral reef site 2
            vector3(2600.2, 4800.8, -12.0), -- Coral reef site 3
            vector3(600.5, 3200.2, -8.0) -- Coral reef site 4
        },
        processor = vector3(1400.8, 4600.5, 35.0), -- Marine research lab
        trader = vector3(1350.2, 4550.8, 35.0) -- Scientific equipment trader
    },

    requirements = {
        license = 'fishing', -- $2,500 fishing license (covers marine activities)
        boat = true, -- Must have boat
        divingGear = true, -- Diving gear required
        items = {'food', 'water', 'diving_knife'}
    },

    collection = {
        time = 8, -- 8 seconds per coral (delicate work)
        yield = 1,
        range = 15, -- Must be underwater near coral
        animation = 'coral_cutting',
        cancellable = true,
        underwater = true,
        depthRequired = -5 -- Must be at least 5m underwater
    },

    processing = {
        enabled = true,
        time = 10, -- Longer processing for scientific samples
        yield = 1, -- Coral to coral samples
        range = 25,
        wplBonus = true
    },

    pricing = {
        rawCoral = 500, -- High value due to rarity and difficulty
        coralSamples = 750,
        wplBonus = 0.15,

        fluctuation = {
            enabled = true,
            min = 0.8,
            max = 1.2
        }
    },

    payouts = {
        raw = {
            standard = 105000, -- High payout due to difficulty
            wpl = 120750
        },
        processed = {
            standard = 157500,
            wpl = 181125
        }
    },

    runTime = {
        raw = 1800, -- 30 minutes (includes boat travel and diving)
        processed = 3000 -- 50 minutes with processing
    }
}

-- Job Progression System
Config.JobProgression = {
    enabled = true,

    -- Experience system
    experience = {
        gainPerJob = 10, -- 10 XP per completed job
        bonusForPerfection = 5, -- 5 bonus XP for perfect completion

        -- Level benefits
        levels = {
            [1] = {xpRequired = 0, bonus = 0.0}, -- Novice
            [2] = {xpRequired = 100, bonus = 0.05}, -- Experienced (5% bonus)
            [3] = {xpRequired = 300, bonus = 0.10}, -- Professional (10% bonus)
            [4] = {xpRequired = 600, bonus = 0.15}, -- Expert (15% bonus)
            [5] = {xpRequired = 1000, bonus = 0.20} -- Master (20% bonus)
        }
    },

    -- Reputation system
    reputation = {
        enabled = true,
        affectsPayouts = true,

        -- Reputation factors
        factors = {
            onTimeDelivery = 2, -- +2 rep for on-time delivery
            lateDelivery = -1, -- -1 rep for late delivery
            damagedGoods = -2, -- -2 rep for damaged goods
            perfectJob = 5 -- +5 rep for perfect completion
        }
    }
}

-- ============================================
-- PROCESSING CONFIGURATION
-- Based on original fn_processAction.sqf
-- ============================================

Config.Processing = {
    -- Legal Processing
    salt = {
        input = {"salt"},
        output = "saltr",
        cost = 350,
        time = 3.0,
        legal = true,
        description = "Processing Salt",
        licenseRequired = "processing",
        buffCategory = "legal"
    },
    cement = {
        input = {"rock"},
        output = "cement",
        cost = 450,
        time = 3.0,
        legal = true,
        description = "Mixing Cement",
        licenseRequired = "processing",
        buffCategory = "legal"
    },
    sand = {
        input = {"sand"},
        output = "glass",
        cost = 650,
        time = 3.0,
        legal = true,
        description = "Processing Sand",
        licenseRequired = "processing",
        buffCategory = "legal"
    },
    iron = {
        input = {"ironore"},
        output = "ironr",
        cost = 750,
        time = 3.0,
        legal = true,
        description = "Processing Iron",
        licenseRequired = "processing",
        buffCategory = "legal"
    },
    copper = {
        input = {"copperore"},
        output = "copperr",
        cost = 875,
        time = 3.0,
        legal = true,
        description = "Processing Copper",
        licenseRequired = "processing",
        buffCategory = "legal"
    },
    oil = {
        input = {"oilu"},
        output = "oilp",
        cost = 1130,
        time = 3.0,
        legal = true,
        description = "Processing Oil",
        licenseRequired = "processing",
        buffCategory = "legal"
    },
    diamond = {
        input = {"diamond"},
        output = "diamondc",
        cost = 1200,
        time = 3.0,
        legal = true,
        description = "Processing Diamond",
        licenseRequired = "processing",
        buffCategory = "legal"
    },

    -- Illegal Processing
    marijuana = {
        input = {"cannabis"},
        output = "marijuana",
        cost = 500,
        time = 3.0,
        legal = false,
        description = "Processing Marijuana",
        licenseRequired = nil,
        buffCategory = "illegal",
        cartelDiscount = true
    },
    heroin = {
        input = {"heroinu"},
        output = "heroinp",
        cost = 900,
        time = 4.0,
        legal = false,
        description = "Processing Heroin",
        licenseRequired = nil,
        buffCategory = "illegal",
        cartelDiscount = true
    },
    cocaine = {
        input = {"cocaine"},
        output = "cocainep",
        cost = 1100,
        time = 3.5,
        legal = false,
        description = "Processing Cocaine",
        licenseRequired = nil,
        buffCategory = "illegal",
        cartelDiscount = true
    },
    moonshine = {
        input = {"sugar", "yeast", "corn"},
        output = "moonshine",
        cost = 2200,
        time = 6.5,
        legal = false,
        description = "Processing Moonshine",
        licenseRequired = nil,
        buffCategory = "illegal_three",
        cartelDiscount = true
    },
    crystalmeth = {
        input = {"lithium", "phosphorous", "ephedra"},
        output = "crystalmeth",
        cost = 2400,
        time = 7.0,
        legal = false,
        description = "Processing Meth",
        licenseRequired = nil,
        buffCategory = "illegal_three",
        cartelDiscount = true
    },

    -- Double Processing
    hash = {
        input = {"marijuana"},
        output = "hash",
        cost = 0,
        time = 1.5,
        legal = false,
        description = "Processing Hash",
        licenseRequired = nil,
        buffCategory = "illegal",
        cartelDiscount = true,
        doubleProcess = true
    },
    crack = {
        input = {"cocainep"},
        output = "crack",
        cost = 0,
        time = 2.25,
        legal = false,
        description = "Processing Crack",
        licenseRequired = nil,
        buffCategory = "illegal",
        cartelDiscount = true,
        doubleProcess = true
    }
}

-- Processing Locations
Config.ProcessingLocations = {
    legal_processor = {
        position = vector3(1500.0, 1500.0, 35.0),
        blipSprite = 478,
        blipColor = 2,
        name = "Legal Processor"
    },

    illegal_processor_1 = {
        position = vector3(5000.0, 5000.0, 35.0),
        blipSprite = 478,
        blipColor = 1,
        name = "Illegal Processor"
    },

    illegal_processor_2 = {
        position = vector3(5100.0, 5100.0, 35.0),
        blipSprite = 478,
        blipColor = 1,
        name = "Illegal Processor"
    }
}
