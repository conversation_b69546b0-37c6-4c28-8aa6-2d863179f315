-- ============================================
-- OLYMPUS VIGILANTE SYSTEM - SERVER MAIN
-- Based on original fn_vigiGetSetArrests.sqf and vigilante functions
-- ============================================

local OlympusVigilante = {}

-- ============================================
-- DATABASE INITIALIZATION
-- ============================================

CreateThread(function()
    while not exports['olympus-core'] do
        Wait(100)
    end

    -- Initialize vigilante database tables
    exports['olympus-core']:ExecuteQuery([[
        CREATE TABLE IF NOT EXISTS vigilante_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            vigilante_id VARCHAR(50) NOT NULL,
            target_id VARCHAR(50) NOT NULL,
            bounty_amount INT NOT NULL,
            arrest_type ENUM('arrest', 'kill') NOT NULL,
            location VARCHAR(255),
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_vigilante (vigilante_id),
            INDEX idx_target (target_id),
            INDEX idx_timestamp (timestamp)
        )
    ]])

    exports['olympus-core']:ExecuteQuery([[
        CREATE TABLE IF NOT EXISTS vigilante_buddy_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            requester_id VARCHAR(50) NOT NULL,
            target_id VARCHAR(50) NOT NULL,
            status ENUM('pending', 'accepted', 'declined') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_requester (requester_id),
            INDEX idx_target (target_id)
        )
    ]])

    print("[Olympus Vigilante] Database initialized")
end)

-- ============================================
-- VIGILANTE ARREST SYSTEM
-- Based on original fn_vigiGetSetArrests.sqf
-- ============================================

function OlympusVigilante.GetSetArrests(mode, player)
    if not player or mode == -1 then return end

    local playerData = exports['olympus-core']:GetPlayerData(player)
    if not playerData then return end

    local identifier = playerData.identifier

    if mode == 0 then
        -- Fetch arrests on init
        exports['olympus-core']:ExecuteQuery('SELECT vigiarrests, vigiarrests_stored FROM players WHERE identifier = ?', {identifier}, function(result)
            if result and result[1] then
                local arrests = result[1].vigiarrests or 0
                local arrestsStored = result[1].vigiarrests_stored or 0

                print(string.format("[Vigilante] %s has %d arrests", identifier, arrests))

                -- Set client variables
                TriggerClientEvent('olympus-vigilante:setArrests', player, arrests)
                TriggerClientEvent('olympus-vigilante:setStoredArrests', player, arrestsStored)

                -- Set server variables
                Player(player).state.vigilanteArrests = arrests
                Player(player).state.vigilanteArrestsStored = arrestsStored
            end
        end)

    elseif mode == 1 then
        -- Add an arrest
        exports['olympus-core']:ExecuteQuery('UPDATE players SET vigiarrests = vigiarrests + 1 WHERE identifier = ?', {identifier})

        local currentArrests = Player(player).state.vigilanteArrests or 0
        local newArrests = currentArrests + 1

        TriggerClientEvent('olympus-vigilante:setArrests', player, newArrests)
        Player(player).state.vigilanteArrests = newArrests

        -- Check for tier progression
        OlympusVigilante.CheckTierProgression(player, newArrests)

    elseif mode == 2 then
        -- Down a tier (when arrested by APD)
        local currentArrests = Player(player).state.vigilanteArrests or 0
        local newArrestCount = 0

        if currentArrests >= 200 then
            newArrestCount = 100
        elseif currentArrests >= 100 then
            newArrestCount = 50
        elseif currentArrests >= 50 then
            newArrestCount = 25
        else
            newArrestCount = 0
        end

        TriggerClientEvent('olympus-vigilante:setArrests', player, newArrestCount)
        Player(player).state.vigilanteArrests = newArrestCount

        exports['olympus-core']:ExecuteQuery('UPDATE players SET vigiarrests = ? WHERE identifier = ?', {newArrestCount, identifier})

    elseif mode == 3 then
        -- Wipe arrests (admin action)
        TriggerClientEvent('olympus-vigilante:setArrests', player, 0)
        Player(player).state.vigilanteArrests = 0

        exports['olympus-core']:ExecuteQuery('UPDATE players SET vigiarrests = 0 WHERE identifier = ?', {identifier})

    elseif mode == 4 then
        -- Store arrests
        local currentArrests = Player(player).state.vigilanteArrests or 0

        Player(player).state.vigilanteArrestsStored = currentArrests
        TriggerClientEvent('olympus-vigilante:setStoredArrests', player, currentArrests)

        exports['olympus-core']:ExecuteQuery('UPDATE players SET vigiarrests_stored = ? WHERE identifier = ?', {currentArrests, identifier})

        print(string.format("[Vigilante] %s stored %d arrests", identifier, currentArrests))

        -- Clear current arrests
        Player(player).state.vigilanteArrests = 0
        TriggerClientEvent('olympus-vigilante:setArrests', player, 0)

        exports['olympus-core']:ExecuteQuery('UPDATE players SET vigiarrests = 0 WHERE identifier = ?', {identifier})

    elseif mode == 5 then
        -- Redeem stored arrests
        local storedArrests = Player(player).state.vigilanteArrestsStored or 0

        Player(player).state.vigilanteArrests = storedArrests
        TriggerClientEvent('olympus-vigilante:setArrests', player, storedArrests)

        Player(player).state.vigilanteArrestsStored = 0
        TriggerClientEvent('olympus-vigilante:setStoredArrests', player, 0)

        exports['olympus-core']:ExecuteQuery('UPDATE players SET vigiarrests = ?, vigiarrests_stored = 0 WHERE identifier = ?', {storedArrests, identifier})

        print(string.format("[Vigilante] %s claimed %d arrests", identifier, storedArrests))
    end
end

-- ============================================
-- TIER PROGRESSION SYSTEM
-- ============================================

function OlympusVigilante.CheckTierProgression(player, arrests)
    local tier = OlympusVigilante.GetVigilanteTier(arrests)
    local previousTier = OlympusVigilante.GetVigilanteTier(arrests - 1)

    if tier > previousTier then
        TriggerClientEvent('olympus-vigilante:tierProgression', player, tier, arrests)

        -- Send tier progression notification
        local tierInfo = {
            [1] = {name = "Tier 1", equipment = "P07"},
            [2] = {name = "Tier 2", equipment = "P07, ACP-C2, Sting"},
            [3] = {name = "Tier 3", equipment = "P07, ACP-C2, Sting, T3 Vest"},
            [4] = {name = "Tier 4", equipment = "P07, ACP-C2, Sting, T3 Vest, SPAR16, SPAR16-GL"},
            [5] = {name = "Tier 5", equipment = "P07, ACP-C2, Sting, T3 Vest, SPAR16, SPAR16-GL, SPAR16S, Athira Spawn"}
        }

        if tierInfo[tier] then
            TriggerClientEvent('olympus-core:notify', player,
                string.format("Rank Up: %s\n\nCongratulations! You have advanced to %s.\n\nNew Equipment: %s",
                    tierInfo[tier].name, tierInfo[tier].name, tierInfo[tier].equipment),
                'success', 10000)
        end
    end
end

function OlympusVigilante.GetVigilanteTier(arrests)
    if arrests >= 200 then
        return 5
    elseif arrests >= 100 then
        return 4
    elseif arrests >= 50 then
        return 3
    elseif arrests >= 25 then
        return 2
    else
        return 1
    end
end

-- ============================================
-- BOUNTY PROCESSING SYSTEM
-- Based on original bounty and arrest mechanics
-- ============================================

function OlympusVigilante.ProcessBounty(vigilanteId, targetId, bountyAmount, arrestType)
    local vigilanteData = exports['olympus-core']:GetPlayerData(vigilanteId)
    local targetData = exports['olympus-core']:GetPlayerData(targetId)

    if not vigilanteData or not targetData then return false end

    -- Check minimum bounty requirement ($75,000)
    if bountyAmount < 75000 then
        TriggerClientEvent('olympus-core:notify', vigilanteId,
            'You cannot arrest players with less than $75,000 bounty!', 'error')
        return false
    end

    -- Check if vigilante has license
    if not exports['olympus-licenses']:HasLicense(vigilanteId, 'vigilante') then
        TriggerClientEvent('olympus-core:notify', vigilanteId,
            'You need a vigilante license to arrest wanted players!', 'error')
        return false
    end

    -- Check if vigilante is wanted (cannot arrest while wanted)
    local vigilanteWanted = exports['olympus-apd']:GetPlayerBounty(vigilanteId)
    if vigilanteWanted and vigilanteWanted > 0 then
        TriggerClientEvent('olympus-core:notify', vigilanteId,
            'You cannot arrest players while you have a bounty!', 'error')
        return false
    end

    -- Check gang restrictions (cannot arrest gang members)
    local vigilanteGang = exports['olympus-gangs']:GetPlayerGang(vigilanteId)
    local targetGang = exports['olympus-gangs']:GetPlayerGang(targetId)

    if vigilanteGang and targetGang and vigilanteGang.name == targetGang.name then
        TriggerClientEvent('olympus-core:notify', vigilanteId,
            'You cannot arrest members of your own gang!', 'error')
        return false
    end

    -- Calculate bounty payout
    local payout = OlympusVigilante.CalculateBountyPayout(vigilanteId, bountyAmount, arrestType)

    -- Process buddy system split if applicable
    local buddyId = Player(vigilanteId).state.vigilanteBuddy
    if buddyId and GetPlayerPing(buddyId) > 0 then
        local buddySplit = math.floor(payout * 0.5)
        local vigilanteSplit = payout - buddySplit

        -- Give money to both vigilantes
        exports['olympus-core']:AddMoney(vigilanteId, 'bank', vigilanteSplit)
        exports['olympus-core']:AddMoney(buddyId, 'bank', buddySplit)

        TriggerClientEvent('olympus-core:notify', vigilanteId,
            string.format('Bounty collected: $%s (Buddy split)', exports['olympus-core']:GroupDigits(vigilanteSplit)), 'success')
        TriggerClientEvent('olympus-core:notify', buddyId,
            string.format('Buddy bounty share: $%s', exports['olympus-core']:GroupDigits(buddySplit)), 'success')
    else
        -- Give full payout to vigilante
        exports['olympus-core']:AddMoney(vigilanteId, 'bank', payout)

        TriggerClientEvent('olympus-core:notify', vigilanteId,
            string.format('Bounty collected: $%s', exports['olympus-core']:GroupDigits(payout)), 'success')
    end

    -- Add arrest to vigilante record
    OlympusVigilante.GetSetArrests(1, vigilanteId)

    -- Log the arrest
    local targetPos = GetEntityCoords(GetPlayerPed(targetId))
    exports['olympus-core']:ExecuteQuery([[
        INSERT INTO vigilante_logs (vigilante_id, target_id, bounty_amount, arrest_type, location)
        VALUES (?, ?, ?, ?, ?)
    ]], {
        vigilanteData.identifier,
        targetData.identifier,
        bountyAmount,
        arrestType,
        string.format("%.2f, %.2f, %.2f", targetPos.x, targetPos.y, targetPos.z)
    })

    -- Remove wanted status from target
    exports['olympus-apd']:RemoveWanted(targetId)

    return true
end

function OlympusVigilante.CalculateBountyPayout(vigilanteId, bountyAmount, arrestType)
    local basePayout = bountyAmount
    local tier = OlympusVigilante.GetVigilanteTier(Player(vigilanteId).state.vigilanteArrests or 0)

    -- Tier multipliers (higher tier = better payout)
    local tierMultipliers = {
        [1] = 1.0,
        [2] = 1.05,
        [3] = 1.10,
        [4] = 1.15,
        [5] = 1.20
    }

    -- Apply tier multiplier
    basePayout = math.floor(basePayout * (tierMultipliers[tier] or 1.0))

    -- Arrest type modifier (arrest gives full bounty, kill gives half)
    if arrestType == 'kill' then
        basePayout = math.floor(basePayout * 0.5)
    end

    -- Cap maximum payout at $5,000,000
    if basePayout > 5000000 then
        basePayout = 5000000
    end

    return basePayout
end

-- ============================================
-- BUDDY SYSTEM
-- Based on original fn_vigiBuddy.sqf
-- ============================================

function OlympusVigilante.SendBuddyRequest(requesterId, targetId)
    local requesterData = exports['olympus-core']:GetPlayerData(requesterId)
    local targetData = exports['olympus-core']:GetPlayerData(targetId)

    if not requesterData or not targetData then return false end

    -- Check if both players are vigilantes
    if not exports['olympus-licenses']:HasLicense(requesterId, 'vigilante') or
       not exports['olympus-licenses']:HasLicense(targetId, 'vigilante') then
        TriggerClientEvent('olympus-core:notify', requesterId,
            'Both players must have vigilante licenses!', 'error')
        return false
    end

    -- Check if target already has a buddy
    if Player(targetId).state.vigilanteBuddy then
        TriggerClientEvent('olympus-core:notify', requesterId,
            'That player already has a buddy agreement!', 'error')
        return false
    end

    -- Check if requester already has a buddy
    if Player(requesterId).state.vigilanteBuddy then
        TriggerClientEvent('olympus-core:notify', requesterId,
            'You already have a buddy agreement!', 'error')
        return false
    end

    -- Send buddy request to target
    TriggerClientEvent('olympus-vigilante:receiveBuddyRequest', targetId, requesterId, requesterData.name)

    TriggerClientEvent('olympus-core:notify', requesterId,
        string.format('Buddy request sent to %s', targetData.name), 'info')

    return true
end

function OlympusVigilante.AcceptBuddyRequest(targetId, requesterId)
    local requesterData = exports['olympus-core']:GetPlayerData(requesterId)
    local targetData = exports['olympus-core']:GetPlayerData(targetId)

    if not requesterData or not targetData then return false end

    -- Set buddy relationships
    Player(requesterId).state.vigilanteBuddy = targetId
    Player(targetId).state.vigilanteBuddy = requesterId

    TriggerClientEvent('olympus-core:notify', requesterId,
        string.format('Buddy request accepted by %s', targetData.name), 'success')
    TriggerClientEvent('olympus-core:notify', targetId,
        string.format('You are now buddied with %s', requesterData.name), 'success')

    return true
end

function OlympusVigilante.DeclineBuddyRequest(targetId, requesterId)
    local requesterData = exports['olympus-core']:GetPlayerData(requesterId)
    local targetData = exports['olympus-core']:GetPlayerData(targetId)

    if not requesterData or not targetData then return false end

    TriggerClientEvent('olympus-core:notify', requesterId,
        string.format('Buddy request declined by %s', targetData.name), 'error')

    return true
end

function OlympusVigilante.EndBuddyAgreement(playerId)
    local buddyId = Player(playerId).state.vigilanteBuddy

    if not buddyId then
        TriggerClientEvent('olympus-core:notify', playerId,
            'You do not have a buddy agreement!', 'error')
        return false
    end

    local playerData = exports['olympus-core']:GetPlayerData(playerId)
    local buddyData = exports['olympus-core']:GetPlayerData(buddyId)

    -- Clear buddy relationships
    Player(playerId).state.vigilanteBuddy = nil
    Player(buddyId).state.vigilanteBuddy = nil

    TriggerClientEvent('olympus-core:notify', playerId,
        string.format('Buddy agreement ended with %s', buddyData and buddyData.name or 'Unknown'), 'info')

    if GetPlayerPing(buddyId) > 0 then
        TriggerClientEvent('olympus-core:notify', buddyId,
            string.format('Buddy agreement ended by %s', playerData and playerData.name or 'Unknown'), 'info')
    end

    return true
end

-- ============================================
-- ARREST STORAGE SYSTEM
-- Based on original fn_storeVigilanteArrests.sqf
-- ============================================

function OlympusVigilante.CalculateStorageCost(arrests)
    local pricePerTier = 400000

    if arrests >= 200 then
        return pricePerTier * 5
    elseif arrests >= 100 then
        return pricePerTier * 4
    elseif arrests >= 50 then
        return pricePerTier * 3
    elseif arrests >= 25 then
        return pricePerTier * 2
    else
        return pricePerTier
    end
end

function OlympusVigilante.StoreArrests(playerId)
    local playerData = exports['olympus-core']:GetPlayerData(playerId)
    if not playerData then return false end

    local arrests = Player(playerId).state.vigilanteArrests or 0

    if arrests == 0 then
        TriggerClientEvent('olympus-core:notify', playerId,
            'You do not have any arrests to store!', 'error')
        return false
    end

    -- Check if player is wanted
    local bounty = exports['olympus-apd']:GetPlayerBounty(playerId)
    if bounty and bounty > 0 then
        TriggerClientEvent('olympus-core:notify', playerId,
            'You cannot store arrests while wanted!', 'error')
        return false
    end

    local storageCost = OlympusVigilante.CalculateStorageCost(arrests)
    local playerMoney = exports['olympus-core']:GetMoney(playerId, 'bank')

    if playerMoney < storageCost then
        TriggerClientEvent('olympus-core:notify', playerId,
            'You do not have enough money to store your arrests!', 'error')
        return false
    end

    -- Remove money and store arrests
    exports['olympus-core']:RemoveMoney(playerId, 'bank', storageCost)
    OlympusVigilante.GetSetArrests(4, playerId)

    TriggerClientEvent('olympus-core:notify', playerId,
        string.format('Stored %d arrests for $%s', arrests, exports['olympus-core']:GroupDigits(storageCost)), 'success')

    return true
end

function OlympusVigilante.ClaimStoredArrests(playerId)
    local storedArrests = Player(playerId).state.vigilanteArrestsStored or 0

    if storedArrests == 0 then
        TriggerClientEvent('olympus-core:notify', playerId,
            'You do not have any stored arrests to claim!', 'error')
        return false
    end

    OlympusVigilante.GetSetArrests(5, playerId)

    TriggerClientEvent('olympus-core:notify', playerId,
        string.format('Claimed %d arrests to your active license', storedArrests), 'success')

    return true
end

-- ============================================
-- EVENT HANDLERS
-- ============================================

-- Player initialization
RegisterNetEvent('olympus-vigilante:playerLoaded')
AddEventHandler('olympus-vigilante:playerLoaded', function()
    local source = source
    OlympusVigilante.GetSetArrests(0, source)
end)

-- Buddy system events
RegisterNetEvent('olympus-vigilante:sendBuddyRequest')
AddEventHandler('olympus-vigilante:sendBuddyRequest', function(targetId)
    local source = source
    OlympusVigilante.SendBuddyRequest(source, targetId)
end)

RegisterNetEvent('olympus-vigilante:acceptBuddyRequest')
AddEventHandler('olympus-vigilante:acceptBuddyRequest', function(requesterId)
    local source = source
    OlympusVigilante.AcceptBuddyRequest(source, requesterId)
end)

RegisterNetEvent('olympus-vigilante:declineBuddyRequest')
AddEventHandler('olympus-vigilante:declineBuddyRequest', function(requesterId)
    local source = source
    OlympusVigilante.DeclineBuddyRequest(source, requesterId)
end)

RegisterNetEvent('olympus-vigilante:endBuddyAgreement')
AddEventHandler('olympus-vigilante:endBuddyAgreement', function()
    local source = source
    OlympusVigilante.EndBuddyAgreement(source)
end)

-- Arrest storage events
RegisterNetEvent('olympus-vigilante:storeArrests')
AddEventHandler('olympus-vigilante:storeArrests', function()
    local source = source
    OlympusVigilante.StoreArrests(source)
end)

RegisterNetEvent('olympus-vigilante:claimStoredArrests')
AddEventHandler('olympus-vigilante:claimStoredArrests', function()
    local source = source
    OlympusVigilante.ClaimStoredArrests(source)
end)

-- Bounty processing event
RegisterNetEvent('olympus-vigilante:processBounty')
AddEventHandler('olympus-vigilante:processBounty', function(targetId, bountyAmount, arrestType)
    local source = source
    OlympusVigilante.ProcessBounty(source, targetId, bountyAmount, arrestType)
end)

-- Player disconnect cleanup
AddEventHandler('playerDropped', function()
    local source = source
    local buddyId = Player(source).state.vigilanteBuddy

    if buddyId then
        Player(buddyId).state.vigilanteBuddy = nil
        TriggerClientEvent('olympus-core:notify', buddyId,
            'Your vigilante buddy has disconnected', 'info')
    end
end)

-- ============================================
-- EXPORT FUNCTIONS
-- ============================================

exports('GetSetArrests', function(mode, player)
    return OlympusVigilante.GetSetArrests(mode, player)
end)

exports('ProcessBounty', function(vigilanteId, targetId, bountyAmount, arrestType)
    return OlympusVigilante.ProcessBounty(vigilanteId, targetId, bountyAmount, arrestType)
end)

exports('GetVigilanteTier', function(arrests)
    return OlympusVigilante.GetVigilanteTier(arrests)
end)

exports('GetVigilanteStats', function(playerId)
    return {
        arrests = Player(playerId).state.vigilanteArrests or 0,
        arrestsStored = Player(playerId).state.vigilanteArrestsStored or 0,
        tier = OlympusVigilante.GetVigilanteTier(Player(playerId).state.vigilanteArrests or 0),
        buddy = Player(playerId).state.vigilanteBuddy
    }
end)

exports('IsPlayerVigilante', function(playerId)
    return exports['olympus-licenses']:HasLicense(playerId, 'vigilante')
end)

exports('SendBuddyRequest', function(requesterId, targetId)
    return OlympusVigilante.SendBuddyRequest(requesterId, targetId)
end)

exports('StoreArrests', function(playerId)
    return OlympusVigilante.StoreArrests(playerId)
end)

exports('ClaimStoredArrests', function(playerId)
    return OlympusVigilante.ClaimStoredArrests(playerId)
end)

print("[Olympus Vigilante] Server system loaded - Complete implementation based on original Olympus functions")
