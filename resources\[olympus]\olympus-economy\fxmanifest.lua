fx_version 'cerulean'
game 'gta5'

name 'Olympus Economy System'
description 'Complete economy system for Olympus Framework'
author 'Olympus Development Team'
version '1.0.0'

dependencies {
    'olympus-core',
    'olympus-ui'
}

shared_scripts {
    'config/shared.lua'
}

server_scripts {
    'server/main.lua'
}

client_scripts {
    'client/main.lua'
}

-- Client exports
exports {
    'GetMarketPrice',
    'GetMarketPrices',
    'OpenShop',
    'OpenBlackmarket',
    'IsShopOpen'
}

-- Server exports
server_exports {
    'GetMarketPrice',
    'GetMarketPrices',
    'AddMarketSale',
    'IsEconomicEventActive',
    'GetActiveEconomicEvents',
    'CanAccessBlackmarket'
}
