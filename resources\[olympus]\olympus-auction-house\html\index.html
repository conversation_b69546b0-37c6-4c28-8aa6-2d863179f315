<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Olympus Auction House</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: transparent;
            color: white;
            margin: 0;
            padding: 0;
        }
        
        .auction-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            border-radius: 10px;
            padding: 20px;
            min-width: 600px;
            display: none;
        }
        
        .auction-container.show {
            display: block;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div id="auction-container" class="auction-container">
        <div class="header">
            <h2>Olympus Auction House</h2>
        </div>
        
        <div id="auction-content">
            <p>Auction house system loaded</p>
            <button class="btn" onclick="closeAuction()">Close</button>
        </div>
    </div>

    <script>
        function closeAuction() {
            document.getElementById('auction-container').classList.remove('show');
        }
        
        function GetParentResourceName() {
            return 'olympus-auction-house';
        }
    </script>
</body>
</html>
