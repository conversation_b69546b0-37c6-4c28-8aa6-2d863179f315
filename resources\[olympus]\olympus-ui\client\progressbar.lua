-- ========================================
-- OLYMPUS UI FRAMEWORK - PROGRESS BAR
-- Based on Original Olympus Altis Life
-- ========================================

local OlympusProgressBar = {}
local currentProgressBar = nil
local progressBarActive = false

-- ========================================
-- PROGRESS BAR FUNCTIONS
-- ========================================

function OlympusProgressBar.Show(config, onComplete, onCancel)
    if progressBarActive then
        OlympusProgressBar.Hide()
    end
    
    -- Default configuration
    local defaultConfig = {
        text = "Processing...",
        duration = 5000,
        canCancel = true,
        disableMovement = true,
        disableCarMovement = true,
        disableMouse = false,
        disableCombat = true,
        animation = nil,
        animationDict = nil
    }
    
    -- Merge with provided config
    if type(config) == "string" then
        config = { text = config }
    end
    
    config = config or {}
    for k, v in pairs(defaultConfig) do
        if config[k] == nil then
            config[k] = v
        end
    end
    
    progressBarActive = true
    currentProgressBar = {
        config = config,
        onComplete = onComplete,
        onCancel = onCancel,
        startTime = GetGameTimer()
    }
    
    -- Show progress bar in UI
    exports['olympus-ui']:ShowProgressBar(config)
    
    -- Start progress bar logic
    OlympusProgressBar.StartProgressLogic()
    
    -- Play animation if specified
    if config.animation and config.animationDict then
        OlympusProgressBar.PlayAnimation(config.animationDict, config.animation)
    end
end

function OlympusProgressBar.Hide()
    if not progressBarActive then return end
    
    progressBarActive = false
    
    -- Hide progress bar in UI
    exports['olympus-ui']:HideProgressBar()
    
    -- Stop animation
    OlympusProgressBar.StopAnimation()
    
    -- Reset controls
    OlympusProgressBar.ResetControls()
    
    currentProgressBar = nil
end

function OlympusProgressBar.StartProgressLogic()
    CreateThread(function()
        while progressBarActive and currentProgressBar do
            local config = currentProgressBar.config
            local elapsed = GetGameTimer() - currentProgressBar.startTime
            local progress = math.min(elapsed / config.duration, 1.0)
            
            -- Handle controls
            OlympusProgressBar.HandleControls(config)
            
            -- Check for completion
            if progress >= 1.0 then
                progressBarActive = false
                
                -- Call completion callback
                if currentProgressBar.onComplete then
                    currentProgressBar.onComplete()
                end
                
                -- Hide progress bar
                OlympusProgressBar.Hide()
                break
            end
            
            -- Check for cancellation
            if config.canCancel and OlympusProgressBar.CheckCancellation() then
                progressBarActive = false
                
                -- Call cancel callback
                if currentProgressBar.onCancel then
                    currentProgressBar.onCancel()
                end
                
                -- Hide progress bar
                OlympusProgressBar.Hide()
                break
            end
            
            Wait(0)
        end
    end)
end

function OlympusProgressBar.HandleControls(config)
    local playerPed = PlayerPedId()
    
    -- Disable movement
    if config.disableMovement then
        DisableControlAction(0, 30, true) -- A/D
        DisableControlAction(0, 31, true) -- W/S
        DisableControlAction(0, 21, true) -- Sprint
        DisableControlAction(0, 22, true) -- Jump
        DisableControlAction(0, 36, true) -- Ctrl (Duck)
    end
    
    -- Disable car movement
    if config.disableCarMovement and IsPedInAnyVehicle(playerPed, false) then
        DisableControlAction(0, 71, true) -- W (Accelerate)
        DisableControlAction(0, 72, true) -- S (Brake/Reverse)
        DisableControlAction(0, 63, true) -- A (Turn Left)
        DisableControlAction(0, 64, true) -- D (Turn Right)
        DisableControlAction(0, 75, true) -- F (Exit Vehicle)
    end
    
    -- Disable combat
    if config.disableCombat then
        DisableControlAction(0, 24, true) -- Attack
        DisableControlAction(0, 25, true) -- Aim
        DisableControlAction(0, 47, true) -- Weapon Wheel
        DisableControlAction(0, 58, true) -- Weapon Wheel
        DisableControlAction(0, 140, true) -- Melee Attack Light
        DisableControlAction(0, 141, true) -- Melee Attack Heavy
        DisableControlAction(0, 142, true) -- Melee Attack Alternate
        DisableControlAction(0, 143, true) -- Melee Block
    end
    
    -- Disable mouse
    if config.disableMouse then
        DisableControlAction(0, 1, true) -- Mouse Look LR
        DisableControlAction(0, 2, true) -- Mouse Look UD
    end
end

function OlympusProgressBar.CheckCancellation()
    -- Check for ESC key
    if IsControlJustPressed(0, 322) then -- ESC
        return true
    end
    
    -- Check for X key
    if IsControlJustPressed(0, 73) then -- X
        return true
    end
    
    -- Check if player is taking damage
    local playerPed = PlayerPedId()
    if HasEntityBeenDamagedByAnyPed(playerPed) then
        ClearEntityLastDamageEntity(playerPed)
        return true
    end
    
    -- Check if player moved too much (if movement is disabled)
    if currentProgressBar and currentProgressBar.config.disableMovement then
        if not currentProgressBar.startPosition then
            currentProgressBar.startPosition = GetEntityCoords(playerPed)
        else
            local currentPos = GetEntityCoords(playerPed)
            local distance = #(currentPos - currentProgressBar.startPosition)
            if distance > 2.0 then -- Moved more than 2 meters
                return true
            end
        end
    end
    
    return false
end

function OlympusProgressBar.PlayAnimation(dict, anim, flags)
    local playerPed = PlayerPedId()
    
    RequestAnimDict(dict)
    while not HasAnimDictLoaded(dict) do
        Wait(10)
    end
    
    TaskPlayAnim(playerPed, dict, anim, 8.0, -8.0, -1, flags or 1, 0, false, false, false)
end

function OlympusProgressBar.StopAnimation()
    local playerPed = PlayerPedId()
    ClearPedTasks(playerPed)
end

function OlympusProgressBar.ResetControls()
    -- Re-enable all controls (they will be re-disabled by other systems if needed)
    for i = 0, 350 do
        EnableControlAction(0, i, true)
    end
end

-- ========================================
-- INTERRUPTABLE ACTION SYSTEM
-- ========================================

function OlympusProgressBar.InterruptableAction(config, onComplete, onCancel)
    -- Enhanced version of the original fn_interruptableAction.sqf
    
    if type(config) == "string" then
        config = {
            text = config,
            duration = 5000
        }
    end
    
    -- Add default interruption settings
    config.canCancel = true
    config.disableMovement = true
    config.disableCombat = true
    
    OlympusProgressBar.Show(config, onComplete, onCancel)
end

-- ========================================
-- EXPORTS
-- ========================================

exports('ShowProgressBar', OlympusProgressBar.Show)
exports('HideProgressBar', OlympusProgressBar.Hide)
exports('InterruptableAction', OlympusProgressBar.InterruptableAction)
exports('IsProgressBarActive', function() return progressBarActive end)

-- ========================================
-- EVENT HANDLERS
-- ========================================

RegisterNetEvent('olympus-progressbar:client:show', function(config, onComplete, onCancel)
    OlympusProgressBar.Show(config, onComplete, onCancel)
end)

RegisterNetEvent('olympus-progressbar:client:hide', function()
    OlympusProgressBar.Hide()
end)

RegisterNetEvent('olympus-progressbar:client:interruptableAction', function(config, onComplete, onCancel)
    OlympusProgressBar.InterruptableAction(config, onComplete, onCancel)
end)

-- Handle progress bar completion from UI
RegisterNetEvent('olympus-ui:client:progressBarComplete', function()
    if progressBarActive and currentProgressBar and currentProgressBar.onComplete then
        currentProgressBar.onComplete()
    end
end)

-- Handle progress bar cancellation from UI
RegisterNetEvent('olympus-ui:client:progressBarCancelled', function()
    if progressBarActive and currentProgressBar and currentProgressBar.onCancel then
        currentProgressBar.onCancel()
    end
end)

-- ========================================
-- COMPATIBILITY FUNCTIONS
-- ========================================

-- For compatibility with existing scripts
function life_fnc_interruptableAction(text, duration, onComplete, onCancel)
    OlympusProgressBar.InterruptableAction({
        text = text,
        duration = duration or 5000
    }, onComplete, onCancel)
end

-- Export for global access
_G.life_fnc_interruptableAction = life_fnc_interruptableAction
