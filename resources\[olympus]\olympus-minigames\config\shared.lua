-- Olympus Mini-Games & Peripheral Systems - Complete Implementation
-- Based on Olympus Altis Life interactive mechanics and skill-based systems

Config = {}

-- Mini-Games System Settings
Config.MiniGames = {
    enabled = true,
    
    -- Skill-based mechanics
    skillBased = true, -- Success depends on player skill
    difficultyScaling = true, -- Difficulty scales with value/risk
    timeConstraints = true, -- Time limits for completion
    
    -- Failure consequences
    failureConsequences = {
        alertAPD = true, -- Some failures alert APD
        itemLoss = true, -- May lose items on failure
        cooldownPenalty = true -- Cooldown before retry
    }
}

-- Lockpicking System (Exact Olympus Implementation)
Config.Lockpicking = {
    enabled = true,
    
    -- Vehicle Lockpicking
    vehicles = {
        enabled = true,
        
        -- Requirements
        requirements = {
            item = 'lockpick', -- Lockpick item required
            proximity = 2, -- Must be within 2m of vehicle
            noOwnerNearby = 50, -- Owner must be 50m+ away
            
            -- Restrictions
            restrictions = {
                noAPDVehicles = true, -- Cannot lockpick APD vehicles
                noRnRVehicles = true, -- Cannot lockpick R&R vehicles
                noAdminVehicles = true -- Cannot lockpick admin vehicles
            }
        },
        
        -- Mini-Game Mechanics
        mechanics = {
            type = 'sequence_matching', -- Sequence matching mini-game
            difficulty = 'medium',
            timeLimit = 30, -- 30 seconds to complete
            attempts = 3, -- 3 attempts before lockpick breaks
            
            -- Success/Failure
            successRate = {
                base = 0.7, -- 70% base success rate
                skillModifier = true, -- Improves with practice
                vehicleTypeModifier = true -- Harder for expensive vehicles
            },
            
            failureConsequences = {
                breakLockpick = 0.3, -- 30% chance to break lockpick
                alertOwner = false, -- Don't alert owner
                alertAPD = false, -- Don't alert APD
                cooldown = 10 -- 10 second cooldown
            }
        },
        
        -- Vehicle Type Modifiers
        typeModifiers = {
            civilian = 1.0, -- Normal difficulty
            sports = 0.8, -- 20% harder
            super = 0.6, -- 40% harder
            military = 0.4 -- 60% harder
        }
    },
    
    -- House Lockpicking
    houses = {
        enabled = true,
        
        requirements = {
            item = 'advanced_lockpick', -- Advanced lockpick required
            proximity = 3, -- Must be within 3m of door
            noOwnerOnline = true, -- Owner must be offline
            
            restrictions = {
                keyHoldersOnline = false, -- Cannot pick if key holders online
                recentActivity = 1800 -- Cannot pick if owner active in last 30min
            }
        },
        
        mechanics = {
            type = 'pin_tumbler', -- Pin tumbler mini-game
            difficulty = 'hard',
            timeLimit = 60, -- 60 seconds to complete
            attempts = 2, -- 2 attempts before advanced lockpick breaks
            
            successRate = {
                base = 0.5, -- 50% base success rate
                houseTypeModifier = true, -- Harder for expensive houses
                securitySystemModifier = true -- Much harder with security
            },
            
            failureConsequences = {
                breakLockpick = 0.5, -- 50% chance to break lockpick
                alertAPD = 0.3, -- 30% chance to alert APD
                cooldown = 300 -- 5 minute cooldown
            }
        }
    },
    
    -- Safe Lockpicking
    safes = {
        enabled = true,
        
        requirements = {
            item = 'electronic_lockpick', -- Electronic lockpick required
            proximity = 1, -- Must be within 1m of safe
            
            restrictions = {
                federalSafes = false, -- Cannot pick federal safes (need explosives)
                bankSafes = false -- Cannot pick bank safes
            }
        },
        
        mechanics = {
            type = 'combination_cracking', -- Combination cracking mini-game
            difficulty = 'very_hard',
            timeLimit = 120, -- 2 minutes to complete
            attempts = 1, -- 1 attempt only
            
            successRate = {
                base = 0.3, -- 30% base success rate
                safeValueModifier = true -- Harder for valuable safes
            },
            
            failureConsequences = {
                breakLockpick = 0.8, -- 80% chance to break lockpick
                alertAPD = 0.6, -- 60% chance to alert APD
                cooldown = 600 -- 10 minute cooldown
            }
        }
    }
}

-- Hacking System (Exact Olympus Implementation)
Config.Hacking = {
    enabled = true,
    
    -- Terminal Hacking (Federal Events)
    terminals = {
        enabled = true,
        
        -- Federal Reserve Terminals
        federalReserve = {
            requirements = {
                item = 'hacking_device', -- Hacking device required
                proximity = 2, -- Must be within 2m of terminal
                
                restrictions = {
                    federalEventActive = true, -- Only during federal events
                    apdPresence = false -- Can hack with APD present
                }
            },
            
            mechanics = {
                type = 'circuit_completion', -- Circuit completion mini-game
                difficulty = 'very_hard',
                timeLimit = 45, -- 45 seconds to complete
                attempts = 3, -- 3 attempts before device breaks
                
                successRate = {
                    base = 0.4, -- 40% base success rate
                    skillModifier = true,
                    pressureModifier = true -- Harder under pressure
                },
                
                effects = {
                    disableAntiAir = true, -- Disables anti-air missiles
                    disableDuration = 300, -- 5 minutes disabled
                    alertAPD = true, -- Always alerts APD
                    cooldown = 60 -- 1 minute cooldown between attempts
                }
            }
        },
        
        -- Evidence Lockup Terminals
        evidenceLockup = {
            requirements = {
                item = 'police_hacking_device', -- Special police hacking device
                proximity = 2,
                
                restrictions = {
                    evidenceEventActive = true,
                    minimumRebels = 5 -- Need 5+ rebels online
                }
            },
            
            mechanics = {
                type = 'password_cracking', -- Password cracking mini-game
                difficulty = 'hard',
                timeLimit = 60, -- 60 seconds to complete
                attempts = 2,
                
                successRate = {
                    base = 0.6, -- 60% base success rate
                    skillModifier = true
                },
                
                effects = {
                    unlockEvidence = true, -- Unlocks evidence storage
                    unlockDuration = 600, -- 10 minutes unlocked
                    alertAPD = true,
                    cooldown = 120 -- 2 minute cooldown
                }
            }
        }
    },
    
    -- GPS Disruption Hacking
    gpsDisruption = {
        enabled = true,
        
        requirements = {
            item = 'signal_jammer', -- Signal jammer required
            vehicle = true, -- Must be in vehicle
            
            restrictions = {
                movingVehicle = false, -- Cannot use while moving
                apdNearby = 100 -- Cannot use if APD within 100m
            }
        },
        
        mechanics = {
            type = 'frequency_matching', -- Frequency matching mini-game
            difficulty = 'medium',
            timeLimit = 20, -- 20 seconds to complete
            attempts = 5, -- 5 attempts before jammer breaks
            
            successRate = {
                base = 0.8, -- 80% base success rate
                skillModifier = true
            },
            
            effects = {
                disableGPS = true, -- Disables GPS tracking
                disableDuration = 180, -- 3 minutes disabled
                jammerRadius = 500, -- 500m radius
                alertAPD = false, -- Doesn't alert APD
                cooldown = 30 -- 30 second cooldown
            }
        }
    }
}

-- Safe Cracking System (Exact Olympus Implementation)
Config.SafeCracking = {
    enabled = true,
    
    -- Bank Safes
    bankSafes = {
        enabled = true,
        
        requirements = {
            item = 'thermal_lance', -- Thermal lance required
            proximity = 1, -- Must be within 1m of safe
            
            restrictions = {
                bankRobberyActive = true, -- Only during bank robberies
                minimumRebels = 8 -- Need 8+ rebels for bank robbery
            }
        },
        
        mechanics = {
            type = 'thermal_cutting', -- Thermal cutting mini-game
            difficulty = 'extreme',
            timeLimit = 180, -- 3 minutes to complete
            attempts = 1, -- 1 attempt only
            
            successRate = {
                base = 0.2, -- 20% base success rate
                skillModifier = true,
                teamworkBonus = true -- Bonus with multiple players
            },
            
            effects = {
                openSafe = true, -- Opens bank safe
                alertAPD = true, -- Always alerts APD
                noiseRadius = 1000, -- Can be heard 1km away
                cooldown = 3600 -- 1 hour cooldown
            }
        }
    },
    
    -- House Safes
    houseSafes = {
        enabled = true,
        
        requirements = {
            item = 'drill', -- Drill required
            proximity = 1,
            
            restrictions = {
                ownerOffline = true, -- Owner must be offline
                keyHoldersOffline = true -- Key holders must be offline
            }
        },
        
        mechanics = {
            type = 'pressure_drilling', -- Pressure drilling mini-game
            difficulty = 'hard',
            timeLimit = 90, -- 90 seconds to complete
            attempts = 2,
            
            successRate = {
                base = 0.4, -- 40% base success rate
                skillModifier = true
            },
            
            effects = {
                openSafe = true,
                alertAPD = 0.4, -- 40% chance to alert APD
                noiseRadius = 200, -- Can be heard 200m away
                cooldown = 600 -- 10 minute cooldown
            }
        }
    }
}

-- Evidence Planting/Destroying System (Exact Olympus Implementation)
Config.EvidenceSystem = {
    enabled = true,

    -- Evidence Planting
    planting = {
        enabled = true,

        requirements = {
            item = 'planted_evidence', -- Planted evidence item
            proximity = 5, -- Must be within 5m of target

            restrictions = {
                targetOffline = false, -- Can plant on online players
                apdNearby = 50, -- Cannot plant if APD within 50m
                witnessesNearby = 25 -- Cannot plant if witnesses within 25m
            }
        },

        mechanics = {
            type = 'stealth_planting', -- Stealth planting mini-game
            difficulty = 'medium',
            timeLimit = 15, -- 15 seconds to complete
            attempts = 1, -- 1 attempt only

            successRate = {
                base = 0.7, -- 70% base success rate
                skillModifier = true,
                stealthModifier = true -- Harder if target is alert
            },

            effects = {
                plantEvidence = true, -- Plants evidence on target
                evidenceTypes = {
                    'illegal_weapon',
                    'drug_possession',
                    'stolen_goods',
                    'contraband'
                },
                alertTarget = false, -- Target not immediately alerted
                cooldown = 300 -- 5 minute cooldown
            }
        }
    },

    -- Evidence Destroying
    destroying = {
        enabled = true,

        requirements = {
            item = 'evidence_destroyer', -- Evidence destroyer item
            proximity = 2, -- Must be within 2m of evidence

            restrictions = {
                apdNearby = 100, -- Cannot destroy if APD within 100m
                federalEvidence = false -- Cannot destroy federal evidence
            }
        },

        mechanics = {
            type = 'chemical_dissolution', -- Chemical dissolution mini-game
            difficulty = 'hard',
            timeLimit = 30, -- 30 seconds to complete
            attempts = 2,

            successRate = {
                base = 0.5, -- 50% base success rate
                evidenceTypeModifier = true, -- Harder for certain evidence
                skillModifier = true
            },

            effects = {
                destroyEvidence = true, -- Destroys evidence
                alertAPD = 0.3, -- 30% chance to alert APD
                forensicTrace = 0.2, -- 20% chance to leave forensic trace
                cooldown = 600 -- 10 minute cooldown
            }
        }
    }
}

-- Crafting System (Exact Olympus Implementation)
Config.CraftingSystem = {
    enabled = true,

    -- Drug Crafting Stations
    drugCrafting = {
        enabled = true,

        -- Cocaine Lab Equipment
        cocaineLab = {
            requirements = {
                items = {
                    'coca_leaves',
                    'chemical_catalyst',
                    'lab_equipment'
                },
                location = 'cocaine_processor',
                proximity = 10
            },

            mechanics = {
                type = 'chemical_mixing', -- Chemical mixing mini-game
                difficulty = 'medium',
                timeLimit = 45, -- 45 seconds per batch
                batchSize = 10, -- 10 items per batch

                successRate = {
                    base = 0.8, -- 80% base success rate
                    skillModifier = true,
                    equipmentQuality = true -- Better equipment = higher success
                },

                effects = {
                    produceItem = 'cocaine',
                    yieldModifier = 1.0, -- 1:1 ratio base
                    qualityVariance = true, -- Quality affects price
                    explosionRisk = 0.05 -- 5% chance of explosion on failure
                }
            }
        },

        -- Meth Lab Equipment
        methLab = {
            requirements = {
                items = {
                    'pseudoephedrine',
                    'lithium_strips',
                    'anhydrous_ammonia',
                    'cooking_equipment'
                },
                location = 'meth_processor',
                proximity = 10
            },

            mechanics = {
                type = 'temperature_control', -- Temperature control mini-game
                difficulty = 'hard',
                timeLimit = 60, -- 60 seconds per batch
                batchSize = 8,

                successRate = {
                    base = 0.6, -- 60% base success rate
                    skillModifier = true,
                    temperatureControl = true -- Precise temperature control needed
                },

                effects = {
                    produceItem = 'meth',
                    yieldModifier = 1.0,
                    qualityVariance = true,
                    explosionRisk = 0.15, -- 15% chance of explosion on failure
                    toxicGas = 0.1 -- 10% chance of toxic gas release
                }
            }
        }
    },

    -- Bomb Crafting
    bombCrafting = {
        enabled = true,

        requirements = {
            items = {
                'explosive_material',
                'detonator',
                'timer_device',
                'wiring_kit'
            },
            location = 'rebel_outpost',
            proximity = 15,

            restrictions = {
                rebelLicense = true, -- Must have rebel license
                minimumRank = 'trusted_rebel' -- Must be trusted rebel
            }
        },

        mechanics = {
            type = 'bomb_assembly', -- Bomb assembly mini-game
            difficulty = 'very_hard',
            timeLimit = 120, -- 2 minutes to complete
            attempts = 1, -- 1 attempt only

            successRate = {
                base = 0.3, -- 30% base success rate
                skillModifier = true,
                precisionRequired = true -- Requires precise timing
            },

            effects = {
                produceItem = 'improvised_explosive',
                explosionRisk = 0.25, -- 25% chance of explosion on failure
                injuryRisk = 0.4, -- 40% chance of injury on explosion
                cooldown = 1800 -- 30 minute cooldown
            }
        }
    }
}
