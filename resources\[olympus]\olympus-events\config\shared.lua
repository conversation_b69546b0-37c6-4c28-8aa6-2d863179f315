-- Olympus Events & Activities System - Complete Implementation
-- Based on Olympus Altis Life dynamic events and scheduled activities

Config = {}

-- Events System Settings
Config.EventsSystem = {
    enabled = true,

    -- Dynamic event spawning
    dynamicEvents = true, -- Events spawn automatically
    scheduledEvents = true, -- Staff can schedule events
    playerTriggeredEvents = true, -- Players can trigger certain events

    -- Event balancing
    balancing = {
        playerCountScaling = true, -- Events scale with player count
        factionBalancing = true, -- Consider faction balance
        cooldownSystem = true -- Prevent event spam
    }
}

-- Airdrop System (Exact Olympus Implementation)
Config.AirdropSystem = {
    enabled = true,

    -- Airdrop Mechanics
    mechanics = {
        -- Spawn Conditions
        spawnConditions = {
            minPlayers = 15, -- Minimum 15 players online
            maxAirdrops = 2, -- Maximum 2 airdrops at once
            spawnInterval = {min = 1800, max = 3600}, -- 30-60 minutes between airdrops

            -- Location Requirements
            locationRequirements = {
                minDistanceFromCities = 1000, -- 1km from cities
                minDistanceFromSpawn = 2000, -- 2km from spawn
                avoidWater = true, -- Don't spawn over water
                flatTerrain = true -- Prefer flat terrain
            }
        },

        -- Airdrop Process
        process = {
            announcementDelay = 300, -- 5 minutes before announcement
            flightTime = 180, -- 3 minutes flight time
            parachuteTime = 60, -- 1 minute parachute descent

            -- Visual/Audio Effects
            effects = {
                aircraftSound = true, -- Aircraft flyover sound
                parachuteVisual = true, -- Visible parachute
                smokeSignal = true, -- Smoke signal on landing
                mapMarker = true -- Map marker appears on landing
            }
        },

        -- Airdrop Contents
        contents = {
            -- Loot Tables
            lootTables = {
                weapons = {
                    weight = 0.3, -- 30% chance
                    items = {
                        {item = 'mk20', quantity = 1, rarity = 'common'},
                        {item = 'mx', quantity = 1, rarity = 'uncommon'},
                        {item = 'spar16', quantity = 1, rarity = 'rare'},
                        {item = 'katiba', quantity = 1, rarity = 'very_rare'}
                    }
                },
                equipment = {
                    weight = 0.4, -- 40% chance
                    items = {
                        {item = 'nvg', quantity = 1, rarity = 'uncommon'},
                        {item = 'rangefinder', quantity = 1, rarity = 'rare'},
                        {item = 'toolkit', quantity = 5, rarity = 'common'},
                        {item = 'first_aid', quantity = 10, rarity = 'common'}
                    }
                },
                money = {
                    weight = 0.2, -- 20% chance
                    amounts = {
                        {amount = 50000, rarity = 'common'},
                        {amount = 100000, rarity = 'uncommon'},
                        {amount = 250000, rarity = 'rare'},
                        {amount = 500000, rarity = 'very_rare'}
                    }
                },
                special = {
                    weight = 0.1, -- 10% chance
                    items = {
                        {item = 'gold_bar', quantity = 5, rarity = 'rare'},
                        {item = 'diamond', quantity = 10, rarity = 'rare'},
                        {item = 'special_weapon', quantity = 1, rarity = 'legendary'}
                    }
                }
            }
        },

        -- Competition Mechanics
        competition = {
            enabled = true,

            -- Access Control
            accessControl = {
                firstComeFirstServe = true, -- First player to reach gets priority
                contestable = true, -- Other players can contest
                timeLimit = 600, -- 10 minutes to claim

                -- Combat Zone
                combatZone = {
                    radius = 500, -- 500m combat zone around airdrop
                    kosRules = true, -- KOS rules apply in zone
                    noSafeZone = true -- No safe zones near airdrops
                }
            }
        }
    },

    -- Holiday Special Airdrops
    holidayAirdrops = {
        enabled = true,

        -- Halloween Airdrops
        halloween = {
            enabled = true,
            period = {start = '10-25', ['end'] = '11-02'}, -- Oct 25 - Nov 2

            specialContents = {
                {item = 'pumpkin_mask', quantity = 1, rarity = 'rare'},
                {item = 'halloween_costume', quantity = 1, rarity = 'uncommon'},
                {item = 'candy', quantity = 50, rarity = 'common'},
                {item = 'spooky_weapon_skin', quantity = 1, rarity = 'very_rare'}
            },

            effects = {
                spookyEffects = true, -- Special visual effects
                halloweenMusic = true, -- Halloween music
                pumpkinParachute = true -- Pumpkin-themed parachute
            }
        },

        -- Christmas Airdrops
        christmas = {
            enabled = true,
            period = {start = '12-20', ['end'] = '01-05'}, -- Dec 20 - Jan 5

            specialContents = {
                {item = 'santa_hat', quantity = 1, rarity = 'uncommon'},
                {item = 'christmas_sweater', quantity = 1, rarity = 'common'},
                {item = 'gift_box', quantity = 10, rarity = 'rare'},
                {item = 'festive_weapon_skin', quantity = 1, rarity = 'very_rare'}
            },

            effects = {
                snowEffect = true, -- Snow particle effects
                christmasMusic = true, -- Christmas music
                santaSleigh = true -- Santa sleigh instead of aircraft
            }
        }
    }
}

-- Convoy Ambush System (Exact Olympus Implementation)
Config.ConvoySystem = {
    enabled = true,

    -- Convoy Mechanics
    mechanics = {
        -- Spawn Conditions
        spawnConditions = {
            minPlayers = 20, -- Minimum 20 players online
            minRebels = 8, -- Minimum 8 rebels online
            maxConvoys = 1, -- Maximum 1 convoy at once
            spawnInterval = {min = 2700, max = 5400}, -- 45-90 minutes between convoys

            -- Route Requirements
            routeRequirements = {
                startLocation = 'military_base', -- Start at military base
                endLocation = 'federal_reserve', -- End at federal reserve
                routeLength = {min = 8000, max = 15000}, -- 8-15km route
                avoidCities = false -- Can pass through cities
            }
        },

        -- Convoy Composition
        composition = {
            vehicles = {
                {type = 'hunter_hmg', quantity = 1, position = 'lead'}, -- Lead vehicle
                {type = 'tempest_transport', quantity = 2, position = 'cargo'}, -- Cargo trucks
                {type = 'hunter_hmg', quantity = 1, position = 'rear'} -- Rear guard
            },

            -- AI Crew
            aiCrew = {
                skill = 0.8, -- High skill AI
                equipment = 'military_grade', -- Military equipment
                behavior = 'defensive', -- Defensive behavior

                -- Crew Composition
                crewPerVehicle = {
                    hunter_hmg = 3, -- Driver + gunner + commander
                    tempest_transport = 2 -- Driver + passenger
                }
            }
        },

        -- Convoy Cargo
        cargo = {
            -- Cargo Types
            types = {
                weapons = {
                    weight = 0.4, -- 40% chance
                    value = {min = 500000, max = 1000000}, -- $500k-$1M value
                    items = {
                        'military_rifles',
                        'heavy_weapons',
                        'ammunition',
                        'explosives'
                    }
                },
                money = {
                    weight = 0.3, -- 30% chance
                    value = {min = 750000, max = 1500000}, -- $750k-$1.5M
                    items = {
                        'cash_bundles',
                        'gold_bars',
                        'bearer_bonds'
                    }
                },
                intel = {
                    weight = 0.2, -- 20% chance
                    value = {min = 300000, max = 800000}, -- $300k-$800k
                    items = {
                        'classified_documents',
                        'hard_drives',
                        'encryption_keys'
                    }
                },
                special = {
                    weight = 0.1, -- 10% chance
                    value = {min = 1000000, max = 2000000}, -- $1M-$2M
                    items = {
                        'prototype_weapons',
                        'rare_materials',
                        'government_secrets'
                    }
                }
            }
        },

        -- Ambush Mechanics
        ambush = {
            enabled = true,

            -- Ambush Zones
            zones = {
                -- High-risk ambush locations
                locations = {
                    {name = 'Mountain Pass', coords = vector3(2500, 3000, 100), risk = 'high'},
                    {name = 'Forest Road', coords = vector3(1800, 2200, 50), risk = 'medium'},
                    {name = 'Desert Highway', coords = vector3(3200, 1500, 25), risk = 'low'}
                },

                -- Ambush Requirements
                requirements = {
                    minRebels = 5, -- Minimum 5 rebels to ambush
                    maxDistance = 1000, -- Must be within 1km of convoy
                    preparationTime = 300, -- 5 minutes to prepare ambush

                    -- Equipment Requirements
                    equipment = {
                        explosives = true, -- Need explosives to stop convoy
                        heavyWeapons = true, -- Need heavy weapons
                        vehicles = true -- Need vehicles for escape
                    }
                }
            },

            -- Ambush Rewards
            rewards = {
                successBonus = 0.5, -- 50% bonus for successful ambush
                teamBonus = true, -- Bonus split among team
                escapeBonus = 0.2, -- 20% bonus for successful escape

                -- Risk/Reward Scaling
                scaling = {
                    apdResponse = true, -- Higher reward if APD responds
                    convoyStrength = true, -- Higher reward for stronger convoys
                    ambushDifficulty = true -- Higher reward for difficult ambushes
                }
            }
        }
    }
}

-- Illegal Racing System (Exact Olympus Implementation)
Config.RacingSystem = {
    enabled = true,

    -- Race Mechanics
    mechanics = {
        -- Race Types
        raceTypes = {
            streetRacing = {
                enabled = true,
                minParticipants = 4, -- Minimum 4 racers
                maxParticipants = 12, -- Maximum 12 racers
                entryFee = 25000, -- $25,000 entry fee

                -- Race Tracks
                tracks = {
                    {
                        name = 'Kavala Circuit',
                        length = 8000, -- 8km track
                        difficulty = 'medium',
                        checkpoints = 12,
                        prize = 200000 -- $200,000 winner prize
                    },
                    {
                        name = 'Mountain Rally',
                        length = 12000, -- 12km track
                        difficulty = 'hard',
                        checkpoints = 18,
                        prize = 350000 -- $350,000 winner prize
                    },
                    {
                        name = 'Desert Sprint',
                        length = 6000, -- 6km track
                        difficulty = 'easy',
                        checkpoints = 8,
                        prize = 150000 -- $150,000 winner prize
                    }
                }
            },

            dragRacing = {
                enabled = true,
                minParticipants = 2,
                maxParticipants = 8,
                entryFee = 50000, -- $50,000 entry fee

                tracks = {
                    {
                        name = 'Airport Runway',
                        length = 2000, -- 2km straight
                        difficulty = 'medium',
                        prize = 300000 -- $300,000 winner prize
                    }
                }
            }
        },

        -- Race Organization
        organization = {
            playerOrganized = true, -- Players can organize races
            scheduledRaces = true, -- Staff can schedule races

            -- Organization Requirements
            requirements = {
                organizerFee = 100000, -- $100,000 to organize race
                minimumPot = 200000, -- $200,000 minimum prize pot
                insuranceDeposit = 500000, -- $500,000 insurance deposit

                -- Safety Requirements
                safety = {
                    medicsOnStandby = true, -- R&R must be on standby
                    safetyVehicles = true, -- Safety vehicles required
                    emergencyServices = true -- Emergency services notified
                }
            }
        },

        -- Illegal Aspects
        illegalAspects = {
            apdResponse = true, -- APD will respond to illegal races

            -- APD Response Mechanics
            response = {
                detectionChance = 0.4, -- 40% chance APD detects race
                responseTime = 300, -- 5 minutes response time
                pursuitRules = 'standard', -- Standard pursuit rules apply

                -- Penalties
                penalties = {
                    racingTicket = 15000, -- $15,000 racing ticket
                    recklessDriving = 25000, -- $25,000 reckless driving
                    vehicleImpound = true, -- Vehicle may be impounded
                    jailTime = 300 -- 5 minutes jail time
                }
            }
        }
    }
}

-- Rebel Tournament System (Exact Olympus Implementation)
Config.TournamentSystem = {
    enabled = true,

    -- Tournament Types
    tournamentTypes = {
        -- Rebel-Only Tournaments
        rebelOnly = {
            enabled = true,

            -- Tournament Formats
            formats = {
                elimination = {
                    name = 'Single Elimination',
                    minParticipants = 8,
                    maxParticipants = 32,
                    entryFee = 100000, -- $100,000 entry fee

                    -- Prize Structure
                    prizes = {
                        first = 0.6, -- 60% of pot
                        second = 0.3, -- 30% of pot
                        third = 0.1 -- 10% of pot
                    }
                },

                roundRobin = {
                    name = 'Round Robin',
                    minParticipants = 6,
                    maxParticipants = 12,
                    entryFee = 75000, -- $75,000 entry fee

                    prizes = {
                        first = 0.5, -- 50% of pot
                        second = 0.3, -- 30% of pot
                        third = 0.2 -- 20% of pot
                    }
                }
            },

            -- Tournament Rules
            rules = {
                weaponRestrictions = {
                    allowedWeapons = {
                        'mk20', 'mx', 'spar16', 'katiba',
                        'vermin', 'pdw2000', 'sting'
                    },
                    bannedWeapons = {
                        'lynx', 'mar10', 'cyrus',
                        'titan', 'pcml', 'rpg'
                    }
                },

                equipmentRules = {
                    standardLoadout = true, -- All participants get same loadout
                    noNightVision = true, -- No night vision allowed
                    limitedMedical = true -- Limited medical supplies
                }
            }
        }
    }
}
