-- ========================================
-- OLYMPUS CARTEL & HIDEOUT SERVER
-- Based on Original Olympus Functions
-- ========================================

local OlympusCartelHideouts = {}

-- ========================================
-- DATABASE INITIALIZATION
-- ========================================
function OlympusCartelHideouts.InitDatabase()
    -- Create cartel and hideout tables
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS `cartel_hideouts` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `hideout_id` int(11) NOT NULL,
            `hideout_name` varchar(100) NOT NULL,
            `gang_id` int(11) DEFAULT 0,
            `gang_name` varchar(100) DEFAULT 'Neutral',
            `captured_at` timestamp NULL DEFAULT NULL,
            `last_notification` timestamp NULL DEFAULT NULL,
            `server` varchar(50) DEFAULT 'olympus',
            PRIMARY KEY (`id`),
            UNIQUE KEY `hideout_server` (`hideout_id`, `server`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ]])
    
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS `black_markets` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `market_id` varchar(50) NOT NULL,
            `market_name` varchar(100) NOT NULL,
            `drug_type` varchar(50) NOT NULL,
            `gang_id` int(11) DEFAULT 0,
            `gang_name` varchar(100) DEFAULT 'Neutral',
            `captured_at` timestamp NULL DEFAULT NULL,
            `last_notification` timestamp NULL DEFAULT NULL,
            `server` varchar(50) DEFAULT 'olympus',
            PRIMARY KEY (`id`),
            UNIQUE KEY `market_server` (`market_id`, `server`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ]])
    
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS `cartel_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `player_id` varchar(50) NOT NULL,
            `player_name` varchar(100) NOT NULL,
            `gang_id` int(11) NOT NULL,
            `gang_name` varchar(100) NOT NULL,
            `action_type` enum('capture_hideout','capture_blackmarket','lose_hideout','lose_blackmarket') NOT NULL,
            `location_id` varchar(50) NOT NULL,
            `location_name` varchar(100) NOT NULL,
            `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
            PRIMARY KEY (`id`),
            KEY `player_id` (`player_id`),
            KEY `gang_id` (`gang_id`),
            KEY `action_type` (`action_type`),
            KEY `timestamp` (`timestamp`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ]])
    
    -- Initialize default hideout and black market data
    OlympusCartelHideouts.InitializeLocations()
end

function OlympusCartelHideouts.InitializeLocations()
    -- Initialize hideouts
    for _, hideout in ipairs(Config.Hideouts) do
        exports.oxmysql:execute('INSERT IGNORE INTO cartel_hideouts (hideout_id, hideout_name, gang_id, gang_name, server) VALUES (?, ?, ?, ?, ?)', {
            hideout.id, hideout.name, 0, 'Neutral', 'olympus'
        })
    end
    
    -- Initialize black markets
    for _, market in ipairs(Config.BlackMarkets) do
        exports.oxmysql:execute('INSERT IGNORE INTO black_markets (market_id, market_name, drug_type, gang_id, gang_name, server) VALUES (?, ?, ?, ?, ?, ?)', {
            market.id, market.name, market.drugType, 0, 'Neutral', 'olympus'
        })
    end
end

-- ========================================
-- UTILITY FUNCTIONS
-- ========================================
function OlympusCartelHideouts.GetPlayerData(src)
    local success, result = pcall(function()
        return exports['olympus-core']:GetPlayerData(src)
    end)
    return success and result or nil
end

function OlympusCartelHideouts.GetPlayerGang(src)
    local success, result = pcall(function()
        return exports['olympus-gangs']:GetPlayerGang(src)
    end)
    return success and result or nil
end

function OlympusCartelHideouts.GetOnlineGangMembers(gangId)
    local success, result = pcall(function()
        return exports['olympus-gangs']:GetOnlineGangMembers(gangId)
    end)
    return success and result or {}
end

function OlympusCartelHideouts.LogAction(playerId, playerName, gangId, gangName, actionType, locationId, locationName)
    exports.oxmysql:execute('INSERT INTO cartel_logs (player_id, player_name, gang_id, gang_name, action_type, location_id, location_name) VALUES (?, ?, ?, ?, ?, ?, ?)', {
        playerId, playerName, gangId, gangName, actionType, locationId, locationName
    })
end

-- ========================================
-- HIDEOUT CAPTURE SYSTEM
-- Based on fn_captureHideout.sqf
-- ========================================
function OlympusCartelHideouts.CaptureHideout(src, hideoutId)
    local playerGang = OlympusCartelHideouts.GetPlayerGang(src)
    if not playerGang or not playerGang.id or playerGang.id == 0 then
        TriggerClientEvent('olympus-cartel-hideouts:notify', src, Config.Notifications.hideout.mustBeInGang, 'error')
        return false
    end
    
    -- Get current hideout owner
    local result = exports.oxmysql:query_sync('SELECT * FROM cartel_hideouts WHERE hideout_id = ? AND server = ?', {hideoutId, 'olympus'})
    if not result or #result == 0 then
        TriggerClientEvent('olympus-cartel-hideouts:notify', src, "Hideout not found", 'error')
        return false
    end
    
    local hideoutData = result[1]
    
    -- Check if gang already owns this hideout
    if hideoutData.gang_id == playerGang.id then
        TriggerClientEvent('olympus-cartel-hideouts:notify', src, Config.Notifications.hideout.alreadyOwned, 'error')
        return false
    end
    
    -- Get hideout config
    local hideoutConfig = nil
    for _, hideout in ipairs(Config.Hideouts) do
        if hideout.id == hideoutId then
            hideoutConfig = hideout
            break
        end
    end
    
    if not hideoutConfig then
        TriggerClientEvent('olympus-cartel-hideouts:notify', src, "Invalid hideout", 'error')
        return false
    end
    
    -- Determine capture rate based on current owner
    local captureRate = hideoutData.gang_id == 0 and Config.CartelHideouts.hideoutCapture.captureRate or Config.CartelHideouts.hideoutCapture.contestedRate
    
    -- Send capture confirmation if hideout is owned
    if hideoutData.gang_id ~= 0 then
        TriggerClientEvent('olympus-cartel-hideouts:confirmCapture', src, hideoutId, hideoutData.gang_name, captureRate)
    else
        TriggerClientEvent('olympus-cartel-hideouts:startCapture', src, hideoutId, captureRate)
    end
    
    return true
end

function OlympusCartelHideouts.CompleteHideoutCapture(src, hideoutId)
    local playerGang = OlympusCartelHideouts.GetPlayerGang(src)
    if not playerGang or not playerGang.id or playerGang.id == 0 then
        return false
    end
    
    -- Get hideout config
    local hideoutConfig = nil
    for _, hideout in ipairs(Config.Hideouts) do
        if hideout.id == hideoutId then
            hideoutConfig = hideout
            break
        end
    end
    
    if not hideoutConfig then
        return false
    end
    
    -- Get previous owner for notifications
    local result = exports.oxmysql:query_sync('SELECT * FROM cartel_hideouts WHERE hideout_id = ? AND server = ?', {hideoutId, 'olympus'})
    local previousOwner = result and #result > 0 and result[1] or nil
    
    -- Update hideout ownership
    exports.oxmysql:execute('UPDATE cartel_hideouts SET gang_id = ?, gang_name = ?, captured_at = NOW() WHERE hideout_id = ? AND server = ?', {
        playerGang.id, playerGang.name, hideoutId, 'olympus'
    })
    
    -- Log the capture
    OlympusCartelHideouts.LogAction(
        GetPlayerIdentifier(src, 0), GetPlayerName(src),
        playerGang.id, playerGang.name,
        'capture_hideout', tostring(hideoutId), hideoutConfig.name
    )
    
    -- Notify previous owner if applicable
    if previousOwner and previousOwner.gang_id ~= 0 then
        local onlineMembers = OlympusCartelHideouts.GetOnlineGangMembers(previousOwner.gang_id)
        for _, memberId in ipairs(onlineMembers) do
            TriggerClientEvent('olympus-cartel-hideouts:notify', memberId, 
                string.format("Your gang has lost control of %s", hideoutConfig.name), 'error')
        end
        
        -- Log the loss
        OlympusCartelHideouts.LogAction(
            'system', 'System',
            previousOwner.gang_id, previousOwner.gang_name,
            'lose_hideout', tostring(hideoutId), hideoutConfig.name
        )
    end
    
    -- Broadcast capture
    local message = string.format(Config.Notifications.hideout.broadcastCapture, GetPlayerName(src), hideoutConfig.name, playerGang.name)
    TriggerClientEvent('olympus-cartel-hideouts:broadcast', -1, message)
    
    -- Update client markers
    TriggerClientEvent('olympus-cartel-hideouts:updateHideoutOwner', -1, hideoutId, playerGang.id, playerGang.name)

    return true
end

-- ========================================
-- BLACK MARKET CAPTURE SYSTEM
-- Based on fn_captureBlackMarket.sqf
-- ========================================
function OlympusCartelHideouts.CaptureBlackMarket(src, marketId)
    local playerGang = OlympusCartelHideouts.GetPlayerGang(src)
    if not playerGang or not playerGang.id or playerGang.id == 0 then
        TriggerClientEvent('olympus-cartel-hideouts:notify', src, Config.Notifications.blackMarket.mustBeInGang, 'error')
        return false
    end

    -- Get current black market owner
    local result = exports.oxmysql:query_sync('SELECT * FROM black_markets WHERE market_id = ? AND server = ?', {marketId, 'olympus'})
    if not result or #result == 0 then
        TriggerClientEvent('olympus-cartel-hideouts:notify', src, "Black market not found", 'error')
        return false
    end

    local marketData = result[1]

    -- Check if gang already owns this black market
    if marketData.gang_id == playerGang.id then
        TriggerClientEvent('olympus-cartel-hideouts:notify', src, Config.Notifications.blackMarket.alreadyOwned, 'error')
        return false
    end

    -- Get market config
    local marketConfig = nil
    for _, market in ipairs(Config.BlackMarkets) do
        if market.id == marketId then
            marketConfig = market
            break
        end
    end

    if not marketConfig then
        TriggerClientEvent('olympus-cartel-hideouts:notify', src, "Invalid black market", 'error')
        return false
    end

    -- Notify current owners if applicable
    if marketData.gang_id ~= 0 then
        local currentTime = os.time()
        local lastNotification = marketData.last_notification and os.time(marketData.last_notification) or 0

        if currentTime - lastNotification >= Config.CartelHideouts.blackMarketCapture.notificationCooldown then
            local onlineMembers = OlympusCartelHideouts.GetOnlineGangMembers(marketData.gang_id)
            for _, memberId in ipairs(onlineMembers) do
                TriggerClientEvent('olympus-cartel-hideouts:notify', memberId,
                    string.format(Config.Notifications.blackMarket.gangNotification, marketConfig.name), 'warning')
            end

            -- Update notification timestamp
            exports.oxmysql:execute('UPDATE black_markets SET last_notification = NOW() WHERE market_id = ? AND server = ?', {marketId, 'olympus'})
        end
    end

    -- Start capture process
    TriggerClientEvent('olympus-cartel-hideouts:startBlackMarketCapture', src, marketId, marketConfig)

    return true
end

function OlympusCartelHideouts.CompleteBlackMarketCapture(src, marketId)
    local playerGang = OlympusCartelHideouts.GetPlayerGang(src)
    if not playerGang or not playerGang.id or playerGang.id == 0 then
        return false
    end

    -- Get market config
    local marketConfig = nil
    for _, market in ipairs(Config.BlackMarkets) do
        if market.id == marketId then
            marketConfig = market
            break
        end
    end

    if not marketConfig then
        return false
    end

    -- Get previous owner for notifications
    local result = exports.oxmysql:query_sync('SELECT * FROM black_markets WHERE market_id = ? AND server = ?', {marketId, 'olympus'})
    local previousOwner = result and #result > 0 and result[1] or nil

    -- Update black market ownership
    exports.oxmysql:execute('UPDATE black_markets SET gang_id = ?, gang_name = ?, captured_at = NOW() WHERE market_id = ? AND server = ?', {
        playerGang.id, playerGang.name, marketId, 'olympus'
    })

    -- Log the capture
    OlympusCartelHideouts.LogAction(
        GetPlayerIdentifier(src, 0), GetPlayerName(src),
        playerGang.id, playerGang.name,
        'capture_blackmarket', marketId, marketConfig.name
    )

    -- Notify previous owner if applicable
    if previousOwner and previousOwner.gang_id ~= 0 then
        local onlineMembers = OlympusCartelHideouts.GetOnlineGangMembers(previousOwner.gang_id)
        for _, memberId in ipairs(onlineMembers) do
            TriggerClientEvent('olympus-cartel-hideouts:notify', memberId,
                string.format(Config.Notifications.blackMarket.gangCaptured, marketConfig.drugType), 'error')
        end

        -- Log the loss
        OlympusCartelHideouts.LogAction(
            'system', 'System',
            previousOwner.gang_id, previousOwner.gang_name,
            'lose_blackmarket', marketId, marketConfig.name
        )
    end

    -- Broadcast capture
    local message = string.format(Config.Notifications.blackMarket.broadcastCapture, marketConfig.drugType, playerGang.name)
    TriggerClientEvent('olympus-cartel-hideouts:broadcast', -1, message)

    -- Update client markers
    TriggerClientEvent('olympus-cartel-hideouts:updateBlackMarketOwner', -1, marketId, playerGang.id, playerGang.name)

    -- Notify player
    TriggerClientEvent('olympus-cartel-hideouts:notify', src,
        string.format(Config.Notifications.blackMarket.captureSuccess, marketConfig.drugType), 'success')

    return true
end

-- ========================================
-- UTILITY FUNCTIONS
-- ========================================
function OlympusCartelHideouts.GetHideoutOwner(hideoutId)
    local result = exports.oxmysql:query_sync('SELECT * FROM cartel_hideouts WHERE hideout_id = ? AND server = ?', {hideoutId, 'olympus'})
    return result and #result > 0 and result[1] or nil
end

function OlympusCartelHideouts.GetBlackMarketOwner(marketId)
    local result = exports.oxmysql:query_sync('SELECT * FROM black_markets WHERE market_id = ? AND server = ?', {marketId, 'olympus'})
    return result and #result > 0 and result[1] or nil
end

function OlympusCartelHideouts.GetCartelDiscounts(src, drugType)
    local playerGang = OlympusCartelHideouts.GetPlayerGang(src)
    if not playerGang or not playerGang.id or playerGang.id == 0 then
        return 0
    end

    -- Check if gang owns relevant black market
    for _, market in ipairs(Config.BlackMarkets) do
        if market.drugType == drugType then
            local owner = OlympusCartelHideouts.GetBlackMarketOwner(market.id)
            if owner and owner.gang_id == playerGang.id then
                return Config.CartelHideouts.processingDiscounts.blackMarket
            end
        end
    end

    return 0
end

-- ========================================
-- EVENT HANDLERS
-- ========================================
RegisterNetEvent('olympus-cartel-hideouts:captureHideout', function(hideoutId)
    local src = source
    OlympusCartelHideouts.CaptureHideout(src, hideoutId)
end)

RegisterNetEvent('olympus-cartel-hideouts:completeHideoutCapture', function(hideoutId)
    local src = source
    OlympusCartelHideouts.CompleteHideoutCapture(src, hideoutId)
end)

RegisterNetEvent('olympus-cartel-hideouts:captureBlackMarket', function(marketId)
    local src = source
    OlympusCartelHideouts.CaptureBlackMarket(src, marketId)
end)

RegisterNetEvent('olympus-cartel-hideouts:completeBlackMarketCapture', function(marketId)
    local src = source
    OlympusCartelHideouts.CompleteBlackMarketCapture(src, marketId)
end)

RegisterNetEvent('olympus-cartel-hideouts:requestData', function()
    local src = source

    -- Send all hideout data
    exports.oxmysql:query('SELECT * FROM cartel_hideouts WHERE server = ?', {'olympus'}, function(hideouts)
        TriggerClientEvent('olympus-cartel-hideouts:receiveHideoutData', src, hideouts)
    end)

    -- Send all black market data
    exports.oxmysql:query('SELECT * FROM black_markets WHERE server = ?', {'olympus'}, function(markets)
        TriggerClientEvent('olympus-cartel-hideouts:receiveBlackMarketData', src, markets)
    end)
end)

-- ========================================
-- EXPORT FUNCTIONS
-- ========================================
exports('CaptureHideout', function(src, hideoutId)
    return OlympusCartelHideouts.CaptureHideout(src, hideoutId)
end)

exports('CaptureBlackMarket', function(src, marketId)
    return OlympusCartelHideouts.CaptureBlackMarket(src, marketId)
end)

exports('GetHideoutOwner', function(hideoutId)
    return OlympusCartelHideouts.GetHideoutOwner(hideoutId)
end)

exports('GetBlackMarketOwner', function(marketId)
    return OlympusCartelHideouts.GetBlackMarketOwner(marketId)
end)

exports('IsHideoutCaptured', function(hideoutId, gangId)
    local owner = OlympusCartelHideouts.GetHideoutOwner(hideoutId)
    return owner and owner.gang_id == gangId
end)

exports('IsBlackMarketCaptured', function(marketId, gangId)
    local owner = OlympusCartelHideouts.GetBlackMarketOwner(marketId)
    return owner and owner.gang_id == gangId
end)

exports('GetCartelDiscounts', function(src, drugType)
    return OlympusCartelHideouts.GetCartelDiscounts(src, drugType)
end)

exports('CanUseBlackMarket', function(src, marketId)
    local playerGang = OlympusCartelHideouts.GetPlayerGang(src)
    if not playerGang or not playerGang.id or playerGang.id == 0 then
        return false
    end

    local owner = OlympusCartelHideouts.GetBlackMarketOwner(marketId)
    return owner and (owner.gang_id == 0 or owner.gang_id == playerGang.id)
end)

-- ========================================
-- INITIALIZATION
-- ========================================
CreateThread(function()
    OlympusCartelHideouts.InitDatabase()
    print("^2[Olympus Cartel & Hideouts]^7 Server system initialized")
end)
