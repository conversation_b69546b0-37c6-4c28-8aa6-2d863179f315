-- Olympus Lottery System - Based on original fn_buyLotteryTicket.sqf
-- Simplified lottery system matching original Olympus mechanics

Config = {}

-- Lottery Configuration based on original fn_buyLotteryTicket.sqf
Config.Lottery = {
    ticketPrice = 25000, -- $25,000 per ticket (matching original oev_lotteryPrice)
    maxTicketsPerPlayer = 10, -- Maximum tickets per player (matching original limit)
    drawInterval = 1800, -- 30 minutes in seconds (30 * 60)
    cooldownTime = 900, -- 15 minutes cooldown after lottery ends
    prizePercentage = 0.8, -- 80% of prize pool goes to winner

    -- Server restart protection (matching original serverCycleLength check)
    minTimeBeforeRestart = 2100, -- 35 minutes (35 * 60)

    -- Lottery locations
    locations = {
        {
            name = "Lottery Office - Downtown",
            coords = vector3(24.47, -1346.62, 29.5),
            blip = {
                sprite = 500,
                color = 2,
                scale = 0.8,
                name = "Lottery Office"
            }
        },
        {
            name = "Lottery Office - Vinewood",
            coords = vector3(373.55, 325.56, 103.57),
            blip = {
                sprite = 500,
                color = 2,
                scale = 0.8,
                name = "Lottery Office"
            }
        },
        {
            name = "Lottery Office - Sandy Shores",
            coords = vector3(1961.17, 3740.5, 32.34),
            blip = {
                sprite = 500,
                color = 2,
                scale = 0.8,
                name = "Lottery Office"
            }
        },
        {
            name = "Lottery Office - Paleto Bay",
            coords = vector3(-282.25, 6226.73, 31.7),
            blip = {
                sprite = 500,
                color = 2,
                scale = 0.8,
                name = "Lottery Office"
            }
        }
    }
}

-- Notification settings
Config.Notifications = {
    showLotteryStart = true,
    showLotteryEnd = true,
    showWinnerAnnouncement = true,
    showCooldownNotifications = true
}

-- Animation settings
Config.Animations = {
    buyTicket = {
        dict = "mp_common",
        anim = "givetake1_a",
        duration = 3000
    }
}

-- UI Configuration
Config.UI = {
    lotteryMenu = {
        title = "Olympus Lottery System",
        subtitle = "Purchase lottery tickets and try your luck!"
    }
}
