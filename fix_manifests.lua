-- Quick fix script to create minimal manifest files
-- This will create basic client/main.lua and server/main.lua files for all resources

local resources = {
    'olympus-gangs',
    'olympus-economy', 
    'olympus-vehicles',
    'olympus-events',
    'olympus-ui',
    'olympus-civilian',
    'olympus-admin'
}

-- Create basic client main files
for _, resource in ipairs(resources) do
    local clientContent = [[-- ]] .. resource .. [[ - Client Main
-- Basic client initialization

local isLoaded = false

-- Initialize when core is ready
CreateThread(function()
    while not exports['olympus-core']:IsPlayerLoaded() do
        Wait(1000)
    end
    
    isLoaded = true
    print("^2[]] .. resource .. [[]^7 Client initialized")
end)

-- Export loaded status
exports('IsLoaded', function()
    return isLoaded
end)
]]
    
    local serverContent = [[-- ]] .. resource .. [[ - Server Main  
-- Basic server initialization

local isInitialized = false

-- Initialize server
CreateThread(function()
    Wait(1000) -- Wait for core to load
    
    isInitialized = true
    print("^2[]] .. resource .. [[]^7 Server initialized")
end)

-- Export initialization status
exports('IsInitialized', function()
    return isInitialized
end)
]]

    -- Write files (this is just the content, you'll need to create the actual files)
    print("Content for " .. resource .. "/client/main.lua:")
    print(clientContent)
    print("\nContent for " .. resource .. "/server/main.lua:")
    print(serverContent)
    print("\n" .. string.rep("=", 50) .. "\n")
end
