fx_version 'cerulean'
game 'gta5'

name 'Olympus Login Rewards'
description 'Daily login rewards and milestone system'
author 'Olympus Development Team'
version '1.0.0'

-- Dependencies
dependencies {
    'olympus-core',
    'olympus-ui',
    'olympus-housing',
    'oxmysql'
}

-- Shared scripts
shared_scripts {
    'config/shared.lua'
}

-- Client scripts
client_scripts {
    'client/main.lua',
    'client/rewards_ui.lua',
    'client/y_menu_integration.lua',
    'client/house_comp.lua'
}

-- Server scripts
server_scripts {
    '@oxmysql/lib/MySQL.lua',
    'server/main.lua',
    'server/rewards_system.lua',
    'server/streak_tracking.lua',
    'server/milestone_system.lua',
    'server/house_integration.lua'
}

-- UI files
ui_page 'html/index.html'

files {
    'html/index.html',
    'html/css/*.css',
    'html/js/*.js'
}

-- Exports
exports {
    'GetLoginStreak',
    'ClaimReward',
    'OpenRewardsMenu'
}

server_exports {
    'ProcessDailyLogin',
    'GetPlayerRewards',
    'CheckMilestone'
}
