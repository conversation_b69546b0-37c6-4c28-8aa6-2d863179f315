<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Olympus Betting</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: transparent;
            color: white;
            margin: 0;
            padding: 0;
        }
        
        .betting-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            border-radius: 10px;
            padding: 20px;
            min-width: 400px;
            display: none;
        }
        
        .betting-container.show {
            display: block;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .btn.danger:hover {
            background: #c82333;
        }
        
        input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div id="betting-container" class="betting-container">
        <div class="header">
            <h2>Olympus Betting</h2>
        </div>
        
        <div id="betting-content">
            <p>Betting system loaded</p>
            <button class="btn" onclick="closeBetting()">Close</button>
        </div>
    </div>

    <script>
        window.addEventListener('message', function(event) {
            const data = event.data;
            
            if (data.type === 'toggleBetting') {
                const container = document.getElementById('betting-container');
                if (data.show) {
                    container.classList.add('show');
                } else {
                    container.classList.remove('show');
                }
            }
        });
        
        function closeBetting() {
            fetch(`https://${GetParentResourceName()}/closeBetting`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({})
            });
        }
        
        function GetParentResourceName() {
            return 'olympus-betting';
        }
    </script>
</body>
</html>
