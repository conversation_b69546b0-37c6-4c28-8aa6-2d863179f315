-- Olympus Housing System - Complete Implementation
-- Based on exact Olympus Altis Life housing system with all mechanics

Config = {}

-- Housing System Settings (Exact Olympus Implementation)
Config.Housing = {
    enabled = true,
    homeownerLicense = {
        required = true,
        cost = 100000 -- $100,000 at DMV
    },
    
    -- Tax System (Exact Olympus Implementation)
    taxSystem = {
        enabled = true,
        refreshPeriod = 3888000, -- 45 days in seconds
        taxRate = {
            min = 0.10, -- 10% minimum tax
            max = 0.40 -- 40% maximum tax
        },
        
        -- Activity-based tax modifiers (exact Olympus graph)
        activityModifiers = {
            inactive = 1.5, -- 50% increase for inactive players
            regular = 1.0, -- Normal tax rate
            active = 0.8, -- 20% discount for active players
            veryActive = 0.6 -- 40% discount for very active players
        },
        
        -- Location-based tax multipliers
        locationFactors = {
            highValue = 2.0, -- Near federal events, cartels
            mediumValue = 1.5, -- Near cities, traders
            lowValue = 1.0, -- Remote locations
            veryLowValue = 0.7 -- Very remote locations
        }
    },
    
    -- Key System (Exact Olympus Implementation)
    keySystem = {
        enabled = true,
        maxPermanentKeys = 20, -- Maximum 20 permanent keys
        temporaryKeys = {
            enabled = true,
            virtualOnly = true, -- Only access virtual inventory
            requireOwnerOnline = false
        },
        permanentKeys = {
            enabled = true,
            fullAccess = true, -- Access even when owner offline
            physicalAccess = true -- Access physical inventory
        }
    },
    
    -- Realtor System (Exact Olympus Implementation)
    realtor = {
        enabled = true,
        locations = {
            vector3(1500.2, 2800.5, 35.0), -- Kavala Realtor
            vector3(2100.8, 1850.2, 35.0), -- Pyrgos Realtor
            vector3(1800.5, 3200.2, 35.0)  -- Sofia Realtor
        },
        
        -- Selling mechanics
        selling = {
            bankSale = {
                enabled = true,
                baseReturn = 0.50, -- 50% of initial purchase price
                upgradeReturn = 0.20 -- Small percentage of upgrade value
            },
            marketListing = {
                enabled = true,
                listingFee = {
                    minimum = 75000, -- $75,000 minimum
                    percentage = 0.05 -- 5% of listing price
                },
                searchRange = {
                    min = 0,
                    max = 6000 -- 6km maximum search range
                }
            }
        }
    },
    
    -- House Lockdown Kit (Exact Olympus Implementation)
    lockdownKit = {
        enabled = true,
        purchaseLocation = 'rebel_dealer',
        cost = 50000, -- Estimated cost
        
        -- Lockdown mechanics
        mechanics = {
            activationTime = 600, -- 10 minutes
            defuseTime = 30, -- 30 seconds for APD to stop
            apdNotification = false, -- APD not notified automatically
            effects = {
                doorsUnlocked = true,
                doorsOpened = true,
                storageInaccessible = true
            }
        }
    },
    
    -- Wardrobe System
    wardrobe = {
        enabled = true,
        skinChanging = true, -- Can change clothing skins
        availableSkins = 'player_unlocked' -- Based on player's unlocked skins
    },
    
    -- Daily Login Rewards Integration
    loginRewards = {
        enabled = true,
        deliveryOptions = {
            'house', 'rebel_outpost', 'storage_locker'
        },
        stackable = true -- Item comp stacks if not claimed
    }
}

-- House Types (Exact Olympus Implementation with All Details)
Config.HouseTypes = {
    -- 0-Crate Houses
    ['stone_house_small'] = {
        name = 'Stone House (Small)',
        displayName = 'Stone House (Small)',
        crates = 0,
        startingPrice = 450000,
        physicalStorage = {
            base = 100,
            upgrades = 0, -- No upgrades available
            upgradeCost = 0
        },
        virtualStorage = {
            base = 100,
            upgrades = 0, -- No upgrades available
            upgradeCost = 0
        },
        garage = false,
        totalCostWithUpgrades = 450000
    },
    
    ['house_addon'] = {
        name = 'House Addon',
        displayName = 'House Addon',
        crates = 0,
        startingPrice = 450000,
        physicalStorage = {
            base = 100,
            upgrades = 0,
            upgradeCost = 0
        },
        virtualStorage = {
            base = 100,
            upgrades = 0,
            upgradeCost = 0
        },
        garage = false,
        totalCostWithUpgrades = 450000
    },
    
    ['slum_shack'] = {
        name = 'Slum Shack',
        displayName = 'Slum Shack',
        crates = 0,
        startingPrice = 450000,
        physicalStorage = {
            base = 100,
            upgrades = 0,
            upgradeCost = 0
        },
        virtualStorage = {
            base = 100,
            upgrades = 0,
            upgradeCost = 0
        },
        garage = false,
        totalCostWithUpgrades = 450000
    },
    
    ['garage'] = {
        name = 'Garage',
        displayName = 'Garage',
        crates = 0,
        startingPrice = 500000,
        physicalStorage = {
            base = 100,
            upgrades = 0,
            upgradeCost = 0
        },
        virtualStorage = {
            base = 100,
            upgrades = 0,
            upgradeCost = 0
        },
        garage = true, -- Can retrieve cars
        totalCostWithUpgrades = 500000
    },
    
    ['windmill'] = {
        name = 'Windmill',
        displayName = 'Windmill',
        crates = 0,
        startingPrice = 500000,
        physicalStorage = {
            base = 100,
            upgrades = 0,
            upgradeCost = 0
        },
        virtualStorage = {
            base = 100,
            upgrades = 0,
            upgradeCost = 0
        },
        garage = false,
        totalCostWithUpgrades = 500000
    },
    
    -- 1-Crate Houses
    ['house_small_abandoned'] = {
        name = 'House (Small, Abandoned)',
        displayName = 'House (Small, Abandoned)',
        crates = 1,
        startingPrice = 750000,
        physicalStorage = {
            base = 100,
            maxUpgrade = 300,
            upgradeCost = 200000,
            upgrades = 1
        },
        virtualStorage = {
            base = 100,
            maxUpgrade = 800,
            upgradeCost = 112500,
            upgrades = 1
        },
        garage = false,
        totalCostWithUpgrades = 1062500
    },
    
    ['parking_shelter'] = {
        name = 'Parking Shelter',
        displayName = 'Parking Shelter',
        crates = 1,
        startingPrice = 750000,
        physicalStorage = {
            base = 100,
            maxUpgrade = 300,
            upgradeCost = 200000,
            upgrades = 1
        },
        virtualStorage = {
            base = 100,
            maxUpgrade = 800,
            upgradeCost = 112500,
            upgrades = 1
        },
        garage = true,
        totalCostWithUpgrades = 1062500
    },
    
    ['ghost_hotel_bungalow'] = {
        name = 'Ghost Hotel Bungalow',
        displayName = 'Ghost Hotel Bungalow',
        crates = 1,
        startingPrice = 750000,
        physicalStorage = {
            base = 100,
            maxUpgrade = 300,
            upgradeCost = 200000,
            upgrades = 1
        },
        virtualStorage = {
            base = 100,
            maxUpgrade = 800,
            upgradeCost = 112500,
            upgrades = 1
        },
        garage = false,
        totalCostWithUpgrades = 1062500
    },
    
    ['stone_house'] = {
        name = 'Stone House',
        displayName = 'Stone House',
        crates = 1,
        startingPrice = 750000,
        physicalStorage = {
            base = 100,
            maxUpgrade = 300,
            upgradeCost = 200000,
            upgrades = 1
        },
        virtualStorage = {
            base = 100,
            maxUpgrade = 800,
            upgradeCost = 112500,
            upgrades = 1
        },
        garage = false,
        totalCostWithUpgrades = 1062500
    },

    -- 4-Crate Houses (Exact Olympus Implementation)
    ['house_large'] = {
        name = 'House (Large)',
        displayName = 'House (Large)',
        crates = 4,
        startingPrice = 2200000,
        physicalStorage = {
            base = 100,
            maxUpgrade = 900,
            upgradeCost = 200000,
            upgrades = 4
        },
        virtualStorage = {
            base = 100,
            maxUpgrade = 2900,
            upgradeCost = 330000,
            upgrades = 4
        },
        garage = false,
        totalCostWithUpgrades = 4320000
    },

    ['brick_house_v4'] = {
        name = 'Brick House (v4)',
        displayName = 'Brick House (v4)',
        crates = 4,
        startingPrice = 2200000,
        physicalStorage = {
            base = 100,
            maxUpgrade = 900,
            upgradeCost = 200000,
            upgrades = 4
        },
        virtualStorage = {
            base = 100,
            maxUpgrade = 2900,
            upgradeCost = 330000,
            upgrades = 4
        },
        garage = false,
        totalCostWithUpgrades = 4320000
    },

    -- 5-Crate Houses (Exact Olympus Implementation)
    ['brick_house_v3'] = {
        name = 'Brick House (v3)',
        displayName = 'Brick House (v3)',
        crates = 5,
        startingPrice = 4250000,
        physicalStorage = {
            base = 100,
            maxUpgrade = 1100,
            upgradeCost = 200000,
            upgrades = 5
        },
        virtualStorage = {
            base = 100,
            maxUpgrade = 3600,
            upgradeCost = 638000,
            upgrades = 5
        },
        garage = false,
        totalCostWithUpgrades = 8900000
    },

    ['apartment_building'] = {
        name = 'Apartment Building',
        displayName = 'Apartment Building',
        crates = 5,
        startingPrice = 10000000,
        physicalStorage = {
            base = 100,
            maxUpgrade = 1100,
            upgradeCost = 200000,
            upgrades = 5
        },
        virtualStorage = {
            base = 100,
            maxUpgrade = 3600,
            upgradeCost = 1500000,
            upgrades = 5
        },
        garage = false,
        totalCostWithUpgrades = 19000000
    },

    ['hotel'] = {
        name = 'Hotel',
        displayName = 'Hotel',
        crates = 5,
        startingPrice = 10000000,
        physicalStorage = {
            base = 100,
            maxUpgrade = 1100,
            upgradeCost = 200000,
            upgrades = 5
        },
        virtualStorage = {
            base = 100,
            maxUpgrade = 3600,
            upgradeCost = 1500000,
            upgrades = 5
        },
        garage = false,
        totalCostWithUpgrades = 19000000
    },

    ['lighthouse'] = {
        name = 'Lighthouse',
        displayName = 'Lighthouse',
        crates = 5,
        startingPrice = 10000000,
        physicalStorage = {
            base = 100,
            maxUpgrade = 1100,
            upgradeCost = 200000,
            upgrades = 5
        },
        virtualStorage = {
            base = 100,
            maxUpgrade = 3600,
            upgradeCost = 1500000,
            upgrades = 5
        },
        garage = false,
        totalCostWithUpgrades = 19000000
    },

    ['industrial_shed'] = {
        name = 'Industrial Shed',
        displayName = 'Industrial Shed',
        crates = 5,
        startingPrice = 13500000,
        physicalStorage = {
            base = 100,
            maxUpgrade = 1100,
            upgradeCost = 200000,
            upgrades = 5
        },
        virtualStorage = {
            base = 100,
            maxUpgrade = 3600,
            upgradeCost = 2000000,
            upgrades = 5
        },
        garage = true, -- Has garage
        totalCostWithUpgrades = 25000000
    },

    ['police_station'] = {
        name = 'Police Station',
        displayName = 'Police Station',
        crates = 5,
        startingPrice = 13500000,
        physicalStorage = {
            base = 100,
            maxUpgrade = 1100,
            upgradeCost = 200000,
            upgrades = 5
        },
        virtualStorage = {
            base = 100,
            maxUpgrade = 3600,
            upgradeCost = 2000000,
            upgrades = 5
        },
        garage = false,
        totalCostWithUpgrades = 25000000
    },

    ['warehouse'] = {
        name = 'Warehouse',
        displayName = 'Warehouse',
        crates = 5,
        startingPrice = 13500000,
        physicalStorage = {
            base = 100,
            maxUpgrade = 1100,
            upgradeCost = 200000,
            upgrades = 5
        },
        virtualStorage = {
            base = 100,
            maxUpgrade = 3600,
            upgradeCost = 2000000,
            upgrades = 5
        },
        garage = true, -- Has garage
        totalCostWithUpgrades = 25000000
    },

    ['villa'] = {
        name = 'Villa',
        displayName = 'Villa',
        crates = 5,
        startingPrice = 15000000,
        physicalStorage = {
            base = 100,
            maxUpgrade = 1100,
            upgradeCost = 200000,
            upgrades = 5
        },
        virtualStorage = {
            base = 100,
            maxUpgrade = 3600,
            upgradeCost = 2250000,
            upgrades = 5
        },
        garage = true, -- Has garage
        totalCostWithUpgrades = 27750000
    }
}

-- Special Storage Requirements (Exact Olympus Implementation)
Config.SpecialStorage = {
    oil = {
        renovationRequired = true,
        renovationCost = 50000, -- $50k to renovate house for oil storage
        description = 'Oil requires house renovation regardless of tier'
    }
}

-- APD Raid System (Exact Olympus Implementation)
Config.APDRaids = {
    enabled = true,
    probableCause = {
        homeowner = true, -- Homeowner can provide probable cause
        keyHolders = true -- Key holders can provide probable cause
    },
    searchWarrant = true, -- APD needs search warrant
    raidMechanics = {
        physicalInventory = true, -- Can search physical inventory
        virtualInventory = true, -- Can search virtual inventory
        seizureRules = 'standard' -- Follow standard seizure rules
    }
}
