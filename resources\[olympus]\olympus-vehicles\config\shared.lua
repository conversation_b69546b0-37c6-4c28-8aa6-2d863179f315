-- Olympus Vehicle System - Complete Implementation
-- Based on exact Olympus Altis Life vehicle system with all mechanics

Config = {}

-- Vehicle Settings (Exact Olympus Implementation)
Config.MaxVehiclesPerPlayer = 50 -- Exact Olympus limit
Config.VehicleDespawnTime = 1800 -- 30 minutes
Config.ImpoundCost = 1000
Config.VehicleKeys = true
Config.FuelSystem = true
Config.VehicleDamage = true

-- Olympus Vehicle Upgrade System (Exact Implementation)
Config.OlympusUpgrades = {
    -- Service Station Locations (Exact Olympus)
    serviceStations = {
        air = {
            vector3(1685.0, 3289.0, 41.0), -- Airport
            vector3(-1212.0, -2892.0, 13.0), -- South Airport
            vector3(2126.0, 4796.0, 41.0) -- North Airfield
        },
        ground = {
            vector3(1174.0, 2640.0, 38.0), -- Kavala Garage
            vector3(3003.0, 3310.0, 12.0), -- <PERSON><PERSON><PERSON> Garage
            vector3(1685.0, 3289.0, 41.0), -- Airport (also does ground)
            vector3(-1212.0, -2892.0, 13.0) -- South Airport (also does ground)
        }
    },

    -- Turbo System (Exact Olympus Mechanics)
    turbo = {
        enabled = true,
        tiers = {
            [0] = {multiplier = 1.00, cost = 0.00}, -- No turbo
            [1] = {multiplier = 1.22, cost = 0.05}, -- 5% of vehicle cost
            [2] = {multiplier = 1.44, cost = 0.05}, -- 5% of vehicle cost
            [3] = {multiplier = 1.66, cost = 0.05}, -- 5% of vehicle cost
            [4] = {multiplier = 1.88, cost = 0.05}  -- 5% of vehicle cost
        },
        -- Exact Olympus formulas
        groundFormula = 'mass / (1 + 0.22 * tier)', -- Mass decreased for ground
        airFormula = 'mass * (1 + 0.22 * tier)' -- Mass increased for air
    },

    -- Insurance System (Exact Olympus Implementation)
    insurance = {
        enabled = true,
        tiers = {
            [1] = {
                cost = 0.40, -- 40% of vehicle original cost
                replaceVehicle = true,
                replaceUpgrades = false, -- Only textures/finishes preserved
                description = 'Vehicle replacement only'
            },
            [2] = {
                cost = 0.40, -- 40% of vehicle original cost
                replaceVehicle = true,
                replaceUpgrades = true, -- All upgrades preserved
                description = 'Vehicle and upgrades replacement'
            }
        },
        -- Exact Olympus exclusions
        excludedVehicles = {'ghosthawk', 'blackfish', 'kajman', 'blackfoot'},
        seizureThreshold = 500000, -- $500k+ illegal items = seizure (no insurance)
        coverageEvents = {
            explosion = true,
            chopping = true,
            seizure = false -- Insurance void on seizure
        }
    },

    -- Security System (Exact Olympus Implementation)
    security = {
        enabled = true,
        tiers = {
            [1] = {
                cost = 0.10, -- 10% of vehicle original cost
                lockpickResistance = 2, -- Average 2 lockpicks needed
                alarm = true,
                tracking = false,
                description = 'Basic alarm system'
            },
            [2] = {
                cost = 0.10,
                lockpickResistance = 3,
                alarm = true,
                tracking = false,
                description = 'Enhanced alarm system'
            },
            [3] = {
                cost = 0.10,
                lockpickResistance = 4,
                alarm = true,
                tracking = false,
                description = 'Advanced alarm system'
            },
            [4] = {
                cost = 0.10,
                lockpickResistance = 5,
                alarm = true,
                tracking = true, -- GPS tracking on map (exact Olympus feature)
                description = 'Tracked security system'
            }
        },
        gpsJammer = true -- GPS jammer can disable tier 4 tracking
    },

    -- Trunk Upgrades (Exact Olympus Implementation)
    trunk = {
        enabled = true,
        maxTiers = 4, -- Maximum 4 trunk upgrades
        bonusPerTier = 0.05, -- 5% increase per tier (exact Olympus)
        costPerTier = 0.125, -- 12.5% of vehicle original cost per tier
        description = 'Increases virtual storage space by 5% per tier'
    },

    -- Nitrous System (Exact Olympus Implementation)
    nitrous = {
        enabled = true,
        charges = 2, -- Exactly 2 charges available
        activation = 'UP_ARROW', -- Up arrow key to activate
        excludedVehicles = {'trucks', 'blackwater_vehicles'}, -- Cannot install on these
        removedOnStore = true, -- Removed when vehicle stored in garage
        description = 'Temporary speed boost with 2 charges'
    },

    -- Vehicle Finishes (Exact Olympus Implementation)
    finishes = {
        enabled = true,
        available = {
            'chrome', 'gold', 'fade' -- Exact Olympus finishes
        },
        donorFinishes = {
            'rvmats' -- Special RVMAT finishes for donors only
        },
        cost = 0.05, -- 5% of vehicle original cost
        preservedOnInsurance = true
    },

    -- Vehicle Textures/Skins (Exact Olympus Implementation)
    textures = {
        enabled = true,
        categories = {
            public = true, -- Available to all players
            staff = true, -- Staff team exclusive
            donor = true, -- Donor exclusive
            faction = true, -- APD/R&R exclusive
            gang = true -- Gang skins (purchasable by gangs)
        },
        preservedOnInsurance = true, -- Textures preserved with insurance
        transferWithVehicle = true -- Textures transfer when vehicle claimed
    },

    -- Armed Plane System (Exact Olympus Implementation)
    armedPlanes = {
        enabled = true,
        baseVehicle = 'caesar_btt', -- Caesar BTT Racing only
        upgradeCost = 1500000, -- Exactly $1.5M to arm
        description = 'Upgrade Caesar BTT to armed plane',
        apdRankRequired = 4 -- Sergeant+ for APD use
    }
}

-- Vehicle Ownership System (Exact Olympus Implementation)
Config.OlympusOwnership = {
    -- Vehicle Naming (Exact Olympus Feature)
    naming = {
        enabled = true,
        donorRequired = 50, -- $50+ donation required to rename vehicles
        showInChat = true, -- Names shown when vehicles seized/impounded
        transferWithVehicle = true, -- Names transfer when vehicle claimed
        maxLength = 20 -- Maximum name length
    },

    -- Vehicle Transfer System (Exact Olympus)
    transfer = {
        enabled = true,
        legalVehicles = {
            cost = 0, -- Free to transfer legal vehicles
            restrictions = false
        },
        illegalVehicles = {
            cost = 0.10, -- 10% of original purchase price to transfer
            restrictions = true
        }
    },

    -- Vehicle Selling (Exact Olympus Implementation)
    selling = {
        enabled = true,
        baseReturn = 0.40, -- 40% of original purchase price
        upgradeReturn = 0.20, -- Small percentage of upgrade value returned
        description = 'Sell back to server for 40% + small upgrade value'
    },

    -- Chop Shop System (Exact Olympus Implementation)
    chopShop = {
        enabled = true,
        location = vector3(2500.0, 2000.0, 35.0), -- Chop shop location

        -- Chopping mechanics (exact Olympus)
        chopping = {
            payout = 0.40, -- Same 40% as selling to server
            illegalClaiming = {
                enabled = true,
                cost = 0.32, -- 32% of original purchase price to claim
                description = 'Claim illegal vehicles for 32% of original cost'
            }
        },

        -- Special vehicles (exact Olympus)
        specialVehicles = {
            apdHatchback = {
                claimable = true,
                skin = 'apd_vandal', -- Special vandal skin when claimed
                description = 'APD hatchback with vandal skin when claimed'
            }
        }
    },

    -- Vehicle Seizure Rules (Exact Olympus Implementation)
    seizure = {
        enabled = true,
        conditions = {
            illegalVehicle = true, -- Vehicle itself is illegal
            illegalItems = 500000, -- $500k+ illegal items in vehicle
            federalAntiAir = true -- Inside anti-air ring during federal events
        },

        -- APD seizure rewards (exact Olympus)
        apdReward = {
            percentage = 0.33, -- 33% split among nearby officers
            blackwaterException = {
                armedProwler = 3000000, -- $3M for armed prowler
                otherBW = 4000000 -- $4M for other Blackwater vehicles
            }
        },

        -- Seizure effects (exact Olympus)
        removeFromGarage = true, -- Completely removes from garage
        insuranceVoid = true, -- Insurance doesn't cover seizures
        permanentLoss = true -- Cannot be recovered
    },

    -- Impound vs Seizure (Exact Olympus Distinction)
    impoundVsSeizure = {
        impound = {
            returnsToGarage = true, -- Vehicle returns to garage
            temporaryRemoval = true,
            description = 'Vehicle temporarily removed, returns to garage'
        },
        seizure = {
            removesFromGarage = true, -- Vehicle permanently removed
            permanentRemoval = true,
            apdReward = true,
            description = 'Vehicle permanently removed from garage'
        }
    }
}

-- Fuel System
Config.Fuel = {
    enabled = true,
    consumptionRate = 0.5, -- Fuel consumed per minute
    maxFuel = 100,
    fuelStations = {
        vector3(49.4, 2778.8, 58.0),
        vector3(263.9, 2606.5, 44.9),
        vector3(1039.9, 2671.1, 39.6),
        vector3(1207.3, 2660.2, 37.9),
        vector3(2539.7, 2594.2, 37.9),
        vector3(2679.9, 3263.9, 55.2),
        vector3(2005.0, 3773.9, 32.4),
        vector3(1687.2, 4929.4, 42.1),
        vector3(1701.3, 6416.0, 32.8),
        vector3(179.9, 6602.8, 31.9),
        vector3(-94.5, 6419.6, 31.6),
        vector3(-2554.9, 2334.4, 33.1),
        vector3(-1800.4, 803.7, 138.7),
        vector3(-1437.6, -276.7, 46.2),
        vector3(-2096.2, -320.3, 13.2),
        vector3(-724.6, -935.1, 19.2),
        vector3(-526.0, -1211.0, 18.2),
        vector3(-70.2, -1761.8, 29.5),
        vector3(265.6, -1261.3, 29.3),
        vector3(819.7, -1028.8, 26.4),
        vector3(1208.9, -1402.6, 35.2),
        vector3(1181.4, -330.8, 69.3),
        vector3(620.8, 269.1, 103.1),
        vector3(2581.3, 362.0, 108.5)
    },
    fuelPrice = 2.5 -- Per liter
}

-- Vehicle Classes
Config.VehicleClasses = {
    [0] = 'Compacts',
    [1] = 'Sedans',
    [2] = 'SUVs',
    [3] = 'Coupes',
    [4] = 'Muscle',
    [5] = 'Sports Classics',
    [6] = 'Sports',
    [7] = 'Super',
    [8] = 'Motorcycles',
    [9] = 'Off-road',
    [10] = 'Industrial',
    [11] = 'Utility',
    [12] = 'Vans',
    [13] = 'Cycles',
    [14] = 'Boats',
    [15] = 'Helicopters',
    [16] = 'Planes',
    [17] = 'Service',
    [18] = 'Emergency',
    [19] = 'Military',
    [20] = 'Commercial',
    [21] = 'Trains'
}

-- Vehicle Restrictions
Config.RestrictedVehicles = {
    -- Police Vehicles
    'police', 'police2', 'police3', 'police4', 'policeb', 'polmav', 'riot',
    -- Medical Vehicles
    'ambulance', 'firetruk',
    -- Military Vehicles
    'rhino', 'lazer', 'hydra', 'savage', 'buzzard2',
    -- Special Vehicles
    'insurgent', 'insurgent2', 'technical', 'halftrack'
}

-- Vehicle Modifications
Config.Modifications = {
    engine = {
        name = 'Engine',
        levels = {
            [0] = {name = 'Stock', price = 0},
            [1] = {name = 'Level 1', price = 5000},
            [2] = {name = 'Level 2', price = 10000},
            [3] = {name = 'Level 3', price = 15000},
            [4] = {name = 'Level 4', price = 25000}
        }
    },
    brakes = {
        name = 'Brakes',
        levels = {
            [0] = {name = 'Stock', price = 0},
            [1] = {name = 'Street', price = 2500},
            [2] = {name = 'Sport', price = 5000},
            [3] = {name = 'Race', price = 7500}
        }
    },
    transmission = {
        name = 'Transmission',
        levels = {
            [0] = {name = 'Stock', price = 0},
            [1] = {name = 'Street', price = 3000},
            [2] = {name = 'Sport', price = 6000},
            [3] = {name = 'Race', price = 9000}
        }
    },
    suspension = {
        name = 'Suspension',
        levels = {
            [0] = {name = 'Stock', price = 0},
            [1] = {name = 'Lowered', price = 2000},
            [2] = {name = 'Street', price = 4000},
            [3] = {name = 'Sport', price = 6000},
            [4] = {name = 'Competition', price = 8000}
        }
    },
    armor = {
        name = 'Armor',
        levels = {
            [0] = {name = 'None', price = 0},
            [1] = {name = 'Armor 20%', price = 7500},
            [2] = {name = 'Armor 40%', price = 15000},
            [3] = {name = 'Armor 60%', price = 22500},
            [4] = {name = 'Armor 80%', price = 30000},
            [5] = {name = 'Armor 100%', price = 50000}
        }
    },
    turbo = {
        name = 'Turbo',
        levels = {
            [0] = {name = 'None', price = 0},
            [1] = {name = 'Turbo', price = 15000}
        }
    }
}

-- Vehicle Colors
Config.Colors = {
    primary = {
        {name = 'Black', id = 0},
        {name = 'Carbon Black', id = 147},
        {name = 'Graphite', id = 1},
        {name = 'Anthracite Black', id = 11},
        {name = 'Black Steel', id = 2},
        {name = 'Dark Steel', id = 3},
        {name = 'Silver', id = 4},
        {name = 'Bluish Silver', id = 5},
        {name = 'Rolled Steel', id = 6},
        {name = 'Shadow Silver', id = 7},
        {name = 'Stone Silver', id = 8},
        {name = 'Midnight Silver', id = 9},
        {name = 'Gun Metal', id = 10},
        {name = 'Red', id = 27},
        {name = 'Torino Red', id = 28},
        {name = 'Formula Red', id = 29},
        {name = 'Lava Red', id = 150},
        {name = 'Blaze Red', id = 30},
        {name = 'Grace Red', id = 31},
        {name = 'Garnet Red', id = 32},
        {name = 'Sunset Red', id = 33},
        {name = 'Cabernet Red', id = 34},
        {name = 'Wine Red', id = 143},
        {name = 'Candy Red', id = 35},
        {name = 'Hot Pink', id = 135},
        {name = 'Pfsiter Pink', id = 137},
        {name = 'Salmon Pink', id = 136},
        {name = 'Sunrise Orange', id = 36},
        {name = 'Orange', id = 38},
        {name = 'Bright Orange', id = 138},
        {name = 'Gold', id = 99},
        {name = 'Bronze', id = 90},
        {name = 'Yellow', id = 88},
        {name = 'Race Yellow', id = 89},
        {name = 'Dew Yellow', id = 91},
        {name = 'Dark Green', id = 49},
        {name = 'Racing Green', id = 50},
        {name = 'Sea Green', id = 51},
        {name = 'Olive Green', id = 52},
        {name = 'Bright Green', id = 53},
        {name = 'Gasoline Green', id = 54},
        {name = 'Lime Green', id = 92},
        {name = 'Midnight Blue', id = 61},
        {name = 'Galaxy Blue', id = 62},
        {name = 'Dark Blue', id = 63},
        {name = 'Saxon Blue', id = 64},
        {name = 'Blue', id = 65},
        {name = 'Mariner Blue', id = 66},
        {name = 'Harbor Blue', id = 67},
        {name = 'Diamond Blue', id = 68},
        {name = 'Surf Blue', id = 69},
        {name = 'Nautical Blue', id = 70},
        {name = 'Racing Blue', id = 73},
        {name = 'Ultra Blue', id = 70},
        {name = 'Light Blue', id = 74},
        {name = 'Chocolate Brown', id = 96},
        {name = 'Bison Brown', id = 101},
        {name = 'Creek Brown', id = 95},
        {name = 'Feltzer Brown', id = 94},
        {name = 'Maple Brown', id = 97},
        {name = 'Beechwood Brown', id = 103},
        {name = 'Sienna Brown', id = 104},
        {name = 'Saddle Brown', id = 98},
        {name = 'Moss Brown', id = 100},
        {name = 'Woodbeech Brown', id = 102},
        {name = 'Straw Brown', id = 99},
        {name = 'Sandy Brown', id = 105},
        {name = 'Bleached Brown', id = 106},
        {name = 'Schafter Purple', id = 71},
        {name = 'Spinnaker Purple', id = 72},
        {name = 'Midnight Purple', id = 142},
        {name = 'Bright Purple', id = 145},
        {name = 'Cream', id = 107},
        {name = 'Ice White', id = 111},
        {name = 'Frost White', id = 112}
    }
}

-- Vehicle Spawning
Config.SpawnSettings = {
    spawnInGarage = true,
    spawnWithFuel = true,
    spawnWithKeys = true,
    spawnDistance = 5.0,
    maxSpawnAttempts = 10
}

-- Vehicle Damage
Config.Damage = {
    enabled = true,
    engineDamageMultiplier = 1.0,
    bodyDamageMultiplier = 1.0,
    petrolTankDamageMultiplier = 1.0,
    weaponsDamageMultiplier = 1.0,
    deformationMultiplier = 1.0,
    repairCost = {
        engine = 1000,
        body = 500,
        petrolTank = 750,
        wheels = 250
    }
}

-- Vehicle Insurance
Config.Insurance = {
    enabled = true,
    baseCost = 5000, -- Base insurance cost
    costMultiplier = 0.1, -- 10% of vehicle value
    claimCooldown = 3600, -- 1 hour
    maxClaims = 3, -- Per day
    coverageTypes = {
        basic = {
            name = 'Basic Coverage',
            multiplier = 1.0,
            coverage = 0.5 -- 50% of vehicle value
        },
        full = {
            name = 'Full Coverage',
            multiplier = 2.0,
            coverage = 1.0 -- 100% of vehicle value
        },
        premium = {
            name = 'Premium Coverage',
            multiplier = 3.0,
            coverage = 1.2 -- 120% of vehicle value
        }
    }
}

-- Vehicle Rental
Config.Rental = {
    enabled = true,
    locations = {
        vector3(-1037.0, -2737.0, 20.0), -- Airport
        vector3(110.0, -1090.0, 29.0), -- Downtown
        vector3(1855.0, 3678.0, 33.0) -- Sandy Shores
    },
    vehicles = {
        {model = 'blista', price = 100, deposit = 1000},
        {model = 'dilettante', price = 120, deposit = 1200},
        {model = 'issi2', price = 80, deposit = 800},
        {model = 'panto', price = 60, deposit = 600},
        {model = 'bati', price = 150, deposit = 1500},
        {model = 'akuma', price = 120, deposit = 1200}
    },
    maxRentalTime = 86400, -- 24 hours
    lateFee = 50 -- Per hour late
}

-- Vehicle Theft
Config.Theft = {
    enabled = true,
    lockpickChance = 0.7, -- 70% success rate
    lockpickTime = 10, -- 10 seconds
    hotwireChance = 0.5, -- 50% success rate
    hotwireTime = 15, -- 15 seconds
    alarmChance = 0.3, -- 30% chance to trigger alarm
    policeNotifyChance = 0.2, -- 20% chance to notify police
    skillRequirement = {
        lockpick = 25,
        hotwire = 50
    }
}

-- Vehicle Tracking
Config.Tracking = {
    enabled = true,
    gpsTrackers = {
        basic = {
            name = 'Basic GPS Tracker',
            price = 5000,
            range = 1000, -- meters
            updateInterval = 30 -- seconds
        },
        advanced = {
            name = 'Advanced GPS Tracker',
            price = 15000,
            range = 5000,
            updateInterval = 10
        },
        professional = {
            name = 'Professional GPS Tracker',
            price = 50000,
            range = -1, -- unlimited
            updateInterval = 5
        }
    }
}

-- Impound System
Config.Impound = {
    enabled = true,
    locations = {
        {
            name = 'City Impound',
            coords = vector3(409.0, -1623.0, 29.3),
            spawn = vector3(405.0, -1643.0, 29.3),
            heading = 230.0
        },
        {
            name = 'Sandy Shores Impound',
            coords = vector3(1728.0, 3310.0, 41.2),
            spawn = vector3(1722.0, 3290.0, 41.2),
            heading = 195.0
        }
    },
    baseCost = 1000,
    dailyFee = 100,
    maxImpoundTime = 604800, -- 7 days
    autoDelete = true -- Delete after max time
}
