-- Olympus Framework Loader - Client
-- Handles client-side framework initialization and welcome messages

local frameworkReady = false
local loadingProgress = 0
local loadingSteps = {
    'Initializing Core Systems...',
    'Loading Player Data...',
    'Connecting to Database...',
    'Loading UI Components...',
    'Initializing Faction Systems...',
    'Loading Economy System...',
    'Setting up Vehicle System...',
    'Loading Gang System...',
    'Initializing Events System...',
    'Framework Ready!'
}

-- Update loading progress
function UpdateLoadingProgress(step, progress, message)
    loadingProgress = progress

    -- Update loading screen if it exists
    if exports['olympus-ui'] then
        exports['olympus-ui']:UpdateLoadingScreen({
            progress = progress,
            text = message or loadingSteps[step] or 'Loading...'
        })
    end

    print("^3[Olympus Loader]^7 " .. (message or loadingSteps[step] or 'Loading...') .. " (" .. progress .. "%)")
end

-- Framework ready event from server
RegisterNetEvent('olympus:client:frameworkReady')
AddEventHandler('olympus:client:frameworkReady', function()
    frameworkReady = true

    -- Final loading step
    UpdateLoadingProgress(10, 100, 'Framework Ready!')

    -- Hide loading screen after a short delay
    SetTimeout(2000, function()
        if exports['olympus-ui'] then
            exports['olympus-ui']:HideLoadingScreen()
        end

        -- Show welcome message
        ShowWelcomeMessage()
    end)

    print("^2[Olympus Loader]^7 Client framework ready!")
end)

-- Loading progress events
RegisterNetEvent('olympus:client:loadingProgress')
AddEventHandler('olympus:client:loadingProgress', function(step, progress, message)
    UpdateLoadingProgress(step, progress, message)
end)

-- Show welcome message to player
function ShowWelcomeMessage()
    -- Wait for UI to be ready
    while not exports['olympus-ui'] do
        Wait(100)
    end
    
    -- Show welcome notification
    exports['olympus-ui']:ShowNotification({
        type = 'success',
        title = 'Welcome to Olympus',
        message = 'Altis Life Framework Loaded Successfully',
        duration = 8000
    })
    
    -- Show loading complete message
    SetTimeout(2000, function()
        exports['olympus-ui']:ShowNotification({
            type = 'info',
            title = 'Framework Ready',
            message = 'All systems operational. Enjoy your experience!',
            duration = 5000
        })
    end)
end

-- Export to check if framework is ready
exports('IsFrameworkReady', function()
    return frameworkReady
end)

-- Debug command for players with admin permissions
RegisterCommand('olympus:debug', function(source, args, rawCommand)
    local playerData = exports['olympus-core']:GetPlayerData()
    
    if playerData and playerData.admin_level and playerData.admin_level >= 2 then
        local debugInfo = {
            frameworkReady = frameworkReady,
            playerLoaded = exports['olympus-core']:IsPlayerLoaded(),
            playerData = playerData
        }
        
        print("^3[Olympus Debug]^7 Framework Status: " .. (frameworkReady and "Ready" or "Not Ready"))
        print("^3[Olympus Debug]^7 Player Loaded: " .. (exports['olympus-core']:IsPlayerLoaded() and "Yes" or "No"))
        
        exports['olympus-ui']:ShowNotification({
            type = 'info',
            title = 'Debug Info',
            message = 'Check console for detailed information',
            duration = 3000
        })
    else
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Access Denied',
            message = 'You do not have permission to use this command',
            duration = 3000
        })
    end
end, false)

-- Monitor framework health
CreateThread(function()
    while true do
        Wait(60000) -- Check every minute
        
        if frameworkReady then
            -- Check if core systems are still responsive
            local coreReady = exports['olympus-core'] and exports['olympus-core']:IsPlayerLoaded()
            local uiReady = exports['olympus-ui'] ~= nil
            
            if not coreReady or not uiReady then
                print("^1[Olympus Loader]^7 WARNING: Framework components may have failed!")
                frameworkReady = false
            end
        end
    end
end)
