-- Olympus Core Framework - Map Blips System
-- Creates all map blips for various locations and services

local blips = {}

-- Blip configuration
local BlipConfig = {
    -- Shops and Services (using native GTA 5 blip sprites)
    shops = {
        {coords = vector3(-47.02, -1757.23, 29.42), sprite = 52, color = 2, name = "General Store"},
        {coords = vector3(25.74, -1346.63, 29.50), sprite = 52, color = 2, name = "General Store"},
        {coords = vector3(1135.57, -981.78, 46.42), sprite = 52, color = 2, name = "General Store"},
        {coords = vector3(1163.54, -323.04, 69.21), sprite = 52, color = 2, name = "General Store"},
        {coords = vector3(374.19, 325.18, 103.57), sprite = 52, color = 2, name = "General Store"},
        {coords = vector3(2555.13, 382.18, 108.62), sprite = 52, color = 2, name = "General Store"},
        {coords = vector3(2676.02, 3281.28, 55.24), sprite = 52, color = 2, name = "General Store"},
        {coords = vector3(1960.50, 3741.84, 32.34), sprite = 52, color = 2, name = "General Store"},
        {coords = vector3(549.13, 2670.85, 42.16), sprite = 52, color = 2, name = "General Store"},
        {coords = vector3(1729.54, 6415.63, 35.04), sprite = 52, color = 2, name = "General Store"},
        {coords = vector3(-3243.89, 1001.23, 12.83), sprite = 52, color = 2, name = "General Store"},
        {coords = vector3(-2967.82, 390.93, 15.04), sprite = 52, color = 2, name = "General Store"},
        {coords = vector3(-3041.17, 585.11, 7.91), sprite = 52, color = 2, name = "General Store"},
        {coords = vector3(-1820.82, 792.52, 138.12), sprite = 52, color = 2, name = "General Store"},
        {coords = vector3(-1486.72, -379.10, 40.16), sprite = 52, color = 2, name = "General Store"},
        {coords = vector3(-1223.18, -907.22, 12.33), sprite = 52, color = 2, name = "General Store"}
    },
    
    -- Gas Stations (using native GTA 5 gas pump sprite)
    gasStations = {
        {coords = vector3(49.42, 2778.79, 58.04), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(263.89, 2606.46, 44.98), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(1039.96, 2671.13, 39.55), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(1207.26, 2660.17, 37.90), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(2539.68, 2594.19, 37.94), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(2679.85, 3264.54, 55.24), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(2005.03, 3773.89, 32.40), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(1687.15, 4929.39, 42.08), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(1701.31, 6416.02, 32.76), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(179.86, 6602.84, 31.87), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(-94.46, 6419.59, 31.64), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(-2554.99, 2334.40, 33.07), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(-1800.37, 803.66, 138.65), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(-1437.62, -276.75, 46.21), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(-2096.24, -320.29, 13.17), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(-526.02, -1211.00, 18.18), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(-70.21, -1761.79, 29.53), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(265.65, -1261.31, 29.29), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(819.65, -1028.85, 26.40), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(1208.95, -1402.57, 35.22), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(1181.38, -330.85, 69.32), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(620.84, 269.10, 103.09), sprite = 361, color = 1, name = "Gas Station"},
        {coords = vector3(2581.32, 362.04, 108.47), sprite = 361, color = 1, name = "Gas Station"}
    },
    
    -- ATMs (using native GTA 5 dollar sign sprite)
    atms = {
        {coords = vector3(147.44, -1035.77, 29.34), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(145.88, -1035.18, 29.34), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(-846.30, -340.40, 38.68), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(-1204.35, -324.29, 37.87), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(-1216.27, -331.46, 37.78), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(-56.30, -1752.53, 29.42), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(-261.69, -2012.64, 30.12), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(-273.22, -2025.60, 30.20), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(314.19, -278.62, 54.17), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(314.20, -284.51, 54.17), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(24.59, -946.56, 29.36), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(-254.41, -692.46, 33.61), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(-1570.20, -546.65, 34.96), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(-1415.91, -211.82, 46.50), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(-1430.11, -211.05, 46.50), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(33.23, -1347.32, 29.50), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(129.22, -1292.18, 29.27), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(287.64, -1282.32, 29.65), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(289.01, -1256.78, 29.44), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(1138.23, -468.89, 66.73), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(1167.06, -456.03, 66.79), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(2564.50, 2585.80, 38.08), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(1686.75, 4815.80, 42.01), sprite = 277, color = 2, name = "ATM"},
        {coords = vector3(-386.73, 6046.11, 31.50), sprite = 277, color = 2, name = "ATM"}
    },
    
    -- Hospitals
    hospitals = {
        {coords = vector3(1839.60, 3672.93, 34.28), sprite = 61, color = 1, name = "Sandy Shores Medical Center"},
        {coords = vector3(-247.76, 6331.23, 32.43), sprite = 61, color = 1, name = "Paleto Bay Medical Center"},
        {coords = vector3(357.43, -593.36, 28.79), sprite = 61, color = 1, name = "Pillbox Hill Medical Center"},
        {coords = vector3(295.83, -1446.94, 29.97), sprite = 61, color = 1, name = "Central Los Santos Medical Center"}
    },
    
    -- Police Stations
    policeStations = {
        {coords = vector3(425.13, -979.55, 30.71), sprite = 60, color = 29, name = "Mission Row Police Station"},
        {coords = vector3(1855.10, 3678.93, 33.82), sprite = 60, color = 29, name = "Sandy Shores Sheriff"},
        {coords = vector3(-438.95, 6019.81, 31.49), sprite = 60, color = 29, name = "Paleto Bay Sheriff"},
        {coords = vector3(-1092.18, -809.93, 19.00), sprite = 60, color = 29, name = "Vespucci Police Station"},
        {coords = vector3(638.17, 1.23, 82.79), sprite = 60, color = 29, name = "Vinewood Police Station"},
        {coords = vector3(361.91, -1584.78, 29.29), sprite = 60, color = 29, name = "Davis Police Station"}
    },
    
    -- Clothing Stores
    clothingStores = {
        {coords = vector3(72.25, -1399.10, 29.38), sprite = 73, color = 47, name = "Binco"},
        {coords = vector3(-703.78, -152.26, 37.42), sprite = 73, color = 47, name = "Binco"},
        {coords = vector3(-167.86, -298.99, 39.73), sprite = 73, color = 47, name = "Binco"},
        {coords = vector3(428.69, -800.11, 29.49), sprite = 73, color = 47, name = "Binco"},
        {coords = vector3(-829.41, -1073.71, 11.33), sprite = 73, color = 47, name = "Binco"},
        {coords = vector3(-1447.80, -242.46, 49.82), sprite = 73, color = 47, name = "Binco"},
        {coords = vector3(11.63, 6514.22, 31.88), sprite = 73, color = 47, name = "Binco"},
        {coords = vector3(123.65, -219.44, 54.56), sprite = 73, color = 47, name = "Binco"},
        {coords = vector3(1696.29, 4829.31, 42.06), sprite = 73, color = 47, name = "Binco"},
        {coords = vector3(618.09, 2759.63, 42.09), sprite = 73, color = 47, name = "Binco"},
        {coords = vector3(1190.55, 2713.44, 38.22), sprite = 73, color = 47, name = "Binco"},
        {coords = vector3(-1193.43, -772.26, 17.32), sprite = 73, color = 47, name = "Binco"},
        {coords = vector3(-3172.50, 1048.13, 20.86), sprite = 73, color = 47, name = "Binco"},
        {coords = vector3(-1108.44, 2708.92, 19.11), sprite = 73, color = 47, name = "Binco"}
    },
    
    -- Barber Shops
    barberShops = {
        {coords = vector3(-814.31, -183.82, 37.57), sprite = 71, color = 47, name = "Herr Kutz Barber"},
        {coords = vector3(136.83, -1708.37, 29.29), sprite = 71, color = 47, name = "Herr Kutz Barber"},
        {coords = vector3(-1282.60, -1116.77, 7.00), sprite = 71, color = 47, name = "Herr Kutz Barber"},
        {coords = vector3(1931.51, 3729.67, 32.84), sprite = 71, color = 47, name = "Herr Kutz Barber"},
        {coords = vector3(1212.78, -472.92, 66.21), sprite = 71, color = 47, name = "Herr Kutz Barber"},
        {coords = vector3(-32.89, -152.34, 57.08), sprite = 71, color = 47, name = "Herr Kutz Barber"},
        {coords = vector3(-278.08, 6228.46, 31.70), sprite = 71, color = 47, name = "Herr Kutz Barber"}
    }
}

-- Create blip function
function CreateBlip(coords, sprite, color, name, scale)
    local blip = AddBlipForCoord(coords.x, coords.y, coords.z)
    SetBlipSprite(blip, sprite)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, scale or 0.8)
    SetBlipColour(blip, color)
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(name)
    EndTextCommandSetBlipName(blip)
    return blip
end

-- Initialize all blips
function InitializeBlips()
    print("^3[Olympus Core]^7 Creating map blips...")
    
    -- Create shop blips
    for _, shop in pairs(BlipConfig.shops) do
        local blip = CreateBlip(shop.coords, shop.sprite, shop.color, shop.name)
        table.insert(blips, blip)
    end
    
    -- Create gas station blips
    for _, station in pairs(BlipConfig.gasStations) do
        local blip = CreateBlip(station.coords, station.sprite, station.color, station.name)
        table.insert(blips, blip)
    end
    
    -- Create ATM blips
    for _, atm in pairs(BlipConfig.atms) do
        local blip = CreateBlip(atm.coords, atm.sprite, atm.color, atm.name, 0.6)
        table.insert(blips, blip)
    end
    
    -- Create hospital blips
    for _, hospital in pairs(BlipConfig.hospitals) do
        local blip = CreateBlip(hospital.coords, hospital.sprite, hospital.color, hospital.name, 1.0)
        table.insert(blips, blip)
    end
    
    -- Create police station blips
    for _, station in pairs(BlipConfig.policeStations) do
        local blip = CreateBlip(station.coords, station.sprite, station.color, station.name, 1.0)
        table.insert(blips, blip)
    end
    
    -- Create clothing store blips
    for _, store in pairs(BlipConfig.clothingStores) do
        local blip = CreateBlip(store.coords, store.sprite, store.color, store.name)
        table.insert(blips, blip)
    end
    
    -- Create barber shop blips
    for _, barber in pairs(BlipConfig.barberShops) do
        local blip = CreateBlip(barber.coords, barber.sprite, barber.color, barber.name)
        table.insert(blips, blip)
    end
    
    print(string.format("^2[Olympus Core]^7 Created %d map blips", #blips))
end

-- Clean up blips
function CleanupBlips()
    for _, blip in pairs(blips) do
        if DoesBlipExist(blip) then
            RemoveBlip(blip)
        end
    end
    blips = {}
end

-- Initialize immediately when resource starts
CreateThread(function()
    -- Wait for game to be fully loaded
    while not NetworkIsSessionStarted() do
        Wait(100)
    end

    Wait(2000) -- Wait for everything to load
    print("^3[Olympus Core]^7 Starting blip initialization...")
    InitializeBlips()
end)

-- Cleanup on resource stop
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        CleanupBlips()
    end
end)

print("[Olympus Core] Blips system loaded")
