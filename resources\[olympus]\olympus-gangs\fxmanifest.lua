fx_version 'cerulean'
game 'gta5'

name 'Olympus Gang System'
description 'Gang and Cartel system for Olympus Framework'
author 'Olympus Development Team'
version '1.0.0'

dependencies {
    'olympus-core'
}

shared_scripts {
    'config/shared.lua',
    'config/cartels.lua'
}

server_scripts {
    'server/main.lua',
    'server/territories.lua',
    'server/wars.lua'
}

client_scripts {
    'client/main.lua',
    'client/blips.lua',
    'client/territories.lua'
}

-- No UI files (UI handled by olympus-ui)

exports {
    'IsPlayerInGang',
    'GetPlayerGang',
    'GetGangRank',
    'CanAccessCartel',
    'IsInGangTerritory',
    'GetNearestCartel'
}

server_exports {
    'CreateGang',
    'DisbandGang',
    'AddGangMember',
    'RemoveGangMember',
    'PromoteGangMember',
    'DemoteGangMember',
    'GetGangData',
    'GetAllGangs',
    'StartCartelCapture',
    'EndCartelCapture',
    'InitializeTerritories',
    'GetTerritoryAtPosition',
    'IsPlayerInTerritoryZone',
    'StartTerritoryCapture',
    'GetActiveTerritories',
    'InitializeWarSystem',
    'ArePlayersAtWar',
    'GetActiveWars',
    'GetWarInvites'
}
