-- Olympus Admin & Staff System - Complete Implementation
-- Based on Olympus Altis Life administrative tools and staff management

Config = {}

-- Admin System Settings
Config.AdminSystem = {
    enabled = true,
    
    -- Staff Hierarchy (Exact Olympus Structure)
    staffRanks = {
        [1] = {
            name = 'Support Team',
            permissions = {'spectate', 'teleport_to_player', 'basic_logs', 'kick_player'},
            color = '#00FF00'
        },
        [2] = {
            name = 'Moderator',
            permissions = {'ban_player', 'unban_player', 'advanced_logs', 'item_management'},
            color = '#FFFF00'
        },
        [3] = {
            name = 'Administrator',
            permissions = {'server_management', 'economy_tools', 'faction_management'},
            color = '#FF6600'
        },
        [4] = {
            name = 'Senior Administrator',
            permissions = {'database_access', 'system_override', 'staff_management'},
            color = '#FF0000'
        },
        [5] = {
            name = 'Community Manager',
            permissions = {'full_access', 'server_control', 'policy_enforcement'},
            color = '#9900FF'
        }
    }
}

-- Admin Panel System (Exact Olympus Implementation)
Config.AdminPanel = {
    enabled = true,
    
    -- Panel Access
    access = {
        keyBind = 'F10', -- F10 to open admin panel
        rankRequired = 1, -- Minimum rank 1 (Support Team)
        
        -- Panel Sections
        sections = {
            'player_management',
            'server_tools',
            'economy_tools',
            'faction_tools',
            'system_logs',
            'punishment_tools'
        }
    },
    
    -- Player Management Tools
    playerManagement = {
        enabled = true,
        
        -- Spectate System
        spectate = {
            enabled = true,
            rankRequired = 1, -- Support Team+
            
            -- Spectate Features
            features = {
                invisibleSpectate = true, -- Invisible to target player
                freeCam = true, -- Free camera movement
                playerFollow = true, -- Follow player camera
                inventoryView = true, -- View player inventory
                
                -- Spectate Controls
                controls = {
                    nextPlayer = 'PAGE_UP',
                    prevPlayer = 'PAGE_DOWN',
                    exitSpectate = 'ESC',
                    toggleUI = 'TAB'
                }
            }
        },
        
        -- Player Actions
        playerActions = {
            teleportToPlayer = {
                enabled = true,
                rankRequired = 1,
                logAction = true
            },
            teleportPlayerToMe = {
                enabled = true,
                rankRequired = 2,
                logAction = true
            },
            teleportPlayerToLocation = {
                enabled = true,
                rankRequired = 2,
                logAction = true
            },
            
            -- Health/Status Management
            revivePlayer = {
                enabled = true,
                rankRequired = 1,
                logAction = true
            },
            healPlayer = {
                enabled = true,
                rankRequired = 1,
                logAction = true
            },
            
            -- Punishment Tools
            kickPlayer = {
                enabled = true,
                rankRequired = 1,
                reasonRequired = true,
                logAction = true
            },
            banPlayer = {
                enabled = true,
                rankRequired = 2,
                reasonRequired = true,
                durationOptions = {
                    '1 Hour', '6 Hours', '24 Hours',
                    '3 Days', '7 Days', '30 Days', 'Permanent'
                },
                logAction = true
            }
        }
    },
    
    -- Server Tools
    serverTools = {
        enabled = true,
        rankRequired = 3, -- Administrator+
        
        -- Server Management
        management = {
            restartServer = {
                enabled = true,
                rankRequired = 4,
                confirmationRequired = true,
                warningTime = 300 -- 5 minutes warning
            },
            
            lockServer = {
                enabled = true,
                rankRequired = 3,
                whitelistBypass = true
            },
            
            serverAnnouncement = {
                enabled = true,
                rankRequired = 2,
                globalMessage = true,
                colorOptions = true
            }
        },
        
        -- Weather & Time Control
        environmentControl = {
            enabled = true,
            rankRequired = 3,
            
            -- Weather Control
            weather = {
                clear = true,
                cloudy = true,
                rain = true,
                storm = true,
                fog = true
            },
            
            -- Time Control
            time = {
                setTime = true,
                timeMultiplier = true,
                freezeTime = true
            }
        }
    },
    
    -- Economy Tools
    economyTools = {
        enabled = true,
        rankRequired = 3, -- Administrator+
        
        -- Money Management
        moneyManagement = {
            giveMoney = {
                enabled = true,
                maxAmount = ********, -- $10M maximum
                logAction = true
            },
            removeMoney = {
                enabled = true,
                maxAmount = ********,
                logAction = true
            },
            setBankBalance = {
                enabled = true,
                logAction = true
            }
        },
        
        -- Item Management
        itemManagement = {
            giveItem = {
                enabled = true,
                allItems = true,
                maxQuantity = 1000,
                logAction = true
            },
            removeItem = {
                enabled = true,
                fromInventory = true,
                logAction = true
            },
            clearInventory = {
                enabled = true,
                confirmationRequired = true,
                logAction = true
            }
        },
        
        -- Vehicle Management
        vehicleManagement = {
            spawnVehicle = {
                enabled = true,
                allVehicles = true,
                customPlate = true,
                logAction = true
            },
            deleteVehicle = {
                enabled = true,
                nearbyVehicles = true,
                logAction = true
            },
            repairVehicle = {
                enabled = true,
                fullRepair = true,
                logAction = true
            }
        }
    },
    
    -- Faction Tools
    factionTools = {
        enabled = true,
        rankRequired = 3, -- Administrator+
        
        -- APD Management
        apdManagement = {
            promoteOfficer = {
                enabled = true,
                rankRequired = 4,
                logAction = true
            },
            demoteOfficer = {
                enabled = true,
                rankRequired = 4,
                logAction = true
            },
            addToAPD = {
                enabled = true,
                rankRequired = 4,
                logAction = true
            },
            removeFromAPD = {
                enabled = true,
                rankRequired = 4,
                logAction = true
            }
        },
        
        -- R&R Management
        rnrManagement = {
            promoteMediac = {
                enabled = true,
                rankRequired = 4,
                logAction = true
            },
            demoteMediac = {
                enabled = true,
                rankRequired = 4,
                logAction = true
            },
            addToRnR = {
                enabled = true,
                rankRequired = 4,
                logAction = true
            },
            removeFromRnR = {
                enabled = true,
                rankRequired = 4,
                logAction = true
            }
        },
        
        -- Gang Management
        gangManagement = {
            disbandGang = {
                enabled = true,
                rankRequired = 3,
                confirmationRequired = true,
                logAction = true
            },
            modifyGangFunds = {
                enabled = true,
                rankRequired = 4,
                logAction = true
            },
            transferGangOwnership = {
                enabled = true,
                rankRequired = 4,
                logAction = true
            }
        }
    }
}

-- In-Game Report System (Exact Olympus Implementation)
Config.ReportSystem = {
    enabled = true,
    
    -- Report Menu Access
    access = {
        keyBind = 'F2', -- F2 to open report menu
        allPlayers = true, -- All players can report
        
        -- Report Categories
        categories = {
            'RDM/VDM',
            'Exploiting/Hacking',
            'FailRP/Trolling',
            'Harassment',
            'Bug Report',
            'Other'
        }
    },
    
    -- Report Processing
    processing = {
        enabled = true,
        
        -- Staff Notification
        staffNotification = {
            enabled = true,
            soundAlert = true,
            popupNotification = true,
            
            -- Notification Details
            details = {
                reporterName = true,
                reportedPlayer = true,
                category = true,
                description = true,
                timestamp = true,
                location = true
            }
        },
        
        -- Report Queue
        queue = {
            enabled = true,
            prioritySystem = true,
            
            -- Priority Levels
            priorities = {
                urgent = 1, -- Hacking/Exploiting
                high = 2, -- RDM/VDM
                medium = 3, -- FailRP/Trolling
                low = 4 -- Other issues
            }
        },
        
        -- Report Resolution
        resolution = {
            enabled = true,
            
            -- Resolution Options
            options = {
                'Resolved - Action Taken',
                'Resolved - No Action Required',
                'Resolved - Warning Issued',
                'Resolved - Player Punished',
                'Unable to Resolve'
            },
            
            -- Follow-up Actions
            followUp = {
                notifyReporter = true,
                logResolution = true,
                trackStatistics = true
            }
        }
    }
}

-- Infraction Logging System (Exact Olympus Implementation)
Config.InfractionSystem = {
    enabled = true,

    -- Infraction Types
    infractionTypes = {
        warning = {
            name = 'Warning',
            severity = 1,
            points = 1,
            color = '#FFFF00'
        },
        kick = {
            name = 'Kick',
            severity = 2,
            points = 2,
            color = '#FF6600'
        },
        tempBan = {
            name = 'Temporary Ban',
            severity = 3,
            points = 5,
            color = '#FF0000'
        },
        permBan = {
            name = 'Permanent Ban',
            severity = 4,
            points = 10,
            color = '#990000'
        }
    },

    -- Point System
    pointSystem = {
        enabled = true,

        -- Point Thresholds
        thresholds = {
            warning = 5, -- 5 points = automatic warning
            tempBan = 10, -- 10 points = temporary ban
            permBan = 20 -- 20 points = permanent ban
        },

        -- Point Decay
        decay = {
            enabled = true,
            decayRate = 1, -- 1 point per week
            decayInterval = 604800 -- 7 days
        }
    },

    -- Logging
    logging = {
        enabled = true,

        -- Log Details
        details = {
            staffMember = true,
            targetPlayer = true,
            infractionType = true,
            reason = true,
            timestamp = true,
            evidence = true,
            duration = true
        },

        -- Log Storage
        storage = {
            database = true,
            fileBackup = true,
            retentionPeriod = 31536000 -- 1 year
        }
    }
}

-- Vanish System (Exact Olympus Implementation)
Config.VanishSystem = {
    enabled = true,

    -- Vanish Mechanics
    mechanics = {
        rankRequired = 1, -- Support Team+

        -- Vanish Effects
        effects = {
            invisible = true, -- Invisible to players
            noCollision = true, -- No collision with players/vehicles
            noInteraction = true, -- Cannot interact with world
            godMode = true, -- Invincible while vanished

            -- Visual Indicators
            indicators = {
                nameTag = '[VANISHED]',
                transparency = 0.3, -- 30% transparency for staff
                particleEffect = false -- No particle effects
            }
        },

        -- Vanish Restrictions
        restrictions = {
            noVehicleEntry = true, -- Cannot enter vehicles
            noItemPickup = true, -- Cannot pick up items
            noPlayerDamage = true, -- Cannot damage players
            noWorldInteraction = true -- Cannot interact with world objects
        }
    },

    -- Auto-Vanish
    autoVanish = {
        enabled = true,
        onDuty = true, -- Auto-vanish when going on admin duty
        onSpectate = true -- Auto-vanish when spectating
    }
}

-- Pardon Override System (Exact Olympus Implementation)
Config.PardonOverride = {
    enabled = true,

    -- Override Authority
    authority = {
        rankRequired = 4, -- Senior Administrator+

        -- Override Types
        types = {
            emergencyPardon = {
                enabled = true,
                immediateEffect = true,
                reasonRequired = true,
                logAction = true
            },
            systemOverride = {
                enabled = true,
                bypassCooldown = true,
                bypassRequirements = true,
                logAction = true
            },
            massPardon = {
                enabled = true,
                multipleTargets = true,
                confirmationRequired = true,
                logAction = true
            }
        }
    },

    -- Override Logging
    logging = {
        enabled = true,
        detailedLogs = true,

        -- Log Details
        details = {
            adminName = true,
            targetPlayer = true,
            overrideType = true,
            reason = true,
            timestamp = true,
            chargesAffected = true
        }
    }
}

-- Bounty Deletion System (Exact Olympus Implementation)
Config.BountyDeletion = {
    enabled = true,

    -- Deletion Authority
    authority = {
        rankRequired = 3, -- Administrator+

        -- Deletion Types
        types = {
            singleBounty = {
                enabled = true,
                reasonRequired = true,
                logAction = true
            },
            multipleBounties = {
                enabled = true,
                confirmationRequired = true,
                logAction = true
            },
            fullWipe = {
                enabled = true,
                rankRequired = 4, -- Senior Administrator+
                doubleConfirmation = true,
                logAction = true
            }
        }
    },

    -- Deletion Reasons
    reasons = {
        'Administrative Error',
        'False Charges',
        'System Glitch',
        'Staff Discretion',
        'Player Appeal Approved',
        'Other (Specify)'
    }
}
