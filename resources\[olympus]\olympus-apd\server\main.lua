-- Olympus APD System - Main Server Script
-- Based on original Olympus APD functions from dump
-- Handles APD faction management, wanted system, processing, and core functionality

local OlympusAPD = {}
OlympusAPD.OnlineAPD = {}
OlympusAPD.WantedList = {}
OlympusAPD.ProcessingSuspects = {}
OlympusAPD.ActiveCalls = {}
OlympusAPD.JailedPlayers = {}

-- Initialize APD system
CreateThread(function()
    print("^2[Olympus APD]^7 Initializing APD system...")

    -- Initialize database tables
    OlympusAPD.InitializeDatabase()

    -- Load wanted list from database
    OlympusAPD.LoadWantedList()

    print("^2[Olympus APD]^7 APD system initialized!")
end)

-- Database initialization
function OlympusAPD.InitializeDatabase()
    -- Create wanted table
    exports['olympus-core']:ExecuteQuery([[
        CREATE TABLE IF NOT EXISTS `wanted` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `identifier` varchar(50) NOT NULL,
            `name` varchar(100) NOT NULL,
            `charges` text NOT NULL,
            `bounty` int(11) NOT NULL DEFAULT 0,
            `active` tinyint(1) NOT NULL DEFAULT 1,
            `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
            PRIMARY KEY (`id`),
            KEY `identifier` (`identifier`),
            KEY `active` (`active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]], {})

    -- Create apd_logs table
    exports['olympus-core']:ExecuteQuery([[
        CREATE TABLE IF NOT EXISTS `apd_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `officer_identifier` varchar(50) NOT NULL,
            `suspect_identifier` varchar(50) DEFAULT NULL,
            `action` varchar(100) NOT NULL,
            `details` text DEFAULT NULL,
            `timestamp` int(11) NOT NULL,
            PRIMARY KEY (`id`),
            KEY `officer_identifier` (`officer_identifier`),
            KEY `suspect_identifier` (`suspect_identifier`),
            KEY `timestamp` (`timestamp`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]], {})

    -- Create processing_sessions table
    exports['olympus-core']:ExecuteQuery([[
        CREATE TABLE IF NOT EXISTS `processing_sessions` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `officer_identifier` varchar(50) NOT NULL,
            `suspect_identifier` varchar(50) NOT NULL,
            `charges` text NOT NULL,
            `total_fine` int(11) NOT NULL DEFAULT 0,
            `outcome` varchar(50) DEFAULT NULL,
            `started_at` int(11) NOT NULL,
            `completed_at` int(11) DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `officer_identifier` (`officer_identifier`),
            KEY `suspect_identifier` (`suspect_identifier`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]], {})
end

-- Load wanted list from database
function OlympusAPD.LoadWantedList()
    exports['olympus-core']:ExecuteQuery('SELECT * FROM wanted WHERE active = 1', {}, function(result)
        if result then
            for _, row in ipairs(result) do
                local charges = json.decode(row.charges) or {}
                OlympusAPD.WantedList[row.identifier] = {
                    name = row.name,
                    identifier = row.identifier,
                    charges = charges,
                    bounty = row.bounty,
                    id = row.id
                }
            end
            print("^2[Olympus APD]^7 Loaded " .. #result .. " wanted entries")
        end
    end)
end

-- Player joined APD
RegisterServerEvent('olympus:apd:playerJoined')
AddEventHandler('olympus:apd:playerJoined', function(sourceOverride)
    local source = sourceOverride or source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if playerData and playerData.faction == Config.APDFaction then
        -- Generate callsign based on original Olympus system
        local callsign = OlympusAPD.GenerateCallsign(playerData.faction_rank or 1)

        OlympusAPD.OnlineAPD[source] = {
            source = source,
            identifier = playerData.identifier,
            name = playerData.name,
            rank = playerData.faction_rank or 1,
            callsign = callsign,
            onDuty = false,
            location = nil,
            status = 'available',
            lethalAuthorized = false
        }

        -- Notify other APD members
        TriggerClientEvent('olympus:apd:memberJoined', -1, OlympusAPD.OnlineAPD[source])

        -- Send APD data to player
        TriggerClientEvent('olympus:apd:initialize', source, {
            rank = playerData.faction_rank or 1,
            wantedList = OlympusAPD.WantedList,
            onlineAPD = OlympusAPD.OnlineAPD,
            charges = Config.Charges
        })

        local rankName = Config.Ranks[playerData.faction_rank or 1] and Config.Ranks[playerData.faction_rank or 1].name or "Cadet"
        print("^2[Olympus APD]^7 " .. playerData.name .. " joined APD as " .. rankName)
    end
end)

-- Player left APD
RegisterServerEvent('olympus:apd:playerLeft')
AddEventHandler('olympus:apd:playerLeft', function()
    local source = source

    if OlympusAPD.OnlineAPD[source] then
        local apdData = OlympusAPD.OnlineAPD[source]

        -- Clean up any active processing
        for suspectId, processing in pairs(OlympusAPD.ProcessingSuspects) do
            if processing.officer == source then
                OlympusAPD.ProcessingSuspects[suspectId] = nil
                TriggerClientEvent('olympus:apd:processingCancelled', suspectId, 'Officer disconnected')
            end
        end

        -- Notify other APD members
        TriggerClientEvent('olympus:apd:memberLeft', -1, source)

        -- Remove from online list
        OlympusAPD.OnlineAPD[source] = nil

        print("^3[Olympus APD]^7 " .. apdData.name .. " left APD")
    end
end)

-- Go on/off duty
RegisterServerEvent('olympus:apd:toggleDuty')
AddEventHandler('olympus:apd:toggleDuty', function()
    local source = source

    if OlympusAPD.OnlineAPD[source] then
        local apdData = OlympusAPD.OnlineAPD[source]
        apdData.onDuty = not apdData.onDuty

        -- Update player data in core system
        exports['olympus-core']:UpdatePlayerData(source, 'duty_status', apdData.onDuty)

        -- Update client
        TriggerClientEvent('olympus:apd:dutyStatus', source, apdData.onDuty)

        -- Notify other APD members
        TriggerClientEvent('olympus:apd:memberDutyChanged', -1, source, apdData.onDuty)

        -- Log action
        OlympusAPD.LogAction(source, 'duty_toggle', {
            onDuty = apdData.onDuty,
            rank = apdData.rank
        })

        local status = apdData.onDuty and "on duty" or "off duty"
        print("^3[Olympus APD]^7 " .. apdData.name .. " went " .. status)
    else
        -- Try to add them to APD if they have the faction
        local playerData = exports['olympus-core']:GetPlayerData(source)
        if playerData and playerData.faction == Config.APDFaction then
            TriggerEvent('olympus:apd:playerJoined', source)
        end
    end
end)

-- Update APD status
RegisterServerEvent('olympus:apd:updateStatus')
AddEventHandler('olympus:apd:updateStatus', function(status, location)
    local source = source

    if OlympusAPD.OnlineAPD[source] then
        OlympusAPD.OnlineAPD[source].status = status
        OlympusAPD.OnlineAPD[source].location = location

        -- Notify other APD members
        TriggerClientEvent('olympus:apd:memberStatusChanged', -1, source, status, location)
    end
end)

-- ========================================
-- WANTED SYSTEM (Based on original fn_wantedAdd.sqf)
-- ========================================

-- Add wanted charge
RegisterServerEvent('olympus:apd:addWanted')
AddEventHandler('olympus:apd:addWanted', function(targetIdentifier, targetName, chargeType, customBounty)
    local source = source

    if not OlympusAPD.OnlineAPD[source] then return end

    local charge = Config.Charges[chargeType]
    if not charge then return end

    local bounty = customBounty or charge.fine

    -- Check if player is already wanted
    if OlympusAPD.WantedList[targetIdentifier] then
        local wantedData = OlympusAPD.WantedList[targetIdentifier]

        -- Check if charge already exists
        local chargeExists = false
        for _, existingCharge in ipairs(wantedData.charges) do
            if existingCharge.name == charge.name then
                existingCharge.count = existingCharge.count + 1
                chargeExists = true
                break
            end
        end

        if not chargeExists then
            table.insert(wantedData.charges, {
                name = charge.name,
                count = 1,
                fine = charge.fine
            })
        end

        wantedData.bounty = wantedData.bounty + bounty
    else
        -- Add new wanted entry
        OlympusAPD.WantedList[targetIdentifier] = {
            name = targetName,
            identifier = targetIdentifier,
            charges = {{
                name = charge.name,
                count = 1,
                fine = charge.fine
            }},
            bounty = bounty
        }
    end

    -- Update database
    OlympusAPD.UpdateWantedDatabase(targetIdentifier)

    -- Notify target player if online
    local targetSource = OlympusAPD.GetPlayerByIdentifier(targetIdentifier)
    if targetSource then
        TriggerClientEvent('olympus:apd:wantedUpdated', targetSource, chargeType, targetIdentifier)
    end

    -- Notify all APD
    for apdSource, _ in pairs(OlympusAPD.OnlineAPD) do
        TriggerClientEvent('olympus:apd:wantedListUpdated', apdSource, OlympusAPD.WantedList)
    end

    -- Log action
    OlympusAPD.LogAction(source, 'wanted_add', {
        target = targetName,
        charge = charge.name,
        bounty = bounty
    })

    print("^3[Olympus APD]^7 Added wanted charge: " .. charge.name .. " for " .. targetName .. " ($" .. bounty .. ")")
end)

-- Remove wanted charge
RegisterServerEvent('olympus:apd:removeWanted')
AddEventHandler('olympus:apd:removeWanted', function(targetIdentifier, chargeType)
    local source = source

    if not OlympusAPD.OnlineAPD[source] then return end

    if OlympusAPD.WantedList[targetIdentifier] then
        local wantedData = OlympusAPD.WantedList[targetIdentifier]
        local charge = Config.Charges[chargeType]

        if charge then
            for i, existingCharge in ipairs(wantedData.charges) do
                if existingCharge.name == charge.name then
                    if existingCharge.count > 1 then
                        existingCharge.count = existingCharge.count - 1
                        wantedData.bounty = wantedData.bounty - existingCharge.fine
                    else
                        table.remove(wantedData.charges, i)
                        wantedData.bounty = wantedData.bounty - existingCharge.fine
                    end
                    break
                end
            end

            -- Remove from wanted list if no charges left
            if #wantedData.charges == 0 then
                OlympusAPD.WantedList[targetIdentifier] = nil
                -- Remove from database
                exports['olympus-core']:ExecuteQuery('UPDATE wanted SET active = 0 WHERE identifier = ?', {targetIdentifier})
            else
                -- Update database
                OlympusAPD.UpdateWantedDatabase(targetIdentifier)
            end

            -- Notify all APD
            for apdSource, _ in pairs(OlympusAPD.OnlineAPD) do
                TriggerClientEvent('olympus:apd:wantedListUpdated', apdSource, OlympusAPD.WantedList)
            end

            -- Log action
            OlympusAPD.LogAction(source, 'wanted_remove', {
                target = wantedData.name,
                charge = charge.name
            })
        end
    end
end)

-- Pardon player (remove all wanted charges)
RegisterServerEvent('olympus:apd:pardonPlayer')
AddEventHandler('olympus:apd:pardonPlayer', function(targetIdentifier, reason)
    local source = source

    if not OlympusAPD.OnlineAPD[source] then return end

    if OlympusAPD.WantedList[targetIdentifier] then
        local wantedData = OlympusAPD.WantedList[targetIdentifier]

        -- Remove from wanted list
        OlympusAPD.WantedList[targetIdentifier] = nil

        -- Update database
        exports['olympus-core']:ExecuteQuery('UPDATE wanted SET active = 0 WHERE identifier = ?', {targetIdentifier})

        -- Notify target player if online
        local targetSource = OlympusAPD.GetPlayerByIdentifier(targetIdentifier)
        if targetSource then
            TriggerClientEvent('olympus:apd:pardoned', targetSource, reason)
        end

        -- Notify all APD
        for apdSource, _ in pairs(OlympusAPD.OnlineAPD) do
            TriggerClientEvent('olympus:apd:wantedListUpdated', apdSource, OlympusAPD.WantedList)
        end

        -- Log action
        OlympusAPD.LogAction(source, 'pardon', {
            target = wantedData.name,
            reason = reason
        })

        print("^3[Olympus APD]^7 " .. wantedData.name .. " pardoned by " .. OlympusAPD.OnlineAPD[source].name)
    end
end)

-- ========================================
-- PROCESSING SYSTEM (Based on original processing mechanics)
-- ========================================

-- Start processing suspect
RegisterServerEvent('olympus:apd:startProcessing')
AddEventHandler('olympus:apd:startProcessing', function(suspectId)
    local source = source
    local suspectData = exports['olympus-core']:GetPlayerData(suspectId)

    if suspectData and OlympusAPD.OnlineAPD[source] then
        local processingData = {
            officer = source,
            suspect = suspectId,
            startTime = os.time(),
            maxTime = Config.Processing.maxTime or 900, -- 15 minutes default
            completed = false,
            charges = {},
            totalFine = 0,
            jailTime = 0
        }

        OlympusAPD.ProcessingSuspects[suspectId] = processingData

        -- Notify both officer and suspect
        TriggerClientEvent('olympus:apd:processingStarted', source, processingData)
        TriggerClientEvent('olympus:apd:beingProcessed', suspectId, processingData)

        -- Start processing timer
        CreateThread(function()
            Wait(processingData.maxTime * 1000)

            if OlympusAPD.ProcessingSuspects[suspectId] and not OlympusAPD.ProcessingSuspects[suspectId].completed then
                -- Auto-pardon if processing takes too long (15-minute rule)
                TriggerEvent('olympus:apd:completeProcessing', suspectId, 'pardon', 'Processing time exceeded (15-minute rule)')
            end
        end)

        -- Log processing start
        OlympusAPD.LogAction(source, 'processing_start', {
            suspect = suspectData.name,
            suspect_identifier = suspectData.identifier
        })

        print("^3[Olympus APD]^7 Processing started for " .. suspectData.name .. " by " .. OlympusAPD.OnlineAPD[source].name)
    end
end)

-- Add charge to processing
RegisterServerEvent('olympus:apd:addCharge')
AddEventHandler('olympus:apd:addCharge', function(suspectId, chargeType)
    local source = source

    if OlympusAPD.ProcessingSuspects[suspectId] and OlympusAPD.OnlineAPD[source] then
        local processing = OlympusAPD.ProcessingSuspects[suspectId]

        if processing.officer == source then
            local charge = Config.Charges[chargeType]
            if charge then
                table.insert(processing.charges, {
                    type = chargeType,
                    name = charge.name,
                    fine = charge.fine,
                    jailTime = charge.jailTime or 0
                })

                processing.totalFine = processing.totalFine + charge.fine
                processing.jailTime = processing.jailTime + (charge.jailTime or 0)

                -- Update both officer and suspect
                TriggerClientEvent('olympus:apd:chargeAdded', source, charge, processing.totalFine, processing.jailTime)
                TriggerClientEvent('olympus:apd:chargeAdded', suspectId, charge, processing.totalFine, processing.jailTime)

                print("^3[Olympus APD]^7 Charge added: " .. charge.name .. " ($" .. charge.fine .. ", " .. (charge.jailTime or 0) .. " min jail)")
            end
        end
    end
end)

-- Complete processing (Based on original processing outcomes)
RegisterServerEvent('olympus:apd:completeProcessing')
AddEventHandler('olympus:apd:completeProcessing', function(suspectId, action, reason)
    local source = source

    if OlympusAPD.ProcessingSuspects[suspectId] and OlympusAPD.OnlineAPD[source] then
        local processing = OlympusAPD.ProcessingSuspects[suspectId]

        if processing.officer == source or action == 'pardon' then
            processing.completed = true
            local suspectData = exports['olympus-core']:GetPlayerData(suspectId)

            if action == 'ticket' then
                -- Issue ticket (Based on original fn_ticketGive.sqf)
                OlympusAPD.IssueTicket(suspectId, processing.charges, processing.totalFine)

                -- Remove from wanted list if ticket covers bounty
                if suspectData and OlympusAPD.WantedList[suspectData.identifier] then
                    local wantedData = OlympusAPD.WantedList[suspectData.identifier]
                    if processing.totalFine >= wantedData.bounty then
                        OlympusAPD.WantedList[suspectData.identifier] = nil
                        exports['olympus-core']:ExecuteQuery('UPDATE wanted SET active = 0 WHERE identifier = ?', {suspectData.identifier})
                    end
                end

            elseif action == 'jail' then
                -- Send to jail (Based on original fn_jailSys.sqf)
                OlympusAPD.SendToJail(suspectId, processing.charges, processing.jailTime)

                -- Clear wanted status
                if suspectData and OlympusAPD.WantedList[suspectData.identifier] then
                    OlympusAPD.WantedList[suspectData.identifier] = nil
                    exports['olympus-core']:ExecuteQuery('UPDATE wanted SET active = 0 WHERE identifier = ?', {suspectData.identifier})
                end

            elseif action == 'pardon' then
                -- Pardon suspect
                TriggerClientEvent('olympus:apd:pardoned', suspectId, reason or 'Pardoned by officer')

                -- Clear wanted status
                if suspectData and OlympusAPD.WantedList[suspectData.identifier] then
                    OlympusAPD.WantedList[suspectData.identifier] = nil
                    exports['olympus-core']:ExecuteQuery('UPDATE wanted SET active = 0 WHERE identifier = ?', {suspectData.identifier})
                end
            end

            -- Log processing completion
            OlympusAPD.LogAction(source, 'processing_complete', {
                suspect = suspectData and suspectData.name or 'Unknown',
                action = action,
                charges = #processing.charges,
                fine = processing.totalFine,
                jailTime = processing.jailTime,
                reason = reason
            })

            -- Save processing session to database
            exports['olympus-core']:ExecuteQuery([[
                INSERT INTO processing_sessions (officer_identifier, suspect_identifier, charges, total_fine, outcome, started_at, completed_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ]], {
                OlympusAPD.OnlineAPD[source].identifier,
                suspectData and suspectData.identifier or 'unknown',
                json.encode(processing.charges),
                processing.totalFine,
                action,
                processing.startTime,
                os.time()
            })

            -- Clean up processing data
            OlympusAPD.ProcessingSuspects[suspectId] = nil

            -- Notify both parties
            TriggerClientEvent('olympus:apd:processingCompleted', source, action, reason)
            TriggerClientEvent('olympus:apd:processingCompleted', suspectId, action, reason)

            -- Update wanted list for all APD
            for apdSource, _ in pairs(OlympusAPD.OnlineAPD) do
                TriggerClientEvent('olympus:apd:wantedListUpdated', apdSource, OlympusAPD.WantedList)
            end
        end
    end
end)

-- ========================================
-- TICKET AND JAIL SYSTEM
-- ========================================

-- Issue ticket (Based on original fn_ticketGive.sqf)
function OlympusAPD.IssueTicket(suspectId, charges, totalFine)
    local suspectData = exports['olympus-core']:GetPlayerData(suspectId)
    if not suspectData then return end

    -- Check if player can afford the ticket
    local playerMoney = exports['olympus-core']:GetPlayerMoney(suspectId, 'bank') + exports['olympus-core']:GetPlayerMoney(suspectId, 'cash')

    if playerMoney >= totalFine then
        -- Take money from player
        if exports['olympus-core']:RemovePlayerMoney(suspectId, 'bank', totalFine) then
            -- Ticket paid successfully
            TriggerClientEvent('olympus:client:notify', suspectId, {
                type = 'success',
                title = 'Ticket Paid',
                message = 'You paid a ticket of $' .. totalFine
            })

            -- Log ticket payment
            exports['olympus-core']:LogAction(suspectId, 'ticket_paid', {
                amount = totalFine,
                charges = charges
            })
        else
            -- Try cash if bank failed
            if exports['olympus-core']:RemovePlayerMoney(suspectId, 'cash', totalFine) then
                TriggerClientEvent('olympus:client:notify', suspectId, {
                    type = 'success',
                    title = 'Ticket Paid',
                    message = 'You paid a ticket of $' .. totalFine
                })
            else
                -- Cannot afford ticket, send to jail
                OlympusAPD.SendToJail(suspectId, charges, math.ceil(totalFine / 1000)) -- $1000 per minute
            end
        end
    else
        -- Cannot afford ticket, send to jail
        OlympusAPD.SendToJail(suspectId, charges, math.ceil(totalFine / 1000)) -- $1000 per minute
    end
end

-- Send to jail (Based on original fn_jailSys.sqf)
function OlympusAPD.SendToJail(suspectId, charges, jailTime)
    local suspectData = exports['olympus-core']:GetPlayerData(suspectId)
    if not suspectData then return end

    -- Minimum 5 minutes, maximum based on charges
    jailTime = math.max(5, jailTime)

    OlympusAPD.JailedPlayers[suspectId] = {
        identifier = suspectData.identifier,
        name = suspectData.name,
        charges = charges,
        jailTime = jailTime,
        startTime = os.time(),
        releaseTime = os.time() + (jailTime * 60)
    }

    -- Teleport to jail and restrict player
    TriggerClientEvent('olympus:apd:sendToJail', suspectId, {
        jailTime = jailTime,
        charges = charges,
        location = Config.JailLocation
    })

    -- Start jail timer
    CreateThread(function()
        Wait(jailTime * 60 * 1000) -- Convert minutes to milliseconds

        if OlympusAPD.JailedPlayers[suspectId] then
            -- Release from jail
            OlympusAPD.JailedPlayers[suspectId] = nil
            TriggerClientEvent('olympus:apd:releaseFromJail', suspectId)

            TriggerClientEvent('olympus:client:notify', suspectId, {
                type = 'success',
                title = 'Released',
                message = 'You have been released from jail'
            })
        end
    end)

    print("^3[Olympus APD]^7 " .. suspectData.name .. " sent to jail for " .. jailTime .. " minutes")
end

-- ========================================
-- EQUIPMENT AND VEHICLE SYSTEM
-- ========================================

-- Request APD equipment (Based on original fn_copLoadout.sqf)
RegisterServerEvent('olympus:apd:requestEquipment')
AddEventHandler('olympus:apd:requestEquipment', function()
    local source = source

    if OlympusAPD.OnlineAPD[source] then
        local apdData = OlympusAPD.OnlineAPD[source]
        local rank = apdData.rank

        -- Get equipment based on rank
        local equipment = Config.EquipmentAccess[rank] or Config.EquipmentAccess[1]

        -- Give weapons
        for _, weapon in ipairs(equipment.weapons) do
            TriggerClientEvent('olympus:apd:giveWeapon', source, weapon)
        end

        -- Give gear
        for _, gear in ipairs(equipment.gear) do
            TriggerClientEvent('olympus:apd:giveGear', source, gear)
        end

        TriggerClientEvent('olympus:client:notify', source, {
            type = 'success',
            title = 'APD Equipment',
            message = 'Equipment issued for Rank ' .. rank
        })

        -- Log equipment request
        OlympusAPD.LogAction(source, 'equipment_request', {
            rank = rank,
            weapons = equipment.weapons,
            gear = equipment.gear
        })

        print("^2[Olympus APD]^7 Equipment issued to " .. apdData.name .. " (Rank " .. rank .. ")")
    end
end)

-- Spawn APD vehicle
RegisterServerEvent('olympus:apd:spawnVehicle')
AddEventHandler('olympus:apd:spawnVehicle', function(vehicleModel)
    local source = source

    if OlympusAPD.OnlineAPD[source] then
        local apdData = OlympusAPD.OnlineAPD[source]
        local rank = apdData.rank

        -- Check if player can use this vehicle
        local equipment = Config.EquipmentAccess[rank] or Config.EquipmentAccess[1]
        local canUse = false

        for _, vehicle in ipairs(equipment.vehicles) do
            if vehicle == vehicleModel then
                canUse = true
                break
            end
        end

        if canUse then
            TriggerClientEvent('olympus:apd:spawnVehicle', source, vehicleModel)
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'success',
                title = 'APD Vehicle',
                message = 'Spawning ' .. vehicleModel
            })

            -- Log vehicle spawn
            OlympusAPD.LogAction(source, 'vehicle_spawn', {
                vehicle = vehicleModel,
                rank = rank
            })

            print("^2[Olympus APD]^7 Vehicle " .. vehicleModel .. " spawned for " .. apdData.name)
        else
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'APD Vehicle',
                message = 'You do not have access to this vehicle (Rank ' .. rank .. ')'
            })
        end
    end
end)

-- ========================================
-- BACKUP AND COMMUNICATION SYSTEM
-- ========================================

-- Request backup
RegisterServerEvent('olympus:apd:requestBackup')
AddEventHandler('olympus:apd:requestBackup', function(coords, priority)
    local source = source

    if OlympusAPD.OnlineAPD[source] then
        local apdData = OlympusAPD.OnlineAPD[source]

        -- Notify all on-duty APD
        for playerId, data in pairs(OlympusAPD.OnlineAPD) do
            if data.onDuty and playerId ~= source then
                TriggerClientEvent('olympus:apd:backupRequest', playerId, {
                    officer = apdData.name,
                    callsign = apdData.callsign,
                    coords = coords,
                    priority = priority or 'normal',
                    timestamp = os.time()
                })
            end
        end

        -- Log backup request
        OlympusAPD.LogAction(source, 'backup_request', {
            coords = coords,
            priority = priority or 'normal'
        })

        print("^3[Olympus APD]^7 Backup requested by " .. apdData.name .. " (Priority: " .. (priority or 'normal') .. ")")
    end
end)

-- APD Chat system
RegisterServerEvent('olympus:apd:sendMessage')
AddEventHandler('olympus:apd:sendMessage', function(message)
    local source = source

    if OlympusAPD.OnlineAPD[source] then
        local apdData = OlympusAPD.OnlineAPD[source]

        -- Send to all on-duty APD
        for apdSource, data in pairs(OlympusAPD.OnlineAPD) do
            if data.onDuty then
                TriggerClientEvent('olympus:apd:chatMessage', apdSource, {
                    sender = apdData.name,
                    callsign = apdData.callsign,
                    rank = apdData.rank,
                    message = message,
                    timestamp = os.time()
                })
            end
        end

        print("^3[Olympus APD Chat]^7 [" .. apdData.callsign .. "] " .. apdData.name .. ": " .. message)
    end
end)

-- ========================================
-- UTILITY FUNCTIONS
-- ========================================

-- Generate callsign based on rank (Based on original Olympus callsign system)
function OlympusAPD.GenerateCallsign(rank)
    local prefix = ""

    if rank >= 8 then -- Chief
        prefix = "1-"
    elseif rank >= 7 then -- Deputy Chief
        prefix = "1-"
    elseif rank >= 6 then -- Captain
        prefix = "2-"
    elseif rank >= 5 then -- Lieutenant
        prefix = "2-"
    elseif rank >= 4 then -- Sergeant
        prefix = "3-"
    elseif rank >= 3 then -- Corporal
        prefix = "4-"
    else -- Deputy/Cadet
        prefix = "5-"
    end

    return prefix .. math.random(10, 99)
end

-- Update wanted database
function OlympusAPD.UpdateWantedDatabase(identifier)
    local wantedData = OlympusAPD.WantedList[identifier]
    if wantedData then
        exports['olympus-core']:ExecuteQuery([[
            INSERT INTO wanted (identifier, name, charges, bounty, active)
            VALUES (?, ?, ?, ?, 1)
            ON DUPLICATE KEY UPDATE
            name = VALUES(name),
            charges = VALUES(charges),
            bounty = VALUES(bounty),
            active = VALUES(active)
        ]], {
            identifier,
            wantedData.name,
            json.encode(wantedData.charges),
            wantedData.bounty
        })
    end
end

-- Get player by identifier
function OlympusAPD.GetPlayerByIdentifier(identifier)
    for source, playerData in pairs(exports['olympus-core']:GetAllPlayers()) do
        if playerData.identifier == identifier then
            return source
        end
    end
    return nil
end

-- Log APD action
function OlympusAPD.LogAction(source, action, details)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    if playerData then
        exports['olympus-core']:ExecuteQuery([[
            INSERT INTO apd_logs (officer_identifier, action, details, timestamp)
            VALUES (?, ?, ?, ?)
        ]], {
            playerData.identifier,
            action,
            json.encode(details or {}),
            os.time()
        })
    end
end

-- ========================================
-- EXPORT FUNCTIONS
-- ========================================

-- Get online APD count
function OlympusAPD.GetOnlineAPDCount()
    local count = 0
    for _, _ in pairs(OlympusAPD.OnlineAPD) do
        count = count + 1
    end
    return count
end

-- Get on-duty APD count
function OlympusAPD.GetOnDutyAPDCount()
    local count = 0
    for _, apdData in pairs(OlympusAPD.OnlineAPD) do
        if apdData.onDuty then
            count = count + 1
        end
    end
    return count
end

-- Check if player is APD
function OlympusAPD.IsPlayerAPD(source)
    return OlympusAPD.OnlineAPD[source] ~= nil
end

-- Get APD rank
function OlympusAPD.GetAPDRank(source)
    if OlympusAPD.OnlineAPD[source] then
        return OlympusAPD.OnlineAPD[source].rank
    end
    return 0
end

-- Check if player is on duty
function OlympusAPD.IsPlayerOnDuty(source)
    if OlympusAPD.OnlineAPD[source] then
        return OlympusAPD.OnlineAPD[source].onDuty
    end
    return false
end

-- Get wanted list
function OlympusAPD.GetWantedList()
    return OlympusAPD.WantedList
end

-- Check if player is wanted
function OlympusAPD.IsPlayerWanted(identifier)
    return OlympusAPD.WantedList[identifier] ~= nil
end

-- Get player bounty
function OlympusAPD.GetPlayerBounty(identifier)
    if OlympusAPD.WantedList[identifier] then
        return OlympusAPD.WantedList[identifier].bounty
    end
    return 0
end

-- Add wanted charge (for external resources)
function OlympusAPD.AddWantedCharge(identifier, name, chargeType, customBounty)
    TriggerEvent('olympus:apd:addWanted', -1, identifier, name, chargeType, customBounty)
end

-- Exports
exports('GetOnlineAPD', function() return OlympusAPD.OnlineAPD end)
exports('GetOnlineAPDCount', OlympusAPD.GetOnlineAPDCount)
exports('GetOnDutyAPDCount', OlympusAPD.GetOnDutyAPDCount)
exports('IsPlayerAPD', OlympusAPD.IsPlayerAPD)
exports('GetAPDRank', OlympusAPD.GetAPDRank)
exports('IsPlayerOnDuty', OlympusAPD.IsPlayerOnDuty)
exports('GetWantedList', OlympusAPD.GetWantedList)
exports('IsPlayerWanted', OlympusAPD.IsPlayerWanted)
exports('GetPlayerBounty', OlympusAPD.GetPlayerBounty)
exports('AddWantedCharge', OlympusAPD.AddWantedCharge)

-- ========================================
-- PLAYER DISCONNECT CLEANUP
-- ========================================

-- Handle player disconnect
AddEventHandler('playerDropped', function(reason)
    local source = source

    -- Clean up APD data
    if OlympusAPD.OnlineAPD[source] then
        TriggerEvent('olympus:apd:playerLeft')
    end

    -- Clean up processing data
    if OlympusAPD.ProcessingSuspects[source] then
        local processing = OlympusAPD.ProcessingSuspects[source]
        TriggerClientEvent('olympus:apd:processingCancelled', processing.officer, 'Suspect disconnected')
        OlympusAPD.ProcessingSuspects[source] = nil
    end

    -- Clean up jail data
    if OlympusAPD.JailedPlayers[source] then
        OlympusAPD.JailedPlayers[source] = nil
    end
end)

print("^2[Olympus APD]^7 Server system loaded successfully!")
