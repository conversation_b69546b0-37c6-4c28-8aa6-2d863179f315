-- ========================================
-- OLYMPUS TERRITORY SYSTEM - CLIENT
-- Handles territory blips, interactions, and capture UI
-- ========================================

local OlympusTerritories = {}
OlympusTerritories.TerritoryBlips = {}
OlympusTerritories.InTerritoryZone = nil
OlympusTerritories.CaptureUI = false

-- Territory blip configuration
local TERRITORY_BLIP_CONFIG = {
    sprite = 84, -- Territory blip sprite
    color = 1, -- Red for neutral/enemy
    scale = 1.2,
    shortRange = false,
    category = 2
}

-- Territory zone configuration
local TERRITORY_ZONE_CONFIG = {
    checkInterval = 1000, -- Check every second
    captureRadius = 95.0, -- 95 meter radius (matches server)
    blipRadius = 95.0 -- Zone blip radius
}

-- Initialize territory system
function InitializeTerritorySystem()
    print("^2[Olympus Territories]^7 Initializing client territory system...")
    
    -- Start territory zone checker
    StartTerritoryZoneChecker()
    
    -- Request territory data from server
    TriggerServerEvent('olympus:server:requestTerritoryData')
    
    print("^2[Olympus Territories]^7 Client territory system initialized!")
end

-- Create territory blips
function CreateTerritoryBlips(territories)
    -- Clear existing blips
    ClearTerritoryBlips()
    
    for territoryName, territory in pairs(territories) do
        if territory.position then
            -- Create main territory blip
            local blip = AddBlipForCoord(territory.position.x, territory.position.y, territory.position.z)
            SetBlipSprite(blip, TERRITORY_BLIP_CONFIG.sprite)
            SetBlipScale(blip, TERRITORY_BLIP_CONFIG.scale)
            SetBlipAsShortRange(blip, TERRITORY_BLIP_CONFIG.shortRange)
            SetBlipCategory(blip, TERRITORY_BLIP_CONFIG.category)
            
            -- Set blip color based on gang ownership
            local playerData = exports['olympus-core']:GetPlayerData()
            if territory.gang_id == 0 then
                SetBlipColour(blip, 0) -- White for neutral
            elseif playerData and playerData.gang_id == territory.gang_id then
                SetBlipColour(blip, 2) -- Green for owned by player's gang
            else
                SetBlipColour(blip, 1) -- Red for enemy gang
            end
            
            -- Set blip name
            local displayName = GetTerritoryDisplayName(territoryName)
            local blipName = string.format("%s (%s)", displayName, territory.gang_name)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(blipName)
            EndTextCommandSetBlipName(blip)
            
            -- Create capture zone blip (circle)
            local zoneBlip = AddBlipForRadius(territory.position.x, territory.position.y, territory.position.z, TERRITORY_ZONE_CONFIG.blipRadius)
            SetBlipColour(zoneBlip, 1) -- Red zone
            SetBlipAlpha(zoneBlip, 100) -- Semi-transparent
            
            -- Store blips
            OlympusTerritories.TerritoryBlips[territoryName] = {
                main = blip,
                zone = zoneBlip,
                position = territory.position,
                gang_id = territory.gang_id,
                gang_name = territory.gang_name
            }
            
            print(string.format("^3[Olympus Territories]^7 Created blip for %s at %s", territoryName, territory.position))
        end
    end
end

-- Clear territory blips
function ClearTerritoryBlips()
    for territoryName, blips in pairs(OlympusTerritories.TerritoryBlips) do
        if DoesBlipExist(blips.main) then
            RemoveBlip(blips.main)
        end
        if DoesBlipExist(blips.zone) then
            RemoveBlip(blips.zone)
        end
    end
    OlympusTerritories.TerritoryBlips = {}
end

-- Get territory display name
function GetTerritoryDisplayName(territoryName)
    local displayNames = {
        ["Meth"] = "Meth and Weed",
        ["Moonshine"] = "Moonshine and Heroin",
        ["Mushroom"] = "Mushroom and Cocaine",
        ["Arms"] = "Arms Dealer"
    }
    return displayNames[territoryName] or territoryName
end

-- Start territory zone checker
function StartTerritoryZoneChecker()
    CreateThread(function()
        while true do
            Wait(TERRITORY_ZONE_CONFIG.checkInterval)
            
            local playerCoords = GetEntityCoords(PlayerPedId())
            local currentTerritory = nil
            
            -- Check if player is in any territory zone
            for territoryName, blips in pairs(OlympusTerritories.TerritoryBlips) do
                if blips.position then
                    local distance = #(playerCoords - blips.position)
                    if distance <= TERRITORY_ZONE_CONFIG.captureRadius then
                        currentTerritory = territoryName
                        break
                    end
                end
            end
            
            -- Handle territory zone changes
            if currentTerritory ~= OlympusTerritories.InTerritoryZone then
                if OlympusTerritories.InTerritoryZone then
                    -- Left territory zone
                    OnLeaveTerritoryZone(OlympusTerritories.InTerritoryZone)
                end
                
                if currentTerritory then
                    -- Entered territory zone
                    OnEnterTerritoryZone(currentTerritory)
                end
                
                OlympusTerritories.InTerritoryZone = currentTerritory
            end
        end
    end)
end

-- Handle entering territory zone
function OnEnterTerritoryZone(territoryName)
    local territory = OlympusTerritories.TerritoryBlips[territoryName]
    if not territory then return end
    
    local displayName = GetTerritoryDisplayName(territoryName)
    local message = string.format("Entered %s territory", displayName)
    
    if territory.gang_name ~= "Neutral" then
        message = message .. string.format(" (Controlled by %s)", territory.gang_name)
    end
    
    exports['olympus-ui']:ShowNotification({
        type = 'info',
        title = 'Territory',
        message = message,
        duration = 3000
    })
    
    -- Show capture instructions if player can capture
    local playerData = exports['olympus-core']:GetPlayerData()
    if playerData and playerData.gang_id and playerData.gang_id > 0 and playerData.gang_rank >= 3 then
        if territory.gang_id ~= playerData.gang_id then
            exports['olympus-ui']:ShowNotification({
                type = 'info',
                title = 'Territory Capture',
                message = 'Press [E] to start capturing this territory',
                duration = 5000
            })
        end
    end
    
    print(string.format("^3[Olympus Territories]^7 Entered territory: %s", territoryName))
end

-- Handle leaving territory zone
function OnLeaveTerritoryZone(territoryName)
    local displayName = GetTerritoryDisplayName(territoryName)
    
    exports['olympus-ui']:ShowNotification({
        type = 'info',
        title = 'Territory',
        message = string.format("Left %s territory", displayName),
        duration = 2000
    })
    
    print(string.format("^3[Olympus Territories]^7 Left territory: %s", territoryName))
end

-- Handle territory capture key press
CreateThread(function()
    while true do
        Wait(0)
        
        if OlympusTerritories.InTerritoryZone then
            -- Show capture prompt
            local territory = OlympusTerritories.TerritoryBlips[OlympusTerritories.InTerritoryZone]
            if territory then
                local playerData = exports['olympus-core']:GetPlayerData()
                if playerData and playerData.gang_id and playerData.gang_id > 0 and playerData.gang_rank >= 3 then
                    if territory.gang_id ~= playerData.gang_id then
                        -- Draw capture prompt
                        DrawText3D(territory.position.x, territory.position.y, territory.position.z + 2.0, 
                            "[E] Capture Territory", 255, 255, 255, 255)
                        
                        -- Check for key press
                        if IsControlJustPressed(0, 38) then -- E key
                            TriggerServerEvent('olympus:server:startTerritoryCapture', OlympusTerritories.InTerritoryZone)
                        end
                    end
                end
            end
        else
            Wait(500) -- Reduce frequency when not in territory
        end
    end
end)

-- Draw 3D text
function DrawText3D(x, y, z, text, r, g, b, a)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())
    local dist = GetDistanceBetweenCoords(px, py, pz, x, y, z, 1)
    
    local scale = (1 / dist) * 2
    local fov = (1 / GetGameplayCamFov()) * 100
    local scale = scale * fov
    
    if onScreen then
        SetTextScale(0.0 * scale, 0.55 * scale)
        SetTextFont(0)
        SetTextProportional(1)
        SetTextColour(r, g, b, a)
        SetTextDropshadow(0, 0, 0, 0, 255)
        SetTextEdge(2, 0, 0, 0, 150)
        SetTextDropShadow()
        SetTextOutline()
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x, _y)
    end
end

-- Network events
RegisterNetEvent('olympus:client:updateTerritories')
AddEventHandler('olympus:client:updateTerritories', function(territories)
    CreateTerritoryBlips(territories)
end)

RegisterNetEvent('olympus:client:territoryUpdate')
AddEventHandler('olympus:client:territoryUpdate', function(territoryName, gangId, gangName, captureProgress)
    local territory = OlympusTerritories.TerritoryBlips[territoryName]
    if territory then
        territory.gang_id = gangId
        territory.gang_name = gangName
        
        -- Update blip color
        local playerData = exports['olympus-core']:GetPlayerData()
        if gangId == 0 then
            SetBlipColour(territory.main, 0) -- White for neutral
        elseif playerData and playerData.gang_id == gangId then
            SetBlipColour(territory.main, 2) -- Green for owned by player's gang
        else
            SetBlipColour(territory.main, 1) -- Red for enemy gang
        end
        
        -- Update blip name
        local displayName = GetTerritoryDisplayName(territoryName)
        local blipName = string.format("%s (%s)", displayName, gangName)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(blipName)
        EndTextCommandSetBlipName(territory.main)
    end
end)

-- Initialize when loaded
CreateThread(function()
    -- Wait for core to be ready
    while not exports['olympus-core']:GetPlayerData() do
        Wait(100)
    end
    
    Wait(5000) -- Additional delay for gang system to load
    InitializeTerritorySystem()
end)

print("^2[Olympus Territories]^7 Client territory system loaded!")
