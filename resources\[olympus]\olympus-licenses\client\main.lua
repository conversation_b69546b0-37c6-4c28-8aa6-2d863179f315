-- ========================================
-- OLYMPUS LICENSES SYSTEM - CLIENT MAIN
-- Complete recreation based on original Olympus license mechanics
-- Handles DMV interactions, license purchasing, and management
-- ========================================

local OlympusLicenses = {}
OlympusLicenses.PlayerLicenses = {}
OlympusLicenses.AvailableLicenses = {}
OlympusLicenses.MenuOpen = false

-- Configuration is loaded via shared_scripts in fxmanifest.lua

-- Initialize license system
function InitializeLicenseSystem()
    print("^2[Olympus Licenses]^7 Initializing client license system...")

    -- Initialize DMV locations
    InitializeDMVLocations()

    -- Initialize gun store locations
    InitializeGunStoreLocations()

    print("^2[Olympus Licenses]^7 Client license system initialized!")
end

-- Initialize DMV locations
function InitializeDMVLocations()
    CreateThread(function()
        -- Wait for olympus-ui to be loaded
        while not exports['olympus-ui'] or not exports['olympus-ui']:IsLoaded() do
            Wait(1000)
        end

        for i, location in ipairs(Config.Licensing.dmvLocations) do
            -- Create DMV blip
            local blip = AddBlipForCoord(location.x, location.y, location.z)
            SetBlipSprite(blip, 408) -- DMV icon
            SetBlipDisplay(blip, 4)
            SetBlipScale(blip, 0.8)
            SetBlipColour(blip, 3) -- Green
            SetBlipAsShortRange(blip, true)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString("DMV")
            EndTextCommandSetBlipName(blip)

            -- Create interaction point
            exports['olympus-ui']:AddInteraction({
                coords = location,
                distance = 3.0,
                text = 'Press [E] to access DMV Services',
                action = function()
                    OpenDMVMenu()
                end
            })

            print(string.format("^3[Olympus Licenses]^7 DMV location %d initialized", i))
        end
    end)
end

-- Initialize gun store locations
function InitializeGunStoreLocations()
    CreateThread(function()
        -- Wait for olympus-ui to be loaded
        while not exports['olympus-ui'] or not exports['olympus-ui']:IsLoaded() do
            Wait(1000)
        end

        for i, location in ipairs(Config.Licensing.gunStoreLocations) do
            -- Create gun store interaction for licenses
            exports['olympus-ui']:AddInteraction({
                coords = location,
                distance = 3.0,
                text = 'Press [E] to purchase Firearm Licenses',
                action = function()
                    OpenGunStoreLicenseMenu()
                end
            })

            print(string.format("^3[Olympus Licenses]^7 Gun store license location %d initialized", i))
        end
    end)
end

-- Open DMV menu
function OpenDMVMenu()
    if OlympusLicenses.MenuOpen then return end

    OlympusLicenses.MenuOpen = true

    -- Request available licenses for DMV
    TriggerServerEvent('olympus-licenses:server:requestAvailableLicenses', 'dmv')

    -- Wait for response and open menu
    CreateThread(function()
        Wait(500) -- Wait for server response

        exports['olympus-ui']:OpenLicenseMenu({
            title = 'DMV Services',
            description = 'Purchase driving and transportation licenses',
            location = 'dmv',
            licenses = OlympusLicenses.AvailableLicenses,
            playerMoney = exports['olympus-core']:GetPlayerMoney(),
            playerBank = exports['olympus-core']:GetPlayerBank()
        })
    end)
end

-- Open gun store license menu
function OpenGunStoreLicenseMenu()
    if OlympusLicenses.MenuOpen then return end

    OlympusLicenses.MenuOpen = true

    -- Request available licenses for gun store
    TriggerServerEvent('olympus-licenses:server:requestAvailableLicenses', 'gun_store')

    -- Wait for response and open menu
    CreateThread(function()
        Wait(500) -- Wait for server response

        exports['olympus-ui']:OpenLicenseMenu({
            title = 'Firearm License Center',
            description = 'Purchase firearm and weapon licenses',
            location = 'gun_store',
            licenses = OlympusLicenses.AvailableLicenses,
            playerMoney = exports['olympus-core']:GetPlayerMoney(),
            playerBank = exports['olympus-core']:GetPlayerBank()
        })
    end)
end

-- Close license menu
function CloseLicenseMenu()
    OlympusLicenses.MenuOpen = false

    -- Close UI
    exports['olympus-ui']:CloseLicenseMenu()

    -- Restore NUI focus
    SetNuiFocus(false, false)
end

-- Event Handlers
RegisterNetEvent('olympus-licenses:client:updateLicenses')
AddEventHandler('olympus-licenses:client:updateLicenses', function(licenses)
    OlympusLicenses.PlayerLicenses = licenses
end)

RegisterNetEvent('olympus-licenses:client:updateAvailableLicenses')
AddEventHandler('olympus-licenses:client:updateAvailableLicenses', function(licenses)
    OlympusLicenses.AvailableLicenses = licenses
end)

-- NUI Callbacks
RegisterNUICallback('closeLicenseMenu', function(data, cb)
    CloseLicenseMenu()
    cb('ok')
end)

RegisterNUICallback('purchaseLicense', function(data, cb)
    TriggerServerEvent('olympus-licenses:server:purchaseLicense', data.licenseType, data.location)
    cb('ok')
end)

RegisterNUICallback('requestLicenses', function(data, cb)
    TriggerServerEvent('olympus-licenses:server:requestLicenses')
    cb('ok')
end)

-- Check if player has license
function HasLicense(licenseType)
    for _, license in ipairs(OlympusLicenses.PlayerLicenses) do
        if license.type == licenseType then
            return true
        end
    end
    return false
end

-- Initialize system when client starts
CreateThread(function()
    -- Wait for core system
    while not exports['olympus-core']:IsPlayerLoaded() do
        Wait(1000)
    end

    Wait(2000) -- Additional wait for UI system
    InitializeLicenseSystem()

    -- Request player's current licenses
    TriggerServerEvent('olympus-licenses:server:requestLicenses')
end)

-- Handle ESC key to close license menu
CreateThread(function()
    while true do
        Wait(0)
        if OlympusLicenses.MenuOpen then
            if IsControlJustPressed(0, 322) then -- ESC key
                CloseLicenseMenu()
            end
        else
            Wait(500)
        end
    end
end)

-- Export functions
exports('HasLicense', HasLicense)

exports('GetPlayerLicenses', function()
    return OlympusLicenses.PlayerLicenses
end)

exports('OpenLicenseMenu', function(location)
    if location == 'dmv' then
        OpenDMVMenu()
    elseif location == 'gun_store' then
        OpenGunStoreLicenseMenu()
    else
        OpenDMVMenu() -- Default to DMV
    end
end)

exports('RefreshLicenses', function()
    TriggerServerEvent('olympus-licenses:server:requestLicenses')
end)

print("^2[Olympus Licenses]^7 Client module loaded")
