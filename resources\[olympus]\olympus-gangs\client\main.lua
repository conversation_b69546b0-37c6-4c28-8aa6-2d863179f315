-- ========================================
-- OLYMPUS GANG SYSTEM - CLIENT MAIN
-- Complete recreation based on original Olympus dump
-- Matches original client-side gang functions and UI
-- ========================================

local isLoaded = false
local playerGang = {
    id = -1,
    name = "",
    rank = -1,
    bank = 0
}

-- Gang rank names (matches server-side)
local GANG_RANKS = {
    [0] = "Recruit",
    [1] = "Member",
    [2] = "Trusted",
    [3] = "Lieutenant",
    [4] = "Captain",
    [5] = "Leader"
}

-- Gang invitation data
local pendingInvite = nil

-- Initialize when core is ready
CreateThread(function()
    while not exports['olympus-core']:IsPlayerLoaded() do
        Wait(1000)
    end

    -- Load player gang data
    LoadPlayerGangData()

    isLoaded = true
    print("^2[Olympus Gangs]^7 Client initialized")
end)

-- Load player gang data from core
function LoadPlayerGangData()
    local playerData = exports['olympus-core']:GetPlayerData()
    if playerData then
        playerGang.id = playerData.gang_id or -1
        playerGang.name = playerData.gang_name or ""
        playerGang.rank = playerData.gang_rank or -1

        if playerGang.id > 0 then
            print(string.format("^3[Olympus Gangs]^7 Player is in gang: %s (Rank: %s)",
                playerGang.name, GANG_RANKS[playerGang.rank] or "Unknown"))
        end
    end
end

-- Update gang data from server
RegisterNetEvent('olympus:client:updateGangData')
AddEventHandler('olympus:client:updateGangData', function(gangData)
    if gangData then
        playerGang.id = gangData.gang_id or -1
        playerGang.name = gangData.gang_name or ""
        playerGang.rank = gangData.gang_rank or -1

        print(string.format("^3[Olympus Gangs]^7 Gang data updated: %s (Rank: %s)",
            playerGang.name, GANG_RANKS[playerGang.rank] or "Unknown"))
    end
end)

-- Handle gang invitation
RegisterNetEvent('olympus:client:gangInvite')
AddEventHandler('olympus:client:gangInvite', function(inviteData)
    pendingInvite = inviteData

    -- Show invitation notification
    exports['olympus-ui']:ShowNotification({
        type = 'info',
        title = 'Gang Invitation',
        message = string.format('%s invited you to join %s as %s',
            inviteData.inviter_name,
            inviteData.gang_name,
            GANG_RANKS[inviteData.rank] or "Unknown"),
        duration = 10000
    })

    -- Show accept/decline options
    ShowGangInviteDialog(inviteData)
end)

-- Show gang invitation dialog
function ShowGangInviteDialog(inviteData)
    -- This would integrate with the Y menu or create a custom dialog
    -- For now, use chat commands
    TriggerEvent('chat:addMessage', {
        color = {0, 255, 0},
        multiline = true,
        args = {"Gang System", string.format("You have been invited to join %s by %s", inviteData.gang_name, inviteData.inviter_name)}
    })

    TriggerEvent('chat:addMessage', {
        color = {255, 255, 0},
        multiline = true,
        args = {"Gang System", "Type /acceptgang to accept or /declinegang to decline"}
    })
end

-- Accept gang invitation command
RegisterCommand('acceptgang', function()
    if pendingInvite then
        TriggerServerEvent('olympus:server:acceptGangInvite',
            pendingInvite.gang_id,
            pendingInvite.gang_name,
            pendingInvite.rank)

        pendingInvite = nil

        exports['olympus-ui']:ShowNotification({
            type = 'success',
            title = 'Gang Invitation',
            message = 'Gang invitation accepted!',
            duration = 3000
        })
    else
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Gang Invitation',
            message = 'No pending gang invitation',
            duration = 3000
        })
    end
end, false)

-- Decline gang invitation command
RegisterCommand('declinegang', function()
    if pendingInvite then
        pendingInvite = nil

        exports['olympus-ui']:ShowNotification({
            type = 'info',
            title = 'Gang Invitation',
            message = 'Gang invitation declined',
            duration = 3000
        })
    else
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Gang Invitation',
            message = 'No pending gang invitation',
            duration = 3000
        })
    end
end, false)

-- Gang info command
RegisterCommand('gang', function()
    if playerGang.id > 0 then
        exports['olympus-ui']:ShowNotification({
            type = 'info',
            title = 'Gang Information',
            message = string.format('Gang: %s | Rank: %s (%s)',
                playerGang.name,
                playerGang.rank,
                GANG_RANKS[playerGang.rank] or "Unknown"),
            duration = 5000
        })
    else
        exports['olympus-ui']:ShowNotification({
            type = 'warning',
            title = 'No Gang',
            message = 'You are not in a gang. Use /creategang [name] to create one.',
            duration = 5000
        })
    end
end, false)

-- Gang bank balance update
RegisterNetEvent('olympus:client:gangBankBalance')
AddEventHandler('olympus:client:gangBankBalance', function(balance)
    playerGang.bank = balance

    exports['olympus-ui']:ShowNotification({
        type = 'info',
        title = 'Gang Bank',
        message = string.format('Gang Bank Balance: $%s', balance),
        duration = 5000
    })
end)

-- Gang members list
RegisterNetEvent('olympus:client:gangMembersList')
AddEventHandler('olympus:client:gangMembersList', function(members)
    if not members or #members == 0 then
        exports['olympus-ui']:ShowNotification({
            type = 'info',
            title = 'Gang Members',
            message = 'No gang members found',
            duration = 3000
        })
        return
    end

    -- Display members list (this would integrate with Y menu)
    local membersList = "Gang Members:\n"
    for _, member in ipairs(members) do
        local status = member.online and "Online" or "Offline"
        membersList = membersList .. string.format("• %s (%s) - %s\n",
            member.name, member.rank_name, status)
    end

    TriggerEvent('chat:addMessage', {
        color = {0, 255, 255},
        multiline = true,
        args = {"Gang System", membersList}
    })
end)

-- Gang bank commands
RegisterCommand('gangbank', function()
    if playerGang.id <= 0 then
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Gang Bank',
            message = 'You are not in a gang',
            duration = 3000
        })
        return
    end

    -- Request gang bank balance
    TriggerServerEvent('olympus:server:gangBank', 'get_balance')
end, false)

RegisterCommand('gangdeposit', function(source, args)
    if playerGang.id <= 0 then
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Gang Bank',
            message = 'You are not in a gang',
            duration = 3000
        })
        return
    end

    if #args < 1 then
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Usage',
            message = '/gangdeposit [amount]',
            duration = 3000
        })
        return
    end

    local amount = tonumber(args[1])
    if not amount or amount <= 0 then
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Gang Bank',
            message = 'Invalid amount',
            duration = 3000
        })
        return
    end

    TriggerServerEvent('olympus:server:gangBank', 'deposit', amount)
end, false)

RegisterCommand('gangwithdraw', function(source, args)
    if playerGang.id <= 0 then
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Gang Bank',
            message = 'You are not in a gang',
            duration = 3000
        })
        return
    end

    if #args < 1 then
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Usage',
            message = '/gangwithdraw [amount]',
            duration = 3000
        })
        return
    end

    local amount = tonumber(args[1])
    if not amount or amount <= 0 then
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Gang Bank',
            message = 'Invalid amount',
            duration = 3000
        })
        return
    end

    TriggerServerEvent('olympus:server:gangBank', 'withdraw', amount)
end, false)

-- Gang members command
RegisterCommand('gangmembers', function()
    if playerGang.id <= 0 then
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Gang Members',
            message = 'You are not in a gang',
            duration = 3000
        })
        return
    end

    TriggerServerEvent('olympus:server:getGangMembers')
end, false)

-- Gang invite command
RegisterCommand('ganginvite', function(source, args)
    if playerGang.id <= 0 then
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Gang Management',
            message = 'You are not in a gang',
            duration = 3000
        })
        return
    end

    if playerGang.rank < 4 then
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Gang Management',
            message = 'You do not have permission to invite members',
            duration = 3000
        })
        return
    end

    if #args < 1 then
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Usage',
            message = '/ganginvite [player_id]',
            duration = 3000
        })
        return
    end

    local targetId = tonumber(args[1])
    if not targetId then
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Gang Management',
            message = 'Invalid player ID',
            duration = 3000
        })
        return
    end

    -- Get target player identifier
    local targetIdentifier = GetPlayerIdentifier(targetId, 0)
    if targetIdentifier then
        TriggerServerEvent('olympus:server:gangMember', 'invite', targetIdentifier, 0)
    else
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Gang Management',
            message = 'Player not found',
            duration = 3000
        })
    end
end, false)

-- Exports for other resources
exports('IsLoaded', function()
    return isLoaded
end)

exports('GetPlayerGang', function()
    return playerGang
end)

exports('IsInGang', function()
    return playerGang.id > 0
end)

exports('GetGangRank', function()
    return playerGang.rank
end)

exports('GetGangRankName', function()
    return GANG_RANKS[playerGang.rank] or "Unknown"
end)

exports('CanInviteMembers', function()
    return playerGang.rank >= 4
end)

exports('CanWithdrawFromBank', function()
    return playerGang.rank >= 3
end)

print("^2[Olympus Gangs]^7 Client main loaded successfully!")
