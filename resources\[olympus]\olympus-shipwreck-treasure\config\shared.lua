Config = {}

-- Shipwreck & Treasure System Configuration
Config.Shipwreck = {
    -- Shipwreck zones (based on original wreck_1 through wreck_6 markers)
    zones = {
        {
            name = "Shipwreck Zone 1",
            coords = vector3(-1770.0, -1220.0, -30.0), -- Deep water near coast
            radius = 50.0,
            blip = {sprite = 371, color = 27, scale = 0.8} -- Purple shipwreck icon
        },
        {
            name = "Shipwreck Zone 2", 
            coords = vector3(-2890.0, 2850.0, -25.0), -- North coast waters
            radius = 50.0,
            blip = {sprite = 371, color = 27, scale = 0.8}
        },
        {
            name = "Shipwreck Zone 3",
            coords = vector3(3300.0, 5200.0, -35.0), -- Northeast waters
            radius = 50.0,
            blip = {sprite = 371, color = 27, scale = 0.8}
        },
        {
            name = "Shipwreck Zone 4",
            coords = vector3(4100.0, 4200.0, -40.0), -- East coast deep waters
            radius = 50.0,
            blip = {sprite = 371, color = 27, scale = 0.8}
        },
        {
            name = "Shipwreck Zone 5",
            coords = vector3(2800.0, -2900.0, -45.0), -- South waters
            radius = 50.0,
            blip = {sprite = 371, color = 27, scale = 0.8}
        },
        {
            name = "Shipwreck Zone 6",
            coords = vector3(-3500.0, 1200.0, -30.0), -- West coast waters
            radius = 50.0,
            blip = {sprite = 371, color = 27, scale = 0.8}
        }
    },
    
    -- Treasure hunting settings
    settings = {
        minDepth = -12.0, -- Minimum depth required (12m underwater)
        maxShipwreckDistance = 20.0, -- Max distance from actual shipwreck object
        searchDuration = 4000, -- 4 seconds per search (matching original)
        cooldownBetweenSearches = 1000, -- 1 second between searches
        maxInventoryWeight = 100, -- Weight limit for searching
        requireScubaGear = false, -- Whether scuba gear is required
        oxygenConsumption = true -- Whether searching consumes oxygen
    },
    
    -- Loot table (based on original weighted selection)
    lootTable = {
        -- Format: {item, weight, sellPrice}
        {item = "scrap", weight = 0.7, sellPrice = 50, name = "Scrap Metal"},
        {item = "coin", weight = 0.13, sellPrice = 200, name = "Ancient Coin"},
        {item = "wpearl", weight = 0.4, sellPrice = 800, name = "White Pearl"},
        {item = "bpearl", weight = 0.4, sellPrice = 1200, name = "Black Pearl"},
        {item = "emerald", weight = 0.3, sellPrice = 2500, name = "Emerald"},
        {item = "amethyst", weight = 0.3, sellPrice = 2200, name = "Amethyst"},
        {item = "topazr", weight = 0.7, sellPrice = 1800, name = "Topaz"},
        {item = "rum", weight = 0.13, sellPrice = 300, name = "Aged Rum"},
        {item = "lockpick", weight = 0.45, sellPrice = 150, name = "Rusty Lockpick"},
        {item = "glass", weight = 0.3, sellPrice = 75, name = "Sea Glass"}
    },
    
    -- Shipwreck objects (props that need to be near player)
    shipwreckObjects = {
        "prop_wreck_cargoship",
        "prop_wreck_tug",
        "prop_wreck_yacht",
        "prop_container_01a",
        "prop_container_01b",
        "prop_container_01c",
        "prop_container_01d",
        "prop_container_01e",
        "prop_container_01f",
        "prop_container_01g",
        "prop_container_01h",
        "prop_container_02a",
        "prop_container_02b",
        "prop_container_03a",
        "prop_container_03b",
        "prop_container_03_ld",
        "prop_container_04a",
        "prop_container_04b",
        "prop_container_05a",
        "prop_container_05b",
        "prop_container_05c",
        "prop_container_05d",
        "prop_container_05e",
        "prop_container_05f",
        "prop_container_05g",
        "prop_container_05h"
    },
    
    -- Processing locations for selling treasure
    processingLocations = {
        {
            name = "Black Market Fence",
            coords = vector3(707.0, -966.0, 30.4),
            heading = 0.0,
            blip = {sprite = 500, color = 1, scale = 0.7},
            sellMultiplier = 1.0,
            requiresLicense = false
        },
        {
            name = "Pawn Shop",
            coords = vector3(182.0, -1319.0, 29.3),
            heading = 0.0,
            blip = {sprite = 500, color = 5, scale = 0.7},
            sellMultiplier = 0.8, -- Lower prices but legal
            requiresLicense = false
        },
        {
            name = "Antique Dealer",
            coords = vector3(-1455.0, -411.0, 35.9),
            heading = 0.0,
            blip = {sprite = 500, color = 3, scale = 0.7},
            sellMultiplier = 1.2, -- Higher prices for rare items
            requiresLicense = false
        }
    },
    
    -- Animations and effects
    animations = {
        searchAnimation = {
            dict = "amb@world_human_gardener_plant@male@base",
            anim = "base",
            duration = 4000
        },
        underwaterEffects = true,
        particleEffects = true
    },
    
    -- Notifications
    notifications = {
        showZoneNotifications = true,
        showLootNotifications = true,
        showDepthWarnings = true,
        showInventoryWarnings = true
    }
}

-- Debug settings
Config.Debug = false
