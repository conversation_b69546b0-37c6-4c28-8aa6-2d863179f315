-- Olympus Admin System - Shared Configuration
-- Complete administration and moderation system

Config = {}

-- Admin Levels
Config.AdminLevels = {
    [0] = {
        name = 'Player',
        color = '#FFFFFF',
        permissions = {}
    },
    [1] = {
        name = 'Moderator',
        color = '#00FF00',
        permissions = {
            'kick', 'warn', 'mute', 'freeze', 'goto', 'bring', 'spectate',
            'noclip', 'godmode', 'invisible', 'revive', 'heal', 'armor',
            'vehicle_spawn', 'vehicle_delete', 'vehicle_repair', 'vehicle_flip'
        }
    },
    [2] = {
        name = 'Administrator',
        color = '#0066FF',
        permissions = {
            'kick', 'warn', 'mute', 'freeze', 'goto', 'bring', 'spectate',
            'noclip', 'godmode', 'invisible', 'revive', 'heal', 'armor',
            'vehicle_spawn', 'vehicle_delete', 'vehicle_repair', 'vehicle_flip',
            'ban_temp', 'unban', 'money_give', 'money_take', 'item_give',
            'item_take', 'job_set', 'faction_set', 'license_give', 'license_take',
            'weather_set', 'time_set', 'announcement', 'clear_chat'
        }
    },
    [3] = {
        name = 'Senior Admin',
        color = '#FF6600',
        permissions = {
            'kick', 'warn', 'mute', 'freeze', 'goto', 'bring', 'spectate',
            'noclip', 'godmode', 'invisible', 'revive', 'heal', 'armor',
            'vehicle_spawn', 'vehicle_delete', 'vehicle_repair', 'vehicle_flip',
            'ban_temp', 'ban_perm', 'unban', 'money_give', 'money_take',
            'item_give', 'item_take', 'job_set', 'faction_set', 'license_give',
            'license_take', 'weather_set', 'time_set', 'announcement', 'clear_chat',
            'player_wipe', 'gang_disband', 'event_start', 'event_stop',
            'server_restart', 'resource_restart', 'resource_stop', 'resource_start'
        }
    },
    [4] = {
        name = 'Head Admin',
        color = '#FF0000',
        permissions = {
            'kick', 'warn', 'mute', 'freeze', 'goto', 'bring', 'spectate',
            'noclip', 'godmode', 'invisible', 'revive', 'heal', 'armor',
            'vehicle_spawn', 'vehicle_delete', 'vehicle_repair', 'vehicle_flip',
            'ban_temp', 'ban_perm', 'unban', 'money_give', 'money_take',
            'item_give', 'item_take', 'job_set', 'faction_set', 'license_give',
            'license_take', 'weather_set', 'time_set', 'announcement', 'clear_chat',
            'player_wipe', 'gang_disband', 'event_start', 'event_stop',
            'server_restart', 'resource_restart', 'resource_stop', 'resource_start',
            'admin_promote', 'admin_demote', 'database_access', 'logs_access'
        }
    },
    [5] = {
        name = 'Owner',
        color = '#800080',
        permissions = {
            'all' -- Owner has all permissions
        }
    }
}

-- Command Categories
Config.CommandCategories = {
    ['player'] = {
        name = 'Player Management',
        icon = 'fas fa-users',
        commands = {
            'kick', 'ban', 'unban', 'warn', 'mute', 'unmute', 'freeze', 'unfreeze'
        }
    },
    ['teleport'] = {
        name = 'Teleportation',
        icon = 'fas fa-location-arrow',
        commands = {
            'goto', 'bring', 'teleport', 'tpall', 'tphere'
        }
    },
    ['vehicle'] = {
        name = 'Vehicle Management',
        icon = 'fas fa-car',
        commands = {
            'car', 'dv', 'fix', 'flip', 'fuel', 'engine'
        }
    },
    ['player_edit'] = {
        name = 'Player Editing',
        icon = 'fas fa-edit',
        commands = {
            'setjob', 'setfaction', 'givemoney', 'takemoney', 'giveitem', 'takeitem'
        }
    },
    ['server'] = {
        name = 'Server Management',
        icon = 'fas fa-server',
        commands = {
            'restart', 'announce', 'weather', 'time', 'save'
        }
    },
    ['utility'] = {
        name = 'Utilities',
        icon = 'fas fa-tools',
        commands = {
            'noclip', 'godmode', 'invisible', 'spectate', 'revive', 'heal'
        }
    }
}

-- Ban Reasons
Config.BanReasons = {
    ['cheating'] = {
        name = 'Cheating/Hacking',
        defaultDuration = 0, -- Permanent
        severity = 5
    },
    ['exploiting'] = {
        name = 'Exploiting',
        defaultDuration = 604800, -- 7 days
        severity = 4
    },
    ['rdm'] = {
        name = 'Random Death Match',
        defaultDuration = 86400, -- 1 day
        severity = 2
    },
    ['vdm'] = {
        name = 'Vehicle Death Match',
        defaultDuration = 86400, -- 1 day
        severity = 2
    },
    ['failrp'] = {
        name = 'Fail Roleplay',
        defaultDuration = 43200, -- 12 hours
        severity = 1
    },
    ['metagaming'] = {
        name = 'Metagaming',
        defaultDuration = 86400, -- 1 day
        severity = 3
    },
    ['powergaming'] = {
        name = 'Powergaming',
        defaultDuration = 43200, -- 12 hours
        severity = 2
    },
    ['nlr'] = {
        name = 'New Life Rule Violation',
        defaultDuration = 21600, -- 6 hours
        severity = 1
    },
    ['combat_logging'] = {
        name = 'Combat Logging',
        defaultDuration = 86400, -- 1 day
        severity = 3
    },
    ['harassment'] = {
        name = 'Harassment',
        defaultDuration = 259200, -- 3 days
        severity = 4
    },
    ['spam'] = {
        name = 'Spamming',
        defaultDuration = 10800, -- 3 hours
        severity = 1
    },
    ['advertising'] = {
        name = 'Advertising',
        defaultDuration = 86400, -- 1 day
        severity = 2
    }
}

-- Warning System
Config.WarningSystem = {
    enabled = true,
    maxWarnings = 5,
    warningExpiry = 2592000, -- 30 days
    autoActions = {
        [3] = {action = 'kick', reason = 'Too many warnings'},
        [5] = {action = 'ban', duration = 86400, reason = 'Maximum warnings reached'}
    }
}

-- Anti-Cheat Settings
Config.AntiCheat = {
    enabled = true,
    
    -- Speed Detection
    speedCheck = {
        enabled = true,
        maxSpeed = 200, -- km/h
        teleportThreshold = 500, -- meters
        checkInterval = 1000 -- 1 second
    },
    
    -- God Mode Detection
    godModeCheck = {
        enabled = true,
        checkInterval = 5000, -- 5 seconds
        damageThreshold = 100
    },
    
    -- Weapon Detection
    weaponCheck = {
        enabled = true,
        blacklistedWeapons = {
            'weapon_railgun',
            'weapon_minigun',
            'weapon_firework'
        }
    },
    
    -- Resource Injection
    resourceCheck = {
        enabled = true,
        whitelistedResources = {
            'olympus-core',
            'olympus-admin',
            'olympus-ui'
        }
    },
    
    -- Money Detection
    moneyCheck = {
        enabled = true,
        maxMoney = 999999999,
        suspiciousAmount = 10000000
    }
}

-- Logging Settings
Config.Logging = {
    enabled = true,
    
    -- Log Categories
    categories = {
        ['admin'] = {
            name = 'Admin Actions',
            enabled = true,
            webhook = 'ADMIN_WEBHOOK_URL'
        },
        ['moderation'] = {
            name = 'Moderation Actions',
            enabled = true,
            webhook = 'MODERATION_WEBHOOK_URL'
        },
        ['player'] = {
            name = 'Player Actions',
            enabled = true,
            webhook = 'PLAYER_WEBHOOK_URL'
        },
        ['economy'] = {
            name = 'Economy Actions',
            enabled = true,
            webhook = 'ECONOMY_WEBHOOK_URL'
        },
        ['security'] = {
            name = 'Security Events',
            enabled = true,
            webhook = 'SECURITY_WEBHOOK_URL'
        }
    },
    
    -- Discord Integration
    discord = {
        enabled = true,
        botToken = 'YOUR_BOT_TOKEN',
        guildId = 'YOUR_GUILD_ID',
        logChannel = 'YOUR_LOG_CHANNEL_ID'
    }
}

-- Spectate Settings
Config.Spectate = {
    enabled = true,
    showHUD = false,
    showChat = true,
    allowMovement = true,
    invisibleToTarget = true
}

-- NoClip Settings
Config.NoClip = {
    enabled = true,
    speed = {
        normal = 1.0,
        fast = 2.0,
        slow = 0.5
    },
    controls = {
        toggle = 'F2',
        speedUp = 'LSHIFT',
        speedDown = 'LCTRL'
    }
}

-- Teleport Locations
Config.TeleportLocations = {
    ['spawn'] = {
        name = 'Spawn',
        coords = vector3(-1037.0, -2737.0, 20.0)
    },
    ['lspd'] = {
        name = 'LSPD',
        coords = vector3(425.1, -979.5, 30.7)
    },
    ['hospital'] = {
        name = 'Hospital',
        coords = vector3(298.6, -1448.1, 29.9)
    },
    ['airport'] = {
        name = 'Airport',
        coords = vector3(-1037.0, -2737.0, 20.0)
    },
    ['sandy'] = {
        name = 'Sandy Shores',
        coords = vector3(1855.0, 3678.0, 33.0)
    },
    ['paleto'] = {
        name = 'Paleto Bay',
        coords = vector3(-254.8, 6324.5, 32.4)
    },
    ['prison'] = {
        name = 'Prison',
        coords = vector3(1845.9, 2585.9, 46.0)
    },
    ['federal'] = {
        name = 'Federal Reserve',
        coords = vector3(243.2, 225.5, 106.3)
    },
    ['blackwater'] = {
        name = 'Blackwater',
        coords = vector3(2447.9, 1576.9, 33.0)
    }
}

-- Server Monitoring
Config.Monitoring = {
    enabled = true,
    
    -- Performance Monitoring
    performance = {
        enabled = true,
        checkInterval = 30000, -- 30 seconds
        thresholds = {
            serverMs = 50,
            clientMs = 30,
            memoryMB = 1024
        }
    },
    
    -- Player Monitoring
    players = {
        enabled = true,
        trackPlaytime = true,
        trackActions = true,
        trackLocations = true
    },
    
    -- Resource Monitoring
    resources = {
        enabled = true,
        trackUsage = true,
        alertOnFailure = true
    }
}

-- Menu Settings
Config.Menu = {
    enabled = true,
    keybind = 'F6',
    position = 'center',
    theme = 'dark',
    animations = true,
    sounds = true
}

-- Report System
Config.Reports = {
    enabled = true,
    maxReports = 10, -- Per player
    reportCooldown = 300, -- 5 minutes
    autoAssign = true,
    categories = {
        'Player Report',
        'Bug Report',
        'Suggestion',
        'Other'
    }
}
