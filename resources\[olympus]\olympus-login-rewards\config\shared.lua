-- Olympus Login Rewards System - Complete Implementation
-- Based on Olympus Altis Life login reward mechanics with exact specifications

Config = {}

-- Login Rewards System Settings
Config.LoginRewardsSystem = {
    enabled = true,
    
    -- System Requirements
    requirements = {
        minimumPlaytime = 900, -- 15 minutes minimum playtime before rolling reward
        dailyLogin = true, -- Must login daily to maintain streak
        
        -- Reset Schedule
        resetSchedule = {
            time = '07:30', -- 7:30 AM EST
            timezone = 'EST',
            dailyReset = true -- Daily rewards reset at this time
        }
    },
    
    -- Access Methods
    access = {
        command = {
            enabled = true,
            command = ';login', -- ;login command in side chat
            description = 'Access your daily login rewards'
        },
        
        yMenu = {
            enabled = true,
            location = 'below_licenses', -- Below licenses in Y-menu
            menuName = 'Login Rewards'
        }
    },
    
    -- Reward Claiming
    claiming = {
        sameDayOnly = true, -- Rewards must be redeemed the day they are rolled
        notification = true, -- Notify when reward is eligible
        
        -- House Deposit System
        houseDeposit = {
            enabled = true,
            prompt = "What house would you like your daily login reward deposited to?",
            
            -- House Comp System
            houseComp = {
                enabled = true,
                stackable = true, -- House comp will stack
                claimMethod = 'windows_key_house', -- Windows key on house
                claimOption = 'Receive House Comp'
            }
        }
    }
}

-- Default Daily Rewards (Exact Olympus Implementation)
Config.DefaultRewards = {
    enabled = true,
    
    -- Reward Pool (Equal Chance for All)
    rewardPool = {
        cash = {
            enabled = true,
            type = 'cash',
            amount = {min = 6969, max = 50000}, -- $6,969 - $50,000
            weight = 1 -- Equal chance
        },
        
        scrap = {
            enabled = true,
            type = 'item',
            item = 'scrap',
            quantity = {min = 83, max = 500}, -- 83-500 Scrap
            weight = 1
        },
        
        processingBuff = {
            enabled = true,
            type = 'buff',
            buff = 'processing_speed',
            percentage = 0.05, -- 5% Processing Buff
            duration = 86400, -- 24 hours
            weight = 1
        },
        
        pickingBuff = {
            enabled = true,
            type = 'buff',
            buff = 'picking_speed',
            percentage = 0.25, -- 25% Picking Buff
            duration = 86400, -- 24 hours
            weight = 1
        },
        
        vigilanteRenown = {
            enabled = true,
            type = 'faction_currency',
            currency = 'vigilante_renown',
            amount = {min = 2, max = 3}, -- 2-3 Vigilante Renown
            restriction = 'vigilante_only', -- Vigilante exclusive
            weight = 1
        },
        
        warPoints = {
            enabled = true,
            type = 'faction_currency',
            currency = 'war_points',
            amount = {min = 2, max = 3}, -- 2-3 War Points
            weight = 1
        },
        
        vehicles = {
            enabled = true,
            type = 'vehicle',
            vehicles = {
                'quadbike', 'gokart', 'hatchback', 
                'hatchback_sport', 'suv', 'offroad'
            },
            randomSelection = true, -- Random vehicle from list
            weight = 1
        },
        
        consumables = {
            enabled = true,
            type = 'item_bundle',
            items = {
                {item = 'epi', quantity = 1},
                {item = 'redgull', quantity = 10},
                {item = 'toolkit', quantity = 1},
                {item = 'pepsi', quantity = 10},
                {item = 'cupcake', quantity = 10},
                {item = 'beer', quantity = 10}
            },
            randomSelection = true, -- Random item from list
            weight = 1
        }
    }
}

-- Milestone Rewards (Exact Olympus Implementation)
Config.MilestoneRewards = {
    enabled = true,
    
    -- 7 Day Milestone
    sevenDays = {
        enabled = true,
        milestone = 7,
        exclusiveMilestone = true, -- Uses milestone pool exclusively
        
        rewardPool = {
            cash = {
                type = 'cash',
                amount = 100000, -- $100,000
                weight = 1
            },
            
            weapons = {
                type = 'weapon',
                weapons = {'pdw2000', 'protector'},
                randomSelection = true,
                weight = 1
            },
            
            gatherBuff = {
                type = 'buff',
                buff = 'gather_speed',
                percentage = 0.50, -- 50% Gather Buff
                duration = 86400,
                weight = 1
            },
            
            processingBuff = {
                type = 'buff',
                buff = 'processing_speed',
                percentage = 0.10, -- 10% Processing Buff
                duration = 86400,
                weight = 1
            },
            
            warPoints = {
                type = 'faction_currency',
                currency = 'war_points',
                amount = {min = 2, max = 3},
                weight = 1
            },
            
            vigilanteRenown = {
                type = 'faction_currency',
                currency = 'vigilante_renown',
                amount = {min = 2, max = 3},
                weight = 1
            },
            
            wplVehicle = {
                type = 'vehicle',
                vehicle = 'van_transport',
                restriction = 'wpl_only', -- WPL Exclusive
                weight = 1
            },
            
            rebelVehicle = {
                type = 'vehicle',
                vehicle = 'hatchback_sport',
                restriction = 'rebel_only', -- Rebel Exclusive
                weight = 1
            },
            
            hexIcon = {
                type = 'cosmetic',
                item = 'hex_icon',
                quantity = 1,
                weight = 1
            }
        }
    },
    
    -- 14 Day Milestone
    fourteenDays = {
        enabled = true,
        milestone = 14,
        exclusiveMilestone = true,
        
        rewardPool = {
            cash = {
                type = 'cash',
                amount = 250000, -- $250,000
                weight = 1
            },
            
            weapon = {
                type = 'weapon',
                weapon = 'mk20',
                weight = 1
            },
            
            gatherBuff = {
                type = 'buff',
                buff = 'gather_speed',
                percentage = 1.00, -- 100% Gather Buff
                duration = 86400,
                weight = 1
            },
            
            processingBuff = {
                type = 'buff',
                buff = 'processing_speed',
                percentage = 0.15, -- 15% Processing Buff
                duration = 86400,
                weight = 1
            },
            
            warPoints = {
                type = 'faction_currency',
                currency = 'war_points',
                amount = {min = 5, max = 8}, -- 5-8 War Points
                weight = 1
            },
            
            vigilanteRenown = {
                type = 'faction_currency',
                currency = 'vigilante_renown',
                amount = {min = 5, max = 8}, -- 5-8 Vigilante Renown
                weight = 1
            },
            
            wplVehicle = {
                type = 'vehicle',
                vehicle = 'zamak_transport',
                restriction = 'wpl_only',
                weight = 1
            },
            
            rebelVehicle = {
                type = 'vehicle',
                vehicle = 'qilin_light',
                restriction = 'rebel_only',
                weight = 1
            },
            
            hexIcon = {
                type = 'cosmetic',
                item = 'hex_icon',
                quantity = 1,
                weight = 1
            },
            
            title = {
                type = 'title',
                title = 'Hotstreak',
                guaranteed = true, -- Guaranteed reward
                weight = 0 -- Not in random pool
            }
        }
    },
    
    -- 30 Day Milestone
    thirtyDays = {
        enabled = true,
        milestone = 30,
        exclusiveMilestone = true,
        
        rewardPool = {
            cash = {
                type = 'cash',
                amount = 250000, -- $250,000
                weight = 1
            },
            
            processingBuff = {
                type = 'buff',
                buff = 'processing_speed',
                percentage = 0.25, -- 25% Processing Buff
                duration = 86400,
                weight = 1
            },
            
            warPoints = {
                type = 'faction_currency',
                currency = 'war_points',
                amount = 10, -- 10 War Points
                weight = 1
            },
            
            vigilanteRenown = {
                type = 'faction_currency',
                currency = 'vigilante_renown',
                amount = 10, -- 10 Vigilante Renown
                weight = 1
            },
            
            wplVehicle = {
                type = 'vehicle',
                vehicle = 'hemtt_box',
                restriction = 'wpl_only',
                weight = 1
            },
            
            rebelVehicle = {
                type = 'vehicle',
                vehicle = 'hummingbird',
                restriction = 'rebel_only',
                weight = 1
            },
            
            hexIcon = {
                type = 'cosmetic',
                item = 'hex_icon',
                quantity = 1,
                weight = 1
            },
            
            title = {
                type = 'title',
                title = 'Dedicated',
                guaranteed = true,
                weight = 0
            }
        }
    },
    
    -- 60 Day Milestone
    sixtyDays = {
        enabled = true,
        milestone = 60,
        exclusiveMilestone = true,
        
        rewardPool = {
            cash = {
                type = 'cash',
                amount = 250000, -- $250,000
                weight = 1
            },
            
            warPoints = {
                type = 'faction_currency',
                currency = 'war_points',
                amount = 10, -- 10 War Points
                weight = 1
            },
            
            vigilanteRenown = {
                type = 'faction_currency',
                currency = 'vigilante_renown',
                amount = 10, -- 10 Vigilante Renown
                weight = 1
            },
            
            wplVehicle = {
                type = 'vehicle',
                vehicle = 'tempest_device',
                restriction = 'wpl_only',
                weight = 1
            },
            
            rebelVehicle = {
                type = 'vehicle',
                vehicle = 'qilin',
                restriction = 'rebel_only',
                weight = 1
            },
            
            hexIcons = {
                type = 'cosmetic',
                item = 'hex_icon',
                quantity = 2, -- 2 Hex Icons
                weight = 1
            },
            
            title = {
                type = 'title',
                title = 'No Life',
                guaranteed = true,
                weight = 0
            }
        }
    },
    
    -- 90 Day Milestone
    ninetyDays = {
        enabled = true,
        milestone = 90,
        exclusiveMilestone = true,
        
        rewardPool = {
            cash = {
                type = 'cash',
                amount = 350000, -- $350,000
                weight = 1
            },
            
            warPoints = {
                type = 'faction_currency',
                currency = 'war_points',
                amount = 15, -- 15 War Points
                weight = 1
            },
            
            vigilanteRenown = {
                type = 'faction_currency',
                currency = 'vigilante_renown',
                amount = 15, -- 15 Vigilante Renown
                weight = 1
            },
            
            wplVehicle = {
                type = 'vehicle',
                vehicle = 'tempest_device',
                restriction = 'wpl_only',
                weight = 1
            },
            
            rebelVehicle = {
                type = 'vehicle',
                vehicle = 'strider',
                restriction = 'rebel_only',
                weight = 1
            },
            
            hexIcons = {
                type = 'cosmetic',
                item = 'hex_icon',
                quantity = 3, -- 3 Hex Icons
                weight = 1
            },
            
            title = {
                type = 'title',
                title = 'Olympian',
                guaranteed = true,
                weight = 0
            },
            
            vehicleSkin = {
                type = 'vehicle_skin',
                vehicle = 'hatchback_sport',
                skin = 'login_skin_90_day',
                guaranteed = true, -- Guaranteed reward
                weight = 0
            }
        }
    }
}

-- Streak Management System
Config.StreakManagement = {
    enabled = true,
    
    -- Streak Tracking
    streakTracking = {
        enabled = true,
        
        -- Streak Rules
        rules = {
            consecutiveDays = true, -- Must be consecutive days
            missedDayResetsStreak = true, -- Missing a day resets streak
            gracePeriodsDisabled = false, -- No grace periods
            
            -- Milestone Detection
            milestoneDetection = {
                enabled = true,
                checkOnLogin = true, -- Check milestone on login
                automaticReward = true -- Automatically give milestone reward
            }
        }
    },
    
    -- Streak Rewards
    streakRewards = {
        enabled = true,
        
        -- Reward Selection
        rewardSelection = {
            milestoneDay = 'milestone_pool', -- Use milestone pool on milestone days
            defaultDay = 'default_pool', -- Use default pool on regular days
            equalChance = true, -- All rewards in pool have equal chance
            
            -- Guaranteed Rewards
            guaranteedRewards = {
                enabled = true,
                additionalToRandom = true, -- Guaranteed rewards are additional to random
                
                -- Guaranteed Items by Milestone
                guaranteedByMilestone = {
                    [14] = {'title_hotstreak'},
                    [30] = {'title_dedicated'},
                    [60] = {'title_no_life'},
                    [90] = {'title_olympian', 'hatchback_sport_login_skin'}
                }
            }
        }
    }
}

-- Integration Systems
Config.IntegrationSystems = {
    -- Faction Integration
    factionIntegration = {
        enabled = true,
        
        -- Faction-Specific Rewards
        factionRewards = {
            vigilante = {
                enabled = true,
                exclusiveRewards = {'vigilante_renown'},
                restrictedRewards = {'wpl_vehicles', 'rebel_vehicles'}
            },
            
            wpl = {
                enabled = true,
                exclusiveRewards = {'wpl_vehicles'},
                restrictedRewards = {'rebel_vehicles'}
            },
            
            rebel = {
                enabled = true,
                exclusiveRewards = {'rebel_vehicles'},
                restrictedRewards = {'wpl_vehicles'}
            }
        }
    },
    
    -- House Integration
    houseIntegration = {
        enabled = true,
        
        -- House Compensation System
        houseComp = {
            enabled = true,
            stackable = true, -- Multiple rewards stack
            
            -- Claiming Process
            claiming = {
                method = 'windows_key_interaction',
                houseInteraction = true,
                option = 'Receive House Comp',
                
                -- Storage
                storage = {
                    persistent = true, -- Persists until claimed
                    noExpiry = true -- Never expires
                }
            }
        }
    },
    
    -- Notification System
    notificationSystem = {
        enabled = true,
        
        -- Notification Types
        notifications = {
            rewardAvailable = {
                enabled = true,
                message = "Your daily login reward is available! Use ;login or check your Y-menu.",
                sound = true
            },
            
            milestoneReached = {
                enabled = true,
                message = "Congratulations! You've reached a %d day login milestone!",
                sound = true,
                special = true
            },
            
            streakBroken = {
                enabled = true,
                message = "Your login streak has been reset. Start a new streak today!",
                sound = false
            }
        }
    }
}
