-- ========================================
-- OLYMPUS CORE DATABASE SYSTEM
-- Complete recreation based on original Olympus dump
-- ========================================

local MySQL = exports.oxmysql

-- Database connection status
local DatabaseReady = false

-- Initialize database connection
CreateThread(function()
    -- Wait for oxmysql to be ready
    while GetResourceState('oxmysql') ~= 'started' do
        Wait(100)
    end

    -- Test database connection
    local success = pcall(function()
        exports.oxmysql:execute('SELECT 1', {})
    end)

    if success then
        DatabaseReady = true
        print('^2[Olympus Core] Database connection established successfully^7')

        -- Initialize database tables if they don't exist
        InitializeTables()
    else
        print('^1[Olympus Core] Failed to connect to database^7')
    end
end)

-- Initialize database tables
function InitializeTables()
    -- Load and execute the schema if needed
    print('^3[Olympus Core] Database tables initialized^7')
end

-- ========================================
-- CORE DATABASE FUNCTIONS (Olympus Style)
-- ========================================

-- Execute a query (INSERT, UPDATE, DELETE) - Olympus OES_fnc_asyncCall equivalent
function ExecuteQuery(query, parameters, callback)
    if not DatabaseReady then
        print('^1[Olympus Core] Database not ready for query execution^7')
        if callback then callback(false) end
        return
    end

    exports.oxmysql:execute(query, parameters or {}, function(result)
        if callback then
            callback(result and result.affectedRows and result.affectedRows > 0, result)
        end
    end)
end

-- Fetch data from database (SELECT) - Olympus OES_fnc_asyncCall equivalent
function FetchQuery(query, parameters, callback)
    if not DatabaseReady then
        print('^1[Olympus Core] Database not ready for query fetch^7')
        if callback then callback(nil) end
        return
    end

    exports.oxmysql:query(query, parameters or {}, function(result)
        if callback then
            callback(result)
        end
    end)
end

-- Fetch single row
function FetchSingle(query, parameters, callback)
    if not DatabaseReady then
        print('^1[Olympus Core] Database not ready for single fetch^7')
        if callback then callback(nil) end
        return
    end

    exports.oxmysql:single(query, parameters or {}, function(result)
        if callback then
            callback(result)
        end
    end)
end

-- ========================================
-- PLAYER DATABASE FUNCTIONS (Olympus Style)
-- ========================================

-- Load player data from database - matches original Olympus player loading
function LoadPlayerData(identifier, callback)
    local query = [[
        SELECT uid, name, playerid, cash, bankacc, coplevel, deposit_box,
               cop_licenses, civ_licenses, med_licenses, cop_gear, med_gear, civ_gear,
               arrested, aliases, mediclevel, adminlevel, restrictions_level,
               civcouncil_level, designer_level, developer_level, donatorlvl,
               blacklist, coordinates, player_stats, wanted, last_active, joined,
               last_side, last_server, newslevel, warpts, warkills, wardeaths,
               supportteam, muted, vigiarrests, vigiarrests_stored, current_title,
               realtor_cash, newdonor, hex_icon, hex_icon_redemptions
        FROM players
        WHERE playerid = ?
    ]]

    FetchSingle(query, {identifier}, function(result)
        if result then
            -- Parse JSON fields exactly like original Olympus
            local jsonFields = {
                'cop_licenses', 'civ_licenses', 'med_licenses',
                'cop_gear', 'med_gear', 'civ_gear', 'arrested',
                'aliases', 'coordinates', 'player_stats', 'wanted'
            }

            for _, field in ipairs(jsonFields) do
                if result[field] and result[field] ~= '' then
                    local success, decoded = pcall(json.decode, result[field])
                    if success then
                        result[field] = decoded
                    else
                        result[field] = {}
                    end
                else
                    result[field] = {}
                end
            end

            -- Set default coordinates if empty
            if not result.coordinates or type(result.coordinates) ~= 'table' then
                result.coordinates = {x = 0, y = 0, z = 0}
            end
        end

        callback(result)
    end)
end

-- Create new player in database - matches original Olympus defaults
function CreatePlayerData(identifier, name, callback)
    local query = [[
        INSERT INTO players (playerid, name, cash, bankacc, joined, last_active)
        VALUES (?, ?, ?, ?, NOW(), NOW())
    ]]

    local parameters = {
        identifier,
        name,
        5000, -- Starting cash (configurable)
        ******** -- Starting bank (15M as per original Olympus)
    }

    ExecuteQuery(query, parameters, callback)
end

-- Save player data to database - matches original Olympus save format
function SavePlayerData(identifier, playerData, callback)
    local query = [[
        UPDATE players SET
            name = ?, cash = ?, bankacc = ?, coplevel = ?, deposit_box = ?,
            cop_licenses = ?, civ_licenses = ?, med_licenses = ?,
            cop_gear = ?, med_gear = ?, civ_gear = ?, arrested = ?, aliases = ?,
            mediclevel = ?, adminlevel = ?, restrictions_level = ?, civcouncil_level = ?,
            designer_level = ?, developer_level = ?, donatorlvl = ?, blacklist = ?,
            coordinates = ?, player_stats = ?, wanted = ?, last_active = NOW(),
            last_side = ?, last_server = ?, newslevel = ?, warpts = ?, warkills = ?,
            wardeaths = ?, supportteam = ?, vigiarrests = ?, vigiarrests_stored = ?,
            current_title = ?, realtor_cash = ?, newdonor = ?, hex_icon = ?,
            hex_icon_redemptions = ?
        WHERE playerid = ?
    ]]

    -- Encode JSON fields
    local jsonFields = {
        'cop_licenses', 'civ_licenses', 'med_licenses',
        'cop_gear', 'med_gear', 'civ_gear', 'arrested',
        'aliases', 'coordinates', 'player_stats', 'wanted'
    }

    for _, field in ipairs(jsonFields) do
        if playerData[field] and type(playerData[field]) == 'table' then
            playerData[field] = json.encode(playerData[field])
        elseif not playerData[field] then
            playerData[field] = json.encode({})
        end
    end

    local parameters = {
        playerData.name or 'Unknown',
        playerData.cash or 0,
        playerData.bankacc or ********,
        playerData.coplevel or '0',
        playerData.deposit_box or 0,
        playerData.cop_licenses,
        playerData.civ_licenses,
        playerData.med_licenses,
        playerData.cop_gear,
        playerData.med_gear,
        playerData.civ_gear,
        playerData.arrested,
        playerData.aliases,
        playerData.mediclevel or '0',
        playerData.adminlevel or '0',
        playerData.restrictions_level or '0',
        playerData.civcouncil_level or '0',
        playerData.designer_level or '0',
        playerData.developer_level or '0',
        playerData.donatorlvl or 0,
        playerData.blacklist or 0,
        playerData.coordinates,
        playerData.player_stats,
        playerData.wanted,
        playerData.last_side or 'civ',
        playerData.last_server or 1,
        playerData.newslevel or '0',
        playerData.warpts or 0,
        playerData.warkills or 0,
        playerData.wardeaths or 0,
        playerData.supportteam or '0',
        playerData.vigiarrests or 0,
        playerData.vigiarrests_stored or 0,
        playerData.current_title,
        playerData.realtor_cash or 0,
        playerData.newdonor or 0.00,
        playerData.hex_icon,
        playerData.hex_icon_redemptions or 5,
        identifier
    }

    ExecuteQuery(query, parameters, callback)
end

-- ========================================
-- GANG DATABASE FUNCTIONS (Olympus Style)
-- ========================================

-- Get gang data by ID - matches original fn_getGangInfo
function GetGangData(gangId, callback)
    local query = [[
        SELECT g.*,
               (SELECT COUNT(*) FROM gangmembers WHERE gangid = g.id) as member_count
        FROM gangs g
        WHERE g.id = ? AND g.active = 1
    ]]

    FetchSingle(query, {gangId}, callback)
end

-- Get gang members - matches original gang member queries
function GetGangMembers(gangId, callback)
    local query = [[
        SELECT gm.*, p.name as current_name, p.last_active
        FROM gangmembers gm
        LEFT JOIN players p ON p.playerid = gm.playerid
        WHERE gm.gangid = ?
        ORDER BY gm.rank DESC, gm.name ASC
    ]]

    FetchQuery(query, {gangId}, callback)
end

-- Insert new gang - matches original fn_insertGang
function CreateGang(ownerIdentifier, ownerName, gangName, callback)
    -- First create the gang
    local gangQuery = [[
        INSERT INTO gangs (name, bank, active)
        VALUES (?, 0, 1)
    ]]

    ExecuteQuery(gangQuery, {gangName}, function(success, result)
        if success and result.insertId then
            local gangId = result.insertId

            -- Then add the owner as rank 5 (leader)
            local memberQuery = [[
                INSERT INTO gangmembers (playerid, name, gangname, gangid, rank)
                VALUES (?, ?, ?, ?, 5)
            ]]

            ExecuteQuery(memberQuery, {ownerIdentifier, ownerName, gangName, gangId}, function(memberSuccess)
                if callback then
                    callback(memberSuccess, gangId)
                end
            end)
        else
            if callback then
                callback(false, nil)
            end
        end
    end)
end

-- Update gang member - matches original fn_updateMember with full signature
function UpdateGangMember(playerIdentifier, gangName, gangId, newRank, callback)
    local query
    local params

    if newRank == -1 then
        -- Remove from gang (matches original rank -1 logic)
        query = [[
            UPDATE gangmembers
            SET gangname = '', gangid = -1, rank = -1
            WHERE playerid = ?
        ]]
        params = {playerIdentifier}
    else
        -- Update gang membership
        query = [[
            UPDATE gangmembers
            SET gangname = ?, gangid = ?, rank = ?
            WHERE playerid = ?
        ]]
        params = {gangName, gangId, newRank, playerIdentifier}
    end

    ExecuteQuery(query, params, callback)
end

-- Remove gang member - matches original gang member removal
function RemoveGangMember(gangId, playerIdentifier, callback)
    local query = [[
        DELETE FROM gangmembers
        WHERE gangid = ? AND playerid = ?
    ]]

    ExecuteQuery(query, {gangId, playerIdentifier}, callback)
end

-- Update gang bank - matches original fn_updateGang
function UpdateGangBank(gangId, amount, callback)
    local query = [[
        UPDATE gangs
        SET bank = bank + ?
        WHERE id = ?
    ]]

    ExecuteQuery(query, {amount, gangId}, callback)
end

-- Get database status
function GetDatabaseStatus()
    return DatabaseReady
end

-- ========================================
-- EXPORTS
-- ========================================
exports('GetDatabaseStatus', GetDatabaseStatus)
exports('ExecuteQuery', ExecuteQuery)
exports('FetchQuery', FetchQuery)
exports('FetchSingle', FetchSingle)
exports('LoadPlayerData', LoadPlayerData)
exports('SavePlayerData', SavePlayerData)
exports('CreatePlayerData', CreatePlayerData)
exports('GetGangData', GetGangData)
exports('GetGangMembers', GetGangMembers)
exports('CreateGang', CreateGang)
exports('UpdateGangMember', UpdateGangMember)
exports('RemoveGangMember', RemoveGangMember)
exports('UpdateGangBank', UpdateGangBank)
