// ========================================
// OLYMPUS UI FRAMEWORK - JAVASCRIPT
// Based on Original Olympus Altis Life
// ========================================

class OlympusUI {
    constructor() {
        this.notifications = [];
        this.currentProgressBar = null;
        this.currentTitleText = null;
        this.yMenuOpen = false;
        this.currentTab = null;
        
        this.init();
    }

    init() {
        // Initialize event listeners
        this.setupEventListeners();
        
        // Initialize Y Menu
        this.initYMenu();
        
        console.log('[Olympus UI] Framework initialized');
    }

    setupEventListeners() {
        // Listen for messages from Lua
        window.addEventListener('message', (event) => {
            const data = event.data;
            
            switch (data.action) {
                case 'updateHUD':
                    this.updateHUD(data.data);
                    break;
                case 'showNotification':
                    this.showNotification(data.data);
                    break;
                case 'showProgressBar':
                    this.showProgressBar(data.data);
                    break;
                case 'hideProgressBar':
                    this.hideProgressBar();
                    break;
                case 'showTitleText':
                    this.showTitleText(data.data);
                    break;
                case 'hideTitleText':
                    this.hideTitleText();
                    break;
                case 'showYMenu':
                    this.showYMenu(data.data);
                    break;
                case 'hideYMenu':
                    this.hideYMenu();
                    break;
                case 'toggleHUD':
                    this.toggleHUD(data.visible);
                    break;
                case 'updateAdminHUD':
                    this.updateAdminHUD(data.data);
                    break;
                case 'toggleEarplugs':
                    this.toggleEarplugs(data.visible);
                    break;
            }
        });

        // Y Menu close button
        document.getElementById('ymenu-close').addEventListener('click', () => {
            this.hideYMenu();
        });

        // ESC key to close Y Menu
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.yMenuOpen) {
                this.hideYMenu();
            }
        });
    }

    // ========================================
    // HUD SYSTEM
    // ========================================
    
    updateHUD(data) {
        // Update status bars
        if (data.health !== undefined) {
            this.updateStatusBar('health', data.health);
        }
        if (data.food !== undefined) {
            this.updateStatusBar('food', data.food);
        }
        if (data.water !== undefined) {
            this.updateStatusBar('water', data.water);
        }
        if (data.stamina !== undefined) {
            this.updateStatusBar('stamina', data.stamina);
        }
        if (data.wanted !== undefined) {
            this.updateWantedBar(data.wanted);
        }

        // Update info display
        if (data.cash !== undefined) {
            document.getElementById('cash-value').textContent = this.formatMoney(data.cash);
        }
        if (data.bank !== undefined) {
            document.getElementById('bank-value').textContent = this.formatMoney(data.bank);
        }
        if (data.duty !== undefined) {
            this.updateDutyDisplay(data.duty);
        }
        if (data.location !== undefined) {
            document.getElementById('location-value').textContent = data.location;
        }
        if (data.time !== undefined) {
            document.getElementById('time-value').textContent = data.time;
        }
    }

    updateStatusBar(type, value) {
        const fill = document.getElementById(`${type}-fill`);
        const text = document.getElementById(`${type}-text`);
        
        if (fill && text) {
            fill.style.width = `${Math.max(0, Math.min(100, value))}%`;
            text.textContent = `${Math.round(value)}%`;
        }
    }

    updateWantedBar(wantedLevel) {
        const wantedBar = document.getElementById('wanted-bar');
        const wantedFill = document.getElementById('wanted-fill');
        const wantedText = document.getElementById('wanted-text');
        
        if (wantedLevel > 0) {
            wantedBar.style.display = 'flex';
            wantedFill.style.width = '100%';
            wantedText.textContent = `Wanted: $${this.formatMoney(wantedLevel)}`;
        } else {
            wantedBar.style.display = 'none';
        }
    }

    updateDutyDisplay(dutyInfo) {
        const dutyDisplay = document.getElementById('duty-display');
        const dutyValue = document.getElementById('duty-value');
        
        if (dutyInfo.onDuty) {
            dutyDisplay.style.display = 'flex';
            dutyValue.textContent = dutyInfo.faction || 'On Duty';
        } else {
            dutyDisplay.style.display = 'none';
        }
    }

    toggleHUD(visible) {
        const hudContainer = document.getElementById('hud-container');
        hudContainer.style.display = visible ? 'block' : 'none';
    }

    updateAdminHUD(data) {
        const adminHUD = document.getElementById('admin-hud');
        
        if (data.isAdmin) {
            adminHUD.style.display = 'block';
            
            // Update toggles
            Object.keys(data.toggles || {}).forEach(toggle => {
                const element = document.getElementById(`${toggle}-toggle`);
                if (element) {
                    const status = data.toggles[toggle] ? 'ON' : 'OFF';
                    element.textContent = `${toggle.charAt(0).toUpperCase() + toggle.slice(1)}: ${status}`;
                }
            });
        } else {
            adminHUD.style.display = 'none';
        }
    }

    toggleEarplugs(visible) {
        const earplugIcon = document.getElementById('earplugs-icon');
        earplugIcon.style.display = visible ? 'block' : 'none';
    }

    // ========================================
    // NOTIFICATION SYSTEM
    // ========================================
    
    showNotification(data) {
        const container = document.getElementById('notification-container');
        const notification = document.createElement('div');
        
        notification.className = `notification ${data.type || 'info'}`;
        notification.innerHTML = `
            <div class="notification-text">${data.message}</div>
        `;
        
        container.appendChild(notification);
        this.notifications.push(notification);
        
        // Auto-remove after duration
        setTimeout(() => {
            this.removeNotification(notification);
        }, data.duration || 5000);
        
        // Limit max notifications
        if (this.notifications.length > 5) {
            this.removeNotification(this.notifications[0]);
        }
        
        // Play sound if enabled
        if (data.sound) {
            this.playSound(data.sound);
        }
    }

    removeNotification(notification) {
        if (notification && notification.parentNode) {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
                const index = this.notifications.indexOf(notification);
                if (index > -1) {
                    this.notifications.splice(index, 1);
                }
            }, 300);
        }
    }

    // ========================================
    // PROGRESS BAR SYSTEM
    // ========================================
    
    showProgressBar(data) {
        const container = document.getElementById('progressbar-container');
        const fill = document.getElementById('progressbar-fill');
        const text = document.getElementById('progressbar-text');
        
        container.style.display = 'block';
        text.textContent = data.text || 'Processing...';
        
        // Animate progress
        let progress = 0;
        const duration = data.duration || 5000;
        const interval = 50;
        const increment = (100 / duration) * interval;
        
        this.currentProgressBar = setInterval(() => {
            progress += increment;
            fill.style.width = `${Math.min(100, progress)}%`;
            
            if (progress >= 100) {
                clearInterval(this.currentProgressBar);
                this.currentProgressBar = null;
                
                // Auto-hide after completion
                setTimeout(() => {
                    this.hideProgressBar();
                }, 500);
                
                // Callback to Lua
                if (data.onComplete) {
                    this.sendToLua('progressBarComplete', {});
                }
            }
        }, interval);
        
        // Handle cancellation
        if (data.canCancel) {
            document.addEventListener('keydown', this.handleProgressBarCancel.bind(this));
        }
    }

    hideProgressBar() {
        const container = document.getElementById('progressbar-container');
        container.style.display = 'none';
        
        if (this.currentProgressBar) {
            clearInterval(this.currentProgressBar);
            this.currentProgressBar = null;
        }
        
        document.removeEventListener('keydown', this.handleProgressBarCancel);
    }

    handleProgressBarCancel(event) {
        if (event.key === 'Escape' || event.key === 'x' || event.key === 'X') {
            this.hideProgressBar();
            this.sendToLua('progressBarCancelled', {});
        }
    }

    // ========================================
    // UTILITY FUNCTIONS
    // ========================================
    
    formatMoney(amount) {
        return new Intl.NumberFormat('en-US').format(amount);
    }

    playSound(soundFile) {
        try {
            const audio = new Audio(`sounds/${soundFile}`);
            audio.volume = 0.5;
            audio.play().catch(e => console.log('Sound play failed:', e));
        } catch (e) {
            console.log('Sound error:', e);
        }
    }

    sendToLua(action, data) {
        fetch(`https://${GetParentResourceName()}/${action}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        }).catch(e => console.log('Lua callback failed:', e));
    }

    // ========================================
    // Y MENU SYSTEM
    // ========================================

    initYMenu() {
        // Default tabs configuration
        this.yMenuTabs = [
            { id: 'licenses', name: 'Licenses', icon: 'license.png' },
            { id: 'keychain', name: 'Key Chain', icon: 'keys.png' },
            { id: 'gang', name: 'Gang', icon: 'gang.png' },
            { id: 'cell', name: 'Cell Phone', icon: 'phone.png' },
            { id: 'wanted', name: 'Wanted List', icon: 'wanted.png' },
            { id: 'admin', name: 'Admin Menu', icon: 'admin.png' },
            { id: 'settings', name: 'Settings', icon: 'settings.png' },
            { id: 'market', name: 'Market', icon: 'market.png' },
            { id: 'stats', name: 'Statistics', icon: 'stats.png' },
            { id: 'smartphone', name: 'Smart Phone', icon: 'smartphone.png' },
            { id: 'gps', name: 'GPS', icon: 'gps.png' },
            { id: 'sync', name: 'Sync Data', icon: 'sync.png' }
        ];
    }

    showYMenu(data) {
        const container = document.getElementById('ymenu-container');
        container.style.display = 'flex';
        this.yMenuOpen = true;

        // Update tabs if provided
        if (data && data.tabs) {
            this.yMenuTabs = data.tabs;
        }

        this.renderYMenuTabs();

        // Show first available tab
        if (this.yMenuTabs.length > 0) {
            this.switchYMenuTab(this.yMenuTabs[0].id);
        }

        // Enable cursor
        this.sendToLua('setCursor', { enabled: true });
    }

    hideYMenu() {
        const container = document.getElementById('ymenu-container');
        container.style.display = 'none';
        this.yMenuOpen = false;
        this.currentTab = null;

        // Disable cursor
        this.sendToLua('setCursor', { enabled: false });

        // Notify Lua
        this.sendToLua('yMenuClosed', {});
    }

    renderYMenuTabs() {
        const tabsContainer = document.getElementById('ymenu-tabs');
        tabsContainer.innerHTML = '';

        this.yMenuTabs.forEach(tab => {
            const tabElement = document.createElement('div');
            tabElement.className = 'ymenu-tab';
            tabElement.dataset.tabId = tab.id;

            tabElement.innerHTML = `
                <div class="ymenu-tab-icon" style="background-image: url('images/${tab.icon}')"></div>
                <div class="ymenu-tab-text">${tab.name}</div>
            `;

            tabElement.addEventListener('click', () => {
                this.switchYMenuTab(tab.id);
            });

            tabsContainer.appendChild(tabElement);
        });
    }

    switchYMenuTab(tabId) {
        // Update active tab styling
        document.querySelectorAll('.ymenu-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        const activeTab = document.querySelector(`[data-tab-id="${tabId}"]`);
        if (activeTab) {
            activeTab.classList.add('active');
        }

        this.currentTab = tabId;

        // Load tab content
        this.loadYMenuContent(tabId);

        // Notify Lua
        this.sendToLua('yMenuTabChanged', { tabId: tabId });
    }

    loadYMenuContent(tabId) {
        const contentArea = document.getElementById('ymenu-content-area');

        // Show loading state
        contentArea.innerHTML = `
            <h2>Loading ${this.getTabName(tabId)}...</h2>
            <p>Please wait while we load the content.</p>
        `;

        // Request content from Lua
        this.sendToLua('requestYMenuContent', { tabId: tabId });
    }

    updateYMenuContent(data) {
        const contentArea = document.getElementById('ymenu-content-area');

        if (data.html) {
            contentArea.innerHTML = data.html;
        } else if (data.title && data.content) {
            contentArea.innerHTML = `
                <h2>${data.title}</h2>
                <div>${data.content}</div>
            `;
        } else {
            contentArea.innerHTML = `
                <h2>${this.getTabName(this.currentTab)}</h2>
                <p>No content available for this tab.</p>
            `;
        }

        // Setup any interactive elements
        this.setupYMenuInteractions();
    }

    setupYMenuInteractions() {
        // Setup buttons and form elements in Y Menu content
        const buttons = document.querySelectorAll('.ymenu-content-area button');
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                const action = e.target.dataset.action;
                const data = e.target.dataset.data ? JSON.parse(e.target.dataset.data) : {};

                if (action) {
                    this.sendToLua('yMenuAction', { action: action, data: data });
                }
            });
        });

        // Setup form submissions
        const forms = document.querySelectorAll('.ymenu-content-area form');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                e.preventDefault();

                const formData = new FormData(form);
                const data = Object.fromEntries(formData.entries());
                const action = form.dataset.action || 'formSubmit';

                this.sendToLua('yMenuAction', { action: action, data: data });
            });
        });
    }

    getTabName(tabId) {
        const tab = this.yMenuTabs.find(t => t.id === tabId);
        return tab ? tab.name : 'Unknown';
    }

    // ========================================
    // TITLE TEXT SYSTEM
    // ========================================

    showTitleText(data) {
        const container = document.getElementById('titletext-container');
        const content = document.getElementById('titletext-content');

        content.innerHTML = data.text || '';
        container.style.display = 'block';

        // Auto-hide after duration
        if (data.duration && data.duration > 0) {
            this.currentTitleText = setTimeout(() => {
                this.hideTitleText();
            }, data.duration);
        }
    }

    hideTitleText() {
        const container = document.getElementById('titletext-container');
        const content = document.getElementById('titletext-content');

        content.style.animation = 'fadeOut 1s ease-out';

        setTimeout(() => {
            container.style.display = 'none';
            content.style.animation = '';
        }, 1000);

        if (this.currentTitleText) {
            clearTimeout(this.currentTitleText);
            this.currentTitleText = null;
        }
    }
}

// Initialize the UI when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.olympusUI = new OlympusUI();

    // Signal to Lua that UI is ready
    setTimeout(() => {
        fetch(`https://${GetParentResourceName()}/uiReady`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({})
        }).catch(e => console.log('UI ready callback failed:', e));
    }, 100);
});
