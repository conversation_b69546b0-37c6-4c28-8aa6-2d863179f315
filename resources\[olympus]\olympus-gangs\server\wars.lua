-- ========================================
-- OLYMPUS GANG WAR SYSTEM - SERVER
-- Complete recreation based on original fn_warInsertGang.sqf
-- Handles gang war declarations, management, and mechanics
-- ========================================

local OlympusWars = {}
OlympusWars.ActiveWars = {}
OlympusWars.WarInvites = {}

-- War configuration
local WAR_CONFIG = {
    minMembersOnline = 3, -- Minimum members online per gang to start war
    maxWarDuration = 1800, -- 30 minutes maximum war duration
    cooldownBetweenWars = 7200, -- 2 hours cooldown between wars
    inviteTimeout = 300, -- 5 minutes to accept war invite
    minRankToManage = 3, -- Rank 3+ can manage wars (matches original)
    killOnSight = true, -- Enemy gang members are KOS during war
    warPointsEnabled = true -- War points system enabled
}

-- Initialize war system
function InitializeWarSystem()
    print("^2[Olympus Wars]^7 Initializing gang war system...")
    
    -- Load active wars from database
    LoadActiveWarsFromDatabase()
    
    -- Start war cleanup loop
    StartWarCleanupLoop()
    
    print("^2[Olympus Wars]^7 Gang war system initialized!")
end

-- Load active wars from database (matches original query structure)
function LoadActiveWarsFromDatabase()
    local query = [[
        SELECT id, instigator, init_gangid, init_gangname, acceptor, acpt_gangid, acpt_gangname, 
               start_time, end_time, active, init_kills, acpt_kills, init_points, acpt_points
        FROM gangwars 
        WHERE active = '1'
    ]]
    
    exports['olympus-core']:FetchQuery(query, {}, function(result)
        if result then
            for _, war in ipairs(result) do
                OlympusWars.ActiveWars[war.id] = {
                    id = war.id,
                    instigator = war.instigator,
                    init_gang_id = war.init_gangid,
                    init_gang_name = war.init_gangname,
                    acceptor = war.acceptor,
                    acpt_gang_id = war.acpt_gangid,
                    acpt_gang_name = war.acpt_gangname,
                    start_time = war.start_time,
                    end_time = war.end_time,
                    init_kills = war.init_kills or 0,
                    acpt_kills = war.acpt_kills or 0,
                    init_points = war.init_points or 0,
                    acpt_points = war.acpt_points or 0,
                    active = true
                }
                
                print(string.format("^3[Olympus Wars]^7 Loaded active war: %s vs %s", 
                    war.init_gangname, war.acpt_gangname))
            end
        end
        
        print(string.format("^2[Olympus Wars]^7 Loaded %d active wars", 
            result and #result or 0))
    end)
end

-- Send war invitation (matches original fn_warInsertGang.sqf logic)
function SendWarInvitation(instigatorId, instigatorGangId, instigatorGangName, targetGangId, targetGangName)
    -- Validation checks (matches original)
    if instigatorGangId == 0 or targetGangId == 0 then
        return false, "Invalid gang IDs"
    end
    
    if instigatorGangId == targetGangId then
        return false, "Cannot declare war on your own gang"
    end
    
    if instigatorGangName == "" or targetGangName == "" then
        return false, "Invalid gang names"
    end
    
    -- Check for apostrophes (matches original validation)
    if string.find(instigatorGangName, "'") or string.find(targetGangName, "'") then
        return false, "Gang names cannot contain apostrophes"
    end
    
    -- Check if war already exists (matches original query)
    local checkQuery = [[
        SELECT id FROM gangwars 
        WHERE active='1' AND (
            (init_gangid=? AND acpt_gangid=?) OR 
            (acpt_gangid=? AND init_gangid=?)
        )
    ]]
    
    exports['olympus-core']:FetchQuery(checkQuery, {
        instigatorGangId, targetGangId, instigatorGangId, targetGangId
    }, function(result)
        if result and #result > 0 then
            return false, "War already exists between these gangs"
        end
        
        -- Check cooldown
        if IsGangOnWarCooldown(instigatorGangId) or IsGangOnWarCooldown(targetGangId) then
            return false, "One of the gangs is on war cooldown"
        end
        
        -- Check minimum online members
        local instigatorOnline = GetGangOnlineMembers(instigatorGangId)
        local targetOnline = GetGangOnlineMembers(targetGangId)
        
        if #instigatorOnline < WAR_CONFIG.minMembersOnline or #targetOnline < WAR_CONFIG.minMembersOnline then
            return false, string.format("Both gangs need at least %d members online", WAR_CONFIG.minMembersOnline)
        end
        
        -- Create war invitation
        local inviteId = GenerateWarInviteId()
        OlympusWars.WarInvites[inviteId] = {
            id = inviteId,
            instigator_id = instigatorId,
            instigator_gang_id = instigatorGangId,
            instigator_gang_name = instigatorGangName,
            target_gang_id = targetGangId,
            target_gang_name = targetGangName,
            created_time = os.time(),
            expires_time = os.time() + WAR_CONFIG.inviteTimeout
        }
        
        -- Notify target gang leaders
        NotifyGangLeaders(targetGangId, {
            type = 'war_invite',
            title = 'Gang War Invitation',
            message = string.format('%s has declared war on your gang!', instigatorGangName),
            invite_id = inviteId,
            duration = WAR_CONFIG.inviteTimeout
        })
        
        -- Notify instigator gang
        NotifyGangMembers(instigatorGangId, {
            type = 'info',
            title = 'War Declaration',
            message = string.format('War invitation sent to %s', targetGangName)
        })
        
        print(string.format("^3[Olympus Wars]^7 War invitation sent: %s -> %s", 
            instigatorGangName, targetGangName))
        
        return true
    end)
end

-- Accept war invitation
function AcceptWarInvitation(acceptorId, inviteId)
    local invite = OlympusWars.WarInvites[inviteId]
    if not invite then
        return false, "War invitation not found"
    end
    
    -- Check if invite has expired
    if os.time() > invite.expires_time then
        OlympusWars.WarInvites[inviteId] = nil
        return false, "War invitation has expired"
    end
    
    -- Get acceptor data
    local acceptorData = exports['olympus-core']:GetPlayerData(acceptorId)
    if not acceptorData or acceptorData.gang_id ~= invite.target_gang_id then
        return false, "You are not authorized to accept this war"
    end
    
    -- Check rank permission
    if acceptorData.gang_rank < WAR_CONFIG.minRankToManage then
        return false, "You need rank 3+ to accept war invitations"
    end
    
    -- Insert war into database (matches original INSERT query)
    local insertQuery = [[
        INSERT INTO gangwars (instigator, init_gangid, init_gangname, acceptor, acpt_gangid, acpt_gangname, active, start_time) 
        VALUES (?, ?, ?, ?, ?, ?, '1', ?)
    ]]
    
    local instigatorIdentifier = GetPlayerIdentifier(invite.instigator_id, 0) or "unknown"
    local acceptorIdentifier = GetPlayerIdentifier(acceptorId, 0) or "unknown"
    local startTime = os.time()
    
    exports['olympus-core']:ExecuteQuery(insertQuery, {
        instigatorIdentifier,
        invite.instigator_gang_id,
        invite.instigator_gang_name,
        acceptorIdentifier,
        invite.target_gang_id,
        invite.target_gang_name,
        startTime
    }, function(success, result)
        if success and result.insertId then
            -- Create active war
            local warId = result.insertId
            OlympusWars.ActiveWars[warId] = {
                id = warId,
                instigator = instigatorIdentifier,
                init_gang_id = invite.instigator_gang_id,
                init_gang_name = invite.instigator_gang_name,
                acceptor = acceptorIdentifier,
                acpt_gang_id = invite.target_gang_id,
                acpt_gang_name = invite.target_gang_name,
                start_time = startTime,
                end_time = startTime + WAR_CONFIG.maxWarDuration,
                init_kills = 0,
                acpt_kills = 0,
                init_points = 0,
                acpt_points = 0,
                active = true
            }
            
            -- Remove invitation
            OlympusWars.WarInvites[inviteId] = nil
            
            -- Notify all gang members
            NotifyGangMembers(invite.instigator_gang_id, {
                type = 'war_start',
                title = 'Gang War Started',
                message = string.format('War with %s has begun! Enemy gang members are now KOS.', invite.target_gang_name)
            })
            
            NotifyGangMembers(invite.target_gang_id, {
                type = 'war_start',
                title = 'Gang War Started',
                message = string.format('War with %s has begun! Enemy gang members are now KOS.', invite.instigator_gang_name)
            })
            
            -- Start war timer
            StartWarTimer(warId)
            
            print(string.format("^2[Olympus Wars]^7 War started: %s vs %s (ID: %d)", 
                invite.instigator_gang_name, invite.target_gang_name, warId))
            
            return true
        else
            return false, "Failed to create war in database"
        end
    end)
end

-- Decline war invitation
function DeclineWarInvitation(declinerId, inviteId)
    local invite = OlympusWars.WarInvites[inviteId]
    if not invite then
        return false, "War invitation not found"
    end
    
    -- Get decliner data
    local declinerData = exports['olympus-core']:GetPlayerData(declinerId)
    if not declinerData or declinerData.gang_id ~= invite.target_gang_id then
        return false, "You are not authorized to decline this war"
    end
    
    -- Check rank permission
    if declinerData.gang_rank < WAR_CONFIG.minRankToManage then
        return false, "You need rank 3+ to decline war invitations"
    end
    
    -- Remove invitation
    OlympusWars.WarInvites[inviteId] = nil
    
    -- Notify gangs
    NotifyGangMembers(invite.instigator_gang_id, {
        type = 'info',
        title = 'War Declined',
        message = string.format('%s has declined your war invitation', invite.target_gang_name)
    })
    
    NotifyGangMembers(invite.target_gang_id, {
        type = 'info',
        title = 'War Declined',
        message = string.format('War invitation from %s has been declined', invite.instigator_gang_name)
    })
    
    print(string.format("^3[Olympus Wars]^7 War invitation declined: %s -> %s", 
        invite.instigator_gang_name, invite.target_gang_name))
    
    return true
end

-- Generate unique war invite ID
function GenerateWarInviteId()
    return string.format("war_%d_%d", os.time(), math.random(1000, 9999))
end

-- Check if gang is on war cooldown
function IsGangOnWarCooldown(gangId)
    -- Check recent wars for this gang
    local query = [[
        SELECT end_time FROM gangwars 
        WHERE (init_gangid = ? OR acpt_gangid = ?) 
        AND active = '0' 
        AND end_time > ?
        ORDER BY end_time DESC 
        LIMIT 1
    ]]
    
    local cooldownTime = os.time() - WAR_CONFIG.cooldownBetweenWars
    
    exports['olympus-core']:FetchSingle(query, {gangId, gangId, cooldownTime}, function(result)
        return result ~= nil
    end)
    
    return false -- Default to no cooldown for now
end

-- Get gang online members
function GetGangOnlineMembers(gangId)
    local onlineMembers = {}
    for _, playerId in ipairs(GetPlayers()) do
        local playerData = exports['olympus-core']:GetPlayerData(tonumber(playerId))
        if playerData and playerData.gang_id == gangId then
            table.insert(onlineMembers, tonumber(playerId))
        end
    end
    return onlineMembers
end

-- Notify gang leaders (rank 4+)
function NotifyGangLeaders(gangId, notification)
    for _, playerId in ipairs(GetPlayers()) do
        local playerData = exports['olympus-core']:GetPlayerData(tonumber(playerId))
        if playerData and playerData.gang_id == gangId and playerData.gang_rank >= 4 then
            TriggerClientEvent('olympus:client:notify', tonumber(playerId), notification)
        end
    end
end

-- Notify all gang members
function NotifyGangMembers(gangId, notification)
    for _, playerId in ipairs(GetPlayers()) do
        local playerData = exports['olympus-core']:GetPlayerData(tonumber(playerId))
        if playerData and playerData.gang_id == gangId then
            TriggerClientEvent('olympus:client:notify', tonumber(playerId), notification)
        end
    end
end

-- Start war timer
function StartWarTimer(warId)
    CreateThread(function()
        local war = OlympusWars.ActiveWars[warId]
        if not war then return end

        while war.active and os.time() < war.end_time do
            Wait(60000) -- Check every minute
        end

        -- End war due to timeout
        if war.active then
            EndWar(warId, 'timeout')
        end
    end)
end

-- End war
function EndWar(warId, reason)
    local war = OlympusWars.ActiveWars[warId]
    if not war then return end

    -- Determine winner
    local winner = nil
    local winnerName = ""
    local loserName = ""

    if war.init_kills > war.acpt_kills then
        winner = "init"
        winnerName = war.init_gang_name
        loserName = war.acpt_gang_name
    elseif war.acpt_kills > war.init_kills then
        winner = "acpt"
        winnerName = war.acpt_gang_name
        loserName = war.init_gang_name
    else
        winner = "tie"
    end

    -- Update database
    local updateQuery = [[
        UPDATE gangwars
        SET active = '0', end_time = ?, winner = ?, init_kills = ?, acpt_kills = ?, init_points = ?, acpt_points = ?
        WHERE id = ?
    ]]

    exports['olympus-core']:ExecuteQuery(updateQuery, {
        os.time(),
        winner,
        war.init_kills,
        war.acpt_kills,
        war.init_points,
        war.acpt_points,
        warId
    }, function(success)
        if success then
            -- Notify gangs
            local message = ""
            if winner == "tie" then
                message = string.format("War between %s and %s ended in a tie!", war.init_gang_name, war.acpt_gang_name)
            else
                message = string.format("%s won the war against %s! (%d-%d kills)", winnerName, loserName,
                    winner == "init" and war.init_kills or war.acpt_kills,
                    winner == "init" and war.acpt_kills or war.init_kills)
            end

            NotifyGangMembers(war.init_gang_id, {
                type = 'war_end',
                title = 'Gang War Ended',
                message = message
            })

            NotifyGangMembers(war.acpt_gang_id, {
                type = 'war_end',
                title = 'Gang War Ended',
                message = message
            })

            print(string.format("^2[Olympus Wars]^7 War ended: %s (Reason: %s)", message, reason))
        end
    end)

    -- Remove from active wars
    OlympusWars.ActiveWars[warId] = nil
end

-- Record war kill
function RecordWarKill(killerId, victimId)
    local killerData = exports['olympus-core']:GetPlayerData(killerId)
    local victimData = exports['olympus-core']:GetPlayerData(victimId)

    if not killerData or not victimData then return end
    if not killerData.gang_id or not victimData.gang_id then return end
    if killerData.gang_id == victimData.gang_id then return end -- Same gang

    -- Find active war between these gangs
    local warId = nil
    for id, war in pairs(OlympusWars.ActiveWars) do
        if (war.init_gang_id == killerData.gang_id and war.acpt_gang_id == victimData.gang_id) or
           (war.acpt_gang_id == killerData.gang_id and war.init_gang_id == victimData.gang_id) then
            warId = id
            break
        end
    end

    if not warId then return end -- No active war

    local war = OlympusWars.ActiveWars[warId]

    -- Update kill count
    if war.init_gang_id == killerData.gang_id then
        war.init_kills = war.init_kills + 1
    else
        war.acpt_kills = war.acpt_kills + 1
    end

    -- Calculate war points (based on gear value - simplified for now)
    local warPoints = CalculateWarPoints(killerId, victimId)
    if war.init_gang_id == killerData.gang_id then
        war.init_points = war.init_points + warPoints
    else
        war.acpt_points = war.acpt_points + warPoints
    end

    -- Notify gangs
    NotifyGangMembers(killerData.gang_id, {
        type = 'war_kill',
        title = 'War Kill',
        message = string.format('%s killed %s (+%d points)',
            GetPlayerName(killerId), GetPlayerName(victimId), warPoints)
    })

    NotifyGangMembers(victimData.gang_id, {
        type = 'war_death',
        title = 'War Death',
        message = string.format('%s was killed by %s',
            GetPlayerName(victimId), GetPlayerName(killerId))
    })

    print(string.format("^3[Olympus Wars]^7 War kill recorded: %s killed %s (War ID: %d)",
        GetPlayerName(killerId), GetPlayerName(victimId), warId))
end

-- Calculate war points (simplified version)
function CalculateWarPoints(killerId, victimId)
    -- In the original, this was based on gear value
    -- For now, we'll use a simple system
    return math.random(10, 50)
end

-- Check if players are at war
function ArePlayersAtWar(playerId1, playerId2)
    local player1Data = exports['olympus-core']:GetPlayerData(playerId1)
    local player2Data = exports['olympus-core']:GetPlayerData(playerId2)

    if not player1Data or not player2Data then return false end
    if not player1Data.gang_id or not player2Data.gang_id then return false end
    if player1Data.gang_id == player2Data.gang_id then return false end

    -- Check for active war
    for _, war in pairs(OlympusWars.ActiveWars) do
        if (war.init_gang_id == player1Data.gang_id and war.acpt_gang_id == player2Data.gang_id) or
           (war.acpt_gang_id == player1Data.gang_id and war.init_gang_id == player2Data.gang_id) then
            return true
        end
    end

    return false
end

-- Start war cleanup loop
function StartWarCleanupLoop()
    CreateThread(function()
        while true do
            Wait(60000) -- Check every minute

            -- Clean up expired invitations
            for inviteId, invite in pairs(OlympusWars.WarInvites) do
                if os.time() > invite.expires_time then
                    OlympusWars.WarInvites[inviteId] = nil
                    print(string.format("^3[Olympus Wars]^7 Expired war invitation: %s -> %s",
                        invite.instigator_gang_name, invite.target_gang_name))
                end
            end
        end
    end)
end

-- Server events
RegisterServerEvent('olympus:server:declareWar')
AddEventHandler('olympus:server:declareWar', function(targetGangId)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData or not playerData.gang_id or playerData.gang_id <= 0 then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Gang War',
            message = 'You must be in a gang to declare war'
        })
        return
    end

    if playerData.gang_rank < WAR_CONFIG.minRankToManage then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Gang War',
            message = 'You need rank 3+ to declare war'
        })
        return
    end

    -- Get gang data
    exports['olympus-core']:GetGangData(playerData.gang_id, function(instigatorGang)
        if not instigatorGang then return end

        exports['olympus-core']:GetGangData(targetGangId, function(targetGang)
            if not targetGang then
                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'error',
                    title = 'Gang War',
                    message = 'Target gang not found'
                })
                return
            end

            local success, errorMsg = SendWarInvitation(source, playerData.gang_id, instigatorGang.name, targetGangId, targetGang.name)
            if success then
                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'success',
                    title = 'Gang War',
                    message = string.format('War invitation sent to %s', targetGang.name)
                })
            else
                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'error',
                    title = 'Gang War',
                    message = errorMsg or 'Failed to send war invitation'
                })
            end
        end)
    end)
end)

RegisterServerEvent('olympus:server:acceptWar')
AddEventHandler('olympus:server:acceptWar', function(inviteId)
    local source = source
    local success, errorMsg = AcceptWarInvitation(source, inviteId)

    TriggerClientEvent('olympus:client:notify', source, {
        type = success and 'success' or 'error',
        title = 'Gang War',
        message = success and 'War invitation accepted!' or (errorMsg or 'Failed to accept war')
    })
end)

RegisterServerEvent('olympus:server:declineWar')
AddEventHandler('olympus:server:declineWar', function(inviteId)
    local source = source
    local success, errorMsg = DeclineWarInvitation(source, inviteId)

    TriggerClientEvent('olympus:client:notify', source, {
        type = success and 'success' or 'error',
        title = 'Gang War',
        message = success and 'War invitation declined' or (errorMsg or 'Failed to decline war')
    })
end)

-- Hook into player death event
AddEventHandler('olympus:server:playerDeath', function(killerId, victimId)
    if killerId and victimId and killerId ~= victimId then
        RecordWarKill(killerId, victimId)
    end
end)

-- Exports
exports('InitializeWarSystem', InitializeWarSystem)
exports('ArePlayersAtWar', ArePlayersAtWar)
exports('GetActiveWars', function()
    return OlympusWars.ActiveWars
end)
exports('GetWarInvites', function()
    return OlympusWars.WarInvites
end)

-- Initialize when loaded
CreateThread(function()
    -- Wait for core to be ready
    while not exports['olympus-core']:IsDBReady() do
        Wait(100)
    end

    Wait(5000) -- Additional delay for gang system to load
    InitializeWarSystem()
end)

print("^2[Olympus Wars]^7 Gang war system loaded!")
