-- Olympus Items - Speed Bomb Client
-- Based on original fn_speedBomb.sqf from Olympus Altis Life

local isPlantingBomb = false
local activeBombs = {}

-- Use Speed Bomb function
function UseSpeedBomb(target)
    if isPlantingBomb then
        exports['olympus-items']:ShowNotification("You are already planting a bomb!", "error")
        return false
    end
    
    local ped = PlayerPedId()
    local vehicle = target or GetVehiclePedIsLookingAt(ped)
    
    if not vehicle or vehicle == 0 then
        exports['olympus-items']:ShowNotification("No vehicle found!", "error")
        return false
    end
    
    -- Check if player is in the vehicle
    if IsPedInVehicle(ped, vehicle, false) then
        exports['olympus-items']:ShowNotification("You cannot plant a bomb while inside the vehicle!", "error")
        return false
    end
    
    -- Check distance
    local playerPos = GetEntityCoords(ped)
    local vehiclePos = GetEntityCoords(vehicle)
    local distance = #(playerPos - vehiclePos)
    
    if distance > 3.0 then
        exports['olympus-items']:ShowNotification("You are too far from the vehicle!", "error")
        return false
    end
    
    -- Check if vehicle already has a bomb
    if HasSpeedBomb(vehicle) then
        exports['olympus-items']:ShowNotification("This vehicle already has a speed bomb!", "error")
        return false
    end
    
    -- Check if vehicle is valid for bombing
    if not IsValidBombTarget(vehicle) then
        exports['olympus-items']:ShowNotification("You cannot plant a bomb on this vehicle!", "error")
        return false
    end
    
    -- Start bomb planting
    return StartBombPlanting(vehicle)
end

-- Get vehicle player is looking at
function GetVehiclePedIsLookingAt(ped)
    local coords = GetEntityCoords(ped)
    local forward = GetEntityForwardVector(ped)
    local destination = coords + forward * 3.0
    
    local rayHandle = StartExpensiveSynchronousShapeTestLosProbe(
        coords.x, coords.y, coords.z,
        destination.x, destination.y, destination.z,
        10, ped, 0
    )
    
    local _, hit, _, _, entity = GetShapeTestResult(rayHandle)
    
    if hit and IsEntityAVehicle(entity) then
        return entity
    end
    
    return nil
end

-- Check if vehicle has speed bomb
function HasSpeedBomb(vehicle)
    local plate = GetVehicleNumberPlateText(vehicle)
    return activeBombs[plate] ~= nil
end

-- Check if vehicle is valid for bombing
function IsValidBombTarget(vehicle)
    if not DoesEntityExist(vehicle) then return false end
    if not IsEntityAVehicle(vehicle) then return false end
    
    -- Check if it's a restricted vehicle type
    local model = GetEntityModel(vehicle)
    local restrictedModels = {
        `rhino`, `khanjali`, `apc`, `barrage`, `chernobog`, `halftrack`, `minitank`
    }
    
    for _, restrictedModel in ipairs(restrictedModels) do
        if model == restrictedModel then
            return false
        end
    end
    
    return true
end

-- Start bomb planting
function StartBombPlanting(vehicle)
    isPlantingBomb = true
    
    local ped = PlayerPedId()
    
    -- Play planting animation
    local animDict = "anim@amb@clubhouse@tutorial@bkr_tut_ig3@"
    local animName = "machinic_loop_mechandplayer"
    
    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(10)
    end
    
    TaskPlayAnim(ped, animDict, animName, 8.0, -8.0, -1, 1, 0, false, false, false)
    
    -- Show progress bar
    local progressConfig = {
        label = "Planting speed bomb...",
        duration = 8000,
        useWhileDead = false,
        canCancel = true
    }
    
    exports['olympus-items']:ShowProgressBar(progressConfig, function()
        -- Progress completed
        CompleteBombPlanting(vehicle)
    end, function()
        -- Progress cancelled
        CancelBombPlanting()
    end)
    
    return true
end

-- Complete bomb planting
function CompleteBombPlanting(vehicle)
    isPlantingBomb = false
    
    -- Clear animation
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    -- Check if still in range
    local playerPos = GetEntityCoords(ped)
    local vehiclePos = GetEntityCoords(vehicle)
    local distance = #(playerPos - vehiclePos)
    
    if distance > 3.0 then
        exports['olympus-items']:ShowNotification("You moved too far away!", "error")
        return
    end
    
    local plate = GetVehicleNumberPlateText(vehicle)
    local vehicleNetId = NetworkGetNetworkIdFromEntity(vehicle)
    
    -- Add to active bombs
    activeBombs[plate] = {
        vehicle = vehicle,
        netId = vehicleNetId,
        plantTime = GetGameTimer(),
        activated = false,
        plantedBy = GetPlayerServerId(PlayerId()),
        activationSpeed = Config.Items.speedbomb.activationSpeed,
        detonationSpeed = Config.Items.speedbomb.detonationSpeed
    }
    
    -- Notify server
    TriggerServerEvent('olympus-items:server:plantSpeedBomb', {
        vehicle = vehicleNetId,
        plate = plate,
        location = vehiclePos
    })
    
    -- Consume item
    TriggerServerEvent('olympus-items:server:consumeItem', 'speedbomb', true)
    
    exports['olympus-items']:ShowNotification("Speed bomb planted! It will activate when the vehicle exceeds 70 km/h!", "success")
    
    -- Start bomb monitoring
    StartBombMonitoring(plate)
end

-- Cancel bomb planting
function CancelBombPlanting()
    isPlantingBomb = false
    
    -- Clear animation
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    exports['olympus-items']:ShowNotification("Bomb planting cancelled!", "info")
end

-- Start bomb monitoring
function StartBombMonitoring(plate)
    CreateThread(function()
        local bombData = activeBombs[plate]
        if not bombData then return end
        
        while activeBombs[plate] and DoesEntityExist(bombData.vehicle) do
            local vehicle = bombData.vehicle
            local speed = GetEntitySpeed(vehicle) * 3.6 -- Convert to km/h
            
            -- Check if bomb should activate
            if not bombData.activated and speed >= bombData.activationSpeed then
                ActivateSpeedBomb(plate)
            end
            
            -- Check if bomb should detonate
            if bombData.activated and speed <= bombData.detonationSpeed then
                DetonateSpeedBomb(plate)
                break
            end
            
            Wait(100) -- Check every 100ms for responsiveness
        end
        
        -- Clean up if vehicle no longer exists
        if activeBombs[plate] then
            activeBombs[plate] = nil
        end
    end)
end

-- Activate speed bomb
function ActivateSpeedBomb(plate)
    local bombData = activeBombs[plate]
    if not bombData or bombData.activated then return end
    
    bombData.activated = true
    
    -- Notify all players in the vehicle
    local vehicle = bombData.vehicle
    local maxSeats = GetVehicleMaxNumberOfPassengers(vehicle)
    
    for i = -1, maxSeats do
        local ped = GetPedInVehicleSeat(vehicle, i)
        if ped and ped ~= 0 then
            local playerId = NetworkGetPlayerIndexFromPed(ped)
            if playerId ~= -1 then
                local serverId = GetPlayerServerId(playerId)
                TriggerServerEvent('olympus-items:server:notifySpeedBombActivated', serverId)
            end
        end
    end
    
    -- Start warning sounds and effects
    StartBombWarningEffects(vehicle)
    
    -- Notify server
    TriggerServerEvent('olympus-items:server:speedBombActivated', {
        vehicle = bombData.netId,
        plate = plate
    })
end

-- Start bomb warning effects
function StartBombWarningEffects(vehicle)
    CreateThread(function()
        local startTime = GetGameTimer()
        local warningDuration = 30000 -- 30 seconds of warnings
        
        while GetGameTimer() - startTime < warningDuration and DoesEntityExist(vehicle) do
            -- Play beeping sound
            local vehiclePos = GetEntityCoords(vehicle)
            PlaySoundFromCoord(-1, "Beep_Red", vehiclePos.x, vehiclePos.y, vehiclePos.z, "DLC_HEIST_HACKING_SNAKE_SOUNDS", false, 10.0, false)
            
            -- Flash vehicle lights
            if IsVehicleLightDamaged(vehicle, 0) == false then
                SetVehicleLights(vehicle, 2) -- Turn on lights
                Wait(200)
                SetVehicleLights(vehicle, 0) -- Turn off lights
            end
            
            Wait(800) -- Beep every 800ms
        end
    end)
end

-- Detonate speed bomb
function DetonateSpeedBomb(plate)
    local bombData = activeBombs[plate]
    if not bombData then return end
    
    local vehicle = bombData.vehicle
    if not DoesEntityExist(vehicle) then
        activeBombs[plate] = nil
        return
    end
    
    local vehiclePos = GetEntityCoords(vehicle)
    
    -- Create explosion
    AddExplosion(vehiclePos.x, vehiclePos.y, vehiclePos.z, 5, 50.0, true, false, 1.0)
    
    -- Damage vehicle severely
    SetEntityHealth(vehicle, 0)
    SetVehicleEngineHealth(vehicle, -4000.0)
    SetVehicleBodyHealth(vehicle, 0.0)
    
    -- Set vehicle on fire
    StartVehicleFire(vehicle)
    
    -- Notify server
    TriggerServerEvent('olympus-items:server:speedBombDetonated', {
        vehicle = bombData.netId,
        plate = plate,
        location = vehiclePos
    })
    
    -- Remove from active bombs
    activeBombs[plate] = nil
    
    exports['olympus-items']:ShowNotification("Speed bomb detonated!", "warning")
end

-- Remove speed bomb (for defusal)
function RemoveSpeedBomb(plate)
    if activeBombs[plate] then
        activeBombs[plate] = nil
        exports['olympus-items']:ShowNotification(string.format("Speed bomb removed from vehicle %s", plate), "success")
    end
end

-- Get active bombs
function GetActiveBombs()
    local bombs = {}
    
    for plate, data in pairs(activeBombs) do
        if DoesEntityExist(data.vehicle) then
            table.insert(bombs, {
                plate = plate,
                vehicle = data.vehicle,
                activated = data.activated,
                plantedBy = data.plantedBy,
                plantTime = data.plantTime
            })
        end
    end
    
    return bombs
end

-- Check if player moved too far during planting
CreateThread(function()
    while true do
        if isPlantingBomb then
            local ped = PlayerPedId()
            
            -- Check if player is in a vehicle
            if IsPedInAnyVehicle(ped, false) then
                CancelBombPlanting()
            end
        end
        
        Wait(500)
    end
end)

-- Export functions
exports('UseSpeedBomb', UseSpeedBomb)
exports('RemoveSpeedBomb', RemoveSpeedBomb)
exports('GetActiveBombs', GetActiveBombs)

-- Event handlers
RegisterNetEvent('olympus-items:client:bombInterrupted', function()
    if isPlantingBomb then
        CancelBombPlanting()
    end
end)

RegisterNetEvent('olympus-items:client:removeSpeedBomb', function(plate)
    RemoveSpeedBomb(plate)
end)

RegisterNetEvent('olympus-items:client:speedBombWarning', function()
    exports['olympus-items']:ShowNotification("WARNING: Speed bomb activated! Keep speed above 50 km/h or it will detonate!", "error")
end)
