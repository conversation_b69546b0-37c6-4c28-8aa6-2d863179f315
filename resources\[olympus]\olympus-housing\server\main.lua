-- ========================================
-- OLYMPUS HOUSING SYSTEM - SERVER MAIN
-- Complete recreation based on original fn_initHouses.sqf
-- Handles property ownership, storage, keys, and management
-- ========================================

local OlympusHousing = {}
OlympusHousing.Houses = {}
OlympusHousing.HouseCount = 0

-- Housing configuration (matches original Olympus)
local HOUSING_CONFIG = {
    maxHousesPerPlayer = 3, -- Maximum houses per player (matches original oev_houseLimit)
    taxRate = 0.001, -- 0.1% daily tax rate
    sellPriceMultiplier = 0.75, -- 75% return on sale
    defaultStorageCapacity = 100, -- Default virtual storage
    defaultPhysicalCapacity = 100, -- Default physical storage
    expirationDays = 30, -- Houses expire after 30 days without payment
    nearExpireWarning = 5 -- Warn when 5 days left
}

-- House types and prices (matches original house configs)
local HOUSE_TYPES = {
    ["small_house"] = {price = 75000, storage = 100, physical = 100, name = "Small House"},
    ["medium_house"] = {price = 125000, storage = 150, physical = 150, name = "Medium House"},
    ["large_house"] = {price = 200000, storage = 200, physical = 200, name = "Large House"},
    ["mansion"] = {price = 350000, storage = 300, physical = 300, name = "Mansion"}
}

-- Initialize housing system (matches original fn_initHouses.sqf)
function InitializeHousingSystem()
    print("^2[Olympus Housing]^7 Initializing housing system...")

    -- Load houses from database in batches (matches original batch loading)
    LoadHousesFromDatabase()

    -- Start house expiration checker
    StartHouseExpirationChecker()

    print("^2[Olympus Housing]^7 Housing system initialized!")
end

-- Load houses from database (matches original query structure)
function LoadHousesFromDatabase()
    -- Get total count first (matches original COUNT query)
    local countQuery = [[
        SELECT COUNT(*) as count
        FROM houses
        WHERE owned = '1' AND server = ?
    ]]

    exports['olympus-core']:FetchSingle(countQuery, {GetConvar('sv_servername', 'olympus')}, function(countResult)
        if countResult then
            OlympusHousing.HouseCount = countResult.count

            -- Load houses in batches of 10 (matches original LIMIT logic)
            for i = 0, OlympusHousing.HouseCount, 10 do
                LoadHouseBatch(i)
            end
        end
    end)
end

-- Load house batch (matches original batch loading with LIMIT)
function LoadHouseBatch(offset)
    local query = [[
        SELECT h.id, h.pid, h.pos, p.name, h.player_keys, h.inventory, h.storageCapacity,
               h.inAH, h.oil, h.physical_inventory, h.physicalStorageCapacity,
               DATEDIFF(h.expires_on, CURRENT_DATE()) as days_left
        FROM houses h
        INNER JOIN players p ON h.pid = p.playerid
        WHERE h.owned = '1' AND h.server = ?
        LIMIT ?, 10
    ]]

    exports['olympus-core']:FetchQuery(query, {GetConvar('sv_servername', 'olympus'), offset}, function(result)
        if result and #result > 0 then
            for _, house in ipairs(result) do
                ProcessHouseData(house)
            end
        end
    end)
end

-- Process house data (matches original house variable setting)
function ProcessHouseData(houseData)
    local houseId = houseData.id
    local position = json.decode(houseData.pos or '{}')

    if position.x and position.y and position.z then
        -- Parse storage data (matches original trunk parsing)
        local inventory = {}
        local physicalInventory = {}
        local keyPlayers = {}

        if houseData.inventory then
            local success, parsed = pcall(json.decode, houseData.inventory)
            if success then inventory = parsed end
        end

        if houseData.physical_inventory then
            local success, parsed = pcall(json.decode, houseData.physical_inventory)
            if success then physicalInventory = parsed end
        end

        if houseData.player_keys then
            local success, parsed = pcall(json.decode, houseData.player_keys)
            if success then keyPlayers = parsed end
        end

        -- Create house object (matches original house variable structure)
        OlympusHousing.Houses[houseId] = {
            id = houseId,
            owner_id = houseData.pid,
            owner_name = houseData.name,
            position = vector3(position.x, position.y, position.z),
            locked = true, -- Houses start locked (matches original)
            inventory = inventory, -- Virtual storage (matches original Trunk)
            physical_inventory = physicalInventory, -- Physical storage (matches original PhysicalTrunk)
            storage_capacity = houseData.storageCapacity or HOUSING_CONFIG.defaultStorageCapacity,
            physical_capacity = houseData.physicalStorageCapacity or HOUSING_CONFIG.defaultPhysicalCapacity,
            key_players = keyPlayers, -- Players with keys (matches original keyPlayers)
            for_sale = houseData.inAH or 0, -- Auction house listing (matches original for_sale)
            oil_storage = houseData.oil == 1, -- Oil storage capability (matches original oilstorage)
            days_left = houseData.days_left or 30, -- Days until expiration
            expire_warning_sent = false
        }

        -- Log expiration warnings (matches original expiration logging)
        if houseData.days_left and houseData.days_left <= HOUSING_CONFIG.nearExpireWarning then
            print(string.format("^3[Olympus Housing]^7 House ID %d owned by %s will expire in %d days",
                houseId, houseData.name, houseData.days_left))
        end

        print(string.format("^3[Olympus Housing]^7 Loaded house ID %d at %s (Owner: %s)",
            houseId, position, houseData.name))
    end
end

-- Start house expiration checker
function StartHouseExpirationChecker()
    CreateThread(function()
        while true do
            Wait(3600000) -- Check every hour

            for houseId, house in pairs(OlympusHousing.Houses) do
                -- Check for expiration warnings
                if house.days_left <= HOUSING_CONFIG.nearExpireWarning and not house.expire_warning_sent then
                    -- Send warning to owner if online
                    for _, playerId in ipairs(GetPlayers()) do
                        local playerData = exports['olympus-core']:GetPlayerData(tonumber(playerId))
                        if playerData and playerData.identifier == house.owner_id then
                            TriggerClientEvent('olympus:client:notify', tonumber(playerId), {
                                type = 'warning',
                                title = 'House Expiration',
                                message = string.format('Your house will expire in %d days! Pay your taxes to extend it.', house.days_left)
                            })
                            break
                        end
                    end
                    house.expire_warning_sent = true
                end

                -- Check for expired houses
                if house.days_left <= 0 then
                    ExpireHouse(houseId)
                end
            end
        end
    end)
end

-- Expire house (matches original house cleanup)
function ExpireHouse(houseId)
    local house = OlympusHousing.Houses[houseId]
    if not house then return end

    -- Update database to mark as unowned (matches original cleanup)
    local query = [[
        UPDATE houses
        SET owned = '0', pos = '[]', pid = '', player_keys = '[]', inventory = '{}', physical_inventory = '{}'
        WHERE id = ?
    ]]

    exports['olympus-core']:ExecuteQuery(query, {houseId}, function(success)
        if success then
            -- Remove from memory
            OlympusHousing.Houses[houseId] = nil

            -- Notify all clients to update house markers
            TriggerClientEvent('olympus:client:houseExpired', -1, houseId)

            print(string.format("^1[Olympus Housing]^7 House ID %d expired and was cleaned up", houseId))
        end
    end)
end

-- Initialize when loaded
CreateThread(function()
    -- Wait for core to be ready
    while not exports['olympus-core']:ExecuteQuery do
        Wait(100)
    end

    Wait(5000) -- Additional delay for core system to load
    InitializeHousingSystem()
end)

-- Helper functions
function GetPlayerHouseCount(playerIdentifier)
    local count = 0
    for _, house in pairs(OlympusHousing.Houses) do
        if house.owner_id == playerIdentifier then
            count = count + 1
        end
    end
    return count
end

function IsLocationOccupied(position, radius)
    radius = radius or 50.0
    for _, house in pairs(OlympusHousing.Houses) do
        if #(house.position - position) < radius then
            return true
        end
    end
    return false
end

-- Buy house (matches original fn_buyHouse.sqf logic)
RegisterServerEvent('olympus:server:buyHouse')
AddEventHandler('olympus:server:buyHouse', function(housePosition, houseType)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData then return end

    -- Check if player has home license (matches original license check)
    if not playerData.licenses or not playerData.licenses.home then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Housing',
            message = 'You need a home license to buy property'
        })
        return
    end

    -- Check house limit (matches original house limit check)
    local playerHouseCount = GetPlayerHouseCount(playerData.identifier)
    if playerHouseCount >= HOUSING_CONFIG.maxHousesPerPlayer then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Housing',
            message = string.format('You can only own %d houses maximum', HOUSING_CONFIG.maxHousesPerPlayer)
        })
        return
    end

    -- Get house configuration
    local houseConfig = HOUSE_TYPES[houseType]
    if not houseConfig then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Housing',
            message = 'Invalid house type'
        })
        return
    end

    -- Check if player has enough money (matches original money check)
    if playerData.bank < houseConfig.price then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Housing',
            message = 'Insufficient funds in bank account'
        })
        return
    end

    -- Check if location is available
    if IsLocationOccupied(housePosition) then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Housing',
            message = 'This location is already occupied'
        })
        return
    end

    -- Process purchase (matches original transaction logic)
    local success = exports['olympus-core']:RemoveMoney(source, 'bank', houseConfig.price)
    if success then
        -- Insert house into database (matches original house insertion)
        local insertQuery = [[
            INSERT INTO houses (pid, pos, owned, storageCapacity, physicalStorageCapacity,
                              player_keys, inventory, physical_inventory, expires_on, server)
            VALUES (?, ?, '1', ?, ?, ?, '{}', '{}', DATE_ADD(NOW(), INTERVAL 30 DAY), ?)
        ]]

        local keyPlayers = json.encode({playerData.identifier})
        local positionJson = json.encode({x = housePosition.x, y = housePosition.y, z = housePosition.z})

        exports['olympus-core']:ExecuteQuery(insertQuery, {
            playerData.identifier,
            positionJson,
            houseConfig.storage,
            houseConfig.physical,
            keyPlayers,
            GetConvar('sv_servername', 'olympus')
        }, function(success, result)
            if success and result.insertId then
                local houseId = result.insertId

                -- Add to memory (matches original house variable setting)
                OlympusHousing.Houses[houseId] = {
                    id = houseId,
                    owner_id = playerData.identifier,
                    owner_name = playerData.name,
                    position = housePosition,
                    locked = true,
                    inventory = {},
                    physical_inventory = {},
                    storage_capacity = houseConfig.storage,
                    physical_capacity = houseConfig.physical,
                    key_players = {playerData.identifier},
                    for_sale = 0,
                    oil_storage = false,
                    days_left = 30,
                    expire_warning_sent = false
                }

                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'success',
                    title = 'Housing',
                    message = string.format('House purchased for $%s!', houseConfig.price)
                })

                -- Update all clients with new house (matches original house ownership broadcast)
                TriggerClientEvent('olympus:client:houseOwnership', -1, houseId, 1, playerData.identifier)

                print(string.format("^2[Olympus Housing]^7 %s purchased house ID %d for $%s",
                    playerData.name, houseId, houseConfig.price))
            else
                -- Refund money if database insertion failed
                exports['olympus-core']:AddMoney(source, 'bank', houseConfig.price)
                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'error',
                    title = 'Housing',
                    message = 'Failed to purchase house - money refunded'
                })
            end
        end)
    end
end)

-- Sell house (matches original fn_sellHouse.sqf logic)
RegisterServerEvent('olympus:server:sellHouse')
AddEventHandler('olympus:server:sellHouse', function(houseId)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData then return end

    local house = OlympusHousing.Houses[houseId]
    if not house then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Housing',
            message = 'House not found'
        })
        return
    end

    -- Check ownership (matches original ownership check)
    if house.owner_id ~= playerData.identifier then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Housing',
            message = 'You do not own this house'
        })
        return
    end

    -- Calculate sell price (matches original 75% return)
    local houseType = GetHouseTypeByCapacity(house.storage_capacity)
    local originalPrice = HOUSE_TYPES[houseType] and HOUSE_TYPES[houseType].price or 100000
    local sellPrice = math.floor(originalPrice * HOUSING_CONFIG.sellPriceMultiplier)

    -- Update database to mark as unowned (matches original fn_sellHouse.sqf)
    local query = [[
        UPDATE houses
        SET owned = '0', pos = '[]', pid = '', player_keys = '[]', inventory = '{}', physical_inventory = '{}'
        WHERE id = ? AND pid = ?
    ]]

    exports['olympus-core']:ExecuteQuery(query, {houseId, playerData.identifier}, function(success)
        if success then
            -- Give money to player
            exports['olympus-core']:AddMoney(source, 'bank', sellPrice)

            -- Remove from memory
            OlympusHousing.Houses[houseId] = nil

            TriggerClientEvent('olympus:client:notify', source, {
                type = 'success',
                title = 'Housing',
                message = string.format('House sold for $%s', sellPrice)
            })

            -- Update all clients (matches original house ownership broadcast)
            TriggerClientEvent('olympus:client:houseOwnership', -1, houseId, 2, playerData.identifier)

            print(string.format("^2[Olympus Housing]^7 %s sold house ID %d for $%s",
                playerData.name, houseId, sellPrice))
        else
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Housing',
                message = 'Failed to sell house'
            })
        end
    end)
end)

function GetHouseTypeByCapacity(capacity)
    for houseType, config in pairs(HOUSE_TYPES) do
        if config.storage == capacity then
            return houseType
        end
    end
    return "small_house" -- Default
end

-- Exports (matches original export structure)
exports('PurchaseHouse', function(source, housePosition, houseType)
    TriggerEvent('olympus:server:buyHouse', housePosition, houseType)
end)

exports('SellHouse', function(source, houseId)
    TriggerEvent('olympus:server:sellHouse', houseId)
end)

exports('GetHouseInfo', function(houseId)
    return OlympusHousing.Houses[houseId]
end)

exports('GetAllHouses', function()
    return OlympusHousing.Houses
end)

exports('InitializeHousingSystem', function()
    InitializeHousingSystem()
end)

-- House data request handler
RegisterServerEvent('olympus:server:requestHouseData')
AddEventHandler('olympus:server:requestHouseData', function()
    local source = source
    TriggerClientEvent('olympus:client:updateHouses', source, OlympusHousing.Houses)
end)

-- Toggle house lock (matches original lock functionality)
RegisterServerEvent('olympus:server:toggleHouseLock')
AddEventHandler('olympus:server:toggleHouseLock', function(houseId)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData then return end

    local house = OlympusHousing.Houses[houseId]
    if not house then return end

    -- Check if player has access (owner or key holder)
    local hasAccess = house.owner_id == playerData.identifier or HasHouseKey(house, playerData.identifier)
    if not hasAccess then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Housing',
            message = 'You do not have access to this house'
        })
        return
    end

    -- Toggle lock status
    house.locked = not house.locked

    -- Update database
    local query = "UPDATE houses SET locked = ? WHERE id = ?"
    exports['olympus-core']:ExecuteQuery(query, {house.locked and 1 or 0, houseId}, function(success)
        if success then
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'success',
                title = 'Housing',
                message = house.locked and 'House locked' or 'House unlocked'
            })

            -- Notify all clients of lock change
            TriggerClientEvent('olympus:client:houseLockChanged', -1, houseId, house.locked)
        end
    end)
end)

-- Open house storage (placeholder for storage system)
RegisterServerEvent('olympus:server:openHouseStorage')
AddEventHandler('olympus:server:openHouseStorage', function(houseId, storageType)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData then return end

    local house = OlympusHousing.Houses[houseId]
    if not house then return end

    -- Check if player has access
    local hasAccess = house.owner_id == playerData.identifier or HasHouseKey(house, playerData.identifier)
    if not hasAccess then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Housing',
            message = 'You do not have access to this house storage'
        })
        return
    end

    -- TODO: Implement storage system integration
    TriggerClientEvent('olympus:client:notify', source, {
        type = 'info',
        title = 'Housing',
        message = 'Storage system coming soon!'
    })
end)

-- Open house keys management (placeholder for key system)
RegisterServerEvent('olympus:server:openHouseKeys')
AddEventHandler('olympus:server:openHouseKeys', function(houseId)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData then return end

    local house = OlympusHousing.Houses[houseId]
    if not house then return end

    -- Only owner can manage keys
    if house.owner_id ~= playerData.identifier then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Housing',
            message = 'Only the house owner can manage keys'
        })
        return
    end

    -- TODO: Implement key management system
    TriggerClientEvent('olympus:client:notify', source, {
        type = 'info',
        title = 'Housing',
        message = 'Key management system coming soon!'
    })
end)

-- Helper function to check house keys
function HasHouseKey(house, playerIdentifier)
    if not house.key_players then return false end

    for _, keyHolder in ipairs(house.key_players) do
        if keyHolder == playerIdentifier then
            return true
        end
    end
    return false
end

print("^2[Olympus Housing]^7 Server main loaded!")
