# 🚔 APD System Testing Guide

## 📋 **Test 1: APD Processing System**

### **Commands to Test:**
- `/process` - Start processing a suspect

### **Test Steps:**
1. **Find Another Player** (or create a second character)
2. **Get Close** (within 5 meters)
3. **Type `/process`** 
4. **Expected Result:**
   - <PERSON>sole shows: "Started processing [PlayerName]"
   - 15-minute processing timer starts
   - Both officer and suspect get notifications

### **Processing Actions to Test:**
- **Add Charges** (server events)
- **Issue Ticket** (complete processing with ticket)
- **Send to Jail** (complete processing with jail)
- **Pardon** (complete processing with pardon)

---

## 🔫 **Test 2: APD Equipment System**

### **Commands to Test:**
- `/equipment` - Request APD equipment

### **Test Steps:**
1. **Type `/equipment`**
2. **Expected Result:**
   - Weapons given based on your rank (4 = Sergeant)
   - Your rank 4 should get: stungun, flashlight, nightstick, pistol, carbinerifle
   - Gear given: radio, handcuffs, citation_book, spike_strips, defuse_kit, road_kit
   - Console shows: "Equipment issued to [<PERSON><PERSON><PERSON>] (Rank 4)"

### **Rank-Based Equipment:**
- **Rank 1 (Deputy)**: stungun, flashlight, nightstick
- **Rank 2 (Patrol Officer)**: + pistol
- **Rank 3 (Corporal)**: + carbinerifle
- **Rank 4+ (Sergeant+)**: All weapons

---

## 🚗 **Test 3: APD Vehicle System**

### **Commands to Test:**
- `/apdveh [vehicle]` - Spawn APD vehicle

### **Test Steps:**
1. **Type `/apdveh police`** (basic police car)
2. **Type `/apdveh police2`** (another police car)
3. **Type `/apdveh polmav`** (police helicopter - rank 3+ only)
4. **Expected Results:**
   - Vehicle spawns near you with APD license plate
   - Console shows: "Spawned vehicle: [vehiclename]"
   - Higher rank vehicles require appropriate rank

### **Available Vehicles by Rank:**
- **Rank 1-2**: police, police2, police3
- **Rank 3+**: + police4, polmav (helicopter)
- **Rank 4+**: All vehicles

---

## 📡 **Test 4: APD Communication & Dispatch**

### **Commands to Test:**
- `/dispatch [type] [message]` - Create dispatch call
- `/backup` - Request backup

### **Test Steps:**
1. **Type `/dispatch emergency Officer needs assistance at bank`**
2. **Type `/backup`**
3. **Expected Results:**
   - Dispatch call created with your message
   - Backup request sent to all on-duty APD
   - Other APD get notifications and waypoints

---

## ⚖️ **Test 5: Processing Workflow**

### **Complete Processing Test:**
1. **Start Processing**: `/process` (near another player)
2. **Add Charges**: (would use UI or additional commands)
3. **Complete Processing**: Choose ticket, jail, or pardon
4. **Expected Results:**
   - Processing completes within 15 minutes
   - Appropriate action taken (ticket/jail/pardon)
   - Both parties notified

---

## 🎯 **Quick Test Commands Summary:**

```
/duty          - Toggle on/off duty
/process       - Process nearest player
/equipment     - Get APD equipment
/apdveh police - Spawn police car
/dispatch emergency Test message
/backup        - Request backup
```

---

## 🔍 **What to Look For:**

### **Console Messages:**
- APD debug messages showing system working
- Equipment/vehicle spawn confirmations
- Processing status updates

### **UI Updates:**
- HUD shows duty status changes
- Notifications for all actions
- Backup requests with waypoints

### **Game Effects:**
- Weapons appear in inventory
- Vehicles spawn with APD plates
- Processing timers and notifications

---

## 🚨 **Expected Issues & Solutions:**

### **"No player nearby"**
- **Solution**: Get within 5 meters of another player

### **"You do not have access to this vehicle"**
- **Solution**: Use vehicles appropriate for your rank

### **Processing not working**
- **Solution**: Ensure both players are loaded and nearby

---

## 📊 **Test Results Checklist:**

- [ ] `/duty` command works and updates HUD
- [ ] `/process` starts processing with nearby player
- [ ] `/equipment` gives rank-appropriate weapons/gear
- [ ] `/apdveh` spawns vehicles with proper access control
- [ ] `/dispatch` creates dispatch calls
- [ ] `/backup` sends backup requests to other APD
- [ ] Processing system completes with ticket/jail/pardon options
- [ ] All notifications and console messages appear correctly

---

**Ready to test? Start with `/equipment` to get your gear, then try `/apdveh police` to get a patrol car!** 🚔
