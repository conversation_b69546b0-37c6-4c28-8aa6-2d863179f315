-- Olympus Legal System - Server Main

CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end
    
    print("[Olympus Legal System] Server initialized")
end)

exports('RegisterLawyer', function(source)
    print("[Olympus Legal System] Registering lawyer")
end)

exports('IssueTicket', function(source, targetId, ticketData)
    print("[Olympus Legal System] Issuing ticket")
end)

exports('ProcessPardon', function(source, pardonData)
    print("[Olympus Legal System] Processing pardon")
end)

exports('GetPlayerCharges', function(source)
    return {}
end)

print("[Olympus Legal System] Server module loaded")
