-- ============================================
-- OLYMPUS CONQUEST SYSTEM - CLIENT
-- Based on original fn_conquestClient.sqf
-- ============================================

local OlympusConquest = {}

-- ============================================
-- CLIENT STATE
-- ============================================

local conquestActive = false
local conquestData = {}
local conquestMarkers = {}
local conquestFlags = {}
local conquestLines = {}
local lockedChopShop = ""
local isCapturing = false
local captureProgress = 0
local captureStartTime = 0

-- ============================================
-- UTILITY FUNCTIONS
-- ============================================

function OlympusConquest.GetPlayerGangData()
    local success, result = pcall(function()
        return exports['olympus-gangs']:GetPlayerGang()
    end)
    
    if success and result then
        return result
    end
    return nil
end

function OlympusConquest.IsPlayerInGang()
    local gangData = OlympusConquest.GetPlayerGangData()
    return gangData ~= nil and gangData.id ~= nil
end

function OlympusConquest.DrawText3D(coords, text, scale)
    local onScreen, _x, _y = World3dToScreen2d(coords.x, coords.y, coords.z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())
    local dist = GetDistanceBetweenCoords(px, py, pz, coords.x, coords.y, coords.z, 1)
    
    if onScreen then
        local scale = (1 / dist) * scale
        local fov = (1 / GetGameplayCamFov()) * 100
        scale = scale * fov
        
        SetTextScale(0.0 * scale, 0.55 * scale)
        SetTextFont(0)
        SetTextProportional(1)
        SetTextColour(255, 255, 255, 255)
        SetTextDropshadow(0, 0, 0, 0, 255)
        SetTextEdge(2, 0, 0, 0, 150)
        SetTextDropShadow()
        SetTextOutline()
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x, _y)
    end
end

function OlympusConquest.IsPointInPolygon(point, polygon)
    local x, y = point.x, point.y
    local inside = false
    local j = #polygon
    
    for i = 1, #polygon do
        local xi, yi = polygon[i][1], polygon[i][2]
        local xj, yj = polygon[j][1], polygon[j][2]
        
        if ((yi > y) ~= (yj > y)) and (x < (xj - xi) * (y - yi) / (yj - yi) + xi) then
            inside = not inside
        end
        j = i
    end
    
    return inside
end

-- ============================================
-- MARKER MANAGEMENT
-- ============================================

function OlympusConquest.CreatePointMarker(markerId, coords, name)
    conquestMarkers[markerId] = {
        coords = vector3(coords[1], coords[2], coords[3]),
        name = name,
        active = false
    }
    
    -- Create blip
    local blip = AddBlipForCoord(coords[1], coords[2], coords[3])
    SetBlipSprite(blip, 84) -- Capture point icon
    SetBlipColour(blip, 5) -- Yellow
    SetBlipScale(blip, 1.0)
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(string.format("Capture Point %s", name))
    EndTextCommandSetBlipName(blip)
    
    conquestMarkers[markerId].blip = blip
end

function OlympusConquest.CreateLineMarker(markerId, startCoords, endCoords)
    conquestLines[markerId] = {
        start = vector3(startCoords[1], startCoords[2], startCoords[3]),
        endPoint = vector3(endCoords[1], endCoords[2], endCoords[3])
    }
end

function OlympusConquest.ActivateMarkers()
    for markerId, marker in pairs(conquestMarkers) do
        marker.active = true
        
        -- Change blip color to red (active)
        if marker.blip then
            SetBlipColour(marker.blip, 1) -- Red
        end
    end
end

function OlympusConquest.CreateFlag(flagIndex, flagData)
    conquestFlags[flagIndex] = {
        coords = vector3(flagData.coords[1], flagData.coords[2], flagData.coords[3]),
        owner = flagData.owner,
        phonetic = flagData.phonetic,
        capturing = flagData.capturing,
        object = nil
    }
    
    -- Spawn flag object (you might want to use a custom prop)
    local flagCoords = conquestFlags[flagIndex].coords
    local flagObject = CreateObject(GetHashKey("prop_flag_us"), flagCoords.x, flagCoords.y, flagCoords.z, false, false, false)
    SetEntityHeading(flagObject, 0.0)
    FreezeEntityPosition(flagObject, true)
    
    conquestFlags[flagIndex].object = flagObject
end

function OlympusConquest.CleanupMarkers()
    -- Remove blips
    for _, marker in pairs(conquestMarkers) do
        if marker.blip then
            RemoveBlip(marker.blip)
        end
    end
    
    -- Remove flag objects
    for _, flag in pairs(conquestFlags) do
        if flag.object then
            DeleteObject(flag.object)
        end
    end
    
    -- Clear tables
    conquestMarkers = {}
    conquestFlags = {}
    conquestLines = {}
    
    conquestActive = false
    isCapturing = false
    captureProgress = 0
end

-- ============================================
-- CAPTURE MECHANICS
-- ============================================

function OlympusConquest.StartCapture(flagIndex)
    if isCapturing then return end
    
    local flag = conquestFlags[flagIndex]
    if not flag then return end
    
    isCapturing = true
    captureProgress = 0
    captureStartTime = GetGameTimer()
    
    local playerPed = PlayerPedId()
    local weapon = GetSelectedPedWeapon(playerPed)
    
    -- Start capture progress
    CreateThread(function()
        while isCapturing do
            Wait(100)
            
            local currentTime = GetGameTimer()
            local elapsed = currentTime - captureStartTime
            captureProgress = math.min(elapsed / 10000, 1.0) -- 10 seconds to capture
            
            -- Check if player moved too far or stopped aiming
            local playerPos = GetEntityCoords(playerPed)
            local distance = #(playerPos - flag.coords)
            
            if distance > Config.Settings.captureRadius then
                OlympusConquest.CancelCapture(flagIndex)
                exports['olympus-ui']:ShowNotification("You moved too far from the capture point!", "error")
                break
            end
            
            -- Check if player is still aiming
            if not IsPlayerFreeAiming(PlayerId()) then
                OlympusConquest.CancelCapture(flagIndex)
                exports['olympus-ui']:ShowNotification("You must keep aiming to capture!", "error")
                break
            end
            
            -- Check weapon and ammo
            local currentWeapon = GetSelectedPedWeapon(playerPed)
            local ammo = GetAmmoInPedWeapon(playerPed, currentWeapon)
            
            if currentWeapon ~= weapon or ammo <= 0 then
                OlympusConquest.CancelCapture(flagIndex)
                exports['olympus-ui']:ShowNotification("You need to maintain your weapon and ammo!", "error")
                break
            end
            
            -- Complete capture
            if captureProgress >= 1.0 then
                TriggerServerEvent('olympus-conquest:captureComplete', flagIndex)
                isCapturing = false
                captureProgress = 0
                exports['olympus-ui']:ShowNotification("Capture point secured!", "success")
                break
            end
        end
    end)
end

function OlympusConquest.CancelCapture(flagIndex)
    if not isCapturing then return end
    
    isCapturing = false
    captureProgress = 0
    
    TriggerServerEvent('olympus-conquest:captureCancel', flagIndex)
end

-- ============================================
-- MAIN THREAD
-- ============================================

CreateThread(function()
    while true do
        Wait(0)
        
        if conquestActive then
            local playerPed = PlayerPedId()
            local playerPos = GetEntityCoords(playerPed)
            
            -- Draw zone boundary lines
            for _, line in pairs(conquestLines) do
                DrawLine(line.start.x, line.start.y, line.start.z + 50.0,
                        line.endPoint.x, line.endPoint.y, line.endPoint.z + 50.0,
                        255, 255, 0, 255)
            end
            
            -- Handle capture points
            for flagIndex, flag in pairs(conquestFlags) do
                local distance = #(playerPos - flag.coords)
                
                -- Draw capture point marker
                DrawMarker(1, flag.coords.x, flag.coords.y, flag.coords.z - 1.0,
                          0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                          Config.Settings.captureRadius * 2, Config.Settings.captureRadius * 2, 1.0,
                          255, 255, 0, 100, false, true, 2, false, nil, nil, false)
                
                -- Draw 3D text
                if distance < 50.0 then
                    local ownerText = flag.owner ~= -1 and "Owned" or "Neutral"
                    local text = string.format("Point %s\n%s", flag.phonetic, ownerText)
                    OlympusConquest.DrawText3D(flag.coords + vector3(0, 0, 2), text, 2.0)
                end
                
                -- Handle capture interaction
                if distance <= Config.Settings.captureRadius and not isCapturing then
                    if OlympusConquest.IsPlayerInGang() then
                        exports['olympus-ui']:ShowNotification("Hold [E] to capture this point", "info")
                        
                        if IsControlJustPressed(0, 38) then -- E key
                            TriggerServerEvent('olympus-conquest:capturePoint', flagIndex)
                        end
                    else
                        exports['olympus-ui']:ShowNotification("You must be in a gang to capture!", "error")
                    end
                end
            end
            
            -- Draw capture progress
            if isCapturing then
                local progress = math.floor(captureProgress * 100)
                exports['olympus-ui']:ShowNotification(string.format("Capturing... %d%%", progress), "info")
            end
        else
            Wait(1000) -- Reduce frequency when not active
        end
    end
end)

-- ============================================
-- EVENT HANDLERS
-- ============================================

-- Server notifications
RegisterNetEvent('olympus-conquest:notify', function(message, type)
    exports['olympus-ui']:ShowNotification(message, type or "info")
end)

-- Conquest data updates
RegisterNetEvent('olympus-conquest:updateData', function(data)
    conquestData = data
    conquestActive = data.active
end)

-- Marker creation
RegisterNetEvent('olympus-conquest:createPointMarker', function(markerId, coords, name)
    OlympusConquest.CreatePointMarker(markerId, coords, name)
end)

RegisterNetEvent('olympus-conquest:createLineMarker', function(markerId, startCoords, endCoords)
    OlympusConquest.CreateLineMarker(markerId, startCoords, endCoords)
end)

RegisterNetEvent('olympus-conquest:activateMarkers', function()
    OlympusConquest.ActivateMarkers()
    conquestActive = true
end)

-- Flag management
RegisterNetEvent('olympus-conquest:createFlag', function(flagIndex, flagData)
    OlympusConquest.CreateFlag(flagIndex, flagData)
end)

RegisterNetEvent('olympus-conquest:flagCaptured', function(flagIndex, gangId, gangName)
    if conquestFlags[flagIndex] then
        conquestFlags[flagIndex].owner = gangId
        conquestFlags[flagIndex].capturing = false

        exports['olympus-ui']:ShowNotification(
            string.format("Point %s captured by %s!",
                conquestFlags[flagIndex].phonetic, gangName),
            "success"
        )
    end
end)

RegisterNetEvent('olympus-conquest:captureCancelled', function(flagIndex)
    if conquestFlags[flagIndex] then
        conquestFlags[flagIndex].capturing = false
    end

    if isCapturing then
        isCapturing = false
        captureProgress = 0
        exports['olympus-ui']:ShowNotification("Capture cancelled!", "error")
    end
end)

-- Capture process
RegisterNetEvent('olympus-conquest:startCapture', function(flagIndex)
    OlympusConquest.StartCapture(flagIndex)
end)

-- Cleanup
RegisterNetEvent('olympus-conquest:cleanup', function()
    OlympusConquest.CleanupMarkers()
end)

-- Chop shop locking
RegisterNetEvent('olympus-conquest:setLockedChopShop', function(chopShop)
    lockedChopShop = chopShop

    if chopShop ~= "" then
        exports['olympus-ui']:ShowNotification(
            string.format("Chop shop %s is locked during conquest!", chopShop),
            "warning"
        )
    end
end)

-- House unlocking
RegisterNetEvent('olympus-conquest:unlockHouses', function(polygon)
    -- This would integrate with the housing system to unlock houses in the conquest zone
    exports['olympus-ui']:ShowNotification("Houses in the conquest zone are now unlocked!", "info")
end)

-- Big gang cooldown
RegisterNetEvent('olympus-conquest:bigGangCooldown', function(gangId)
    local playerGang = OlympusConquest.GetPlayerGangData()
    if playerGang and playerGang.id == gangId then
        exports['olympus-ui']:ShowNotification(
            "Your gang has too many players in the zone! Cooldown applied.",
            "warning"
        )
    end
end)

-- Voting system
RegisterNetEvent('olympus-conquest:voteStart', function()
    exports['olympus-ui']:ShowNotification(
        "Conquest vote started! Use /vote to select a location.",
        "info"
    )
end)

RegisterNetEvent('olympus-conquest:openVoteMenu', function()
    -- This would open a NUI menu for voting
    -- For now, just show available zones
    local message = "Available zones:\n"
    for i, zone in ipairs(Config.VoteZones) do
        message = message .. string.format("%d. %s\n", i, zone)
    end
    message = message .. "Use /vote [number] to vote"

    exports['olympus-ui']:ShowNotification(message, "info")
end)

-- Payout notification
RegisterNetEvent('olympus-conquest:payoutReceived', function(amount)
    exports['olympus-ui']:ShowNotification(
        string.format("Conquest payout received: $%s", ESX.Math.GroupDigits(amount)),
        "success"
    )
end)

-- ============================================
-- DEATH HANDLING
-- ============================================

AddEventHandler('gameEventTriggered', function(name, args)
    if name == 'CEventNetworkEntityDamage' then
        local victim = args[1]
        local attacker = args[2]
        local isDead = args[4]

        if victim == PlayerPedId() and isDead and conquestActive then
            -- Player died during conquest
            TriggerServerEvent('olympus-conquest:playerDeath', attacker)

            -- Cancel any ongoing capture
            if isCapturing then
                isCapturing = false
                captureProgress = 0
            end
        end
    end
end)

-- ============================================
-- EXPORT FUNCTIONS
-- ============================================

exports('IsConquestActive', function()
    return conquestActive
end)

exports('GetConquestData', function()
    return conquestData
end)

exports('IsCapturing', function()
    return isCapturing
end)

exports('GetCaptureProgress', function()
    return captureProgress
end)

exports('IsChopShopLocked', function(chopShop)
    return lockedChopShop == chopShop
end)

print("^2[Olympus Conquest]^7 Client system loaded successfully")
