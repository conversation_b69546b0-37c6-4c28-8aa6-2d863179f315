-- Olympus Core Framework - Main Server Script
-- This is the primary server-side entry point for the framework

local OlympusCore = {}
OlympusCore.Players = {}
OlympusCore.UsableItems = {}
OlympusCore.ServerCallbacks = {}

-- Framework initialization
CreateThread(function()
    print("^2[Olympus Core]^7 Starting Olympus Altis Life Framework...")

    -- Wait for database to be ready
    while not exports['olympus-core']:IsDBReady() do
        Wait(100)
    end

    print("^2[Olympus Core]^7 Framework successfully initialized!")
end)

-- Player connection handling
AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)

    deferrals.defer()

    Wait(0)

    deferrals.update("Checking player data...")

    -- Load player data using new database system
    exports['olympus-core']:LoadPlayerData(identifier, function(playerData)
        if not playerData then
            -- Create new player
            exports['olympus-core']:CreatePlayerData(identifier, name, function(success)
                if success then
                    deferrals.done()
                else
                    deferrals.done("Failed to create player data. Please try again.")
                end
            end)
        else
            deferrals.done()
        end
    end)
end)

-- Player spawned
AddEventHandler('playerJoined', function()
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    
    -- Initialize player
    TriggerEvent('olympus:server:playerJoined', source, identifier)
end)

-- Player disconnection
AddEventHandler('playerDropped', function(reason)
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    
    if OlympusCore.Players[source] then
        -- Save player data
        TriggerEvent('olympus:server:savePlayerData', source)
        
        -- Clean up player data
        OlympusCore.Players[source] = nil
        
        print("^3[Olympus Core]^7 Player " .. GetPlayerName(source) .. " disconnected: " .. reason)
    end
end)

-- Player joined event
RegisterServerEvent('olympus:server:playerJoined')
AddEventHandler('olympus:server:playerJoined', function()
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)

    print(string.format("^3[Olympus Core]^7 Player joining: %s (ID: %s)", GetPlayerName(source), identifier))

    -- Load player data using new database system
    exports['olympus-core']:LoadPlayerData(identifier, function(playerData)
        if not playerData then
            print(string.format("^3[Olympus Core]^7 Creating new player data for: %s", GetPlayerName(source)))
            exports['olympus-core']:CreatePlayerData(identifier, GetPlayerName(source), function(success)
                if success then
                    -- Reload the player data after creation
                    exports['olympus-core']:LoadPlayerData(identifier, function(newPlayerData)
                        if newPlayerData then
                            InitializePlayer(source, identifier, newPlayerData)
                        else
                            print("^1[Olympus Core]^7 Failed to load newly created player data for " .. GetPlayerName(source))
                            DropPlayer(source, "Failed to load player data. Please try again.")
                        end
                    end)
                else
                    print("^1[Olympus Core]^7 Failed to create player data for " .. GetPlayerName(source))
                    DropPlayer(source, "Failed to create player data. Please try again.")
                end
            end)
        else
            InitializePlayer(source, identifier, playerData)
        end
    end)
end)

-- Initialize player function
function InitializePlayer(source, identifier, playerData)
    OlympusCore.Players[source] = {
        source = source,
        identifier = identifier,
        name = GetPlayerName(source),
        data = playerData,
        lastSave = GetGameTimer()
    }

    -- Send player data to client
    TriggerClientEvent('olympus:client:playerLoaded', source, playerData)

    -- Send notification
    TriggerClientEvent('olympus:client:notify', source, {
        type = 'success',
        title = 'Welcome to Olympus',
        message = 'Welcome to Olympus, ' .. GetPlayerName(source) .. '!'
    })

    print("^2[Olympus Core]^7 Player " .. GetPlayerName(source) .. " loaded successfully")
end

-- Save player data event
RegisterServerEvent('olympus:server:savePlayerData')
AddEventHandler('olympus:server:savePlayerData', function(source)
    if not source then source = source end

    local player = OlympusCore.Players[source]
    if player and player.data then
        exports['olympus-core']:SavePlayerData(player.identifier, player.data, function(success)
            if success then
                player.lastSave = GetGameTimer()
            else
                print("^1[Olympus Core]^7 Failed to save player data for " .. GetPlayerName(source))
            end
        end)
    end
end)

-- Save player data periodically
CreateThread(function()
    while true do
        Wait(300000) -- Save every 5 minutes

        for source, player in pairs(OlympusCore.Players) do
            if player and player.data then
                TriggerEvent('olympus:server:savePlayerData', source)
            end
        end

        print("^3[Olympus Core]^7 Auto-saved all player data")
    end
end)

-- Get player data
function GetPlayerData(source)
    if OlympusCore.Players[source] then
        return OlympusCore.Players[source].data
    end
    return nil
end

-- Update player data
function UpdatePlayerData(source, key, value)
    if OlympusCore.Players[source] and OlympusCore.Players[source].data then
        OlympusCore.Players[source].data[key] = value
        TriggerClientEvent('olympus:client:updatePlayerData', source, key, value)
        return true
    end
    return false
end

-- Get all players
function GetAllPlayers()
    return OlympusCore.Players
end

-- Get player by identifier
function GetPlayerByIdentifier(identifier)
    for source, player in pairs(OlympusCore.Players) do
        if player.identifier == identifier then
            return player
        end
    end
    return nil
end

-- Server callbacks system
function RegisterServerCallback(name, cb)
    OlympusCore.ServerCallbacks[name] = cb
end

RegisterServerEvent('olympus:server:triggerCallback')
AddEventHandler('olympus:server:triggerCallback', function(name, requestId, ...)
    local source = source
    
    if OlympusCore.ServerCallbacks[name] then
        OlympusCore.ServerCallbacks[name](source, function(...)
            TriggerClientEvent('olympus:client:serverCallback', source, requestId, ...)
        end, ...)
    else
        print("^1[Olympus Core]^7 Server callback '" .. name .. "' does not exist")
    end
end)

-- Usable items system
function RegisterUsableItem(item, cb)
    OlympusCore.UsableItems[item] = cb
end

RegisterServerEvent('olympus:server:useItem')
AddEventHandler('olympus:server:useItem', function(item, ...)
    local source = source
    
    if OlympusCore.UsableItems[item] then
        OlympusCore.UsableItems[item](source, ...)
    end
end)

-- Admin commands
RegisterCommand('olympus', function(source, args, rawCommand)
    if source == 0 then -- Console
        if args[1] == 'save' then
            for src, player in pairs(OlympusCore.Players) do
                TriggerEvent('olympus:server:savePlayerData', src)
            end
            print("^2[Olympus Core]^7 Saved all player data")
        elseif args[1] == 'reload' then
            print("^3[Olympus Core]^7 Reloading framework...")
            TriggerEvent('olympus:server:reload')
        end
    else
        local playerData = GetPlayerData(source)
        if playerData and playerData.admin_level and playerData.admin_level >= 2 then
            if args[1] == 'save' then
                for src, player in pairs(OlympusCore.Players) do
                    TriggerEvent('olympus:server:savePlayerData', src)
                end
                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'success',
                    title = 'Admin',
                    message = 'Saved all player data'
                })
            end
        end
    end
end, false)

-- Exports
exports('GetPlayerData', GetPlayerData)
exports('UpdatePlayerData', UpdatePlayerData)
exports('GetAllPlayers', GetAllPlayers)
exports('GetPlayerByIdentifier', GetPlayerByIdentifier)
exports('RegisterServerCallback', RegisterServerCallback)
exports('RegisterUsableItem', RegisterUsableItem)

-- Global functions for other resources
function GetPlayerDataServer(source)
    return GetPlayerData(source)
end

function SavePlayerData(source)
    TriggerEvent('olympus:server:savePlayerData', source)
end

function LogAction(source, action, details)
    local playerData = GetPlayerData(source)
    if playerData then
        local logData = {
            player_id = playerData.id,
            identifier = playerData.identifier,
            name = playerData.name,
            action = action,
            details = details,
            timestamp = os.time()
        }
        
        TriggerEvent('olympus:server:logAction', logData)
    end
end

function BanPlayer(source, reason, duration)
    local playerData = GetPlayerData(source)
    if playerData then
        TriggerEvent('olympus:server:banPlayer', playerData.identifier, reason, duration)
        DropPlayer(source, "You have been banned: " .. reason)
    end
end

function KickPlayer(source, reason)
    DropPlayer(source, reason or "You have been kicked from the server")
end

function SendAdminMessage(message)
    for source, player in pairs(OlympusCore.Players) do
        local playerData = player.data
        if playerData.admin_level and playerData.admin_level >= 1 then
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'info',
                title = 'Admin Message',
                message = message
            })
        end
    end
end

-- Server callbacks storage
local ServerCallbacks = {}

-- Register server callback function
function RegisterServerCallback(name, callback)
    ServerCallbacks[name] = callback
end

-- Trigger server callback function
function TriggerServerCallback(name, source, callback, ...)
    if ServerCallbacks[name] then
        ServerCallbacks[name](source, callback, ...)
    else
        print(string.format("^1[Olympus Core]^7 Server callback '%s' not found", name))
    end
end

-- Export server functions
exports('GetPlayerDataServer', GetPlayerDataServer)
exports('SavePlayerData', SavePlayerData)
exports('LogAction', LogAction)
exports('BanPlayer', BanPlayer)
exports('KickPlayer', KickPlayer)
exports('SendAdminMessage', SendAdminMessage)
exports('RegisterServerCallback', RegisterServerCallback)
exports('RegisterServerCallback', RegisterServerCallback)
exports('TriggerServerCallback', TriggerServerCallback)
exports('IsDBReady', function()
    return exports['olympus-core']:GetDatabaseStatus()
end)

-- Debug command to test database
RegisterCommand('testdb', function(source, args, rawCommand)
    if source == 0 then -- Console only
        print("^3[Olympus Core]^7 Testing database connection...")

        local success, result = pcall(function()
            return exports.oxmysql:query_async('SELECT COUNT(*) as count FROM olympus_players', {})
        end)

        if success and result then
            print(string.format("^2[Olympus Core]^7 Database test successful! Players in database: %s", result[1].count))
        else
            print("^1[Olympus Core]^7 Database test failed: " .. tostring(result))
        end
    end
end, true)

-- Export: Send notification to player
exports('Notify', function(source, message, type, duration)
    type = type or 'info'
    duration = duration or 5000

    TriggerClientEvent('olympus-core:client:Notify', source, message, type, duration)
end)
