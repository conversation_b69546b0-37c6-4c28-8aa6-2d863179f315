// Olympus Altis Life Loading Screen - Authentic Recreation
// Based on original Olympus loading screen system

// Global variables
let loadingProgress = 0;
let loadingActive = true;
let currentIconFrame = 1;
let loadingText = 'Loading...';

// Content structure (matches original Olympus tree structure)
const contentStructure = {
    'Development Changelog': {
        type: 'parent',
        children: {
            'Changelog 2024-01-01': { type: 'child', file: 'change_2024_01_01.txt' },
            'Changelog 2023-12-01': { type: 'child', file: 'change_2023_12_01.txt' },
            'Changelog 2023-11-01': { type: 'child', file: 'change_2023_11_01.txt' },
            'Changelog 2023-10-01': { type: 'child', file: 'change_2023_10_01.txt' },
            'Changelog 2023-09-01': { type: 'child', file: 'change_2023_09_01.txt' }
        }
    },
    'Server Information': {
        type: 'parent',
        children: {
            'TeamSpeak and Server Info': { type: 'child', file: 'info_ts_server.txt' }
        }
    },
    'Server Rules': {
        type: 'parent',
        children: {
            'General Rules': { type: 'child', file: 'general_rules.txt' },
            'Altis Life Rules': { type: 'child', file: 'rules_server.txt' }
        }
    },
    'Staff Directory': {
        type: 'parent',
        children: {
            'Administrators': { type: 'child', file: 'administrators.txt' },
            'Designers': { type: 'child', file: 'designers.txt' },
            'Developers': { type: 'child', file: 'developers.txt' },
            'Moderators': { type: 'child', file: 'moderators.txt' },
            'Support Team': { type: 'child', file: 'supportteam.txt' }
        }
    },
    'New Player Information': {
        type: 'parent',
        file: 'newplayer.txt',
        children: {
            'Report a Player': { type: 'child', file: 'reporting.txt' },
            'Run Information': { type: 'child', file: 'runInformation.txt' },
            'Blackwater Info': { type: 'child', file: 'bwInfo.txt' },
            'Jail Info': { type: 'child', file: 'jailInfo.txt' },
            'Federal Reserve Info': { type: 'child', file: 'fedInfo.txt' },
            'Epi-Pens and Dopamine': { type: 'child', file: 'dopamine.txt' },
            'Vehicle Modifications': { type: 'child', file: 'vehInfo.txt' },
            'Vigilante Rules': { type: 'child', file: 'vigiInfo.txt' }
        }
    },
    'Police Department Information': {
        type: 'parent',
        file: 'apd.txt'
    },
    'Rescue and Recovery Information': {
        type: 'parent',
        file: 'RNR.txt'
    }
};

// Content files (sample content matching original Olympus style)
const contentFiles = {
    'change_2024_01_01.txt': `
        <div style="color: #1BFF00; font-weight: bold;">Added:</div><br/>
        • Enhanced Y Menu System<br/>
        &nbsp;&nbsp;&nbsp;&nbsp;• Improved gang management interface<br/>
        &nbsp;&nbsp;&nbsp;&nbsp;• New license management system<br/>
        &nbsp;&nbsp;&nbsp;&nbsp;• Enhanced key management<br/>
        • New Federal Events<br/>
        &nbsp;&nbsp;&nbsp;&nbsp;• Bank robbery improvements<br/>
        &nbsp;&nbsp;&nbsp;&nbsp;• Federal Reserve enhancements<br/>
        • Vehicle System Overhaul<br/>
        &nbsp;&nbsp;&nbsp;&nbsp;• Improved vehicle spawning<br/>
        &nbsp;&nbsp;&nbsp;&nbsp;• Enhanced garage system<br/><br/>

        <div style="color: #FFAE00; font-weight: bold;">Changed:</div><br/>
        • Economy rebalancing<br/>
        • Performance optimizations<br/>
        • Database improvements<br/><br/>

        <div style="color: #FF0000; font-weight: bold;">Fixed:</div><br/>
        • Various bug fixes<br/>
        • Stability improvements<br/>
        • UI enhancements<br/>
    `,

    'info_ts_server.txt': `
        <div style="text-align: center; font-weight: bold; font-size: 1.1em;">TeamSpeak and Game Server IP's</div><br/>
        You can use these IP's to remote connect directly to the server. Instead of searching in the browser list you can remote connect and it shows up instantly.<br/><br/>
        • <span style="color: #FF4500;">FiveM Server:</span> connect your-server-ip:30120<br/>
        • <span style="color: #C71585;">Discord:</span> discord.gg/olympus<br/>
        • <span style="color: #4169E1;">Website:</span> olympus-entertainment.com<br/>
    `,

    'general_rules.txt': `
        <div style="text-align: center; font-weight: bold; font-size: 1.1em;">General Server Rules</div><br/>

        <div style="color: #FF4500; font-weight: bold;">1. Respect</div><br/>
        • Treat all players and staff with respect<br/>
        • No harassment, discrimination, or toxic behavior<br/>
        • Keep chat appropriate and family-friendly<br/><br/>

        <div style="color: #FF4500; font-weight: bold;">2. No Random Death Match (RDM)</div><br/>
        • You must have a valid roleplay reason to kill another player<br/>
        • Proper initiation is required before engaging in combat<br/>
        • Self-defense is allowed when your life is threatened<br/><br/>

        <div style="color: #FF4500; font-weight: bold;">3. No Vehicle Death Match (VDM)</div><br/>
        • Using vehicles as weapons is prohibited<br/>
        • Accidents happen, but intentional ramming is not allowed<br/>
        • Report VDM incidents to staff immediately<br/><br/>

        <div style="color: #FF4500; font-weight: bold;">4. Roleplay Quality</div><br/>
        • Maintain high-quality roleplay at all times<br/>
        • Stay in character during all interactions<br/>
        • Use proper grammar and spelling in chat<br/>
    `,

    'newplayer.txt': `
        <div style="text-align: center; font-weight: bold; font-size: 1.1em;">Welcome to Olympus Altis Life!</div><br/>

        <div style="color: #4169E1; font-weight: bold;">Getting Started:</div><br/>
        • Press <span style="color: #FFD700;">Y</span> to open your inventory and main menu<br/>
        • Visit the DMV to get your driver's license<br/>
        • Start with legal jobs like apple picking or mining<br/>
        • Save money to buy your first vehicle and house<br/><br/>

        <div style="color: #4169E1; font-weight: bold;">Important Keybinds:</div><br/>
        • <span style="color: #FFD700;">Y</span> - Main Menu/Inventory<br/>
        • <span style="color: #FFD700;">T</span> - Chat<br/>
        • <span style="color: #FFD700;">F</span> - Interact<br/>
        • <span style="color: #FFD700;">U</span> - Lock/Unlock Vehicle<br/>
        • <span style="color: #FFD700;">H</span> - Holster/Unholster Weapon<br/><br/>

        <div style="color: #4169E1; font-weight: bold;">Need Help?</div><br/>
        • Ask questions in side chat (blue text)<br/>
        • Contact support team members<br/>
        • Visit our Discord for community support<br/>
        • Read the rules and guides thoroughly<br/>
    `
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeLoadingScreen();
    buildContentTree();
    startLoadingAnimation();
    setupFiveMHandlers();
});

// Initialize loading screen
function initializeLoadingScreen() {
    console.log('Olympus Altis Life Loading Screen initialized');

    // Set initial values
    updateLoadingText('Loading...');
    updateProgress(0);

    // Load default content (first changelog)
    loadContent('change_2024_01_01.txt');
}

// Build the content tree (matches original Olympus structure)
function buildContentTree() {
    const treeContainer = document.getElementById('content-tree');
    if (!treeContainer) return;

    let treeHTML = '';

    Object.keys(contentStructure).forEach(parentKey => {
        const parent = contentStructure[parentKey];

        // Add parent item
        treeHTML += `<div class="tree-item tree-parent" onclick="selectTreeItem('${parentKey}')">${parentKey}</div>`;

        // Add children if they exist
        if (parent.children) {
            Object.keys(parent.children).forEach(childKey => {
                const child = parent.children[childKey];
                treeHTML += `<div class="tree-item tree-child-1" onclick="selectTreeItem('${childKey}', '${child.file}')">${childKey}</div>`;
            });
        }
    });

    treeContainer.innerHTML = treeHTML;
}

// Handle tree item selection
function selectTreeItem(itemName, fileName = null) {
    // If it's a parent with its own file, load that
    const parentItem = contentStructure[itemName];
    if (parentItem && parentItem.file && !fileName) {
        loadContent(parentItem.file);
        return;
    }

    // If it's a child item with a file, load that
    if (fileName) {
        loadContent(fileName);
        return;
    }

    // If it's a parent without a file, show default content
    if (parentItem && !parentItem.file) {
        loadContent('thanks.txt');
    }
}

// Load content into the right panel
function loadContent(fileName) {
    const contentDisplay = document.getElementById('content-display');
    if (!contentDisplay) return;

    // Get content from our content files
    const content = contentFiles[fileName] || getDefaultContent(fileName);
    contentDisplay.innerHTML = content;
}

// Get default content for files we don't have
function getDefaultContent(fileName) {
    return `
        <div style="text-align: center; font-weight: bold; font-size: 1.1em;">Olympus Altis Life</div><br/>
        <div style="text-align: center;">Content for ${fileName} is being updated.</div><br/>
        <div style="text-align: center;">Please check back later for the latest information.</div><br/><br/>
        <div style="text-align: center; color: #4169E1;">
            Thank you for your patience!<br/>
            - The Olympus Development Team
        </div>
    `;
}

// Update loading text
function updateLoadingText(text) {
    loadingText = text;
    const loadingTextElement = document.getElementById('loading-text');
    if (loadingTextElement) {
        loadingTextElement.textContent = text;
    }
}

// Update progress
function updateProgress(progress) {
    loadingProgress = progress;
    const progressElement = document.getElementById('progress-percentage');
    if (progressElement) {
        progressElement.textContent = Math.round(progress) + '%';
    }
}

// Start loading animation (matches original Olympus icon rotation)
function startLoadingAnimation() {
    // Animate loading icon (10-frame rotation like original)
    setInterval(() => {
        if (loadingActive) {
            currentIconFrame = (currentIconFrame % 10) + 1;
            const iconImg = document.getElementById('loading-icon-img');
            if (iconImg) {
                iconImg.src = `images/icons/load-${currentIconFrame}.png`;
            }
        }
    }, 100);

    // Simulate loading progress
    let progress = 0;
    const progressInterval = setInterval(() => {
        if (progress < 100) {
            progress += Math.random() * 2 + 0.5;
            progress = Math.min(progress, 100);
            updateProgress(progress);

            // Update loading text based on progress
            if (progress < 20) {
                updateLoadingText('Initializing...');
            } else if (progress < 40) {
                updateLoadingText('Loading Resources...');
            } else if (progress < 60) {
                updateLoadingText('Connecting to Server...');
            } else if (progress < 80) {
                updateLoadingText('Synchronizing Data...');
            } else if (progress < 95) {
                updateLoadingText('Finalizing...');
            } else {
                updateLoadingText('Complete!');
                loadingActive = false;
                clearInterval(progressInterval);
            }
        }
    }, 150);
}







// Setup FiveM event handlers
function setupFiveMHandlers() {
    // Listen for loading progress updates from FiveM
    window.addEventListener('message', function(event) {
        const data = event.data;

        if (data.type === 'loadProgress') {
            loadingProgress = data.progress || 0;
            updateProgress(loadingProgress);
            if (data.label) {
                updateLoadingText(data.label);
            }
        }

        if (data.type === 'serverInfo') {
            // Handle server info updates if needed
            console.log('Server info received:', data);
        }
    });

    // Notify FiveM that loading screen is ready
    if (window.invokeNative) {
        window.invokeNative('LOADING_SCREEN_READY');
    }
}

// Return to lobby function (matches original Olympus functionality)
function returnToLobby() {
    if (window.invokeNative) {
        // FiveM native call to return to main menu
        window.invokeNative('SHUTDOWN_LOADING_SCREEN');
    } else {
        console.log('Return to lobby clicked');
    }
}

// Add smooth transitions to elements
document.addEventListener('DOMContentLoaded', function() {
    const elements = document.querySelectorAll('#loading-text, #current-tip');
    elements.forEach(element => {
        element.style.transition = 'opacity 0.3s ease';
    });
});
