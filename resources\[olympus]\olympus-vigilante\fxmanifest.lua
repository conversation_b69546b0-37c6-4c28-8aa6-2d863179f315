fx_version 'cerulean'
game 'gta5'

name 'Olympus Vigilante System'
description 'Complete vigilante system based on original Olympus fn_vigiGetSetArrests.sqf and vigilante functions'
author 'Olympus Development Team'
version '2.0.0'

-- Dependencies
dependencies {
    'olympus-core',
    'olympus-licenses',
    'olympus-apd',
    'olympus-gangs'
}

-- Shared scripts
shared_scripts {
    'config/shared.lua'
}

-- Client scripts
client_scripts {
    'client/main.lua'
}

-- Server scripts
server_scripts {
    'server/main.lua'
}

-- Client exports
exports {
    'IsPlayerVigilante',
    'GetVigilanteRank',
    'GetVigilanteArrests',
    'GetStoredArrests',
    'CanArrestPlayer',
    'ArrestPlayer',
    'GetVigilanteLoadout',
    'GiveVigilanteLoadout',
    'SendBuddyRequest',
    'EndBuddyAgreement',
    'StoreArrests',
    'ClaimStoredArrests'
}

-- Server exports
server_exports {
    'GetSetArrests',
    'ProcessBounty',
    'GetVigilanteTier',
    'GetVigilanteStats',
    'IsPlayerVigilante',
    'SendBuddyRequest',
    'StoreArrests',
    'ClaimStoredArrests'
}
