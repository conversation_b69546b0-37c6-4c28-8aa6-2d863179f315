-- Olympus Login Rewards - Server Main

CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end
    
    print("[Olympus Login Rewards] Server initialized")
end)

exports('ProcessDailyLogin', function(source)
    print("[Olympus Login Rewards] Processing daily login")
end)

exports('GetPlayerRewards', function(source)
    return {}
end)

exports('CheckMilestone', function(source)
    return false
end)

print("[Olympus Login Rewards] Server module loaded")
