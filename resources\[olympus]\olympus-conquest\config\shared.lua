Config = {}

-- Conquest zones based on original Olympus locations
Config.Zones = {
    {
        name = "Ghost Hotel",
        capturePoints = {
            {21980.107, 21035.193, 0},
            {23620.52, 21078.857, 0},
            {22422.44, 20011.785, 0}
        },
        polygon = {
            {23050.629, 21847.943, 0},
            {24186.055, 21492.576, 0},
            {23838.908, 20403.652, 0},
            {22453.787, 19368.78, 0},
            {21684.2, 19945.623, 0},
            {21397.473, 21163.564, 0}
        },
        lockedChopShop = "chop_shop_2",
        spawnMarker = "conq_spawn_0"
    },
    {
        name = "<PERSON><PERSON>",
        capturePoints = {
            {18366.957, 15525.622, 0},
            {19409.207, 15389.239, 0},
            {18551.406, 14661.3, 0}
        },
        polygon = {
            {19545.887, 16041.155, 0},
            {20156.535, 15469.738, 0},
            {20029.672, 14402.67, 0},
            {18232.605, 14422.520, 0},
            {17650.369, 15792.173, 0},
            {18446.352, 16273.845, 0}
        },
        lockedChopShop = "chop_shop_5",
        spawnMarker = "conq_spawn_1"
    },
    {
        name = "Kavala",
        capturePoints = {
            {6460.088, 13773.676, 0},
            {7701.869, 14354.458, 0},
            {7524.256, 12829.32, 0}
        },
        polygon = {
            {7273.354, 15584.284, 0},
            {8528.051, 14909.702, 0},
            {8552.593, 13074.955, 0},
            {7350.592, 12173.107, 0},
            {5680.194, 13794.09, 0}
        },
        lockedChopShop = "chop_shop_3",
        spawnMarker = "conq_spawn_2"
    },
    {
        name = "Syrta",
        capturePoints = {
            {8386.573, 18246.102, 0},
            {10329.974, 17991.703, 0},
            {9210.08, 19282.574, 0}
        },
        polygon = {
            {10872.613, 19782.01, 0},
            {11536.093, 18478.842, 0},
            {10373.243, 17009.912, 0},
            {8504.066, 17098.674, 0},
            {7587.297, 18417.629, 0},
            {8381.020, 19973.844, 0}
        },
        lockedChopShop = "chop_shop_1",
        spawnMarker = "conq_spawn_3"
    },
    {
        name = "Oreokastro",
        capturePoints = {
            {3778.348, 21496.563, 0},
            {4887.847, 21929.684, 0},
            {3536.734, 19982.57, 0}
        },
        polygon = {
            {5654.07, 22528.244, 0},
            {6298.479, 21317.852, 0},
            {5289.289, 20124.133, 0},
            {3398.640, 19368.648, 0},
            {2699.221, 20390.408, 0},
            {3296.478, 22198.137, 0}
        },
        lockedChopShop = "chop_shop_3",
        spawnMarker = "conq_spawn_4"
    },
    {
        name = "Warzone",
        capturePoints = {
            {9777.72, 9382, 0},
            {12099, 10482, 0},
            {11207, 8701, 0},
            {8926, 7479, 0},
            {11555, 7040.68, 0}
        },
        polygon = {
            {9947, 10882, 0},
            {12525, 10880, 0},
            {14480, 6747, 0},
            {13911, 5935, 0},
            {8534, 5940, 0},
            {7864, 6698, 0},
            {7875, 7210, 0}
        },
        lockedChopShop = "chop_shop_1",
        spawnMarkers = {"conq_spawn_5_1", "conq_spawn_5_2"},
        maxPoints = 5000 -- Special zone with higher point requirement
    },
    {
        name = "Panagia",
        capturePoints = {
            {20228, 8928, 0},
            {22715, 6917, 0},
            {20059, 6739, 0}
        },
        polygon = {
            {23762.1, 5670.82, 0},
            {19604.5, 5718.92, 0},
            {18581.7, 8493.94, 0},
            {20160.2, 10021.6, 0},
            {23385.7, 9444.38, 0}
        },
        lockedChopShop = "chop_shop_5",
        spawnMarker = "conq_spawn_7"
    },
    {
        name = "Sofia",
        capturePoints = {
            {26001, 22571, 0},
            {25400, 20316, 0},
            {26762, 21200, 0}
        },
        polygon = {
            {25979, 23114, 0},
            {27085, 22363, 0},
            {27200, 20786, 0},
            {25249, 19590, 0},
            {23698, 21270, 0},
            {24559, 22360, 0}
        },
        lockedChopShop = "chop_shop_2",
        spawnMarker = "conq_spawn_8"
    }
}

-- Conquest settings
Config.Settings = {
    defaultMaxPoints = 3000,
    pointsPerTick = 5,
    tickInterval = 5000, -- 5 seconds
    captureRadius = 15,
    maxPlayersPerGang = 12,
    bigGangCooldown = 300, -- 5 minutes
    
    -- Timing
    preparationTime = 600, -- 10 minutes
    warningTimes = {300, 60}, -- 5 minutes, 1 minute
    voteTime = 120, -- 2 minutes
    
    -- Payouts
    basePrizePool = 10000000,
    firstPlaceMultiplier = 1.10,
    secondPlaceMultiplier = 1.05,
    thirdPlaceMultiplier = 1.03,
    
    -- Requirements
    minimumPotForSecondConquest = 15000000,
    requiredWeaponCalibres = {
        "WEAPON_CARBINERIFLE",
        "WEAPON_ASSAULTRIFLE",
        "WEAPON_SPECIALCARBINE",
        "WEAPON_BULLPUPRIFLE",
        "WEAPON_ADVANCEDRIFLE",
        "WEAPON_COMPACTRIFLE"
    }
}

-- Point names for capture points
Config.PointNames = {"Alpha", "Bravo", "Charlie", "Delta", "Echo"}

-- Vote zone names
Config.VoteZones = {
    "Ghost Hotel",
    "Nifi", 
    "Kavala",
    "Syrta",
    "Oreokastro",
    "Warzone",
    "Panagia",
    "Sofia"
}
