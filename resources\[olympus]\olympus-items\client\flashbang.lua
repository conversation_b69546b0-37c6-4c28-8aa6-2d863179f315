-- Olympus Items - Flashbang Client
-- Based on original fn_flashbang.sqf from Olympus Altis Life

local isThrowingFlashbang = false
local flashbangEffectActive = false
local ppEffects = {}

-- Use flashbang function
function UseFlashbang(target)
    if isThrowingFlashbang then
        exports['olympus-items']:ShowNotification("You are already throwing a flashbang!", "error")
        return false
    end
    
    local ped = PlayerPedId()
    
    -- Check if player is in a vehicle
    if IsPedInAnyVehicle(ped, false) then
        exports['olympus-items']:ShowNotification("You cannot use flashbangs in a vehicle!", "error")
        return false
    end
    
    -- Get throw position
    local playerPos = GetEntityCoords(ped)
    local forward = GetEntityForwardVector(ped)
    local throwPos = playerPos + forward * 10.0 + vector3(0, 0, -1.0)
    
    -- Start throwing process
    return StartThrowingFlashbang(throwPos)
end

-- Start throwing flashbang
function StartThrowingFlashbang(throwPos)
    isThrowingFlashbang = true
    
    local ped = PlayerPedId()
    
    -- Play throwing animation
    local animDict = "weapons@first_person@aim_rng@generic@projectile@thermal_charge@"
    local animName = "throw_m_fb_stand"
    
    RequestAnimDict(animDict)
    while not HasAnimDictLoaded(animDict) do
        Wait(10)
    end
    
    TaskPlayAnim(ped, animDict, animName, 8.0, -8.0, 1500, 0, 0, false, false, false)
    
    -- Wait for throw animation
    Wait(800)
    
    -- Create flashbang projectile
    CreateFlashbangProjectile(throwPos)
    
    isThrowingFlashbang = false
    
    return true
end

-- Create flashbang projectile
function CreateFlashbangProjectile(impactPos)
    local ped = PlayerPedId()
    local playerPos = GetEntityCoords(ped)
    
    -- Create a simple projectile effect
    local projectile = CreateObject(GetHashKey("prop_ld_bomb_01"), playerPos.x, playerPos.y, playerPos.z + 1.0, true, true, true)
    
    -- Calculate trajectory
    local direction = impactPos - playerPos
    direction = direction / #direction -- Normalize
    
    -- Apply physics to projectile
    SetEntityVelocity(projectile, direction.x * 15.0, direction.y * 15.0, direction.z * 8.0)
    SetEntityRotation(projectile, math.random(0, 360), math.random(0, 360), math.random(0, 360), 2, true)
    
    -- Start projectile tracking
    CreateThread(function()
        local startTime = GetGameTimer()
        local maxFlightTime = 5000 -- 5 seconds max flight time
        
        while DoesEntityExist(projectile) do
            local currentTime = GetGameTimer()
            local projectilePos = GetEntityCoords(projectile)
            local velocity = GetEntityVelocity(projectile)
            
            -- Check if projectile has landed (low velocity or hit ground)
            if #velocity < 2.0 or projectilePos.z <= GetGroundZFor_3dCoord(projectilePos.x, projectilePos.y, projectilePos.z, false) + 0.5 then
                -- Projectile has landed
                DetonateFlashbang(projectilePos)
                DeleteEntity(projectile)
                break
            end
            
            -- Check max flight time
            if currentTime - startTime > maxFlightTime then
                DetonateFlashbang(projectilePos)
                DeleteEntity(projectile)
                break
            end
            
            Wait(50)
        end
    end)
end

-- Detonate flashbang
function DetonateFlashbang(position)
    -- Create explosion effect (small, no damage)
    AddExplosion(position.x, position.y, position.z, 1, 0.1, false, true, 0.0)
    
    -- Create light effect
    CreateFlashbangLight(position)
    
    -- Play sound effect
    PlaySoundFromCoord(-1, "GRENADE_EXPLOSION_WATER", position.x, position.y, position.z, "FAMILY_5_SOUNDS", false, 50.0, false)
    
    -- Apply effects to nearby players
    ApplyFlashbangEffectsToNearbyPlayers(position)
    
    -- Notify server
    TriggerServerEvent('olympus-items:server:flashbangDetonated', position)
end

-- Create flashbang light effect
function CreateFlashbangLight(position)
    -- Create bright light
    local light = CreateObject(GetHashKey("prop_spot_01"), position.x, position.y, position.z, false, false, false)
    SetEntityAlpha(light, 0, false)
    
    -- Light effect duration
    CreateThread(function()
        local startTime = GetGameTimer()
        local duration = 2000 -- 2 seconds
        
        while GetGameTimer() - startTime < duration do
            local alpha = math.floor(255 * (1.0 - (GetGameTimer() - startTime) / duration))
            SetEntityAlpha(light, alpha, false)
            Wait(50)
        end
        
        DeleteEntity(light)
    end)
end

-- Apply flashbang effects to nearby players
function ApplyFlashbangEffectsToNearbyPlayers(position)
    local players = GetActivePlayers()
    local effectRadius = Config.Items.flashbang.effectRadius
    
    for _, player in ipairs(players) do
        local playerPed = GetPlayerPed(player)
        local playerPos = GetEntityCoords(playerPed)
        local distance = #(position - playerPos)
        
        if distance <= effectRadius then
            -- Check line of sight
            if HasLineOfSight(position, playerPos, playerPed) then
                -- Apply effect based on distance and angle
                local effectStrength = CalculateFlashbangEffect(position, playerPos, playerPed)
                
                if player == PlayerId() then
                    -- Apply effect to local player
                    ApplyFlashbangEffect(effectStrength)
                else
                    -- Notify other player
                    TriggerServerEvent('olympus-items:server:applyFlashbangEffect', GetPlayerServerId(player), effectStrength)
                end
            end
        end
    end
end

-- Check line of sight
function HasLineOfSight(fromPos, toPos, ped)
    local rayHandle = StartExpensiveSynchronousShapeTestLosProbe(
        fromPos.x, fromPos.y, fromPos.z,
        toPos.x, toPos.y, toPos.z,
        1, ped, 0
    )
    
    local _, hit, _, _, _ = GetShapeTestResult(rayHandle)
    return not hit
end

-- Calculate flashbang effect strength
function CalculateFlashbangEffect(flashPos, playerPos, playerPed)
    local distance = #(flashPos - playerPos)
    local maxDistance = Config.Items.flashbang.effectRadius
    
    -- Distance factor (closer = stronger effect)
    local distanceFactor = 1.0 - (distance / maxDistance)
    
    -- Angle factor (looking at flashbang = stronger effect)
    local playerHeading = GetEntityHeading(playerPed)
    local angleToFlash = math.atan2(flashPos.y - playerPos.y, flashPos.x - playerPos.x) * 180 / math.pi
    local angleDiff = math.abs(playerHeading - angleToFlash)
    if angleDiff > 180 then angleDiff = 360 - angleDiff end
    
    local angleFactor = 1.0 - (angleDiff / 180.0)
    
    -- Combined effect strength
    local effectStrength = (distanceFactor * 0.7) + (angleFactor * 0.3)
    
    return math.max(0.0, math.min(1.0, effectStrength))
end

-- Apply flashbang effect to local player
function ApplyFlashbangEffect(strength)
    if flashbangEffectActive then return end
    if strength < 0.1 then return end
    
    flashbangEffectActive = true
    
    local duration = math.floor(Config.Items.flashbang.duration * strength * 1000)
    
    -- Create post-processing effects
    CreateFlashbangPostProcessing(strength, duration)
    
    -- Disable player controls temporarily
    DisablePlayerControls(duration)
    
    -- Play ringing sound
    PlayRingingSound(duration)
    
    exports['olympus-items']:ShowNotification("You have been flashbanged!", "warning")
end

-- Create post-processing effects
function CreateFlashbangPostProcessing(strength, duration)
    -- White screen effect
    local whiteEffect = CreatePostProcessingEffect("ColorCorrections", 2500)
    SetPostProcessingEffectAdjust(whiteEffect, 1.0, 1.0, -0.01, 
        {1.0, 1.0, 1.0, 1.0}, 
        {1.0 * strength, 1.0 * strength, 1.0 * strength, 1.0}, 
        {1.0, 1.0, 1.0, 1.0}
    )
    EnablePostProcessingEffect(whiteEffect, true)
    table.insert(ppEffects, whiteEffect)
    
    -- Blur effect
    local blurEffect = CreatePostProcessingEffect("MotionBlur", 2501)
    SetPostProcessingEffectAdjust(blurEffect, 0.5 * strength, 0.5 * strength, 0.0, 0.0)
    EnablePostProcessingEffect(blurEffect, true)
    table.insert(ppEffects, blurEffect)
    
    -- Gradually fade effects
    CreateThread(function()
        local startTime = GetGameTimer()
        
        while GetGameTimer() - startTime < duration do
            local progress = (GetGameTimer() - startTime) / duration
            local fadeStrength = strength * (1.0 - progress)
            
            -- Update white effect
            SetPostProcessingEffectAdjust(whiteEffect, 1.0, 1.0, -0.01,
                {1.0, 1.0, 1.0, 1.0},
                {fadeStrength, fadeStrength, fadeStrength, 1.0},
                {1.0, 1.0, 1.0, 1.0}
            )
            
            -- Update blur effect
            SetPostProcessingEffectAdjust(blurEffect, 0.5 * fadeStrength, 0.5 * fadeStrength, 0.0, 0.0)
            
            Wait(50)
        end
        
        -- Remove effects
        for _, effect in ipairs(ppEffects) do
            EnablePostProcessingEffect(effect, false)
            DestroyPostProcessingEffect(effect)
        end
        ppEffects = {}
        
        flashbangEffectActive = false
    end)
end

-- Disable player controls
function DisablePlayerControls(duration)
    CreateThread(function()
        local startTime = GetGameTimer()
        
        while GetGameTimer() - startTime < duration do
            DisableControlAction(0, 1, true)   -- LookLeftRight
            DisableControlAction(0, 2, true)   -- LookUpDown
            DisableControlAction(0, 24, true)  -- Attack
            DisableControlAction(0, 25, true)  -- Aim
            DisableControlAction(0, 44, true)  -- Cover
            DisableControlAction(0, 37, true)  -- SelectWeapon
            
            Wait(0)
        end
    end)
end

-- Play ringing sound
function PlayRingingSound(duration)
    CreateThread(function()
        local startTime = GetGameTimer()
        
        while GetGameTimer() - startTime < duration do
            PlaySoundFrontend(-1, "Beep_Red", "DLC_HEIST_HACKING_SNAKE_SOUNDS", true)
            Wait(1000)
        end
    end)
end

-- Create post-processing effect (simplified)
function CreatePostProcessingEffect(effectType, id)
    -- This is a simplified version - actual implementation would use natives
    return id
end

-- Set post-processing effect adjust (simplified)
function SetPostProcessingEffectAdjust(effect, ...)
    -- Simplified - actual implementation would use natives
end

-- Enable post-processing effect (simplified)
function EnablePostProcessingEffect(effect, enable)
    -- Simplified - actual implementation would use natives
end

-- Destroy post-processing effect (simplified)
function DestroyPostProcessingEffect(effect)
    -- Simplified - actual implementation would use natives
end

-- Handle flashbang effect from server
function HandleFlashbangEffect(position)
    local ped = PlayerPedId()
    local playerPos = GetEntityCoords(ped)
    local distance = #(position - playerPos)
    
    if distance <= Config.Items.flashbang.effectRadius then
        if HasLineOfSight(position, playerPos, ped) then
            local effectStrength = CalculateFlashbangEffect(position, playerPos, ped)
            ApplyFlashbangEffect(effectStrength)
        end
    end
end

-- Export functions
exports('UseFlashbang', UseFlashbang)
exports('HandleFlashbangEffect', HandleFlashbangEffect)

-- Event handlers
RegisterNetEvent('olympus-items:client:flashbangEffect', function(position)
    HandleFlashbangEffect(position)
end)

RegisterNetEvent('olympus-items:client:applyFlashbangEffect', function(strength)
    ApplyFlashbangEffect(strength)
end)
