-- Olympus Mini-Games - Client Main

CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end
    
    print("[Olympus Mini-Games] Client initialized")
end)

exports('StartLockpicking', function()
    print("[Olympus Mini-Games] Starting lockpicking")
end)

exports('StartHacking', function()
    print("[Olympus Mini-Games] Starting hacking")
end)

exports('StartSafeCracking', function()
    print("[Olympus Mini-Games] Starting safe cracking")
end)

exports('StartCrafting', function()
    print("[Olympus Mini-Games] Starting crafting")
end)

print("[Olympus Mini-Games] Client module loaded")
