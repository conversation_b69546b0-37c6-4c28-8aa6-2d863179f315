-- Olympus Shipwreck & Treasure System - Server Main
-- Based on original fn_searchShipWreck.sqf

local OlympusShipwreck = {}
local activeSearches = {}

-- Initialize system
CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end
    
    print("[Olympus Shipwreck] Server initialized")
    
    -- Initialize database tables
    OlympusShipwreck.InitializeDatabase()
end)

-- Database initialization
function OlympusShipwreck.InitializeDatabase()
    local success = pcall(function()
        -- Create shipwreck_logs table
        exports.oxmysql:execute([[
            CREATE TABLE IF NOT EXISTS shipwreck_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                player_id VARCHAR(50) NOT NULL,
                player_name VARCHAR(100) NOT NULL,
                zone_name VARCHAR(255) NOT NULL,
                item_found VARCHAR(50) NOT NULL,
                item_value INT DEFAULT 0,
                depth FLOAT DEFAULT 0,
                search_duration INT DEFAULT 0,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_player_id (player_id),
                INDEX idx_zone_name (zone_name),
                INDEX idx_timestamp (timestamp)
            )
        ]])
        
        -- Create shipwreck_stats table
        exports.oxmysql:execute([[
            CREATE TABLE IF NOT EXISTS shipwreck_stats (
                id INT AUTO_INCREMENT PRIMARY KEY,
                player_id VARCHAR(50) NOT NULL UNIQUE,
                total_searches INT DEFAULT 0,
                total_items_found INT DEFAULT 0,
                total_value_found INT DEFAULT 0,
                best_find VARCHAR(50),
                deepest_search FLOAT DEFAULT 0,
                last_search TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_player_id (player_id)
            )
        ]])
        
        print("[Olympus Shipwreck] Database tables initialized")
    end)
    
    if not success then
        print("[Olympus Shipwreck] ERROR: Failed to initialize database tables")
    end
end

-- Utility Functions
function OlympusShipwreck.GetPlayerData(source)
    local success, result = pcall(function()
        return exports['olympus-core']:GetPlayerData(source)
    end)
    return success and result or nil
end

function OlympusShipwreck.GetPlayerInventoryWeight(source)
    local Player = OlympusShipwreck.GetPlayerData(source)
    if not Player then return 0 end
    
    -- Calculate current inventory weight
    local weight = 0
    if Player.inventory then
        for _, item in pairs(Player.inventory) do
            if item and item.amount then
                weight = weight + (item.amount * (item.weight or 1))
            end
        end
    end
    
    return weight
end

function OlympusShipwreck.AddPlayerItem(source, item, amount)
    local success = pcall(function()
        exports['olympus-core']:AddItem(source, item, amount)
    end)
    return success
end

function OlympusShipwreck.GetPlayerMoney(source, type)
    local Player = OlympusShipwreck.GetPlayerData(source)
    if not Player then return 0 end
    
    if type == "cash" then
        return Player.money and Player.money.cash or 0
    elseif type == "bank" then
        return Player.money and Player.money.bank or 0
    end
    return 0
end

function OlympusShipwreck.AddPlayerMoney(source, type, amount)
    local success = pcall(function()
        exports['olympus-core']:AddMoney(source, type, amount)
    end)
    return success
end

function OlympusShipwreck.GetPlayerJob(source)
    local Player = OlympusShipwreck.GetPlayerData(source)
    if not Player then return nil end
    
    return Player.job and Player.job.name or "unemployed"
end

function OlympusShipwreck.IsPlayerInZone(source, zoneCoords, radius)
    local playerPed = GetPlayerPed(source)
    local coords = GetEntityCoords(playerPed)
    local distance = #(coords - zoneCoords)
    
    return distance <= radius
end

function OlympusShipwreck.GetPlayerDepth(source)
    local playerPed = GetPlayerPed(source)
    local coords = GetEntityCoords(playerPed)
    
    return coords.z
end

function OlympusShipwreck.IsNearShipwreck(source, maxDistance)
    local playerPed = GetPlayerPed(source)
    local coords = GetEntityCoords(playerPed)
    
    -- Check for nearby shipwreck objects
    for _, objectModel in pairs(Config.Shipwreck.shipwreckObjects) do
        local objectHash = GetHashKey(objectModel)
        local nearbyObjects = GetGamePool('CObject')
        
        for _, object in pairs(nearbyObjects) do
            if GetEntityModel(object) == objectHash then
                local objectCoords = GetEntityCoords(object)
                local distance = #(coords - objectCoords)
                
                if distance <= maxDistance then
                    return true
                end
            end
        end
    end
    
    return false
end

function OlympusShipwreck.SelectRandomLoot()
    -- Calculate total weight
    local totalWeight = 0
    for _, loot in pairs(Config.Shipwreck.lootTable) do
        totalWeight = totalWeight + loot.weight
    end
    
    -- Random selection based on weights
    local random = math.random() * totalWeight
    local currentWeight = 0
    
    for _, loot in pairs(Config.Shipwreck.lootTable) do
        currentWeight = currentWeight + loot.weight
        if random <= currentWeight then
            return loot
        end
    end
    
    -- Fallback to first item
    return Config.Shipwreck.lootTable[1]
end

function OlympusShipwreck.LogSearch(playerId, playerName, zoneName, itemFound, itemValue, depth, duration)
    exports.oxmysql:execute('INSERT INTO shipwreck_logs (player_id, player_name, zone_name, item_found, item_value, depth, search_duration) VALUES (?, ?, ?, ?, ?, ?, ?)', {
        playerId, playerName, zoneName, itemFound, itemValue, depth, duration
    })
end

function OlympusShipwreck.UpdatePlayerStats(playerId, itemFound, itemValue, depth)
    exports.oxmysql:query('SELECT * FROM shipwreck_stats WHERE player_id = ?', {playerId}, function(result)
        if result and #result > 0 then
            -- Update existing stats
            local stats = result[1]
            local newTotalSearches = stats.total_searches + 1
            local newTotalItems = stats.total_items_found + 1
            local newTotalValue = stats.total_value_found + itemValue
            local newBestFind = stats.best_find
            local newDeepestSearch = math.min(stats.deepest_search, depth)
            
            -- Check if this is the best find
            for _, loot in pairs(Config.Shipwreck.lootTable) do
                if loot.item == itemFound and loot.sellPrice > (stats.best_find_value or 0) then
                    newBestFind = itemFound
                end
            end
            
            exports.oxmysql:execute('UPDATE shipwreck_stats SET total_searches = ?, total_items_found = ?, total_value_found = ?, best_find = ?, deepest_search = ? WHERE player_id = ?', {
                newTotalSearches, newTotalItems, newTotalValue, newBestFind, newDeepestSearch, playerId
            })
        else
            -- Create new stats
            exports.oxmysql:execute('INSERT INTO shipwreck_stats (player_id, total_searches, total_items_found, total_value_found, best_find, deepest_search) VALUES (?, ?, ?, ?, ?, ?)', {
                playerId, 1, 1, itemValue, itemFound, depth
            })
        end
    end)
end

-- Treasure Hunting System (based on fn_searchShipWreck.sqf)
function OlympusShipwreck.StartTreasureHunt(source)
    local Player = OlympusShipwreck.GetPlayerData(source)
    if not Player then return false end
    
    local playerPed = GetPlayerPed(source)
    local coords = GetEntityCoords(playerPed)
    local depth = coords.z
    
    -- Check if player is already searching
    if activeSearches[source] then
        TriggerClientEvent('olympus-shipwreck:client:notify', source, 'You are already searching!', 'error')
        return false
    end
    
    -- Check if player is civilian (matching original restriction)
    local playerJob = OlympusShipwreck.GetPlayerJob(source)
    if playerJob == "police" or playerJob == "ambulance" then
        TriggerClientEvent('olympus-shipwreck:client:notify', source, 'Only civilians can search for treasure!', 'error')
        return false
    end
    
    -- Check if player is in a shipwreck zone
    local inZone = false
    local currentZone = nil
    for _, zone in pairs(Config.Shipwreck.zones) do
        if OlympusShipwreck.IsPlayerInZone(source, zone.coords, zone.radius) then
            inZone = true
            currentZone = zone
            break
        end
    end
    
    if not inZone then
        TriggerClientEvent('olympus-shipwreck:client:notify', source, 'You need to be near a purple ship wreck marker at sea to excavate.', 'error')
        return false
    end
    
    -- Check if near actual shipwreck object (matching original 20m requirement)
    if not OlympusShipwreck.IsNearShipwreck(source, Config.Shipwreck.settings.maxShipwreckDistance) then
        TriggerClientEvent('olympus-shipwreck:client:notify', source, 'There are no shipwrecks near you. You need to be within 20m of a wreck.', 'error')
        return false
    end
    
    -- Check depth requirement (matching original -12m requirement)
    if depth > Config.Shipwreck.settings.minDepth then
        TriggerClientEvent('olympus-shipwreck:client:notify', source, 'You need to be in deeper waters.', 'error')
        return false
    end
    
    -- Check if player is alive
    if GetEntityHealth(playerPed) <= 0 then
        TriggerClientEvent('olympus-shipwreck:client:notify', source, 'You cannot search while dead!', 'error')
        return false
    end
    
    -- Check if player is in vehicle
    local vehicle = GetVehiclePedIsIn(playerPed, false)
    if vehicle ~= 0 then
        TriggerClientEvent('olympus-shipwreck:client:notify', source, 'You need to exit your vehicle to search!', 'error')
        return false
    end
    
    -- Check inventory weight
    local currentWeight = OlympusShipwreck.GetPlayerInventoryWeight(source)
    if currentWeight >= Config.Shipwreck.settings.maxInventoryWeight then
        TriggerClientEvent('olympus-shipwreck:client:notify', source, 'Your inventory is full.', 'error')
        return false
    end
    
    -- Start treasure hunting process
    activeSearches[source] = {
        startTime = os.time(),
        zone = currentZone,
        depth = depth,
        searchCount = 0
    }
    
    TriggerClientEvent('olympus-shipwreck:client:startTreasureHunt', source, currentZone.name)
    
    return true
end

function OlympusShipwreck.ProcessTreasureFind(source)
    local search = activeSearches[source]
    if not search then return false end

    local Player = OlympusShipwreck.GetPlayerData(source)
    if not Player then return false end

    -- Check if still in valid conditions
    local playerPed = GetPlayerPed(source)
    local coords = GetEntityCoords(playerPed)
    local depth = coords.z

    -- Validate conditions again
    if not OlympusShipwreck.IsPlayerInZone(source, search.zone.coords, search.zone.radius) then
        OlympusShipwreck.StopTreasureHunt(source, 'You moved too far from the shipwreck zone!')
        return false
    end

    if not OlympusShipwreck.IsNearShipwreck(source, Config.Shipwreck.settings.maxShipwreckDistance) then
        OlympusShipwreck.StopTreasureHunt(source, 'You moved too far from the shipwreck!')
        return false
    end

    if depth > Config.Shipwreck.settings.minDepth then
        OlympusShipwreck.StopTreasureHunt(source, 'You need to stay in deeper waters!')
        return false
    end

    if GetEntityHealth(playerPed) <= 0 then
        OlympusShipwreck.StopTreasureHunt(source, 'You cannot search while dead!')
        return false
    end

    local vehicle = GetVehiclePedIsIn(playerPed, false)
    if vehicle ~= 0 then
        OlympusShipwreck.StopTreasureHunt(source, 'You cannot search from a vehicle!')
        return false
    end

    -- Check inventory weight
    local currentWeight = OlympusShipwreck.GetPlayerInventoryWeight(source)
    if currentWeight >= Config.Shipwreck.settings.maxInventoryWeight then
        OlympusShipwreck.StopTreasureHunt(source, 'Your inventory is full.')
        return false
    end

    -- Select random loot based on weighted table
    local foundLoot = OlympusShipwreck.SelectRandomLoot()

    -- Add item to player inventory
    if OlympusShipwreck.AddPlayerItem(source, foundLoot.item, 1) then
        search.searchCount = search.searchCount + 1

        -- Notify player
        TriggerClientEvent('olympus-shipwreck:client:notify', source,
            string.format('You recovered 1 %s.', foundLoot.name), 'success')

        -- Log the find
        local duration = os.time() - search.startTime
        OlympusShipwreck.LogSearch(
            GetPlayerIdentifier(source, 0),
            GetPlayerName(source),
            search.zone.name,
            foundLoot.item,
            foundLoot.sellPrice,
            depth,
            duration
        )

        -- Update player stats
        OlympusShipwreck.UpdatePlayerStats(
            GetPlayerIdentifier(source, 0),
            foundLoot.item,
            foundLoot.sellPrice,
            depth
        )

        return true
    else
        TriggerClientEvent('olympus-shipwreck:client:notify', source, 'Failed to add item to inventory!', 'error')
        return false
    end
end

function OlympusShipwreck.StopTreasureHunt(source, reason)
    if not activeSearches[source] then return end

    if reason then
        TriggerClientEvent('olympus-shipwreck:client:notify', source, reason, 'error')
    end

    TriggerClientEvent('olympus-shipwreck:client:stopTreasureHunt', source)
    activeSearches[source] = nil
end

function OlympusShipwreck.SellTreasure(source, itemType, quantity, location)
    local Player = OlympusShipwreck.GetPlayerData(source)
    if not Player then return false end

    -- Find the item in loot table
    local itemData = nil
    for _, loot in pairs(Config.Shipwreck.lootTable) do
        if loot.item == itemType then
            itemData = loot
            break
        end
    end

    if not itemData then
        TriggerClientEvent('olympus-shipwreck:client:notify', source, 'Invalid item type!', 'error')
        return false
    end

    -- Find processing location
    local locationData = nil
    for _, loc in pairs(Config.Shipwreck.processingLocations) do
        if loc.name == location then
            locationData = loc
            break
        end
    end

    if not locationData then
        TriggerClientEvent('olympus-shipwreck:client:notify', source, 'Invalid location!', 'error')
        return false
    end

    -- Check if player has the item
    local hasItem = pcall(function()
        return exports['olympus-core']:HasItem(source, itemType, quantity)
    end)

    if not hasItem then
        TriggerClientEvent('olympus-shipwreck:client:notify', source, 'You don\'t have enough of this item!', 'error')
        return false
    end

    -- Calculate sell price
    local sellPrice = math.floor(itemData.sellPrice * locationData.sellMultiplier * quantity)

    -- Remove item and add money
    local success = pcall(function()
        exports['olympus-core']:RemoveItem(source, itemType, quantity)
        exports['olympus-core']:AddMoney(source, "cash", sellPrice)
    end)

    if success then
        TriggerClientEvent('olympus-shipwreck:client:notify', source,
            string.format('Sold %dx %s for $%s', quantity, itemData.name, sellPrice), 'success')

        -- Log the sale
        OlympusShipwreck.LogSearch(
            GetPlayerIdentifier(source, 0),
            GetPlayerName(source),
            location,
            itemType .. "_sale",
            sellPrice,
            0,
            0
        )

        return true
    else
        TriggerClientEvent('olympus-shipwreck:client:notify', source, 'Failed to complete sale!', 'error')
        return false
    end
end

-- Event Handlers
RegisterNetEvent('olympus-shipwreck:server:startTreasureHunt', function()
    local source = source
    OlympusShipwreck.StartTreasureHunt(source)
end)

RegisterNetEvent('olympus-shipwreck:server:processTreasureFind', function()
    local source = source
    OlympusShipwreck.ProcessTreasureFind(source)
end)

RegisterNetEvent('olympus-shipwreck:server:stopTreasureHunt', function(reason)
    local source = source
    OlympusShipwreck.StopTreasureHunt(source, reason)
end)

RegisterNetEvent('olympus-shipwreck:server:sellTreasure', function(itemType, quantity, location)
    local source = source
    OlympusShipwreck.SellTreasure(source, itemType, quantity, location)
end)

RegisterNetEvent('olympus-shipwreck:server:requestPlayerStats', function()
    local source = source
    local playerId = GetPlayerIdentifier(source, 0)

    exports.oxmysql:query('SELECT * FROM shipwreck_stats WHERE player_id = ?', {playerId}, function(result)
        if result and #result > 0 then
            TriggerClientEvent('olympus-shipwreck:client:receivePlayerStats', source, result[1])
        else
            TriggerClientEvent('olympus-shipwreck:client:receivePlayerStats', source, {
                total_searches = 0,
                total_items_found = 0,
                total_value_found = 0,
                best_find = "None",
                deepest_search = 0
            })
        end
    end)
end)

-- Player disconnect cleanup
AddEventHandler('playerDropped', function(reason)
    local source = source
    if activeSearches[source] then
        activeSearches[source] = nil
    end
end)

-- Export Functions
exports('ProcessTreasureFind', function(playerId)
    return OlympusShipwreck.ProcessTreasureFind(playerId)
end)

exports('GetShipwreckData', function(playerId)
    return activeSearches[playerId]
end)

exports('ResetShipwreck', function(playerId)
    if activeSearches[playerId] then
        activeSearches[playerId] = nil
    end
    return true
end)

print("[Olympus Shipwreck] Server module loaded")
