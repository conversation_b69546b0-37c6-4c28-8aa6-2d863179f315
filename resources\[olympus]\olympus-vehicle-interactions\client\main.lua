-- ========================================
-- OLYMPUS VEHICLE INTERACTIONS CLIENT
-- Based on Original Olympus Functions
-- ========================================

local OlympusVehicleInteractions = {}

-- ========================================
-- UTILITY FUNCTIONS
-- ========================================
function OlympusVehicleInteractions.Notify(message, type)
    -- Use your notification system here
    if exports['olympus-ui'] and exports['olympus-ui'].ShowNotification then
        exports['olympus-ui']:ShowNotification(message, type)
    else
        print(message)
    end
end

function OlympusVehicleInteractions.GetClosestVehicle()
    local playerPed = PlayerPedId()
    local pos = GetEntityCoords(playerPed)
    local vehicle = GetClosestVehicle(pos.x, pos.y, pos.z, 10.0, 0, 71)
    
    if vehicle and vehicle ~= 0 then
        local vehiclePos = GetEntityCoords(vehicle)
        local distance = #(pos - vehiclePos)
        return vehicle, distance
    end
    
    return nil, -1
end

function OlympusVehicleInteractions.LoadAnimDict(dict)
    if not HasAnimDictLoaded(dict) then
        RequestAnimDict(dict)
        while not HasAnimDictLoaded(dict) do
            Wait(1)
        end
    end
end

function OlympusVehicleInteractions.PlayAnimation(dict, anim, flag, duration)
    OlympusVehicleInteractions.LoadAnimDict(dict)
    TaskPlayAnim(PlayerPedId(), dict, anim, 8.0, -8.0, duration or -1, flag or 0, 0, false, false, false)
end

function OlympusVehicleInteractions.ShowProgressBar(text, duration)
    if exports['olympus-ui'] and exports['olympus-ui'].ShowProgressBar then
        exports['olympus-ui']:ShowProgressBar(text, duration)
    end
end

-- ========================================
-- VEHICLE FLIPPING SYSTEM
-- Based on fn_flipAction.sqf
-- ========================================
function OlympusVehicleInteractions.FlipVehicle()
    local vehicle, distance = OlympusVehicleInteractions.GetClosestVehicle()
    
    if not vehicle or distance > Config.VehicleInteractions.flipping.maxDistance then
        OlympusVehicleInteractions.Notify(Config.Notifications.flipping.tooFar, 'error')
        return
    end
    
    -- Check if vehicle is a car/truck
    local vehicleClass = GetVehicleClass(vehicle)
    if vehicleClass == 15 or vehicleClass == 16 then -- Aircraft
        OlympusVehicleInteractions.Notify(Config.Notifications.flipping.notCar, 'error')
        return
    end
    
    -- Check if vehicle has occupants
    local occupants = GetVehicleNumberOfPassengers(vehicle)
    if occupants > 0 then
        OlympusVehicleInteractions.Notify(Config.Notifications.flipping.occupied, 'error')
        return
    end
    
    -- Check vehicle damage
    local damage = GetVehicleEngineHealth(vehicle) / 1000.0
    if damage < 0.15 then
        OlympusVehicleInteractions.Notify(Config.Notifications.flipping.tooRekt, 'error')
        return
    end
    
    -- Check if Strider in water
    local vehicleModel = GetEntityModel(vehicle)
    local vehicleName = GetDisplayNameFromVehicleModel(vehicleModel):lower()
    if vehicleName:find('strider') and IsEntityInWater(vehicle) then
        OlympusVehicleInteractions.Notify(Config.Notifications.flipping.striderWater, 'error')
        return
    end
    
    -- Play animation and show progress
    OlympusVehicleInteractions.PlayAnimation(
        Config.Animations.flipping.dict,
        Config.Animations.flipping.anim,
        Config.Animations.flipping.flag,
        Config.VehicleInteractions.flipping.duration
    )
    
    OlympusVehicleInteractions.ShowProgressBar(Config.Notifications.flipping.start, Config.VehicleInteractions.flipping.duration)
    
    Wait(Config.VehicleInteractions.flipping.duration)
    
    -- Trigger server event
    local vehicleNetId = NetworkGetNetworkIdFromEntity(vehicle)
    TriggerServerEvent('olympus-vehicle-interactions:flipVehicle', vehicleNetId)
end

-- ========================================
-- VEHICLE PUSHING SYSTEM
-- Based on fn_pushVehicle.sqf
-- ========================================
function OlympusVehicleInteractions.PushVehicle()
    local vehicle, distance = OlympusVehicleInteractions.GetClosestVehicle()
    
    if not vehicle or distance > Config.VehicleInteractions.pushing.maxDistance then
        OlympusVehicleInteractions.Notify(Config.Notifications.pushing.tooFar, 'error')
        return
    end
    
    -- Play animation and show progress
    OlympusVehicleInteractions.PlayAnimation(
        Config.Animations.pushing.dict,
        Config.Animations.pushing.anim,
        Config.Animations.pushing.flag,
        Config.VehicleInteractions.pushing.duration
    )
    
    OlympusVehicleInteractions.ShowProgressBar(Config.Notifications.pushing.start, Config.VehicleInteractions.pushing.duration)
    
    Wait(Config.VehicleInteractions.pushing.duration)
    
    -- Trigger server event
    local vehicleNetId = NetworkGetNetworkIdFromEntity(vehicle)
    local playerPed = PlayerPedId()
    local direction = GetEntityHeading(playerPed)
    
    TriggerServerEvent('olympus-vehicle-interactions:pushVehicle', vehicleNetId, direction)
end

-- ========================================
-- VEHICLE SEARCHING SYSTEM
-- Based on fn_searchVehAction.sqf
-- ========================================
function OlympusVehicleInteractions.SearchVehicle()
    local vehicle, distance = OlympusVehicleInteractions.GetClosestVehicle()
    
    if not vehicle or distance > Config.VehicleInteractions.searching.maxDistance then
        OlympusVehicleInteractions.Notify(Config.Notifications.searching.tooFar, 'error')
        return
    end
    
    -- Play animation and show progress
    OlympusVehicleInteractions.PlayAnimation(
        Config.Animations.searching.dict,
        Config.Animations.searching.anim,
        Config.Animations.searching.flag,
        Config.VehicleInteractions.searching.duration
    )
    
    OlympusVehicleInteractions.ShowProgressBar(Config.Notifications.searching.start, Config.VehicleInteractions.searching.duration)
    
    Wait(Config.VehicleInteractions.searching.duration)
    
    -- Trigger server event
    local vehicleNetId = NetworkGetNetworkIdFromEntity(vehicle)
    TriggerServerEvent('olympus-vehicle-interactions:searchVehicle', vehicleNetId)
end

-- ========================================
-- VEHICLE IMPOUNDING SYSTEM
-- Based on fn_impoundAction.sqf
-- ========================================
function OlympusVehicleInteractions.ImpoundVehicle()
    local vehicle, distance = OlympusVehicleInteractions.GetClosestVehicle()
    
    if not vehicle or distance > Config.VehicleInteractions.impounding.maxDistance then
        OlympusVehicleInteractions.Notify(Config.Notifications.impounding.tooFar, 'error')
        return
    end
    
    -- Check if vehicle has occupants
    local occupants = GetVehicleNumberOfPassengers(vehicle)
    if occupants > 0 then
        OlympusVehicleInteractions.Notify(Config.Notifications.impounding.occupied, 'error')
        return
    end
    
    -- Check vehicle damage
    local damage = GetVehicleEngineHealth(vehicle) / 1000.0
    if damage < 0.15 then
        OlympusVehicleInteractions.Notify(Config.Notifications.impounding.tooRekt, 'error')
        return
    end
    
    -- Play animation and show progress
    OlympusVehicleInteractions.PlayAnimation(
        Config.Animations.impounding.dict,
        Config.Animations.impounding.anim,
        Config.Animations.impounding.flag,
        Config.VehicleInteractions.impounding.duration
    )
    
    OlympusVehicleInteractions.ShowProgressBar(Config.Notifications.impounding.start, Config.VehicleInteractions.impounding.duration)
    
    Wait(Config.VehicleInteractions.impounding.duration)
    
    -- Trigger server event
    local vehicleNetId = NetworkGetNetworkIdFromEntity(vehicle)
    TriggerServerEvent('olympus-vehicle-interactions:impoundVehicle', vehicleNetId)
end

-- ========================================
-- VEHICLE REPAIR SYSTEM
-- Based on fn_repairTruck.sqf
-- ========================================
function OlympusVehicleInteractions.RepairVehicle()
    local vehicle, distance = OlympusVehicleInteractions.GetClosestVehicle()
    
    if not vehicle or distance > Config.VehicleInteractions.repair.maxDistance then
        OlympusVehicleInteractions.Notify(Config.Notifications.repair.tooFar, 'error')
        return
    end
    
    -- Check if player is in vehicle
    local playerPed = PlayerPedId()
    if IsPedInAnyVehicle(playerPed, false) then
        OlympusVehicleInteractions.Notify(Config.Notifications.repair.inVehicle, 'error')
        return
    end
    
    -- Get current damage to calculate repair time
    local currentDamage = 1.0 - (GetVehicleEngineHealth(vehicle) / 1000.0)
    local repairTime = math.max(2000, currentDamage * 10000) -- 2-10 seconds based on damage
    
    -- Play animation and show progress
    OlympusVehicleInteractions.PlayAnimation(
        Config.Animations.repairing.dict,
        Config.Animations.repairing.anim,
        Config.Animations.repairing.flag,
        repairTime
    )
    
    OlympusVehicleInteractions.ShowProgressBar(Config.Notifications.repair.start, repairTime)
    
    Wait(repairTime)
    
    -- Trigger server event
    local vehicleNetId = NetworkGetNetworkIdFromEntity(vehicle)
    TriggerServerEvent('olympus-vehicle-interactions:repairVehicle', vehicleNetId)
end

-- ========================================
-- COMMAND REGISTRATION
-- ========================================
RegisterCommand('flip', function()
    OlympusVehicleInteractions.FlipVehicle()
end, false)

RegisterCommand('push', function()
    OlympusVehicleInteractions.PushVehicle()
end, false)

RegisterCommand('searchveh', function()
    OlympusVehicleInteractions.SearchVehicle()
end, false)

RegisterCommand('impound', function()
    OlympusVehicleInteractions.ImpoundVehicle()
end, false)

RegisterCommand('repair', function()
    OlympusVehicleInteractions.RepairVehicle()
end, false)

-- ========================================
-- EVENT HANDLERS
-- ========================================
RegisterNetEvent('olympus-vehicle-interactions:notify', function(message, type)
    OlympusVehicleInteractions.Notify(message, type)
end)

RegisterNetEvent('olympus-vehicle-interactions:searchResult', function(data)
    local message = string.format("Vehicle: %s\nOwners: %s", data.vehicleName, data.owners)
    OlympusVehicleInteractions.Notify(message, data.isStolen and 'error' or 'info')
end)

-- ========================================
-- EXPORT FUNCTIONS
-- ========================================
exports('FlipVehicle', function()
    return OlympusVehicleInteractions.FlipVehicle()
end)

exports('PushVehicle', function()
    return OlympusVehicleInteractions.PushVehicle()
end)

exports('SearchVehicle', function()
    return OlympusVehicleInteractions.SearchVehicle()
end)

exports('ImpoundVehicle', function()
    return OlympusVehicleInteractions.ImpoundVehicle()
end)

exports('RepairVehicle', function()
    return OlympusVehicleInteractions.RepairVehicle()
end)

exports('CanInteractWithVehicle', function()
    local vehicle, distance = OlympusVehicleInteractions.GetClosestVehicle()
    return vehicle ~= nil and distance <= Config.VehicleInteractions.flipping.maxDistance
end)

-- ========================================
-- INITIALIZATION
-- ========================================
CreateThread(function()
    print("^2[Olympus Vehicle Interactions]^7 Client system initialized")
end)
