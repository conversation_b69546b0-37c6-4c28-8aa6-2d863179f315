fx_version 'cerulean'
game 'gta5'

name 'Olympus Events System'
description 'Federal events and server events for Olympus Framework'
author 'Olympus Development Team'
version '1.0.0'

dependencies {
    'olympus-core',
    'olympus-apd',
    'olympus-ui'
}

shared_scripts {
    'config/shared.lua',
    'config/federal.lua'
}

server_scripts {
    'server/main.lua'
}

client_scripts {
    'client/main.lua'
}

-- Client exports
exports {
    'GetActiveEvents',
    'IsInEventZone',
    'GetCurrentEventZone',
    'IsAntiAirActive'
}

-- Server exports
server_exports {
    'GetActiveEvents',
    'IsEventActive',
    'GetEventCooldowns',
    'CanStartEvent',
    'GetOnlineAPDCount'
}
