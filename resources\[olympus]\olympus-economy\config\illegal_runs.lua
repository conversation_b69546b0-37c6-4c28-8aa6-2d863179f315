-- Olympus Economy System - Complete Illegal Runs Implementation
-- Based on exact Olympus Altis Life drug running system with all mechanics

Config.IllegalRuns = {}

-- Cocaine Run (Exact Olympus Implementation)
Config.IllegalRuns['cocaine'] = {
    name = 'Cocaine',
    displayName = 'Cocaine Run',
    
    -- Exact Olympus Locations
    locations = {
        field = vector3(1093.6, -3195.6, -39.0), -- Northeast of DP11
        processor = vector3(1050.2, -3240.8, -39.0), -- South of cocaine field
        dealer = vector3(980.5, -3180.2, 35.0) -- South of Neochori (closest)
    },
    
    -- Exact Olympus Requirements
    requirements = {
        license = 'cocaine_processing',
        licenseCost = 30000, -- $30,000 at processor
        vehicle = true, -- Vehicle required for storage
        backpack = true, -- Bergen backpack recommended
        items = {'food', 'water'} -- Food and water recommended
    },
    
    -- Exact Olympus Processing Mechanics
    processing = {
        time = 5, -- 5 seconds per item
        yield = 1, -- 1:1 ratio (unprocessed to processed)
        range = 25, -- Must stay within 25m of NPC
        cancellable = true, -- Can cancel and keep progress
        
        -- Double Processing (Crack Cocaine)
        doubleProcess = {
            enabled = true,
            name = 'Crack Cocaine',
            multiplier = 1.25, -- x1.25 current price
            time = 5 -- Additional 5 seconds per item
        }
    },
    
    -- Exact Olympus Pricing (Based on Wiki Data)
    pricing = {
        base = 3325, -- Base price per processed cocaine
        processed = 3325, -- $3,325 per processed cocaine
        doubleProcessed = 4156, -- $4,156 per crack cocaine (25% bonus)
        
        -- Market fluctuation
        fluctuation = {
            enabled = true,
            min = 0.8, -- 80% of base price
            max = 1.2 -- 120% of base price
        }
    },
    
    -- Gang Tax System (Exact Olympus)
    gangTax = {
        rate = 0.15, -- 15% tax if gang doesn't control cartel
        exemption = true, -- No tax if gang controls cartel
        cartelBonus = 0.15 -- 15% more profits if controlling cartel
    },
    
    -- Exact Olympus Payouts (Box Truck + Bergen = 210 cocaine)
    payouts = {
        processed = 698250, -- $698,250 for 210 processed cocaine
        doubleProcessed = 872760 -- $872,760 for 210 crack cocaine
    },
    
    -- Run Time (Exact Olympus Data)
    runTime = {
        processed = 1816, -- 30 minutes 16 seconds
        doubleProcessed = 2700 -- 40-45 minutes
    }
}

-- Heroin Run (Exact Olympus Implementation)
Config.IllegalRuns['heroin'] = {
    name = 'Heroin',
    displayName = 'Heroin Run',
    
    -- Exact Olympus Locations
    locations = {
        field = vector3(2100.5, 1850.2, 35.0), -- East of Pygros
        processor = vector3(2050.8, 1820.5, 35.0), -- Southwest of heroin field
        dealer = vector3(1980.2, 1800.8, 35.0) -- West of Pygros (closest)
    },
    
    -- Exact Olympus Requirements
    requirements = {
        license = 'heroin_processing',
        licenseCost = 25000, -- $25,000 at processor
        vehicle = true,
        backpack = true,
        items = {'food', 'water'}
    },
    
    -- Exact Olympus Processing Mechanics
    processing = {
        time = 5, -- 5 seconds per item
        yield = 1, -- 1:1 ratio
        range = 25, -- Must stay within 25m of NPC
        cancellable = true,
        
        -- Double Processing (Pure Heroin)
        doubleProcess = {
            enabled = true,
            name = 'Pure Heroin',
            multiplier = 1.25, -- x1.25 current price
            time = 5
        }
    },
    
    -- Exact Olympus Pricing
    pricing = {
        base = 3048, -- Base price per black heroin
        processed = 3048, -- $3,048 per black heroin
        doubleProcessed = 3810, -- $3,810 per pure heroin (25% bonus)
        
        fluctuation = {
            enabled = true,
            min = 0.8,
            max = 1.2
        }
    },
    
    -- Gang Tax System
    gangTax = {
        rate = 0.15,
        exemption = true,
        cartelBonus = 0.15
    },
    
    -- Exact Olympus Payouts (Box Truck + Bergen = 210 heroin)
    payouts = {
        processed = 640080, -- $640,080 for 210 black heroin
        doubleProcessed = 800100 -- $800,100 for 210 pure heroin
    },
    
    -- Run Time (Exact Olympus Data)
    runTime = {
        processed = 2116, -- 35 minutes 16 seconds
        doubleProcessed = 2460 -- 41 minutes
    }
}

-- Weed Run (Exact Olympus Implementation)
Config.IllegalRuns['weed'] = {
    name = 'Weed',
    displayName = 'Weed Run',
    
    -- Exact Olympus Locations
    locations = {
        field = vector3(1500.2, 2800.5, 35.0), -- Weed field location
        processor = vector3(1450.8, 2750.2, 35.0), -- Weed processor
        dealer = vector3(1400.5, 2700.8, 35.0) -- Drug dealer
    },
    
    requirements = {
        license = 'weed_processing',
        licenseCost = 20000, -- $20,000 at processor
        vehicle = true,
        backpack = true,
        items = {'food', 'water'}
    },
    
    processing = {
        time = 4, -- 4 seconds per item (faster than cocaine/heroin)
        yield = 1,
        range = 25,
        cancellable = true,
        
        -- Double Processing (High-Grade Marijuana)
        doubleProcess = {
            enabled = true,
            name = 'High-Grade Marijuana',
            multiplier = 1.25,
            time = 4
        }
    },
    
    pricing = {
        base = 2500, -- Lower base price than cocaine/heroin
        processed = 2500,
        doubleProcessed = 3125, -- 25% bonus
        
        fluctuation = {
            enabled = true,
            min = 0.8,
            max = 1.2
        }
    },
    
    gangTax = {
        rate = 0.15,
        exemption = true,
        cartelBonus = 0.15
    },
    
    payouts = {
        processed = 525000, -- Lower payout than cocaine/heroin
        doubleProcessed = 656250
    },
    
    runTime = {
        processed = 1500, -- 25 minutes (faster processing)
        doubleProcessed = 2100 -- 35 minutes
    }
}

-- Meth Run (Exact Olympus Implementation)
Config.IllegalRuns['meth'] = {
    name = 'Meth',
    displayName = 'Meth Run',
    
    locations = {
        field = vector3(2200.5, 1200.8, 35.0), -- Meth field
        processor = vector3(2150.2, 1150.5, 35.0), -- Meth processor
        dealer = vector3(2100.8, 1100.2, 35.0) -- Drug dealer
    },
    
    requirements = {
        license = 'meth_processing',
        licenseCost = 35000, -- Higher cost than cocaine
        vehicle = true,
        backpack = true,
        items = {'food', 'water'}
    },
    
    processing = {
        time = 6, -- Slower than cocaine (more dangerous)
        yield = 1,
        range = 25,
        cancellable = true,
        
        doubleProcess = {
            enabled = true,
            name = 'Crystal Meth',
            multiplier = 1.25,
            time = 6
        }
    },
    
    pricing = {
        base = 3500, -- Higher than cocaine
        processed = 3500,
        doubleProcessed = 4375,
        
        fluctuation = {
            enabled = true,
            min = 0.8,
            max = 1.2
        }
    },
    
    gangTax = {
        rate = 0.15,
        exemption = true,
        cartelBonus = 0.15
    },
    
    payouts = {
        processed = 735000,
        doubleProcessed = 918750
    },
    
    runTime = {
        processed = 2000, -- 33 minutes (slower processing)
        doubleProcessed = 2800 -- 46 minutes
    }
}

-- Global Drug Dealer Locations (Exact Olympus)
Config.DrugDealers = {
    vector3(980.5, -3180.2, 35.0), -- Neochori (closest to cocaine)
    vector3(1980.2, 1800.8, 35.0), -- Pygros (closest to heroin)
    vector3(1400.5, 2700.8, 35.0), -- Weed dealer
    vector3(2100.8, 1100.2, 35.0), -- Meth dealer
    vector3(1200.5, 1500.8, 35.0), -- Additional dealer
    vector3(2500.2, 2200.5, 35.0)  -- Additional dealer
}

-- ATM Locations for Money Storage
Config.ATMLocations = {
    vector3(1000.2, -3150.5, 35.0), -- Neochori Hospital
    vector3(2000.8, 1850.2, 35.0), -- DP 21
    vector3(1450.5, 2750.8, 35.0), -- Near weed processor
    vector3(2150.2, 1200.5, 35.0)  -- Near meth processor
}
