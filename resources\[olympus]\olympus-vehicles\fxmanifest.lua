fx_version 'cerulean'
game 'gta5'

name 'Olympus Vehicle System'
description 'Complete vehicle system for Olympus Framework'
author 'Olympus Development Team'
version '1.0.0'

dependencies {
    'olympus-core'
}

shared_scripts {
    'config/shared.lua'
}

server_scripts {
    'server/main.lua'
}

client_scripts {
    'client/main.lua'
}

-- No UI files (UI handled by olympus-ui)

exports {
    'GetPlayerVehicles',
    'SpawnVehicle',
    'DespawnVehicle',
    'GetVehicleData',
    'HasVehicleKeys',
    'GiveVehicleKeys',
    'GetNearestGarage'
}

server_exports {
    'CreateVehicle',
    'DeleteVehicle',
    'TransferVehicle',
    'ImpoundVehicle',
    'UnimpoundVehicle',
    'GetVehicleOwner',
    'SetVehicleData'
}
