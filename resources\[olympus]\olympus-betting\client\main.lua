-- Olympus Player Betting System - Client Main
-- Based on original fn_betMoney.sqf from Olympus Altis Life

local OlympusBetting = {}

-- Client state
local isInBetDialog = false
local currentBetRequest = nil
local nearbyPlayers = {}

-- Initialize client system
CreateThread(function()
    while not exports['olympus-core'] do
        Wait(100)
    end

    print("[Olympus Betting] Client initialized - Based on original fn_betMoney.sqf")
end)

-- Utility Functions
function OlympusBetting.Notify(message, type)
    TriggerEvent('olympus-core:client:notify', message, type or 'info')
end

function OlympusBetting.FormatMoney(amount)
    return string.format("$%s", exports['olympus-core']:CommaValue(amount))
end

function OlympusBetting.GetNearbyPlayers()
    local players = {}
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)

    for _, playerId in ipairs(GetActivePlayers()) do
        local targetPed = GetPlayerPed(playerId)
        if targetPed ~= playerPed then
            local targetCoords = GetEntityCoords(targetPed)
            local distance = #(playerCoords - targetCoords)

            if distance <= 20.0 then -- Within 20 meters like original
                table.insert(players, {
                    id = GetPlayerServerId(playerId),
                    name = GetPlayerName(playerId),
                    distance = math.floor(distance)
                })
            end
        end
    end

    return players
end

-- Bet Money Function (Exact Olympus Implementation)
function OlympusBetting.BetMoney(type, val, player)
    if type == "amount" then
        -- Open bet dialog
        OlympusBetting.OpenBetDialog()
    elseif type == "bet" then
        -- Send bet request to server
        TriggerServerEvent('olympus-betting:server:betMoney', type, val, player)
    end
end

-- Open Bet Dialog (Exact Olympus Implementation)
function OlympusBetting.OpenBetDialog()
    if isInBetDialog then return end

    -- Get nearby players
    nearbyPlayers = OlympusBetting.GetNearbyPlayers()

    if #nearbyPlayers == 0 then
        OlympusBetting.Notify("No players nearby to bet with!", 'error')
        return
    end

    isInBetDialog = true

    -- Show player selection dialog
    local playerOptions = {}
    for i, player in ipairs(nearbyPlayers) do
        table.insert(playerOptions, {
            label = string.format("%s (%dm)", player.name, player.distance),
            value = player.id
        })
    end

    -- Open NUI for player selection
    SetNuiFocus(true, true)
    SendNUIMessage({
        action = 'openPlayerSelection',
        players = playerOptions
    })
end

-- Show Bet Amount Dialog
function OlympusBetting.ShowBetAmountDialog(targetPlayerId)
    -- Open NUI for amount input
    SendNUIMessage({
        action = 'openAmountInput',
        targetId = targetPlayerId,
        minAmount = Config.BettingSystem.requirements.wagerLimits.minimum,
        maxAmount = Config.BettingSystem.requirements.wagerLimits.maximum
    })
end

-- Show Bet Confirmation Dialog (For Target Player)
function OlympusBetting.ShowBetConfirmation(amount, senderName, senderId)
    currentBetRequest = {
        senderId = senderId,
        senderName = senderName,
        amount = amount
    }

    -- Show confirmation dialog
    SetNuiFocus(true, true)
    SendNUIMessage({
        action = 'openBetConfirmation',
        senderName = senderName,
        amount = amount,
        senderId = senderId
    })

    -- Show notification with F8/F9 options
    OlympusBetting.Notify(string.format('%s wants to bet %s with you. Press F8 to accept or F9 to decline.',
        senderName, OlympusBetting.FormatMoney(amount)), 'info')
end

-- Close all dialogs
function OlympusBetting.CloseDialogs()
    isInBetDialog = false
    SetNuiFocus(false, false)
    SendNUIMessage({
        action = 'closeAll'
    })
end

-- Event Handlers (Exact Olympus Implementation)

-- Handle bet money events from server
RegisterNetEvent('olympus-betting:client:betMoney', function(type, val, player)
    if type == "confirm" then
        -- Show bet confirmation dialog
        OlympusBetting.ShowBetConfirmation(val, GetPlayerName(player), player)

    elseif type == "win" then
        -- Player won the bet
        OlympusBetting.Notify(string.format("Congrats you won the bet worth %s!", OlympusBetting.FormatMoney(val)), 'success')
        TriggerServerEvent('olympus-betting:server:betResult', "win", val, player)

    elseif type == "lose" then
        -- Player lost the bet
        OlympusBetting.Notify("You lost the bet. Better luck next time!", 'error')
        TriggerServerEvent('olympus-betting:server:betResult', "lose", val, player)

    elseif type == "no" then
        -- Bet was declined
        OlympusBetting.Notify("The other player declined your bet.", 'error')

    elseif type == "failU" then
        -- Target player is busy
        OlympusBetting.Notify(Config.ValidationMessages.targetInUse, 'error')

    elseif type == "failM" then
        -- Target player doesn't have enough money
        OlympusBetting.Notify(Config.ValidationMessages.targetNoMoney, 'error')

    elseif type == "failB" then
        -- Target player is already in a bet
        OlympusBetting.Notify(Config.ValidationMessages.targetInBet, 'error')

    elseif type == "failC" then
        -- Target player has cooldown
        OlympusBetting.Notify(Config.ValidationMessages.targetCooldown, 'error')

    elseif type == "failE" then
        -- Target player has betting disabled
        OlympusBetting.Notify(Config.ValidationMessages.targetDisabled, 'error')

    elseif type == "failT" then
        -- Target player doesn't have enough playtime
        OlympusBetting.Notify(Config.ValidationMessages.targetPlaytime, 'error')

    elseif type == "failD" then
        -- Player doesn't have enough money for payout (exploit attempt)
        OlympusBetting.Notify("Insufficient funds for bet payout!", 'error')
    end
end)

-- Open bet dialog from server
RegisterNetEvent('olympus-betting:client:openBetDialog', function()
    OlympusBetting.OpenBetDialog()
end)

-- Show bet confirmation from server
RegisterNetEvent('olympus-betting:client:showBetConfirmation', function(amount, senderName, senderId)
    OlympusBetting.ShowBetConfirmation(amount, senderName, senderId)
end)

-- Update nearby players
RegisterNetEvent('olympus-betting:client:updateNearbyPlayers', function(players)
    nearbyPlayers = players
    SendNUIMessage({
        action = 'updateNearbyPlayers',
        players = players
    })
end)

-- Update betting settings
RegisterNetEvent('olympus-betting:client:updateBettingSettings', function(enabled)
    OlympusBetting.Notify(string.format("Betting %s", enabled and "enabled" or "disabled"), 'info')
end)

-- NUI Callbacks (Exact Olympus Implementation)
RegisterNUICallback('closeDialog', function(data, cb)
    OlympusBetting.CloseDialogs()
    cb('ok')
end)

RegisterNUICallback('selectPlayer', function(data, cb)
    -- Player selected, show amount input
    OlympusBetting.ShowBetAmountDialog(data.playerId)
    cb('ok')
end)

RegisterNUICallback('submitBet', function(data, cb)
    -- Submit bet request
    local amount = tonumber(data.amount)
    local targetId = tonumber(data.targetId)

    if amount and targetId then
        TriggerServerEvent('olympus-betting:server:betMoney', "bet", amount, targetId)
        OlympusBetting.CloseDialogs()
    end
    cb('ok')
end)

RegisterNUICallback('respondToBet', function(data, cb)
    -- Respond to bet confirmation
    if currentBetRequest then
        if data.accept then
            TriggerServerEvent('olympus-betting:server:betConfirm', "accept", currentBetRequest.amount, currentBetRequest.senderId)
        else
            TriggerServerEvent('olympus-betting:server:betConfirm', "decline", currentBetRequest.amount, currentBetRequest.senderId)
        end
        currentBetRequest = nil
        OlympusBetting.CloseDialogs()
    end
    cb('ok')
end)

-- Key bindings for bet responses (Exact Olympus Implementation)
RegisterKeyMapping('accept_bet', 'Accept Bet Request', 'keyboard', 'F8')
RegisterKeyMapping('decline_bet', 'Decline Bet Request', 'keyboard', 'F9')

RegisterCommand('accept_bet', function()
    if currentBetRequest then
        TriggerServerEvent('olympus-betting:server:betConfirm', "accept", currentBetRequest.amount, currentBetRequest.senderId)
        currentBetRequest = nil
        OlympusBetting.CloseDialogs()
    end
end, false)

RegisterCommand('decline_bet', function()
    if currentBetRequest then
        TriggerServerEvent('olympus-betting:server:betConfirm', "decline", currentBetRequest.amount, currentBetRequest.senderId)
        currentBetRequest = nil
        OlympusBetting.CloseDialogs()
    end
end, false)

-- Commands (Exact Olympus Implementation)
RegisterCommand('bet', function()
    TriggerServerEvent('olympus-betting:server:betMoney', "amount")
end, false)

-- Export Functions
exports('BetMoney', OlympusBetting.BetMoney)
exports('CanPlayerBet', function()
    local canBet, reason = exports['olympus-betting']:CanPlayerBet(GetPlayerServerId(PlayerId()))
    return canBet
end)
exports('IsPlayerInBet', function()
    return exports['olympus-betting']:IsPlayerInBet(GetPlayerServerId(PlayerId()))
end)
exports('GetBetCooldown', function()
    return exports['olympus-betting']:GetBetCooldown(GetPlayerServerId(PlayerId()))
end)

print("[Olympus Betting] Client module loaded - Based on original fn_betMoney.sqf")
