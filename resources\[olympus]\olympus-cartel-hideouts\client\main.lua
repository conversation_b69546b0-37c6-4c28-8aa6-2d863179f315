-- ========================================
-- OLYMPUS CARTEL & HIDEOUT CLIENT
-- Based on Original Olympus Functions
-- ========================================

local OlympusCartelHideouts = {}

-- ========================================
-- CLIENT INITIALIZATION
-- ========================================
local hideoutBlips = {}
local blackMarketBlips = {}
local hideoutData = {}
local blackMarketData = {}
local isCapturing = false

function OlympusCartelHideouts.Init()
    -- Request data from server
    TriggerServerEvent('olympus-cartel-hideouts:requestData')
    
    -- Create blips for all locations
    OlympusCartelHideouts.CreateHideoutBlips()
    OlympusCartelHideouts.CreateBlackMarketBlips()
    
    -- Start interaction thread
    CreateThread(OlympusCartelHideouts.InteractionThread)
end

-- ========================================
-- UTILITY FUNCTIONS
-- ========================================
function OlympusCartelHideouts.Notify(message, type)
    -- Use your notification system
    if exports['olympus-core'] and exports['olympus-core'].Notify then
        exports['olympus-core']:Notify(message, type or 'info')
    else
        print(message)
    end
end

function OlympusCartelHideouts.PlayAnimation(dict, anim, flag, duration)
    RequestAnimDict(dict)
    while not HasAnimDictLoaded(dict) do
        Wait(1)
    end
    
    TaskPlayAnim(PlayerPedId(), dict, anim, 8.0, -8.0, duration or -1, flag or 0, 0, false, false, false)
end

function OlympusCartelHideouts.ShowProgressBar(text, duration)
    -- Use your progress bar system
    if exports['olympus-core'] and exports['olympus-core'].ShowProgressBar then
        exports['olympus-core']:ShowProgressBar(text, duration)
    end
end

function OlympusCartelHideouts.GetClosestHideout()
    local playerCoords = GetEntityCoords(PlayerPedId())
    local closestHideout = nil
    local closestDistance = math.huge
    
    for _, hideout in ipairs(Config.Hideouts) do
        local distance = #(playerCoords - hideout.coords)
        if distance < closestDistance and distance <= Config.CartelHideouts.hideoutCapture.maxDistance then
            closestDistance = distance
            closestHideout = hideout
        end
    end
    
    return closestHideout, closestDistance
end

function OlympusCartelHideouts.GetClosestBlackMarket()
    local playerCoords = GetEntityCoords(PlayerPedId())
    local closestMarket = nil
    local closestDistance = math.huge
    
    for _, market in ipairs(Config.BlackMarkets) do
        local distance = #(playerCoords - market.coords)
        if distance < closestDistance and distance <= Config.CartelHideouts.blackMarketCapture.maxDistance then
            closestDistance = distance
            closestMarket = market
        end
    end
    
    return closestMarket, closestDistance
end

-- ========================================
-- BLIP MANAGEMENT
-- ========================================
function OlympusCartelHideouts.CreateHideoutBlips()
    for _, hideout in ipairs(Config.Hideouts) do
        local blip = AddBlipForCoord(hideout.coords.x, hideout.coords.y, hideout.coords.z)
        SetBlipSprite(blip, hideout.blipSprite)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, hideout.blipScale)
        SetBlipColour(blip, hideout.blipColor)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(hideout.name)
        EndTextCommandSetBlipName(blip)
        
        hideoutBlips[hideout.id] = blip
    end
end

function OlympusCartelHideouts.CreateBlackMarketBlips()
    for _, market in ipairs(Config.BlackMarkets) do
        local blip = AddBlipForCoord(market.coords.x, market.coords.y, market.coords.z)
        SetBlipSprite(blip, market.blipSprite)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, market.blipScale)
        SetBlipColour(blip, market.blipColor)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(market.name)
        EndTextCommandSetBlipName(blip)
        
        blackMarketBlips[market.id] = blip
    end
end

function OlympusCartelHideouts.UpdateHideoutBlip(hideoutId, gangId, gangName)
    local blip = hideoutBlips[hideoutId]
    if blip then
        local hideoutConfig = nil
        for _, hideout in ipairs(Config.Hideouts) do
            if hideout.id == hideoutId then
                hideoutConfig = hideout
                break
            end
        end
        
        if hideoutConfig then
            local blipName = hideoutConfig.name
            if gangId and gangId ~= 0 then
                blipName = blipName .. " (" .. gangName .. ")"
                SetBlipColour(blip, 1) -- Red for captured
            else
                SetBlipColour(blip, hideoutConfig.blipColor) -- Default color for neutral
            end
            
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(blipName)
            EndTextCommandSetBlipName(blip)
        end
    end
end

function OlympusCartelHideouts.UpdateBlackMarketBlip(marketId, gangId, gangName)
    local blip = blackMarketBlips[marketId]
    if blip then
        local marketConfig = nil
        for _, market in ipairs(Config.BlackMarkets) do
            if market.id == marketId then
                marketConfig = market
                break
            end
        end
        
        if marketConfig then
            local blipName = marketConfig.name
            if gangId and gangId ~= 0 then
                blipName = blipName .. " (" .. gangName .. ")"
                SetBlipColour(blip, 1) -- Red for captured
            else
                SetBlipColour(blip, marketConfig.blipColor) -- Default color for neutral
            end
            
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(blipName)
            EndTextCommandSetBlipName(blip)
        end
    end
end

-- ========================================
-- HIDEOUT CAPTURE SYSTEM
-- Based on fn_captureHideout.sqf
-- ========================================
function OlympusCartelHideouts.CaptureHideout()
    if isCapturing then
        OlympusCartelHideouts.Notify("You are already capturing something", 'error')
        return
    end
    
    local hideout, distance = OlympusCartelHideouts.GetClosestHideout()
    if not hideout then
        OlympusCartelHideouts.Notify("No hideout nearby", 'error')
        return
    end
    
    TriggerServerEvent('olympus-cartel-hideouts:captureHideout', hideout.id)
end

function OlympusCartelHideouts.StartHideoutCapture(hideoutId, captureRate)
    isCapturing = true
    
    local hideoutConfig = nil
    for _, hideout in ipairs(Config.Hideouts) do
        if hideout.id == hideoutId then
            hideoutConfig = hideout
            break
        end
    end
    
    if not hideoutConfig then
        isCapturing = false
        return
    end
    
    -- Start capture animation
    OlympusCartelHideouts.PlayAnimation(
        Config.Animations.capturing.dict,
        Config.Animations.capturing.anim,
        Config.Animations.capturing.flag
    )
    
    -- Show progress bar
    local captureTime = Config.CartelHideouts.hideoutCapture.captureTime
    OlympusCartelHideouts.ShowProgressBar(Config.Notifications.hideout.capturing, captureTime)
    
    -- Capture progress loop
    local startTime = GetGameTimer()
    local progress = 0.0
    
    while progress < 1.0 do
        Wait(250)
        
        local currentTime = GetGameTimer()
        progress = (currentTime - startTime) / captureTime
        
        -- Check if player is still alive and in range
        if not IsEntityDead(PlayerPedId()) then
            local playerCoords = GetEntityCoords(PlayerPedId())
            local distance = #(playerCoords - hideoutConfig.coords)
            
            if distance > Config.CartelHideouts.hideoutCapture.maxDistance then
                OlympusCartelHideouts.Notify(Config.Notifications.hideout.tooFar, 'error')
                break
            end
        else
            break
        end
        
        -- Check for interruption
        if IsControlJustPressed(0, 73) then -- X key
            OlympusCartelHideouts.Notify(Config.Notifications.hideout.captureCancel, 'warning')
            break
        end
    end
    
    -- Stop animation
    ClearPedTasks(PlayerPedId())
    isCapturing = false
    
    -- Complete capture if successful
    if progress >= 1.0 then
        TriggerServerEvent('olympus-cartel-hideouts:completeHideoutCapture', hideoutId)
    end
end

-- ========================================
-- BLACK MARKET CAPTURE SYSTEM
-- Based on fn_captureBlackMarket.sqf
-- ========================================
function OlympusCartelHideouts.CaptureBlackMarket()
    if isCapturing then
        OlympusCartelHideouts.Notify("You are already capturing something", 'error')
        return
    end

    local market, distance = OlympusCartelHideouts.GetClosestBlackMarket()
    if not market then
        OlympusCartelHideouts.Notify("No black market nearby", 'error')
        return
    end

    TriggerServerEvent('olympus-cartel-hideouts:captureBlackMarket', market.id)
end

function OlympusCartelHideouts.StartBlackMarketCapture(marketId, marketConfig)
    isCapturing = true

    -- Show initial notification
    OlympusCartelHideouts.Notify(Config.Notifications.blackMarket.stayNear, 'info')

    -- Show progress bar
    local captureTime = Config.CartelHideouts.blackMarketCapture.captureTime
    OlympusCartelHideouts.ShowProgressBar(Config.Notifications.blackMarket.capturing, captureTime)

    -- Capture progress loop
    local startTime = GetGameTimer()
    local progress = 0.0

    while progress < 1.0 do
        Wait(250)

        local currentTime = GetGameTimer()
        progress = (currentTime - startTime) / captureTime

        -- Check if player is still alive and in range
        if not IsEntityDead(PlayerPedId()) then
            local playerCoords = GetEntityCoords(PlayerPedId())
            local distance = #(playerCoords - marketConfig.coords)

            if distance > Config.CartelHideouts.blackMarketCapture.maxDistance then
                OlympusCartelHideouts.Notify(Config.Notifications.blackMarket.tooFar, 'error')
                break
            end

            -- Check if player is in a vehicle
            if IsPedInAnyVehicle(PlayerPedId(), false) then
                OlympusCartelHideouts.Notify("You cannot capture while in a vehicle", 'error')
                break
            end
        else
            break
        end
    end

    isCapturing = false

    -- Complete capture if successful
    if progress >= 1.0 then
        -- Add random delay like original
        local randomDelay = math.random(Config.CartelHideouts.blackMarketCapture.randomDelay[1], Config.CartelHideouts.blackMarketCapture.randomDelay[2])
        Wait(randomDelay * 1000)

        TriggerServerEvent('olympus-cartel-hideouts:completeBlackMarketCapture', marketId)
    end
end

-- ========================================
-- INTERACTION SYSTEM
-- ========================================
function OlympusCartelHideouts.InteractionThread()
    while true do
        Wait(0)

        if not isCapturing then
            local playerCoords = GetEntityCoords(PlayerPedId())

            -- Check for hideout interaction
            local hideout, hideoutDistance = OlympusCartelHideouts.GetClosestHideout()
            if hideout and hideoutDistance <= 5.0 then
                -- Draw 3D text
                DrawText3D(hideout.coords.x, hideout.coords.y, hideout.coords.z + 1.0, "[E] Capture Hideout")

                if IsControlJustPressed(0, 38) then -- E key
                    OlympusCartelHideouts.CaptureHideout()
                end
            end

            -- Check for black market interaction
            local market, marketDistance = OlympusCartelHideouts.GetClosestBlackMarket()
            if market and marketDistance <= 5.0 then
                -- Draw 3D text
                DrawText3D(market.coords.x, market.coords.y, market.coords.z + 1.0, "[E] Capture Black Market")

                if IsControlJustPressed(0, 38) then -- E key
                    OlympusCartelHideouts.CaptureBlackMarket()
                end
            end
        end
    end
end

function DrawText3D(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())

    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x, _y)
    local factor = (string.len(text)) / 370
    DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 41, 11, 41, 68)
end

-- ========================================
-- EVENT HANDLERS
-- ========================================
RegisterNetEvent('olympus-cartel-hideouts:notify', function(message, type)
    OlympusCartelHideouts.Notify(message, type)
end)

RegisterNetEvent('olympus-cartel-hideouts:broadcast', function(message)
    OlympusCartelHideouts.Notify(message, 'info')
end)

RegisterNetEvent('olympus-cartel-hideouts:confirmCapture', function(hideoutId, currentOwner, captureRate)
    -- Show confirmation dialog
    local confirmed = true -- You can implement a proper dialog here

    if confirmed then
        OlympusCartelHideouts.StartHideoutCapture(hideoutId, captureRate)
    end
end)

RegisterNetEvent('olympus-cartel-hideouts:startCapture', function(hideoutId, captureRate)
    OlympusCartelHideouts.StartHideoutCapture(hideoutId, captureRate)
end)

RegisterNetEvent('olympus-cartel-hideouts:startBlackMarketCapture', function(marketId, marketConfig)
    OlympusCartelHideouts.StartBlackMarketCapture(marketId, marketConfig)
end)

RegisterNetEvent('olympus-cartel-hideouts:updateHideoutOwner', function(hideoutId, gangId, gangName)
    OlympusCartelHideouts.UpdateHideoutBlip(hideoutId, gangId, gangName)
end)

RegisterNetEvent('olympus-cartel-hideouts:updateBlackMarketOwner', function(marketId, gangId, gangName)
    OlympusCartelHideouts.UpdateBlackMarketBlip(marketId, gangId, gangName)
end)

RegisterNetEvent('olympus-cartel-hideouts:receiveHideoutData', function(data)
    hideoutData = data

    -- Update blips with current ownership
    for _, hideout in ipairs(data) do
        OlympusCartelHideouts.UpdateHideoutBlip(hideout.hideout_id, hideout.gang_id, hideout.gang_name)
    end
end)

RegisterNetEvent('olympus-cartel-hideouts:receiveBlackMarketData', function(data)
    blackMarketData = data

    -- Update blips with current ownership
    for _, market in ipairs(data) do
        OlympusCartelHideouts.UpdateBlackMarketBlip(market.market_id, market.gang_id, market.gang_name)
    end
end)

-- ========================================
-- EXPORT FUNCTIONS
-- ========================================
exports('CaptureHideout', function()
    return OlympusCartelHideouts.CaptureHideout()
end)

exports('CaptureBlackMarket', function()
    return OlympusCartelHideouts.CaptureBlackMarket()
end)

exports('IsCapturing', function()
    return isCapturing
end)

exports('GetHideoutData', function()
    return hideoutData
end)

exports('GetBlackMarketData', function()
    return blackMarketData
end)

-- ========================================
-- INITIALIZATION
-- ========================================
CreateThread(function()
    OlympusCartelHideouts.Init()
    print("^2[Olympus Cartel & Hideouts]^7 Client system initialized")
end)
