-- Olympus Items - Main Client Script
-- Based on original Olympus Altis Life special items system

local OlympusItems = {}
local playerData = {}
local isUsingItem = false
local itemCooldowns = {}

-- Initialize
CreateThread(function()
    while not exports['olympus-core'] do
        Wait(100)
    end
    
    -- Register item use callbacks
    RegisterItemCallbacks()
    
    -- Start cooldown cleanup thread
    CreateThread(CooldownCleanup)
end)

-- Get player data
function GetPlayerData()
    if exports['olympus-core'] then
        return exports['olympus-core']:GetPlayerData()
    end
    return nil
end

-- Check if player has required faction access
function HasFactionAccess(item)
    local playerData = GetPlayerData()
    if not playerData then return false end
    
    local faction = playerData.faction
    local restrictions = Config.FactionRestrictions
    
    -- Check if item is restricted to specific factions
    for factionName, items in pairs(restrictions) do
        if table.contains(items, item) then
            return faction == factionName
        end
    end
    
    return true -- Item not restricted
end

-- Check cooldown
function IsOnCooldown(item)
    if not itemCooldowns[item] then return false end
    return GetGameTimer() < itemCooldowns[item]
end

-- Set cooldown
function SetCooldown(item)
    local cooldownTime = Config.Cooldowns[item] or 0
    if cooldownTime > 0 then
        itemCooldowns[item] = GetGameTimer() + (cooldownTime * 1000)
    end
end

-- Cooldown cleanup thread
function CooldownCleanup()
    while true do
        local currentTime = GetGameTimer()
        for item, expireTime in pairs(itemCooldowns) do
            if currentTime >= expireTime then
                itemCooldowns[item] = nil
            end
        end
        Wait(5000) -- Check every 5 seconds
    end
end

-- Utility function to check if table contains value
function table.contains(table, element)
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

-- Show notification
function ShowNotification(message, type)
    if exports['olympus-ui'] then
        exports['olympus-ui']:ShowNotification({
            type = type or 'info',
            message = message,
            duration = 3000
        })
    else
        -- Fallback to native notification
        SetNotificationTextEntry('STRING')
        AddTextComponentString(message)
        DrawNotification(false, false)
    end
end

-- Progress bar function
function ShowProgressBar(config, onComplete, onCancel)
    if exports['olympus-ui'] then
        exports['olympus-ui']:ShowProgressBar(config, onComplete, onCancel)
    else
        -- Fallback - just wait and complete
        Wait(config.duration or 5000)
        if onComplete then onComplete() end
    end
end

-- Play animation
function PlayAnimation(animConfig)
    if not animConfig then return end
    
    local ped = PlayerPedId()
    local dict = animConfig.dict
    local name = animConfig.name
    local duration = animConfig.duration or 1500
    
    RequestAnimDict(dict)
    while not HasAnimDictLoaded(dict) do
        Wait(10)
    end
    
    TaskPlayAnim(ped, dict, name, 8.0, -8.0, duration, 1, 0, false, false, false)
end

-- Register item use callbacks
function RegisterItemCallbacks()
    -- Register NUI callbacks for item usage
    RegisterNUICallback('useItem', function(data, cb)
        local item = data.item
        local target = data.target
        
        if UseItem(item, target) then
            cb({success = true})
        else
            cb({success = false})
        end
    end)
end

-- Main item use function
function UseItem(item, target)
    if isUsingItem then
        ShowNotification("You are already using an item!", "error")
        return false
    end
    
    if IsOnCooldown(item) then
        ShowNotification("This item is on cooldown!", "error")
        return false
    end
    
    if not HasFactionAccess(item) then
        ShowNotification("You don't have access to this item!", "error")
        return false
    end
    
    local itemConfig = Config.Items[item]
    if not itemConfig then
        ShowNotification("Unknown item!", "error")
        return false
    end
    
    -- Set using flag
    isUsingItem = true
    
    -- Handle specific item types
    local success = false
    
    if item == "lockpick" then
        success = exports['olympus-items']:UseLockpick(target)
    elseif item == "boltcutter" then
        success = exports['olympus-items']:UseBoltcutter(target)
    elseif item == "blastingcharge" then
        success = exports['olympus-items']:UseBlastingCharge(target)
    elseif item == "gpstracker" then
        success = exports['olympus-items']:UseGPSTracker(target)
    elseif item == "speedbomb" then
        success = exports['olympus-items']:UseSpeedBomb(target)
    elseif item == "flashbang" then
        success = exports['olympus-items']:UseFlashbang(target)
    elseif item == "blindfold" then
        success = exports['olympus-items']:UseBlindfold(target)
    elseif item == "bloodbag" then
        success = exports['olympus-items']:UseBloodBag()
    elseif item == "epipen" then
        success = exports['olympus-items']:UseEpiPen(target)
    elseif item == "dopeshot" then
        success = exports['olympus-items']:UseDopeShot()
    elseif item == "fireaxe" then
        success = exports['olympus-items']:UseFireAxe(target)
    elseif item == "slimjim" then
        success = exports['olympus-items']:UseSlimJim(target)
    elseif item == "spikestrip" then
        success = exports['olympus-items']:UseSpikeStrip()
    elseif item == "defusekit" then
        success = exports['olympus-items']:UseDefuseKit(target)
    elseif item == "helitowhook" then
        success = exports['olympus-items']:UseHeliTowHook(target)
    elseif item == "pocketgokart" then
        success = exports['olympus-items']:UsePocketGoKart()
    elseif item == "takeoverterminal" then
        success = exports['olympus-items']:UseTakeoverTerminal(target)
    elseif item == "vehammo" then
        success = exports['olympus-items']:UseVehicleAmmo(target)
    elseif item == "fireworks" then
        success = exports['olympus-items']:UseFireworks()
    else
        ShowNotification("This item cannot be used!", "error")
    end
    
    -- Set cooldown if successful
    if success then
        SetCooldown(item)
    end
    
    -- Clear using flag
    isUsingItem = false
    
    return success
end

-- Export functions
exports('UseItem', UseItem)
exports('HasFactionAccess', HasFactionAccess)
exports('IsOnCooldown', IsOnCooldown)
exports('ShowNotification', ShowNotification)
exports('ShowProgressBar', ShowProgressBar)
exports('PlayAnimation', PlayAnimation)

-- Event handlers
RegisterNetEvent('olympus-items:client:useItem', function(item, target)
    UseItem(item, target)
end)

RegisterNetEvent('olympus-items:client:notification', function(message, type)
    ShowNotification(message, type)
end)

RegisterNetEvent('olympus-items:client:flashbangEffect', function(position)
    exports['olympus-items']:HandleFlashbangEffect(position)
end)

RegisterNetEvent('olympus-items:client:teargasEffect', function(position)
    exports['olympus-items']:HandleTeargasEffect(position)
end)

RegisterNetEvent('olympus-items:client:applyBlindfold', function()
    exports['olympus-items']:ApplyBlindfold()
end)

RegisterNetEvent('olympus-items:client:removeBlindfold', function()
    exports['olympus-items']:RemoveBlindfold()
end)
