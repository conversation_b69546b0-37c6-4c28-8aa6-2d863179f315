-- ========================================
-- OLYMPUS MEDICAL SYSTEM - CLIENT MAIN
-- Complete recreation based on original Olympus medical mechanics
-- Handles R&R client-side functionality, death system, and medical interactions
-- ========================================

local OlympusMedical = {}
OlympusMedical.isMedic = false
OlympusMedical.medicRank = 0
OlympusMedical.onDuty = false
OlympusMedical.isDead = false
OlympusMedical.deathTime = 0
OlympusMedical.medicRequests = {}
OlympusMedical.currentBuddy = nil
OlympusMedical.epiActive = false
OlympusMedical.hospitalBlips = {}
OlympusMedical.medicBlips = {}

-- Configuration is loaded via shared_scripts in fxmanifest.lua

-- Initialize Medical client
function InitializeMedicalClient()
    print("^2[Olympus Medical]^7 Initializing client medical system...")

    -- Wait for player to be loaded
    while not exports['olympus-core']:IsPlayerLoaded() do
        Wait(1000)
    end

    -- Check if player is medical
    local playerData = exports['olympus-core']:GetPlayerData()
    if playerData and playerData.faction == Config.MedicalFaction then
        OlympusMedical.isMedic = true
        OlympusMedical.medicRank = playerData.faction_rank or 1

        print("^2[Olympus Medical]^7 Medical client initialized - Rank: " .. OlympusMedical.medicRank)

        -- Setup medical loadout
        SetupMedicalLoadout()

        -- Create hospital blips
        CreateHospitalBlips()
    end

    -- Setup death system
    SetupDeathSystem()

    print("^2[Olympus Medical]^7 Client medical system initialized!")
end

-- Setup medical loadout (based on original fn_medicLoadout.sqf)
function SetupMedicalLoadout()
    if not OlympusMedical.isMedic then return end

    -- Give medical equipment based on rank
    local items = {
        'medikit',
        'toolkit',
        'firstaidkit'
    }

    -- Higher ranks get additional equipment
    if OlympusMedical.medicRank >= 2 then
        table.insert(items, 'bloodbag')
    end

    if OlympusMedical.medicRank >= 3 then
        table.insert(items, 'epipen')
        table.insert(items, 'dopamine')
    end

    -- Request loadout from server
    TriggerServerEvent('olympus-core:server:giveItems', items)
end

-- Create hospital blips
function CreateHospitalBlips()
    for _, hospital in pairs(Config.Hospitals) do
        local blip = AddBlipForCoord(hospital.coords.x, hospital.coords.y, hospital.coords.z)
        SetBlipSprite(blip, 61)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, 0.8)
        SetBlipColour(blip, 2)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(hospital.name)
        EndTextCommandSetBlipName(blip)

        table.insert(OlympusMedical.hospitalBlips, blip)
    end
end

-- Setup death system
function SetupDeathSystem()
    CreateThread(function()
        while true do
            Wait(0)

            local ped = PlayerPedId()
            local health = GetEntityHealth(ped)

            -- Check if player died
            if health <= 0 and not OlympusMedical.isDead then
                OnPlayerDeath()
            elseif health > 0 and OlympusMedical.isDead then
                OnPlayerRevive()
            end
        end
    end)
end

-- Handle player death (based on original fn_onPlayerKilled.sqf)
function OnPlayerDeath()
    OlympusMedical.isDead = true
    OlympusMedical.deathTime = GetGameTimer()

    -- Notify server
    TriggerServerEvent('olympus-core:server:setPlayerData', 'isDead', true)
    TriggerServerEvent('olympus-core:server:setPlayerData', 'deathTime', OlympusMedical.deathTime)

    -- Start death screen
    StartDeathScreen()

    -- Disable controls
    DisablePlayerControls()
end

-- Handle player revive
function OnPlayerRevive()
    OlympusMedical.isDead = false
    OlympusMedical.deathTime = 0

    -- Notify server
    TriggerServerEvent('olympus-core:server:setPlayerData', 'isDead', false)
    TriggerServerEvent('olympus-core:server:setPlayerData', 'deathTime', nil)

    -- Stop death screen
    StopDeathScreen()

    -- Enable controls
    EnablePlayerControls()
end

-- Start death screen (based on original fn_deathScreen.sqf)
function StartDeathScreen()
    CreateThread(function()
        while OlympusMedical.isDead do
            Wait(0)

            -- Draw death screen
            DrawRect(0.0, 0.0, 2.0, 2.0, 0, 0, 0, 150)

            -- Draw death text
            SetTextFont(4)
            SetTextProportional(1)
            SetTextScale(0.0, 0.6)
            SetTextColour(255, 255, 255, 255)
            SetTextDropshadow(0, 0, 0, 0, 255)
            SetTextEdge(1, 0, 0, 0, 255)
            SetTextDropShadow()
            SetTextOutline()
            SetTextEntry("STRING")
            AddTextComponentString("~r~You are unconscious~w~\nPress ~g~E~w~ to request medical assistance\nPress ~r~R~w~ to respawn (5 minute timer)")
            DrawText(0.5, 0.4)

            -- Handle input
            if IsControlJustPressed(0, 38) then -- E key
                RequestMedicalAssistance()
            end

            if IsControlJustPressed(0, 45) then -- R key
                local timeSinceDeath = (GetGameTimer() - OlympusMedical.deathTime) / 1000
                if timeSinceDeath >= 300 then -- 5 minutes
                    RespawnPlayer()
                else
                    local timeLeft = math.ceil(300 - timeSinceDeath)
                    exports['olympus-ui']:ShowNotification({
                        type = 'error',
                        title = 'Respawn',
                        message = 'You must wait ' .. timeLeft .. ' seconds before respawning',
                        duration = 3000
                    })
                end
            end
        end
    end)
end

-- Stop death screen
function StopDeathScreen()
    -- Clear any death screen elements
end

-- Disable player controls during death
function DisablePlayerControls()
    CreateThread(function()
        while OlympusMedical.isDead do
            Wait(0)

            -- Disable most controls
            DisableAllControlActions(0)

            -- Allow camera movement
            EnableControlAction(0, 1, true) -- LookLeftRight
            EnableControlAction(0, 2, true) -- LookUpDown

            -- Allow death screen controls
            EnableControlAction(0, 38, true) -- E key
            EnableControlAction(0, 45, true) -- R key
            EnableControlAction(0, 245, true) -- T key (chat)
        end
    end)
end

-- Enable player controls after revive
function EnablePlayerControls()
    -- Controls are automatically re-enabled when the disable loop stops
end

-- Request medical assistance
function RequestMedicalAssistance()
    if not OlympusMedical.isDead then return end

    TriggerServerEvent('olympus-medical:server:requestMedic')
end

-- Respawn player
function RespawnPlayer()
    if not OlympusMedical.isDead then return end

    -- Find nearest hospital
    local playerCoords = GetEntityCoords(PlayerPedId())
    local nearestHospital = nil
    local nearestDistance = math.huge

    for _, hospital in pairs(Config.Hospitals) do
        local distance = #(playerCoords - hospital.coords)
        if distance < nearestDistance then
            nearestDistance = distance
            nearestHospital = hospital
        end
    end

    if nearestHospital then
        -- Respawn at hospital
        local ped = PlayerPedId()
        SetEntityCoords(ped, nearestHospital.spawn.x, nearestHospital.spawn.y, nearestHospital.spawn.z)
        SetEntityHeading(ped, nearestHospital.spawn.w or 0.0)
        SetEntityHealth(ped, 200)

        -- Clear death status
        OnPlayerRevive()

        exports['olympus-ui']:ShowNotification({
            type = 'info',
            title = 'Hospital',
            message = 'You have respawned at ' .. nearestHospital.name,
            duration = 5000
        })
    end
end

-- Get nearest player function
function GetNearestPlayer(maxDistance)
    maxDistance = maxDistance or 5.0
    local players = GetActivePlayers()
    local ped = PlayerPedId()
    local pos = GetEntityCoords(ped)
    local nearestPlayer = nil
    local nearestDistance = maxDistance

    for _, player in ipairs(players) do
        if player ~= PlayerId() then
            local targetPed = GetPlayerPed(player)
            local targetPos = GetEntityCoords(targetPed)
            local distance = #(pos - targetPos)

            if distance < nearestDistance then
                nearestDistance = distance
                nearestPlayer = player
            end
        end
    end

    return nearestPlayer, nearestDistance
end

-- Medical duty toggle command
RegisterCommand('medduty', function()
    if OlympusMedical.isMedic then
        TriggerServerEvent('olympus-medical:server:toggleDuty')
    else
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Medical',
            message = 'You are not a medic',
            duration = 3000
        })
    end
end, false)

-- Revive command
RegisterCommand('revive', function()
    if not OlympusMedical.isMedic or not OlympusMedical.onDuty then
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Medical',
            message = 'You must be a medic on duty to use this command',
            duration = 3000
        })
        return
    end

    local nearestPlayer, distance = GetNearestPlayer(3.0)
    if nearestPlayer then
        TriggerServerEvent('olympus-medical:server:revivePlayer', GetPlayerServerId(nearestPlayer))
    else
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Medical',
            message = 'No unconscious player nearby',
            duration = 3000
        })
    end
end, false)

-- Dopamine command
RegisterCommand('dopamine', function()
    if not OlympusMedical.isMedic or not OlympusMedical.onDuty then
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Medical',
            message = 'You must be a medic on duty to use this command',
            duration = 3000
        })
        return
    end

    if OlympusMedical.medicRank < 3 then
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Medical',
            message = 'Senior Paramedic rank or higher required for dopamine',
            duration = 3000
        })
        return
    end

    local nearestPlayer, distance = GetNearestPlayer(3.0)
    if nearestPlayer then
        TriggerServerEvent('olympus-medical:server:giveDopamine', GetPlayerServerId(nearestPlayer))
    else
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Medical',
            message = 'No player nearby requiring dopamine',
            duration = 3000
        })
    end
end, false)

-- Medical request command (for civilians)
RegisterCommand('medic', function()
    if OlympusMedical.isDead then
        RequestMedicalAssistance()
    else
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Medical',
            message = 'You must be unconscious to request medical assistance',
            duration = 3000
        })
    end
end, false)

-- Event Handlers

-- Medical duty status update
RegisterNetEvent('olympus-medical:client:dutyStatus')
AddEventHandler('olympus-medical:client:dutyStatus', function(status)
    OlympusMedical.onDuty = status
    local statusText = status and "on duty" or "off duty"

    exports['olympus-ui']:ShowNotification({
        type = status and 'success' or 'info',
        title = 'Medical Duty',
        message = 'You are now ' .. statusText,
        duration = 3000
    })
end)

-- Player revive event
RegisterNetEvent('olympus-medical:client:revivePlayer')
AddEventHandler('olympus-medical:client:revivePlayer', function()
    local ped = PlayerPedId()
    SetEntityHealth(ped, 200)
    OnPlayerRevive()
end)

-- Heal player event
RegisterNetEvent('olympus-medical:client:healPlayer')
AddEventHandler('olympus-medical:client:healPlayer', function(health)
    local ped = PlayerPedId()
    SetEntityHealth(ped, health or 200)

    -- Clear epi status
    OlympusMedical.epiActive = false
end)

-- Medical request notification
RegisterNetEvent('olympus-medical:client:medicRequest')
AddEventHandler('olympus-medical:client:medicRequest', function(requestData)
    if not OlympusMedical.isMedic or not OlympusMedical.onDuty then return end

    -- Create blip for medical request
    local blip = AddBlipForCoord(requestData.coords.x, requestData.coords.y, requestData.coords.z)
    SetBlipSprite(blip, 153)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, 1.0)
    SetBlipColour(blip, 1)
    SetBlipFlashes(blip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("Medical Request - " .. requestData.playerName)
    EndTextCommandSetBlipName(blip)

    -- Store request
    OlympusMedical.medicRequests[requestData.playerId] = {
        blip = blip,
        data = requestData
    }

    -- Remove blip after 5 minutes
    SetTimeout(300000, function()
        if OlympusMedical.medicRequests[requestData.playerId] then
            RemoveBlip(OlympusMedical.medicRequests[requestData.playerId].blip)
            OlympusMedical.medicRequests[requestData.playerId] = nil
        end
    end)

    exports['olympus-ui']:ShowNotification({
        type = 'info',
        title = 'Medical Request',
        message = requestData.playerName .. ' is requesting medical assistance',
        duration = 10000
    })
end)

-- Buddy request notification
RegisterNetEvent('olympus-medical:client:buddyRequest')
AddEventHandler('olympus-medical:client:buddyRequest', function(requestData)
    if not OlympusMedical.isMedic then return end

    -- Show buddy request dialog
    exports['olympus-ui']:ShowDialog({
        title = 'Medical Buddy Request',
        message = requestData.requesterName .. ' wants to be your medical buddy',
        buttons = {
            {
                text = 'Accept',
                action = function()
                    TriggerServerEvent('olympus-medical:server:acceptBuddy', requestData.requesterId)
                end
            },
            {
                text = 'Decline',
                action = function()
                    -- Do nothing
                end
            }
        }
    })
end)

-- Medical invoice notification
RegisterNetEvent('olympus-medical:client:receiveInvoice')
AddEventHandler('olympus-medical:client:receiveInvoice', function(invoiceData)
    exports['olympus-ui']:ShowDialog({
        title = 'Medical Invoice',
        message = 'You have received a medical invoice from ' .. invoiceData.medicName .. ' for $' .. invoiceData.amount .. '\nReason: ' .. invoiceData.reason,
        buttons = {
            {
                text = 'Pay Invoice',
                action = function()
                    TriggerServerEvent('olympus-medical:server:payInvoice', invoiceData.invoiceId)
                end
            },
            {
                text = 'Decline',
                action = function()
                    exports['olympus-ui']:ShowNotification({
                        type = 'info',
                        title = 'Medical Invoice',
                        message = 'Invoice declined',
                        duration = 3000
                    })
                end
            }
        }
    })
end)

-- Update medic count
RegisterNetEvent('olympus-medical:client:updateMedicCount')
AddEventHandler('olympus-medical:client:updateMedicCount', function(count)
    -- Update UI with medic count if needed
end)

-- Initialize system on resource start
CreateThread(function()
    InitializeMedicalClient()
end)

-- Export functions
exports('IsMedic', function()
    return OlympusMedical.isMedic
end)

exports('GetMedicRank', function()
    return OlympusMedical.medicRank
end)

exports('IsOnDuty', function()
    return OlympusMedical.onDuty
end)

exports('IsDead', function()
    return OlympusMedical.isDead
end)

exports('GetDeathTime', function()
    return OlympusMedical.deathTime
end)

exports('RequestMedic', function()
    RequestMedicalAssistance()
end)

exports('RevivePlayer', function(targetId)
    if OlympusMedical.isMedic and OlympusMedical.onDuty then
        TriggerServerEvent('olympus-medical:server:revivePlayer', targetId)
    end
end)

exports('GiveDopamine', function(targetId)
    if OlympusMedical.isMedic and OlympusMedical.onDuty and OlympusMedical.medicRank >= 3 then
        TriggerServerEvent('olympus-medical:server:giveDopamine', targetId)
    end
end)

print("^2[Olympus Medical]^7 Client system loaded successfully!")
