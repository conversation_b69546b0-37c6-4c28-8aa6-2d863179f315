-- Olympus Community Goals - Client Main

CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end
    
    print("[Olympus Community Goals] Client initialized")
end)

exports('GetActiveGoals', function()
    return {}
end)

exports('GetPlayerContribution', function()
    return 0
end)

exports('OpenGoalsMenu', function()
    print("[Olympus Community Goals] Opening goals menu")
end)

print("[Olympus Community Goals] Client module loaded")
