fx_version 'cerulean'
game 'gta5'

name 'Olympus Civilian System'
description 'Complete civilian mechanics for Olympus Framework'
author 'Olympus Development Team'
version '1.0.0'

dependencies {
    'olympus-core'
}

shared_scripts {
    'config/shared.lua'
}

server_scripts {
    'server/main.lua'
}

client_scripts {
    'client/main.lua'
}

ui_page 'html/index.html'

files {
    'html/index.html',
    'html/css/*.css',
    'html/js/*.js',
    'html/img/**/*'
}

exports {
    'GetPlayerJob',
    'SetPlayerJob',
    'HasLicense',
    'GetLicenses',
    'CanDoActivity',
    'GetNearestJobLocation'
}

server_exports {
    'AddJobPayment',
    'RemoveJobPayment',
    'IssueLicense',
    'RevokeLicense',
    'LogCivilianActivity',
    'GetJobData'
}
