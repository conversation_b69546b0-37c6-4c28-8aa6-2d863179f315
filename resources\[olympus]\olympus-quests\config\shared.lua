-- Olympus Quests System - Complete Implementation
-- Based on Olympus Altis Life quest mechanics with exact specifications

Config = {}

-- Quest System Settings
Config.QuestSystem = {
    enabled = true,
    
    -- System Description
    description = "Quest are missions players can do to achieve in-game rewards",
    
    -- Quest Difficulty Range
    difficultyRange = {
        minimum = 300, -- 5 minutes minimum
        maximum = 7200 -- 2 hours maximum
    },
    
    -- Quest Types
    questTypes = {
        tutorial = true, -- How-to quests for new players
        easterEgg = true, -- Mini easter egg hunts
        standard = true -- Standard reward quests
    },
    
    -- Access Method
    access = {
        yMenu = {
            enabled = true,
            location = 'below_bank_symbol', -- Below bank symbol in Y-menu
            button = 'Quest Menu', -- Green "Quest Menu" button
            color = 'green'
        }
    },
    
    -- Quest Protection Rules
    protection = {
        enabled = true,
        tutorialProtection = true, -- Special protection for tutorial quest
        rulesReference = 'Chapter 22: Interacting with Whitelist Training / Player Tutorial'
    }
}

-- Quest Interface System
Config.QuestInterface = {
    enabled = true,
    
    -- Quest Menu
    questMenu = {
        enabled = true,
        
        -- Menu Features
        features = {
            questList = true, -- Show all available quests
            questDescription = true, -- Show quest descriptions
            questRewards = true, -- Show quest rewards
            startQuest = true, -- Start quest button
            forfeitQuest = true, -- Forfeit quest button
            
            -- Quest Information Display
            informationDisplay = {
                questName = true,
                questDescription = true,
                estimatedTime = true,
                rewardAmount = true,
                difficulty = true
            }
        }
    },
    
    -- Quest Progress GUI
    progressGUI = {
        enabled = true,
        position = 'left_side', -- Left side of screen
        
        -- GUI Features
        features = {
            stepByStep = true, -- Show each step
            currentStep = true, -- Highlight current step
            nextStepIndicator = true, -- Map indicator for next step
            progressBar = true, -- Overall progress bar
            
            -- Step Information
            stepInformation = {
                stepNumber = true,
                stepDescription = true,
                stepLocation = true,
                stepRequirements = true
            }
        }
    },
    
    -- Map Integration
    mapIntegration = {
        enabled = true,
        
        -- Map Markers
        mapMarkers = {
            nextStep = {
                enabled = true,
                icon = 'quest_marker',
                color = 'yellow',
                description = 'Next Quest Step'
            },
            
            questLocation = {
                enabled = true,
                icon = 'location_marker',
                color = 'blue',
                description = 'Quest Location'
            }
        }
    }
}

-- Tutorial Quest (Exact Olympus Implementation)
Config.TutorialQuest = {
    enabled = true,
    
    -- Quest Information
    information = {
        name = 'Tutorial Quest',
        description = 'New to the island? This is the perfect quest to start with. You will start off in Athira and learn the basic steps to make money.',
        reward = 250000, -- $250,000 reward
        estimatedTime = 1800, -- 30 minutes
        difficulty = 'beginner',
        startLocation = 'Athira'
    },
    
    -- Quest Steps (Exact Olympus Steps)
    steps = {
        [1] = {
            description = 'Walk to the clothing store and fetch a Carryall backpack',
            location = vector3(1400.0, 2600.0, 35.0), -- Athira clothing store
            requirement = 'obtain_carryall_backpack',
            mapMarker = true
        },
        
        [2] = {
            description = 'Walk over to the Market',
            location = vector3(1410.0, 2610.0, 35.0), -- Athira market
            requirement = 'reach_market',
            mapMarker = true
        },
        
        [3] = {
            description = 'Buy a pickaxe, some food, some water, and some Redgull from the market',
            location = vector3(1410.0, 2610.0, 35.0), -- Athira market
            requirement = 'purchase_items',
            items = {'pickaxe', 'food', 'water', 'redgull'},
            note = 'The Redgull will give you no stamina for 3 minutes when used and can be bound to custom action 11',
            mapMarker = true
        },
        
        [4] = {
            description = 'Walk over to the DMV',
            location = vector3(1420.0, 2620.0, 35.0), -- Athira DMV
            requirement = 'reach_dmv',
            mapMarker = true
        },
        
        [5] = {
            description = 'Buy a Driver License',
            location = vector3(1420.0, 2620.0, 35.0), -- Athira DMV
            requirement = 'purchase_driver_license',
            cost = 500, -- $500 driver license
            mapMarker = true
        },
        
        [6] = {
            description = 'Walk over to the Civilian Vehicle Garage',
            location = vector3(1430.0, 2630.0, 35.0), -- Athira garage
            requirement = 'reach_garage',
            mapMarker = true
        },
        
        [7] = {
            description = 'Take out an offroad from your garage, Press (U) to unlock it and get in the driver seat',
            location = vector3(1430.0, 2630.0, 35.0), -- Athira garage
            requirement = 'spawn_and_enter_offroad',
            vehicle = 'offroad',
            instructions = 'Press U to unlock, then enter driver seat',
            mapMarker = true
        },
        
        [8] = {
            description = 'Open your Map with (M) and find the Silver Mine. Then drive over to the silver mine',
            location = vector3(2500.0, 3000.0, 35.0), -- Silver mine location
            requirement = 'reach_silver_mine',
            instructions = 'Press M to open map, find Silver Mine, drive there',
            mapMarker = true
        },
        
        [9] = {
            description = 'While at the silver mine, press the Windows Key to gather unprocessed silver nuggets',
            location = vector3(2500.0, 3000.0, 35.0), -- Silver mine
            requirement = 'start_gathering_silver',
            instructions = 'Press Windows Key to gather. You gather faster if you run around while gathering. Press Tab to cancel gathering',
            mapMarker = true
        },
        
        [10] = {
            description = 'Continue gathering unprocessed silver till you have at least 40 silver',
            location = vector3(2500.0, 3000.0, 35.0), -- Silver mine
            requirement = 'gather_40_silver',
            targetAmount = 40,
            instructions = 'You gather faster if you run around while gathering. Press Tab to cancel gathering',
            mapMarker = true
        },
        
        [11] = {
            description = 'Press (T) while looking at your offroad to open its inventory',
            location = vector3(2500.0, 3000.0, 35.0), -- Silver mine
            requirement = 'open_vehicle_inventory',
            instructions = 'Look at your offroad and press T',
            mapMarker = false
        },
        
        [12] = {
            description = 'Fill the offroad\'s trunk with your unprocessed silver (40 Silver)',
            location = vector3(2500.0, 3000.0, 35.0), -- Silver mine
            requirement = 'store_silver_in_trunk',
            targetAmount = 40,
            mapMarker = false
        },
        
        [13] = {
            description = 'Continue filling the trunk until it is full (40 Silver)',
            location = vector3(2500.0, 3000.0, 35.0), -- Silver mine
            requirement = 'fill_trunk_completely',
            targetAmount = 40,
            instructions = 'You gather faster if you run around while gathering. Press Tab to cancel gathering',
            mapMarker = false
        },
        
        [14] = {
            description = 'Finish gathering by filling up your inventory',
            location = vector3(2500.0, 3000.0, 35.0), -- Silver mine
            requirement = 'fill_inventory_silver',
            instructions = 'You gather faster if you run around while gathering. Press Tab to cancel gathering',
            mapMarker = false
        },
        
        [15] = {
            description = 'Open your map with (M) and find the Silver Processor. Then drive over to the silver processor',
            location = vector3(2600.0, 3100.0, 35.0), -- Silver processor
            requirement = 'reach_silver_processor',
            instructions = 'Press M to open map, find Silver Processor, drive there',
            mapMarker = true
        },
        
        [16] = {
            description = 'Use your scroll wheel on the processor to start processing your inventory and turn the silver into ingots',
            location = vector3(2600.0, 3100.0, 35.0), -- Silver processor
            requirement = 'start_processing_silver',
            instructions = 'Use scroll wheel on processor to process silver into ingots',
            mapMarker = true
        },
        
        [17] = {
            description = 'Continue processing your silver till your inventory and trunk no longer has unprocessed silver',
            location = vector3(2600.0, 3100.0, 35.0), -- Silver processor
            requirement = 'process_all_silver',
            instructions = 'Process all unprocessed silver from inventory and trunk',
            mapMarker = true
        },
        
        [18] = {
            description = 'Open your map with (M) and find the Silver Trader. Then drive over to the silver trader',
            location = vector3(2700.0, 3200.0, 35.0), -- Silver trader
            requirement = 'reach_silver_trader',
            instructions = 'Press M to open map, find Silver Trader, drive there',
            mapMarker = true
        },
        
        [19] = {
            description = 'Sell all the silver ingots in your inventory and trunk to the silver trader',
            location = vector3(2700.0, 3200.0, 35.0), -- Silver trader
            requirement = 'sell_all_silver_ingots',
            instructions = 'Sell all processed silver ingots from inventory and trunk',
            mapMarker = true
        },
        
        [20] = {
            description = 'Go to the ATM by the Vehicle Car Shop',
            location = vector3(1440.0, 2640.0, 35.0), -- ATM location
            requirement = 'reach_atm',
            instructions = 'Marked Gas Stations always have at least one ATM',
            mapMarker = true
        },
        
        [21] = {
            description = 'Use the ATM by pressing Windows Key on it and depositing all your on-hand cash',
            location = vector3(1440.0, 2640.0, 35.0), -- ATM location
            requirement = 'deposit_cash_at_atm',
            instructions = 'Press Windows Key on ATM and deposit all cash',
            mapMarker = true
        },
        
        [22] = {
            description = 'Open your map with (M) and find the nearest Gun Store. Drive over to the gun store',
            location = vector3(1450.0, 2650.0, 35.0), -- Gun store
            requirement = 'reach_gun_store',
            instructions = 'Now that you have some money you could go to the nearby gas station to the south and upgrade your offroad',
            mapMarker = true
        },
        
        [23] = {
            description = 'Use the gun store and buy yourself a Firearms License and a Protector',
            location = vector3(1450.0, 2650.0, 35.0), -- Gun store
            requirement = 'purchase_firearms_license_and_protector',
            items = {'firearms_license', 'protector'},
            mapMarker = true
        },
        
        [24] = {
            description = 'Walk outside and speak with the Worker Protection License NPC',
            location = vector3(1460.0, 2660.0, 35.0), -- WPL NPC
            requirement = 'speak_with_wpl_npc',
            mapMarker = true
        },
        
        [25] = {
            description = 'Speak with the Vigilante NPC',
            location = vector3(1470.0, 2670.0, 35.0), -- Vigilante NPC
            requirement = 'speak_with_vigilante_npc',
            mapMarker = true
        },
        
        [26] = {
            description = 'Speak with the Rebel NPC',
            location = vector3(1480.0, 2680.0, 35.0), -- Rebel NPC
            requirement = 'speak_with_rebel_npc',
            mapMarker = true
        },
        
        [27] = {
            description = 'Speak with the Altis Police Department (APD) NPC',
            location = vector3(1490.0, 2690.0, 35.0), -- APD NPC
            requirement = 'speak_with_apd_npc',
            mapMarker = true
        },
        
        [28] = {
            description = 'Speak with the Rescue & Recovery (RNR) NPC',
            location = vector3(1500.0, 2700.0, 35.0), -- R&R NPC
            requirement = 'speak_with_rnr_npc',
            mapMarker = true,
            final = true -- Final step
        }
    }
}

-- Cement Run Quest (Exact Olympus Implementation)
Config.CementQuest = {
    enabled = true,
    
    information = {
        name = 'Cement Run Quest',
        description = 'Learn the cement processing run and earn money',
        reward = 150000, -- Estimated reward
        estimatedTime = 1200, -- 20 minutes
        difficulty = 'beginner',
        startLocation = 'Rock Quarry'
    },
    
    steps = {
        [1] = {
            description = 'Go to the Rock Quarry and gather rocks',
            location = vector3(3000.0, 2000.0, 35.0), -- Rock quarry
            requirement = 'gather_rocks',
            targetAmount = 50,
            mapMarker = true
        },
        
        [2] = {
            description = 'Process rocks into cement at the Cement Mixer',
            location = vector3(3100.0, 2100.0, 35.0), -- Cement mixer
            requirement = 'process_cement',
            mapMarker = true
        },
        
        [3] = {
            description = 'Sell cement at the Construction Site',
            location = vector3(3200.0, 2200.0, 35.0), -- Construction site
            requirement = 'sell_cement',
            mapMarker = true,
            final = true
        }
    }
}

-- Glass Run Quest (Exact Olympus Implementation)
Config.GlassQuest = {
    enabled = true,
    
    information = {
        name = 'Glass Run Quest',
        description = 'Learn the glass processing run and earn money',
        reward = 175000, -- Estimated reward
        estimatedTime = 1500, -- 25 minutes
        difficulty = 'beginner',
        startLocation = 'Sand Mine'
    },
    
    steps = {
        [1] = {
            description = 'Go to the Sand Mine and gather sand',
            location = vector3(2800.0, 1800.0, 35.0), -- Sand mine
            requirement = 'gather_sand',
            targetAmount = 50,
            mapMarker = true
        },
        
        [2] = {
            description = 'Process sand into glass at the Glass Factory',
            location = vector3(2900.0, 1900.0, 35.0), -- Glass factory
            requirement = 'process_glass',
            mapMarker = true
        },
        
        [3] = {
            description = 'Sell glass at the Glass Trader',
            location = vector3(3000.0, 2000.0, 35.0), -- Glass trader
            requirement = 'sell_glass',
            mapMarker = true,
            final = true
        }
    }
}

-- Meth Run Quest (Exact Olympus Implementation)
Config.MethQuest = {
    enabled = true,
    
    information = {
        name = 'Meth Run Quest',
        description = 'Learn the dangerous meth processing run',
        reward = 500000, -- Higher reward for illegal activity
        estimatedTime = 2400, -- 40 minutes
        difficulty = 'advanced',
        startLocation = 'Meth Lab',
        warning = 'This is an illegal activity and carries significant risks'
    },
    
    steps = {
        [1] = {
            description = 'Obtain the required meth processing license',
            location = vector3(1500.0, 1500.0, 35.0), -- License location
            requirement = 'obtain_meth_license',
            cost = 75000, -- $75,000 license cost
            mapMarker = true
        },
        
        [2] = {
            description = 'Gather the required materials for meth production',
            location = vector3(1600.0, 1600.0, 35.0), -- Materials location
            requirement = 'gather_meth_materials',
            items = {'pseudoephedrine', 'lithium_strips', 'anhydrous_ammonia'},
            mapMarker = true
        },
        
        [3] = {
            description = 'Process materials into meth at the Meth Lab',
            location = vector3(1700.0, 1700.0, 35.0), -- Meth lab
            requirement = 'process_meth',
            warning = 'Be careful - meth labs can explode!',
            mapMarker = true
        },
        
        [4] = {
            description = 'Sell meth at the Drug Dealer (avoid police!)',
            location = vector3(1800.0, 1800.0, 35.0), -- Drug dealer
            requirement = 'sell_meth',
            warning = 'This is illegal - avoid APD patrols!',
            mapMarker = true,
            final = true
        }
    }
}

-- Moonshine Run Quest (Exact Olympus Implementation)
Config.MoonshineQuest = {
    enabled = true,
    
    information = {
        name = 'Moonshine Run Quest',
        description = 'Learn the moonshine distilling process',
        reward = 400000, -- High reward for illegal activity
        estimatedTime = 2100, -- 35 minutes
        difficulty = 'intermediate',
        startLocation = 'Moonshine Still',
        warning = 'This is an illegal activity'
    },
    
    steps = {
        [1] = {
            description = 'Obtain moonshine processing license',
            location = vector3(2000.0, 2000.0, 35.0), -- License location
            requirement = 'obtain_moonshine_license',
            cost = 50000, -- $50,000 license cost
            mapMarker = true
        },
        
        [2] = {
            description = 'Gather corn and other ingredients',
            location = vector3(2100.0, 2100.0, 35.0), -- Corn field
            requirement = 'gather_moonshine_ingredients',
            items = {'corn', 'yeast', 'sugar'},
            mapMarker = true
        },
        
        [3] = {
            description = 'Distill moonshine at the Moonshine Still',
            location = vector3(2200.0, 2200.0, 35.0), -- Moonshine still
            requirement = 'distill_moonshine',
            mapMarker = true
        },
        
        [4] = {
            description = 'Sell moonshine at the Bootlegger',
            location = vector3(2300.0, 2300.0, 35.0), -- Bootlegger
            requirement = 'sell_moonshine',
            warning = 'Avoid APD - this is illegal!',
            mapMarker = true,
            final = true
        }
    }
}
