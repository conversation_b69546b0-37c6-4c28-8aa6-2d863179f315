fx_version 'cerulean'
game 'gta5'

name 'Olympus Auction House'
description 'Complete auction house system for player-to-player trading'
author 'Olympus Development Team'
version '1.0.0'

-- Dependencies
dependencies {
    'olympus-core',
    'olympus-ui',
    'oxmysql'
}

-- Shared scripts
shared_scripts {
    'config/shared.lua'
}

-- Client scripts
client_scripts {
    'client/main.lua'
}

-- Server scripts
server_scripts {
    'server/main.lua'
}

-- UI files
ui_page 'html/index.html'

files {
    'html/index.html',
    'html/css/*.css',
    'html/js/*.js',
    'html/images/*.png'
}

-- Exports
exports {
    'OpenAuctionHouse',
    'GetPlayerListings',
    'GetPlayerStorage'
}

server_exports {
    'CreateListing',
    'PurchaseListing',
    'RetractListing',
    'GetAuctionStats'
}
