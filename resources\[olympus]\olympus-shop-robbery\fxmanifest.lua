fx_version 'cerulean'
game 'gta5'

author 'Olympus Development'
description 'Olympus Shop Robbery System'
version '1.0.0'

dependencies {
    'olympus-core',
    'olympus-apd'
}

shared_scripts {
    'config/shared.lua'
}

client_scripts {
    'client/main.lua'
}

server_scripts {
    'server/main.lua'
}

exports {
    'IsShopBeingRobbed',
    'GetRobberyStatus',
    'StartRobbery'
}

server_exports {
    'ProcessRobbery',
    'GetShopRobberyData',
    'ResetShopRobbery'
}
