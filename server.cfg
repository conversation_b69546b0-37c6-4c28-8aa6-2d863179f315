# Olympus Altis Life FiveM Server Configuration
# Custom framework that perfectly replicates Olympus Altis Life from Arma 3

# Server Information
sv_hostname "Test"
sets sv_projectName "Life"
sets sv_projectDesc "Experience the authentic Life gameplay in FiveM with our custom framework"
sets tags "roleplay,life,custom-framework,economy,gangs,police,medical"
sets locale "en-US"

# Server Settings
sv_enforceGameBuild 2944
sv_licenseKey "pq8bljns1gg7rozaxhqm0vmvv4y0ly4z"
sv_maxclients 48
endpoint_add_tcp "0.0.0.0:30120"
endpoint_add_udp "0.0.0.0:30120"
set steam_webApiKey "none"
set resources_useSystemChat true
sv_endpointprivacy true

# OneSync Settings (Required for 200+ players)
## [txAdmin CFG validator]: onesync MUST only be set in the txAdmin settings page.
# set onesync on
set onesync_enableInfinity 1
set onesync_enableBeyond 1
set onesync_population 1

# Database Configuration
# Updated with common default MySQL credentials
# If your MySQL has a password for root, change the @ to :your_password
set mysql_connection_string "mysql://root:@localhost/olympus_altis_life?charset=utf8mb4"

# Core Resources
ensure mapmanager
ensure chat
ensure spawnmanager
ensure sessionmanager
ensure basic-gamemode
ensure hardcap
ensure rconlog

# Database Resources
ensure oxmysql

# Olympus Framework Loader (Must load first)
ensure olympus-loader

# Olympus Framework Core
ensure olympus-core

# Loading Screen
ensure olympus-loadscreen

# Faction Systems
ensure olympus-apd
ensure olympus-medical

# Gang and Cartel System
ensure olympus-gangs

# Economy System
ensure olympus-economy

# Civilian Jobs System
ensure olympus-civilian-jobs

# Fishing & Hunting System
ensure olympus-fishing-hunting

# Shop Robbery System
ensure olympus-shop-robbery

# Shipwreck & Treasure System
ensure olympus-shipwreck-treasure
ensure olympus-lottery
ensure olympus-player-interactions
ensure olympus-vehicle-interactions
ensure olympus-apd-search-seizure
ensure olympus-cartel-hideouts
ensure olympus-betting
ensure olympus-suicide-vest
ensure olympus-groups

# Vehicle System
ensure olympus-vehicles

# Events System
ensure olympus-events

# Conquest System
ensure olympus-conquest
ensure olympus-vigilante

# UI System
ensure olympus-ui

# Special Items System
ensure olympus-items

# Framework Configuration
set olympus_debug "true"
set olympus_max_players "200"
set olympus_starting_money "5000"
set olympus_max_money "999999999"
set olympus_respawn_time "300"
set olympus_nlr_time "900"
set olympus_max_inventory_slots "50"
set olympus_max_inventory_weight "100"

# APD Settings
set olympus_apd_max_online "50"
set olympus_apd_min_for_events "4"
set olympus_apd_processing_time "900"

# Medical Settings
set olympus_medical_max_online "25"
set olympus_medical_revive_time "10"

# Gang Settings
set olympus_max_gangs "20"
set olympus_max_gang_members "15"
set olympus_gang_creation_cost "100000"

# Economy Settings
set olympus_market_update_interval "1800"
set olympus_price_fluctuation "0.15"
set olympus_bank_interest_rate "0.001"
set olympus_tax_rate "0.05"

# Vehicle Settings
set olympus_max_vehicles_per_player "20"
set olympus_vehicle_despawn_time "1800"
set olympus_impound_cost "1000"
set olympus_fuel_consumption_rate "0.5"

# Event Settings
set olympus_federal_cooldown "3600"
set olympus_cartel_capture_time "900"

# Admin System
add_ace group.admin command allow
add_ace group.admin command.quit deny
add_principal identifier.fivem:534750 group.admin #zzz11

# ===== ADMIN SETUP FOR 110000111d3607a =====
add_ace group.admin command allow
add_ace group.admin olympus.admin allow
add_ace group.admin olympus.moderator allow
add_ace group.admin builtin.everyone allow
add_principal identifier.steam:110000111d3607a group.admin
add_ace identifier.steam:110000111d3607a command allow
add_ace identifier.steam:110000111d3607a olympus.admin allow
add_ace identifier.steam:110000111d3607a olympus.moderator allow
add_ace identifier.steam:110000111d3607a builtin.everyone allow

# Voice Chat Settings
setr voice_useNativeAudio true
setr voice_useSendingRangeOnly true
setr voice_defaultCycle "GRAVE"
setr voice_defaultVolume 0.3

# Performance Settings
set sv_scriptHookAllowed 0
set sv_useDirectListing true
set sv_anticheat "FairPlay"
set sv_anticheatLevel 1

# Server Messages (echo commands don't work in FiveM)
# Starting Olympus Altis Life Server...
# Framework Version: 1.0.0
# Max Players: 200
# Visit: https://olympus-entertainment.com
