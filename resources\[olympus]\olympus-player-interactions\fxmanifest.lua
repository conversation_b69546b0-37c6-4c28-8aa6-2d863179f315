fx_version 'cerulean'
game 'gta5'

author 'Olympus Development'
description 'Olympus Player Interaction System - Complete player-to-player interaction mechanics'
version '1.0.0'

dependencies {
    'olympus-core',
    'olympus-apd'
}

shared_scripts {
    'config/shared.lua'
}

client_scripts {
    'client/main.lua'
}

server_scripts {
    'server/main.lua'
}

exports {
    'RobPlayer',
    'EscortPlayer',
    'StopEscorting',
    'ToggleSurrender',
    'IsPlayerSurrendered',
    'IsPlayerEscorted',
    'IsPlayerBeingRobbed',
    'CanInteractWithPlayer'
}
