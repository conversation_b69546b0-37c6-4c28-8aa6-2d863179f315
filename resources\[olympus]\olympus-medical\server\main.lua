-- ========================================
-- OLYMPUS MEDICAL SYSTEM - SERVER MAIN
-- Complete recreation based on original Olympus medical mechanics
-- Handles R&R faction, medical procedures, revive system, and hospital operations
-- ========================================

local OlympusMedical = {}
OlympusMedical.OnlineMedics = {}
OlympusMedical.MedicBuddies = {}
OlympusMedical.ActiveInvoices = {}
OlympusMedical.MedicalRequests = {}

-- Configuration is loaded as shared script in fxmanifest.lua
-- local Config = require('config/shared')

-- Initialize medical system
function InitializeMedicalSystem()
    print("^2[Olympus Medical]^7 Initializing server medical system...")

    -- Initialize database tables if needed
    exports.oxmysql:execute('CREATE TABLE IF NOT EXISTS medical_logs (id INT AUTO_INCREMENT PRIMARY KEY, medic_identifier VARCHAR(50), patient_identifier VARCHAR(50), action_type VARCHAR(50), amount INT, timestamp INT)', {})
    exports.oxmysql:execute('CREATE TABLE IF NOT EXISTS medical_invoices (id INT AUTO_INCREMENT PRIMARY KEY, medic_identifier VARCHAR(50), patient_identifier VARCHAR(50), amount INT, reason VARCHAR(255), timestamp INT, paid BOOLEAN DEFAULT FALSE)', {})
    exports.oxmysql:execute('CREATE TABLE IF NOT EXISTS medical_stats (id INT AUTO_INCREMENT PRIMARY KEY, medic_identifier VARCHAR(50), revives INT DEFAULT 0, treatments INT DEFAULT 0, dopamine_given INT DEFAULT 0, response_time_avg FLOAT DEFAULT 0, timestamp INT)', {})

    print("^2[Olympus Medical]^7 Server medical system initialized!")
end

-- Check if player is medic
function IsPlayerMedic(source)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    return playerData and playerData.faction == Config.MedicalFaction
end

-- Get medic rank
function GetMedicRank(source)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    if playerData and playerData.faction == Config.MedicalFaction then
        return playerData.faction_rank or 1
    end
    return 0
end

-- Get online medics
function GetOnlineMedics()
    local medics = {}
    local players = GetPlayers()

    for _, playerId in ipairs(players) do
        local playerSource = tonumber(playerId)
        if IsPlayerMedic(playerSource) then
            local playerData = exports['olympus-core']:GetPlayerData(playerSource)
            if playerData and playerData.on_duty then
                table.insert(medics, {
                    id = playerSource,
                    name = playerData.name,
                    rank = playerData.faction_rank or 1,
                    coords = GetEntityCoords(GetPlayerPed(playerSource))
                })
            end
        end
    end

    return medics
end

-- Log medical action
function LogMedicalAction(medicSource, patientSource, actionType, amount)
    local medicData = exports['olympus-core']:GetPlayerData(medicSource)
    local patientData = exports['olympus-core']:GetPlayerData(patientSource)

    if medicData and patientData then
        exports.oxmysql:execute('INSERT INTO medical_logs (medic_identifier, patient_identifier, action_type, amount, timestamp) VALUES (?, ?, ?, ?, ?)', {
            medicData.identifier,
            patientData.identifier,
            actionType,
            amount or 0,
            os.time()
        })
    end
end

-- Utility function for table counting
function table.count(t)
    local count = 0
    for _ in pairs(t) do
        count = count + 1
    end
    return count
end

-- Medical duty toggle
RegisterServerEvent('olympus-medical:server:toggleDuty')
AddEventHandler('olympus-medical:server:toggleDuty', function()
    local source = source
    if not IsPlayerMedic(source) then return end

    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData then return end

    local newStatus = not (playerData.on_duty or false)

    -- Update player data
    exports['olympus-core']:SetPlayerData(source, 'on_duty', newStatus)

    -- Update online medics list
    if newStatus then
        OlympusMedical.OnlineMedics[source] = {
            name = playerData.name,
            rank = playerData.faction_rank or 1,
            coords = GetEntityCoords(GetPlayerPed(source))
        }
    else
        OlympusMedical.OnlineMedics[source] = nil
    end

    -- Notify client
    TriggerClientEvent('olympus-medical:client:dutyStatus', source, newStatus)

    -- Notify all players about medic count change
    local medicCount = table.count(OlympusMedical.OnlineMedics)
    TriggerClientEvent('olympus-medical:client:updateMedicCount', -1, medicCount)

    TriggerClientEvent('olympus:client:notify', source, {
        type = newStatus and 'success' or 'info',
        title = 'Medical Duty',
        message = newStatus and 'You are now on duty' or 'You are now off duty',
        duration = 3000
    })
end)

-- Revive player (based on original fn_revivePlayer.sqf)
RegisterServerEvent('olympus-medical:server:revivePlayer')
AddEventHandler('olympus-medical:server:revivePlayer', function(targetId, isAdminRevive, isMassRevive)
    local source = source

    -- Admin revive check
    if isAdminRevive then
        local playerData = exports['olympus-core']:GetPlayerData(source)
        if not playerData or (playerData.adminRank or 0) < 1 then
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Error',
                message = 'Insufficient admin permissions',
                duration = 5000
            })
            return
        end
    else
        -- Regular medic check
        if not IsPlayerMedic(source) then return end

        local playerData = exports['olympus-core']:GetPlayerData(source)
        if not playerData or not playerData.on_duty then
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Medical',
                message = 'You must be on duty to revive players',
                duration = 5000
            })
            return
        end
    end

    local targetData = exports['olympus-core']:GetPlayerData(targetId)
    if not targetData then return end

    -- Check if target is actually dead
    if not targetData.isDead then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Medical',
            message = 'This player is not unconscious',
            duration = 5000
        })
        return
    end

    -- Calculate payout based on original Olympus mechanics
    local payout = Config.ReviveCost
    local medicData = exports['olympus-core']:GetPlayerData(source)

    -- Check if in federal event area (reduced payout)
    local playerCoords = GetEntityCoords(GetPlayerPed(source))
    local inFederalArea = false

    -- Check federal event locations
    for _, event in pairs(Config.FederalEventLocations or {}) do
        if #(playerCoords - event.coords) < event.radius then
            inFederalArea = true
            break
        end
    end

    if inFederalArea then
        payout = payout * 0.5 -- 50% payout in federal areas
    end

    -- Handle buddy system payout split
    if OlympusMedical.MedicBuddies[source] then
        local buddyId = OlympusMedical.MedicBuddies[source]
        local buddyData = exports['olympus-core']:GetPlayerData(buddyId)

        if buddyData and #(GetEntityCoords(GetPlayerPed(source)) - GetEntityCoords(GetPlayerPed(buddyId))) < 2000 then
            -- Split payout with buddy
            local splitPayout = math.floor(payout * 0.5)

            -- Pay medic
            exports['olympus-core']:AddMoney(source, 'bank', splitPayout)

            -- Pay buddy
            exports['olympus-core']:AddMoney(buddyId, 'bank', splitPayout)

            TriggerClientEvent('olympus:client:notify', buddyId, {
                type = 'success',
                title = 'Medical Buddy',
                message = 'Received $' .. splitPayout .. ' from buddy revive',
                duration = 5000
            })
        else
            -- Buddy too far or offline, remove buddy and give full payout
            OlympusMedical.MedicBuddies[source] = nil
            exports['olympus-core']:AddMoney(source, 'bank', payout)

            TriggerClientEvent('olympus:client:notify', source, {
                type = 'warning',
                title = 'Medical Buddy',
                message = 'Buddy too far away or offline. Buddy removed.',
                duration = 5000
            })
        end
    else
        -- No buddy, full payout
        if not isAdminRevive then
            exports['olympus-core']:AddMoney(source, 'bank', payout)
        end
    end

    -- Revive the player
    TriggerClientEvent('olympus-medical:client:revivePlayer', targetId)

    -- Update player data
    exports['olympus-core']:SetPlayerData(targetId, 'isDead', false)
    exports['olympus-core']:SetPlayerData(targetId, 'deathTime', nil)

    -- Log the action
    if not isAdminRevive then
        LogMedicalAction(source, targetId, 'revive', payout)
    end

    -- Notify players
    if not isAdminRevive then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'success',
            title = 'Medical',
            message = 'Player revived successfully. Received $' .. payout,
            duration = 5000
        })
    end

    TriggerClientEvent('olympus:client:notify', targetId, {
        type = 'info',
        title = 'Medical',
        message = isAdminRevive and 'You have been revived by an administrator' or 'You have been revived by ' .. medicData.name,
        duration = 5000
    })

    -- Update statistics
    if not isAdminRevive then
        exports.oxmysql:execute('UPDATE medical_stats SET revives = revives + 1 WHERE medic_identifier = ?', {
            medicData.identifier
        }, function(result)
            if result.affectedRows == 0 then
                exports.oxmysql:execute('INSERT INTO medical_stats (medic_identifier, revives) VALUES (?, ?)', {
                    medicData.identifier,
                    1
                })
            end
        end)
    end
end)

-- Give dopamine (based on original fn_giveDopamine.sqf)
RegisterServerEvent('olympus-medical:server:giveDopamine')
AddEventHandler('olympus-medical:server:giveDopamine', function(targetId)
    local source = source
    if not IsPlayerMedic(source) then return end

    local medicData = exports['olympus-core']:GetPlayerData(source)
    local targetData = exports['olympus-core']:GetPlayerData(targetId)

    if not medicData or not targetData then return end

    -- Check if medic is on duty
    if not medicData.on_duty then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Medical',
            message = 'You must be on duty to administer dopamine',
            duration = 5000
        })
        return
    end

    -- Check if target needs dopamine (has epiActive status)
    if not targetData.epiActive then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Medical',
            message = 'This player does not require dopamine',
            duration = 5000
        })
        return
    end

    -- Check medic rank for dopamine (Senior Paramedic+ required)
    if (medicData.faction_rank or 1) < 3 then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Medical',
            message = 'Senior Paramedic rank or higher required for dopamine',
            duration = 5000
        })
        return
    end

    -- Calculate payout
    local payout = Config.DopamineCost
    local playerCoords = GetEntityCoords(GetPlayerPed(source))
    local inFederalArea = false

    -- Check federal event locations
    for _, event in pairs(Config.FederalEventLocations or {}) do
        if #(playerCoords - event.coords) < event.radius then
            inFederalArea = true
            break
        end
    end

    if inFederalArea then
        payout = payout * 0.5 -- 50% payout in federal areas
    end

    -- Handle buddy system
    if OlympusMedical.MedicBuddies[source] then
        local buddyId = OlympusMedical.MedicBuddies[source]
        local buddyData = exports['olympus-core']:GetPlayerData(buddyId)

        if buddyData and #(GetEntityCoords(GetPlayerPed(source)) - GetEntityCoords(GetPlayerPed(buddyId))) < 2000 then
            local splitPayout = math.floor(payout * 0.5)
            exports['olympus-core']:AddMoney(source, 'bank', splitPayout)
            exports['olympus-core']:AddMoney(buddyId, 'bank', splitPayout)
        else
            OlympusMedical.MedicBuddies[source] = nil
            exports['olympus-core']:AddMoney(source, 'bank', payout)
        end
    else
        exports['olympus-core']:AddMoney(source, 'bank', payout)
    end

    -- Clear epi status and heal target
    exports['olympus-core']:SetPlayerData(targetId, 'epiActive', false)
    exports['olympus-core']:SetPlayerData(targetId, 'epiTime', nil)
    TriggerClientEvent('olympus-medical:client:healPlayer', targetId, 100)

    -- Log the action
    LogMedicalAction(source, targetId, 'dopamine', payout)

    -- Notify players
    TriggerClientEvent('olympus:client:notify', source, {
        type = 'success',
        title = 'Medical',
        message = 'Dopamine administered to ' .. targetData.name .. '. Received $' .. payout,
        duration = 5000
    })

    TriggerClientEvent('olympus:client:notify', targetId, {
        type = 'info',
        title = 'Medical',
        message = 'You have received dopamine from ' .. medicData.name,
        duration = 5000
    })
end)

-- Medical request system (based on original fn_medicRequest.sqf)
RegisterServerEvent('olympus-medical:server:requestMedic')
AddEventHandler('olympus-medical:server:requestMedic', function()
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData then return end

    -- Check if player is dead
    if not playerData.isDead then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Medical',
            message = 'You must be unconscious to request medical assistance',
            duration = 5000
        })
        return
    end

    -- Check if already requested recently
    if OlympusMedical.MedicalRequests[source] and (os.time() - OlympusMedical.MedicalRequests[source]) < 150 then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Medical',
            message = 'You must wait before requesting medical assistance again',
            duration = 5000
        })
        return
    end

    -- Check if medics are online
    local onlineMedics = GetOnlineMedics()
    if #onlineMedics == 0 then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Medical',
            message = 'No medics are currently online',
            duration = 5000
        })
        return
    end

    -- Record request time
    OlympusMedical.MedicalRequests[source] = os.time()

    -- Get player coordinates
    local playerCoords = GetEntityCoords(GetPlayerPed(source))

    -- Notify all online medics
    for _, medic in pairs(onlineMedics) do
        TriggerClientEvent('olympus-medical:client:medicRequest', medic.id, {
            playerId = source,
            playerName = playerData.name,
            coords = playerCoords,
            timestamp = os.time()
        })
    end

    TriggerClientEvent('olympus:client:notify', source, {
        type = 'success',
        title = 'Medical',
        message = 'Medical assistance has been requested',
        duration = 5000
    })
end)

-- Medical buddy system (based on original fn_medicBuddy.sqf)
RegisterServerEvent('olympus-medical:server:medicBuddy')
AddEventHandler('olympus-medical:server:medicBuddy', function(targetId, action)
    local source = source
    if not IsPlayerMedic(source) then return end

    local medicData = exports['olympus-core']:GetPlayerData(source)
    local targetData = exports['olympus-core']:GetPlayerData(targetId)

    if not medicData or not targetData then return end

    if action == 0 then -- Request buddy
        -- Check if target is also a medic
        if not IsPlayerMedic(targetId) then
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Medical Buddy',
                message = 'Target must be a medic',
                duration = 5000
            })
            return
        end

        -- Check if already have a buddy
        if OlympusMedical.MedicBuddies[source] then
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Medical Buddy',
                message = 'You already have a buddy',
                duration = 5000
            })
            return
        end

        -- Send buddy request
        TriggerClientEvent('olympus-medical:client:buddyRequest', targetId, {
            requesterId = source,
            requesterName = medicData.name
        })

        TriggerClientEvent('olympus:client:notify', source, {
            type = 'info',
            title = 'Medical Buddy',
            message = 'Buddy request sent to ' .. targetData.name,
            duration = 5000
        })

    elseif action == 1 then -- End buddy
        if OlympusMedical.MedicBuddies[source] == targetId then
            OlympusMedical.MedicBuddies[source] = nil
            OlympusMedical.MedicBuddies[targetId] = nil

            TriggerClientEvent('olympus:client:notify', source, {
                type = 'info',
                title = 'Medical Buddy',
                message = 'Buddy agreement ended',
                duration = 5000
            })

            TriggerClientEvent('olympus:client:notify', targetId, {
                type = 'info',
                title = 'Medical Buddy',
                message = 'Buddy agreement ended by ' .. medicData.name,
                duration = 5000
            })
        end
    end
end)

-- Accept buddy request
RegisterServerEvent('olympus-medical:server:acceptBuddy')
AddEventHandler('olympus-medical:server:acceptBuddy', function(requesterId)
    local source = source
    if not IsPlayerMedic(source) then return end

    local medicData = exports['olympus-core']:GetPlayerData(source)
    local requesterData = exports['olympus-core']:GetPlayerData(requesterId)

    if not medicData or not requesterData then return end

    -- Set buddy relationship
    OlympusMedical.MedicBuddies[source] = requesterId
    OlympusMedical.MedicBuddies[requesterId] = source

    TriggerClientEvent('olympus:client:notify', source, {
        type = 'success',
        title = 'Medical Buddy',
        message = 'You are now buddies with ' .. requesterData.name,
        duration = 5000
    })

    TriggerClientEvent('olympus:client:notify', requesterId, {
        type = 'success',
        title = 'Medical Buddy',
        message = medicData.name .. ' accepted your buddy request',
        duration = 5000
    })
end)

-- Medical invoice system (based on original fn_medicInvoiceGive.sqf)
RegisterServerEvent('olympus-medical:server:giveInvoice')
AddEventHandler('olympus-medical:server:giveInvoice', function(targetId, amount, reason)
    local source = source
    if not IsPlayerMedic(source) then return end

    local medicData = exports['olympus-core']:GetPlayerData(source)
    local targetData = exports['olympus-core']:GetPlayerData(targetId)

    if not medicData or not targetData then return end

    -- Validate amount
    if not amount or amount <= 0 or amount > 5000000 then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Medical Invoice',
            message = 'Invalid invoice amount',
            duration = 5000
        })
        return
    end

    reason = reason or 'Medical services'

    -- Insert invoice into database
    exports.oxmysql:execute('INSERT INTO medical_invoices (medic_identifier, patient_identifier, amount, reason, timestamp) VALUES (?, ?, ?, ?, ?)', {
        medicData.identifier,
        targetData.identifier,
        amount,
        reason,
        os.time()
    }, function(invoiceId)
        if invoiceId then
            -- Send invoice to target
            TriggerClientEvent('olympus-medical:client:receiveInvoice', targetId, {
                invoiceId = invoiceId,
                medicName = medicData.name,
                amount = amount,
                reason = reason
            })

            TriggerClientEvent('olympus:client:notify', source, {
                type = 'success',
                title = 'Medical Invoice',
                message = 'Invoice sent to ' .. targetData.name .. ' for $' .. amount,
                duration = 5000
            })
        end
    end)
end)

-- Pay medical invoice
RegisterServerEvent('olympus-medical:server:payInvoice')
AddEventHandler('olympus-medical:server:payInvoice', function(invoiceId)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData then return end

    -- Get invoice details
    exports.oxmysql:query('SELECT * FROM medical_invoices WHERE id = ? AND patient_identifier = ? AND paid = FALSE', {
        invoiceId,
        playerData.identifier
    }, function(result)
        if result and result[1] then
            local invoice = result[1]

            -- Check if player has enough money
            if (playerData.money or 0) + (playerData.bank or 0) < invoice.amount then
                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'error',
                    title = 'Medical Invoice',
                    message = 'Insufficient funds to pay invoice',
                    duration = 5000
                })
                return
            end

            -- Process payment
            local success = exports['olympus-core']:RemoveMoney(source, 'bank', invoice.amount)
            if not success then
                success = exports['olympus-core']:RemoveMoney(source, 'cash', invoice.amount)
            end

            if success then
                -- Mark invoice as paid
                exports.oxmysql:execute('UPDATE medical_invoices SET paid = TRUE WHERE id = ?', {invoiceId})

                -- Find medic and pay them
                local players = GetPlayers()
                for _, playerId in ipairs(players) do
                    local playerSource = tonumber(playerId)
                    local medicData = exports['olympus-core']:GetPlayerData(playerSource)
                    if medicData and medicData.identifier == invoice.medic_identifier then
                        exports['olympus-core']:AddMoney(playerSource, 'bank', invoice.amount)

                        TriggerClientEvent('olympus:client:notify', playerSource, {
                            type = 'success',
                            title = 'Medical Invoice',
                            message = 'Invoice paid by ' .. playerData.name .. ' - Received $' .. invoice.amount,
                            duration = 5000
                        })
                        break
                    end
                end

                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'success',
                    title = 'Medical Invoice',
                    message = 'Invoice paid successfully - $' .. invoice.amount,
                    duration = 5000
                })
            end
        end
    end)
end)

-- Player disconnect cleanup
AddEventHandler('playerDropped', function()
    local source = source

    -- Clean up medical data
    OlympusMedical.OnlineMedics[source] = nil
    OlympusMedical.MedicalRequests[source] = nil

    -- Clean up buddy relationships
    if OlympusMedical.MedicBuddies[source] then
        local buddyId = OlympusMedical.MedicBuddies[source]
        OlympusMedical.MedicBuddies[buddyId] = nil
        OlympusMedical.MedicBuddies[source] = nil

        -- Notify buddy
        if buddyId then
            TriggerClientEvent('olympus:client:notify', buddyId, {
                type = 'warning',
                title = 'Medical Buddy',
                message = 'Your buddy has disconnected',
                duration = 5000
            })
        end
    end
end)

-- Initialize system on resource start
CreateThread(function()
    Wait(1000) -- Wait for core to load
    InitializeMedicalSystem()
end)

-- Export functions
exports('IsPlayerMedic', IsPlayerMedic)
exports('GetMedicRank', GetMedicRank)
exports('GetOnlineMedics', GetOnlineMedics)

exports('GetOnlineMedicCount', function()
    return table.count(OlympusMedical.OnlineMedics)
end)

exports('IsPlayerOnDuty', function(source)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    return playerData and playerData.on_duty or false
end)

exports('RevivePlayer', function(source, targetId, isAdminRevive)
    TriggerEvent('olympus-medical:server:revivePlayer', targetId, isAdminRevive or false, false)
end)

exports('GiveDopamine', function(source, targetId)
    TriggerEvent('olympus-medical:server:giveDopamine', targetId)
end)

exports('SendMedicRequest', function(source)
    TriggerEvent('olympus-medical:server:requestMedic')
end)

print("^2[Olympus Medical]^7 Server system loaded successfully!")
