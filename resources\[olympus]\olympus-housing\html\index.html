<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Olympus Housing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: transparent;
            color: white;
            margin: 0;
            padding: 0;
        }
        
        .housing-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            border-radius: 10px;
            padding: 20px;
            min-width: 500px;
            display: none;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div id="housing-container" class="housing-container">
        <h2>Housing System</h2>
        <p>Housing system loaded</p>
        <button class="btn" onclick="closeHousing()">Close</button>
    </div>

    <script>
        function closeHousing() {
            document.getElementById('housing-container').style.display = 'none';
        }
    </script>
</body>
</html>
