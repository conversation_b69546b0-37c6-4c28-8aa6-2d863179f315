-- ========================================
-- OLYMPUS GANG SYSTEM - SERVER MAIN
-- Complete recreation based on original Olympus dump
-- Matches original fn_insertGang, fn_updateMember, fn_gangBank functions
-- ========================================

local OlympusGangs = {}
OlympusGangs.ActiveGangs = {}

-- Gang configuration (matches original Olympus)
local GANG_CREATION_COST = 50000 -- $50k as per original
local GANG_OFFLINE_TAX_RATE = 0.08 -- 8% offline tax as per memories
local MAX_GANG_MEMBERS = 15 -- Original Olympus limit

-- Gang rank names (matches original Olympus hierarchy)
local GANG_RANKS = {
    [0] = "Recruit",
    [1] = "Member",
    [2] = "Trusted",
    [3] = "Lieutenant",
    [4] = "Captain",
    [5] = "Leader"
}

-- Initialize gang system
CreateThread(function()
    print("^2[Olympus Gangs]^7 Gang system initializing...")

    -- Wait for database to be ready
    while not exports['olympus-core']:IsDBReady() do
        Wait(100)
    end

    -- Load active gangs from database
    LoadActiveGangs()

    -- Start offline tax system
    StartOfflineTaxSystem()

    print("^2[Olympus Gangs]^7 Gang system initialized successfully!")
end)

-- Load active gangs from database - matches original gang loading
function LoadActiveGangs()
    exports['olympus-core']:FetchQuery([[
        SELECT g.*, COUNT(gm.id) as member_count
        FROM gangs g
        LEFT JOIN gangmembers gm ON g.id = gm.gangid
        WHERE g.active = 1
        GROUP BY g.id
    ]], {}, function(result)
        if result then
            for _, gang in ipairs(result) do
                OlympusGangs.ActiveGangs[gang.id] = gang
                print(string.format("^3[Olympus Gangs]^7 Loaded gang: %s (ID: %s, Members: %s)",
                    gang.name, gang.id, gang.member_count))
            end
        end
        print("^2[Olympus Gangs]^7 Loaded " .. (result and #result or 0) .. " active gangs")
    end)
end

-- Start offline tax system (8% tax as per memories)
function StartOfflineTaxSystem()
    CreateThread(function()
        while true do
            Wait(3600000) -- Check every hour

            -- Apply offline tax to gangs with offline members
            for gangId, gang in pairs(OlympusGangs.ActiveGangs) do
                ApplyOfflineTax(gangId)
            end
        end
    end)
end

-- Apply offline tax to gang
function ApplyOfflineTax(gangId)
    exports['olympus-core']:GetGangMembers(gangId, function(members)
        if not members then return end

        local offlineMembers = 0
        local totalMembers = #members

        -- Count offline members
        for _, member in ipairs(members) do
            local isOnline = false
            for _, playerId in ipairs(GetPlayers()) do
                if GetPlayerIdentifier(playerId, 0) == member.playerid then
                    isOnline = true
                    break
                end
            end
            if not isOnline then
                offlineMembers = offlineMembers + 1
            end
        end

        -- Apply tax if more than half the gang is offline
        if offlineMembers > (totalMembers / 2) then
            local gang = OlympusGangs.ActiveGangs[gangId]
            if gang and gang.bank > 0 then
                local taxAmount = math.floor(gang.bank * GANG_OFFLINE_TAX_RATE)
                if taxAmount > 0 then
                    exports['olympus-core']:UpdateGangBank(gangId, -taxAmount, function(success)
                        if success then
                            gang.bank = gang.bank - taxAmount
                            print(string.format("^3[Olympus Gangs]^7 Applied offline tax to gang %s: $%s", gang.name, taxAmount))
                        end
                    end)
                end
            end
        end
    end)
end

-- Create new gang - matches original fn_insertGang.sqf
RegisterServerEvent('olympus:server:createGang')
AddEventHandler('olympus:server:createGang', function(gangName)
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Gang Creation Failed',
            message = 'Player data not found'
        })
        return
    end

    -- Check if player already has a gang (matches original validation)
    if playerData.gang_id and playerData.gang_id > 0 then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Gang Creation Failed',
            message = 'You must leave or disband your current gang before creating a new one!'
        })
        return
    end

    -- Check if player has enough money (matches original cost check)
    if playerData.bankacc < GANG_CREATION_COST then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Gang Creation Failed',
            message = string.format('You need $%s in your bank account', GANG_CREATION_COST)
        })
        return
    end

    -- Validate gang name (matches original validation)
    if not gangName or gangName == "" or string.len(gangName) > 32 then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Gang Creation Failed',
            message = 'Invalid gang name (max 32 characters)'
        })
        return
    end

    -- Check for invalid characters (matches original SQF validation)
    if string.find(gangName, "'") or string.find(gangName, "call ") or
       string.find(gangName, "spawn ") or string.find(gangName, "execVM") then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Gang Creation Failed',
            message = 'Invalid characters in gang name'
        })
        return
    end

    -- Check if gang name already exists (matches original database check)
    exports['olympus-core']:FetchSingle([[
        SELECT id FROM gangs WHERE name = ? AND active = 1
    ]], {gangName}, function(existingGang)
        if existingGang then
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Gang Creation Failed',
                message = 'There is already a gang created with that name please pick another name.'
            })
            return
        end

        -- Check for inactive gang with same name (matches original reactivation logic)
        exports['olympus-core']:FetchSingle([[
            SELECT id FROM gangs WHERE name = ? AND active = 0
        ]], {gangName}, function(inactiveGang)
            local gangQuery, gangParams

            if inactiveGang then
                -- Reactivate existing gang
                gangQuery = "UPDATE gangs SET active = 1 WHERE id = ?"
                gangParams = {inactiveGang.id}
            else
                -- Create new gang
                gangQuery = "INSERT INTO gangs (name, bank, active) VALUES (?, 0, 1)"
                gangParams = {gangName}
            end

            exports['olympus-core']:ExecuteQuery(gangQuery, gangParams, function(success, result)
                if not success then
                    TriggerClientEvent('olympus:client:notify', source, {
                        type = 'error',
                        title = 'Gang Creation Failed',
                        message = 'Database error occurred'
                    })
                    return
                end

                local gangId = inactiveGang and inactiveGang.id or result.insertId

                -- Add creator as leader (rank 5) - matches original fn_insertGang
                exports['olympus-core']:CreateGang(identifier, GetPlayerName(source), gangName, function(memberSuccess)
                    if memberSuccess then
                        -- Deduct money from player bank
                        playerData.bankacc = playerData.bankacc - GANG_CREATION_COST
                        exports['olympus-core']:UpdatePlayerData(source, 'bankacc', playerData.bankacc)

                        -- Update player gang data
                        playerData.gang_id = gangId
                        playerData.gang_rank = 5
                        exports['olympus-core']:UpdatePlayerData(source, 'gang_id', gangId)
                        exports['olympus-core']:UpdatePlayerData(source, 'gang_rank', 5)

                        -- Add to active gangs
                        OlympusGangs.ActiveGangs[gangId] = {
                            id = gangId,
                            name = gangName,
                            bank = 0,
                            active = 1,
                            member_count = 1
                        }

                        -- Send success notification
                        TriggerClientEvent('olympus:client:notify', source, {
                            type = 'success',
                            title = 'Gang Created',
                            message = string.format('Gang "%s" has been created successfully! Cost: $%s', gangName, GANG_CREATION_COST)
                        })

                        -- Update client gang data
                        TriggerClientEvent('olympus:client:updateGangData', source, {
                            gang_id = gangId,
                            gang_name = gangName,
                            gang_rank = 5
                        })

                        print(string.format("^2[Olympus Gangs]^7 Gang '%s' created by %s (ID: %s)", gangName, GetPlayerName(source), gangId))
                    else
                        TriggerClientEvent('olympus:client:notify', source, {
                            type = 'error',
                            title = 'Gang Creation Failed',
                            message = 'Failed to add you as gang leader'
                        })
                    end
                end)
            end)
        end)
    end)
end)

-- Gang bank operations - matches original fn_gangBank.sqf
RegisterServerEvent('olympus:server:gangBank')
AddEventHandler('olympus:server:gangBank', function(mode, amount)
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData or not playerData.gang_id or playerData.gang_id <= 0 then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Gang Bank',
            message = 'You are not in a gang'
        })
        return
    end

    local gangId = playerData.gang_id
    local gang = OlympusGangs.ActiveGangs[gangId]

    if not gang then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Gang Bank',
            message = 'Gang data not found'
        })
        return
    end

    if mode == "get_balance" then
        -- Get gang bank balance (mode 0 in original)
        exports['olympus-core']:FetchSingle([[
            SELECT bank FROM gangs WHERE id = ?
        ]], {gangId}, function(result)
            if result then
                TriggerClientEvent('olympus:client:gangBankBalance', source, result.bank)
            end
        end)

    elseif mode == "deposit" then
        -- Deposit money to gang bank (mode 1 in original)
        if not amount or amount <= 0 then
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Gang Bank',
                message = 'Invalid deposit amount'
            })
            return
        end

        if playerData.cash < amount then
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Gang Bank',
                message = 'Insufficient cash'
            })
            return
        end

        -- Update gang bank and player cash
        exports['olympus-core']:UpdateGangBank(gangId, amount, function(success)
            if success then
                playerData.cash = playerData.cash - amount
                exports['olympus-core']:UpdatePlayerData(source, 'cash', playerData.cash)
                gang.bank = gang.bank + amount

                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'success',
                    title = 'Gang Bank',
                    message = string.format('Deposited $%s to gang bank', amount)
                })

                print(string.format("^3[Olympus Gangs]^7 %s deposited $%s to gang %s", GetPlayerName(source), amount, gang.name))
            else
                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'error',
                    title = 'Gang Bank',
                    message = 'Transaction failed'
                })
            end
        end)

    elseif mode == "withdraw" then
        -- Withdraw money from gang bank (mode 1 in original with negative amount)
        if not amount or amount <= 0 then
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Gang Bank',
                message = 'Invalid withdrawal amount'
            })
            return
        end

        -- Check if player has permission (rank 3+ can withdraw)
        if playerData.gang_rank < 3 then
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Gang Bank',
                message = 'You do not have permission to withdraw from gang bank'
            })
            return
        end

        if gang.bank < amount then
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Gang Bank',
                message = 'Insufficient gang funds'
            })
            return
        end

        -- Update gang bank and player cash
        exports['olympus-core']:UpdateGangBank(gangId, -amount, function(success)
            if success then
                playerData.cash = playerData.cash + amount
                exports['olympus-core']:UpdatePlayerData(source, 'cash', playerData.cash)
                gang.bank = gang.bank - amount

                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'success',
                    title = 'Gang Bank',
                    message = string.format('Withdrew $%s from gang bank', amount)
                })

                print(string.format("^3[Olympus Gangs]^7 %s withdrew $%s from gang %s", GetPlayerName(source), amount, gang.name))
            else
                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'error',
                    title = 'Gang Bank',
                    message = 'Transaction failed'
                })
            end
        end)
    end
end)

-- Gang member management - matches original fn_updateMember.sqf
RegisterServerEvent('olympus:server:gangMember')
AddEventHandler('olympus:server:gangMember', function(action, targetIdentifier, newRank, targetName)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData or not playerData.gang_id or playerData.gang_id <= 0 then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Gang Management',
            message = 'You are not in a gang'
        })
        return
    end

    local gangId = playerData.gang_id
    local gang = OlympusGangs.ActiveGangs[gangId]

    if not gang then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Gang Management',
            message = 'Gang data not found'
        })
        return
    end

    if action == "invite" then
        -- Invite player to gang (matches original mode 0)
        if playerData.gang_rank < 4 then
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Gang Management',
                message = 'You do not have permission to invite members'
            })
            return
        end

        local targetSource = nil
        for _, playerId in ipairs(GetPlayers()) do
            if GetPlayerIdentifier(playerId, 0) == targetIdentifier then
                targetSource = tonumber(playerId)
                break
            end
        end

        if not targetSource then
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Gang Management',
                message = 'Target player not found online'
            })
            return
        end

        local targetData = exports['olympus-core']:GetPlayerData(targetSource)
        if not targetData then
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Gang Management',
                message = 'Target player data not found'
            })
            return
        end

        if targetData.gang_id and targetData.gang_id > 0 then
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Gang Management',
                message = 'Target player is already in a gang'
            })
            return
        end

        -- Check gang member limit
        exports['olympus-core']:GetGangMembers(gangId, function(members)
            if members and #members >= MAX_GANG_MEMBERS then
                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'error',
                    title = 'Gang Management',
                    message = 'Gang is at maximum capacity'
                })
                return
            end

            -- Send invitation to target player
            TriggerClientEvent('olympus:client:gangInvite', targetSource, {
                gang_name = gang.name,
                gang_id = gangId,
                inviter_name = GetPlayerName(source),
                rank = newRank or 0
            })

            TriggerClientEvent('olympus:client:notify', source, {
                type = 'success',
                title = 'Gang Management',
                message = string.format('Invitation sent to %s', GetPlayerName(targetSource))
            })
        end)

    elseif action == "kick" then
        -- Kick player from gang (matches original mode 1/2 with rank -1)
        if playerData.gang_rank < 4 then
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Gang Management',
                message = 'You do not have permission to kick members'
            })
            return
        end

        -- Update member in database
        exports['olympus-core']:UpdateGangMember(targetIdentifier, "", -1, -1, function(success)
            if success then
                -- Update online player if they're connected
                local targetSource = nil
                for _, playerId in ipairs(GetPlayers()) do
                    if GetPlayerIdentifier(playerId, 0) == targetIdentifier then
                        targetSource = tonumber(playerId)
                        break
                    end
                end

                if targetSource then
                    local targetData = exports['olympus-core']:GetPlayerData(targetSource)
                    if targetData then
                        targetData.gang_id = -1
                        targetData.gang_rank = -1
                        exports['olympus-core']:UpdatePlayerData(targetSource, 'gang_id', -1)
                        exports['olympus-core']:UpdatePlayerData(targetSource, 'gang_rank', -1)

                        TriggerClientEvent('olympus:client:updateGangData', targetSource, {
                            gang_id = -1,
                            gang_name = "",
                            gang_rank = -1
                        })

                        TriggerClientEvent('olympus:client:notify', targetSource, {
                            type = 'error',
                            title = 'Gang',
                            message = 'You have been kicked from the gang'
                        })
                    end
                end

                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'success',
                    title = 'Gang Management',
                    message = string.format('Successfully kicked %s from gang', targetName or "player")
                })

                print(string.format("^3[Olympus Gangs]^7 %s kicked %s from gang %s", GetPlayerName(source), targetName or targetIdentifier, gang.name))
            else
                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'error',
                    title = 'Gang Management',
                    message = 'Failed to kick member'
                })
            end
        end)
    end
end)

-- Accept gang invitation
RegisterServerEvent('olympus:server:acceptGangInvite')
AddEventHandler('olympus:server:acceptGangInvite', function(gangId, gangName, rank)
    local source = source
    local identifier = GetPlayerIdentifier(source, 0)
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData then return end

    -- Check if player already has a gang
    if playerData.gang_id and playerData.gang_id > 0 then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Gang Invitation',
            message = 'You are already in a gang'
        })
        return
    end

    -- Add player to gang
    exports['olympus-core']:UpdateGangMember(identifier, gangName, gangId, rank or 0, function(success)
        if success then
            -- Update player data
            playerData.gang_id = gangId
            playerData.gang_rank = rank or 0
            exports['olympus-core']:UpdatePlayerData(source, 'gang_id', gangId)
            exports['olympus-core']:UpdatePlayerData(source, 'gang_rank', rank or 0)

            -- Update client
            TriggerClientEvent('olympus:client:updateGangData', source, {
                gang_id = gangId,
                gang_name = gangName,
                gang_rank = rank or 0
            })

            TriggerClientEvent('olympus:client:notify', source, {
                type = 'success',
                title = 'Gang Invitation',
                message = string.format('You have joined %s as %s', gangName, GANG_RANKS[rank or 0])
            })

            print(string.format("^2[Olympus Gangs]^7 %s joined gang %s", GetPlayerName(source), gangName))
        else
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Gang Invitation',
                message = 'Failed to join gang'
            })
        end
    end)
end)

-- Get gang members list
RegisterServerEvent('olympus:server:getGangMembers')
AddEventHandler('olympus:server:getGangMembers', function()
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData or not playerData.gang_id or playerData.gang_id <= 0 then
        return
    end

    exports['olympus-core']:GetGangMembers(playerData.gang_id, function(members)
        if members then
            -- Add online status to members
            for _, member in ipairs(members) do
                member.online = false
                member.rank_name = GANG_RANKS[member.rank] or "Unknown"

                for _, playerId in ipairs(GetPlayers()) do
                    if GetPlayerIdentifier(playerId, 0) == member.playerid then
                        member.online = true
                        break
                    end
                end
            end

            TriggerClientEvent('olympus:client:gangMembersList', source, members)
        end
    end)
end)

-- Exports for other resources
exports('GetGangData', function(gangId)
    return OlympusGangs.ActiveGangs[gangId] or nil
end)

exports('GetPlayerGang', function(source)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    if playerData and playerData.gang_id and playerData.gang_id > 0 then
        return OlympusGangs.ActiveGangs[playerData.gang_id]
    end
    return nil
end)

exports('GetGangRankName', function(rank)
    return GANG_RANKS[rank] or "Unknown"
end)

exports('IsPlayerInGang', function(source)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    return playerData and playerData.gang_id and playerData.gang_id > 0
end)

exports('GetGangCreationCost', function()
    return GANG_CREATION_COST
end)

-- Gang creation command (for testing/admin use)
RegisterCommand('creategang', function(source, args, rawCommand)
    if source == 0 then return end -- Console only

    if #args < 1 then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Usage',
            message = '/creategang [name]'
        })
        return
    end

    local gangName = table.concat(args, " ")
    TriggerEvent('olympus:server:createGang', gangName)
end, false)

-- Territory data request handler
RegisterServerEvent('olympus:server:requestTerritoryData')
AddEventHandler('olympus:server:requestTerritoryData', function()
    local source = source

    -- Get territories from territory system
    if exports['olympus-gangs'] and exports['olympus-gangs']['GetActiveTerritories'] then
        local territories = exports['olympus-gangs']:GetActiveTerritories()
        TriggerClientEvent('olympus:client:updateTerritories', source, territories)
    end
end)

-- Gang member list request handler
RegisterServerEvent('olympus:server:getGangMembers')
AddEventHandler('olympus:server:getGangMembers', function()
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData or not playerData.gang_id or playerData.gang_id <= 0 then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Gang Members',
            message = 'You are not in a gang'
        })
        return
    end

    -- Get gang members from database
    exports['olympus-core']:GetGangMembers(playerData.gang_id, function(members)
        if members then
            -- Add online status
            for _, member in ipairs(members) do
                member.online = false
                for _, playerId in ipairs(GetPlayers()) do
                    local onlinePlayerData = exports['olympus-core']:GetPlayerData(tonumber(playerId))
                    if onlinePlayerData and onlinePlayerData.identifier == member.playerid then
                        member.online = true
                        break
                    end
                end

                -- Add rank name
                member.rank_name = GANG_RANKS[member.rank] or "Unknown"
            end

            TriggerClientEvent('olympus:client:gangMembersList', source, members)
        else
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Gang Members',
                message = 'Failed to retrieve gang members'
            })
        end
    end)
end)

print("^2[Olympus Gangs]^7 Server main loaded successfully!")
