-- Olympus Items - Lockpick Client
-- Based on original fn_lockpick.sqf from Olympus Altis Life

local isLockpicking = false
local lockpickTarget = nil

-- Use lockpick function
function UseLockpick(target)
    if isLockpicking then
        exports['olympus-items']:ShowNotification("You cannot spam lockpicks!", "error")
        return false
    end
    
    local ped = PlayerPedId()
    local vehicle = target or GetVehiclePedIsLookingAt(ped)
    
    if not vehicle or vehicle == 0 then
        exports['olympus-items']:ShowNotification("No valid target found!", "error")
        return false
    end
    
    -- Check if player is in a vehicle
    if IsPedInAnyVehicle(ped, false) then
        exports['olympus-items']:ShowNotification("You cannot lockpick while in a vehicle!", "error")
        return false
    end
    
    -- Check distance
    local playerPos = GetEntityCoords(ped)
    local vehiclePos = GetEntityCoords(vehicle)
    local distance = #(playerPos - vehiclePos)
    
    if distance > 5.0 then
        exports['olympus-items']:ShowNotification("You are too far from the vehicle!", "error")
        return false
    end
    
    -- Check if vehicle is valid for lockpicking
    if not IsValidLockpickTarget(vehicle) then
        exports['olympus-items']:ShowNotification("You cannot lockpick this vehicle!", "error")
        return false
    end
    
    -- Check if player already has access
    if DoesPlayerHaveVehicleAccess(vehicle) then
        exports['olympus-items']:ShowNotification("You already have access to this vehicle!", "error")
        return false
    end
    
    -- Start lockpicking process
    return StartLockpicking(vehicle)
end

-- Check if vehicle is valid for lockpicking
function IsValidLockpickTarget(vehicle)
    if not DoesEntityExist(vehicle) then return false end
    if not IsEntityAVehicle(vehicle) then return false end
    
    -- Check if it's a restricted vehicle type
    local model = GetEntityModel(vehicle)
    local restrictedModels = {
        `rhino`, `khanjali`, `apc`, `barrage`, `chernobog`, `halftrack`, `minitank`, `scarab`, `scarab2`, `scarab3`
    }
    
    for _, restrictedModel in ipairs(restrictedModels) do
        if model == restrictedModel then
            return false
        end
    end
    
    return true
end

-- Check if player has vehicle access
function DoesPlayerHaveVehicleAccess(vehicle)
    -- This would typically check against the vehicle ownership system
    -- For now, we'll check if the vehicle is unlocked
    return GetVehicleDoorLockStatus(vehicle) == 1 -- Unlocked
end

-- Get vehicle player is looking at
function GetVehiclePedIsLookingAt(ped)
    local coords = GetEntityCoords(ped)
    local forward = GetEntityForwardVector(ped)
    local destination = coords + forward * 5.0
    
    local rayHandle = StartExpensiveSynchronousShapeTestLosProbe(
        coords.x, coords.y, coords.z,
        destination.x, destination.y, destination.z,
        10, ped, 0
    )
    
    local _, hit, _, _, entity = GetShapeTestResult(rayHandle)
    
    if hit and IsEntityAVehicle(entity) then
        return entity
    end
    
    return nil
end

-- Start lockpicking process
function StartLockpicking(vehicle)
    isLockpicking = true
    lockpickTarget = vehicle
    
    local ped = PlayerPedId()
    local playerData = exports['olympus-items']:GetPlayerData()
    
    -- Get lockpick skill level
    local skillLevel = 0
    if playerData and playerData.skills then
        skillLevel = playerData.skills.lockpicking or 0
    end
    
    -- Calculate success chance
    local baseChance = Config.Items.lockpick.successChance.base
    local successChance = baseChance
    
    if skillLevel >= 2 then
        successChance = Config.Items.lockpick.successChance.tier2
    elseif skillLevel >= 1 then
        successChance = Config.Items.lockpick.successChance.tier1
    end
    
    -- Play animation
    exports['olympus-items']:PlayAnimation(Config.Animations.lockpick)
    
    -- Show progress bar
    local progressConfig = Config.ProgressBar.lockpick
    
    exports['olympus-items']:ShowProgressBar(progressConfig, function()
        -- Progress completed
        CompleteLockpicking(vehicle, successChance)
    end, function()
        -- Progress cancelled
        CancelLockpicking()
    end)
    
    return true
end

-- Complete lockpicking
function CompleteLockpicking(vehicle, successChance)
    isLockpicking = false
    lockpickTarget = nil
    
    -- Clear animation
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    -- Check if still in range
    local playerPos = GetEntityCoords(ped)
    local vehiclePos = GetEntityCoords(vehicle)
    local distance = #(playerPos - vehiclePos)
    
    if distance > 5.0 then
        exports['olympus-items']:ShowNotification("You moved too far away!", "error")
        return
    end
    
    -- Roll for success
    local roll = math.random(1, 100)
    local success = roll <= successChance
    
    if success then
        -- Success - unlock vehicle and give access
        SetVehicleDoorsLocked(vehicle, 1) -- Unlock
        
        -- Notify server of successful lockpick
        TriggerServerEvent('olympus-items:server:lockpickSuccess', {
            vehicle = NetworkGetNetworkIdFromEntity(vehicle),
            plate = GetVehicleNumberPlateText(vehicle)
        })
        
        exports['olympus-items']:ShowNotification("Successfully picked the lock!", "success")
        
        -- Add to player's vehicle list (temporary access)
        TriggerEvent('olympus-vehicles:client:addTempAccess', vehicle)
        
    else
        -- Failure - notify police and consume item
        TriggerServerEvent('olympus-items:server:lockpickFailed', {
            vehicle = NetworkGetNetworkIdFromEntity(vehicle),
            plate = GetVehicleNumberPlateText(vehicle),
            location = GetEntityCoords(ped)
        })
        
        exports['olympus-items']:ShowNotification("Failed to pick the lock!", "error")
    end
    
    -- Consume lockpick (server handles durability)
    TriggerServerEvent('olympus-items:server:consumeItem', 'lockpick', success)
end

-- Cancel lockpicking
function CancelLockpicking()
    isLockpicking = false
    lockpickTarget = nil
    
    -- Clear animation
    local ped = PlayerPedId()
    ClearPedTasks(ped)
    
    exports['olympus-items']:ShowNotification("Lockpicking cancelled!", "info")
end

-- Check if player moved too far during lockpicking
CreateThread(function()
    while true do
        if isLockpicking and lockpickTarget then
            local ped = PlayerPedId()
            local playerPos = GetEntityCoords(ped)
            local vehiclePos = GetEntityCoords(lockpickTarget)
            local distance = #(playerPos - vehiclePos)
            
            if distance > 5.0 then
                CancelLockpicking()
            end
            
            -- Check if player is in a vehicle
            if IsPedInAnyVehicle(ped, false) then
                CancelLockpicking()
            end
        end
        
        Wait(500)
    end
end)

-- Export functions
exports('UseLockpick', UseLockpick)

-- Event handlers
RegisterNetEvent('olympus-items:client:lockpickInterrupted', function()
    if isLockpicking then
        CancelLockpicking()
    end
end)
