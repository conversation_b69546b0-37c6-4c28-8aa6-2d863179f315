-- ========================================
-- OLYMPUS UI FRAMEWORK - SERVER MAIN
-- Based on Original Olympus Altis Life
-- ========================================

local OlympusUI = {}

-- ========================================
-- INITIALIZATION
-- ========================================

CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end
    
    print("[Olympus UI] Server initialized")
end)

-- ========================================
-- NOTIFICATION SYSTEM
-- ========================================

function OlympusUI.Notify(source, message, type, duration)
    local data = {
        message = message,
        type = type or 'info',
        duration = duration or 5000
    }
    
    TriggerClientEvent('olympus-ui:client:showNotification', source, data)
end

function OlympusUI.NotifyAll(message, type, duration)
    local data = {
        message = message,
        type = type or 'info',
        duration = duration or 5000
    }
    
    TriggerClientEvent('olympus-ui:client:showNotification', -1, data)
end

-- ========================================
-- TITLE TEXT SYSTEM (Based on Original fn_titleNotification.sqf)
-- ========================================

function OlympusUI.ShowTitleText(source, text, duration)
    local data = {
        text = text,
        duration = duration or 5000
    }
    
    TriggerClientEvent('olympus-ui:client:showTitleText', source, data)
end

function OlympusUI.ShowTitleTextAll(text, duration)
    local data = {
        text = text,
        duration = duration or 5000
    }
    
    TriggerClientEvent('olympus-ui:client:showTitleText', -1, data)
end

-- ========================================
-- CLIENT MESSAGE SYSTEM (Based on Original fn_clientMessage.sqf)
-- ========================================

function OlympusUI.SendClientMessage(source, message, from, messageType, requester, cop2ems)
    messageType = messageType or 0
    cop2ems = cop2ems or false
    
    -- Encode special characters (based on original)
    local function encodeText(text)
        if not text then return "" end
        text = string.gsub(text, "&", "&amp;")
        text = string.gsub(text, "<", "&lt;")
        text = string.gsub(text, ">", "&gt;")
        text = string.gsub(text, '"', "&quot;")
        text = string.gsub(text, "'", "&apos;")
        return text
    end
    
    message = encodeText(message)
    from = encodeText(from)
    
    local notificationData = {}
    local titleText = ""
    
    if messageType == 0 then
        -- Private Message
        notificationData = {
            message = string.format("You Received A New Private Message From %s", from),
            type = "info",
            duration = 5000
        }
        titleText = string.format("<t color='#FFCC00'><t size='2'><t align='center'>New Message<br/><br/><t color='#33CC33'><t align='left'><t size='1'>To: <t color='#ffffff'>You<br/><t color='#33CC33'>From: <t color='#ffffff'>%s<br/><br/><t color='#33CC33'>Message:<br/><t color='#ffffff'>%s", from, message)
        
    elseif messageType == 1 then
        -- APD Dispatch
        notificationData = {
            message = string.format("New Police Report From: %s", from),
            type = "police",
            duration = 5000
        }
        titleText = string.format("<t color='#316dff'><t size='2'><t align='center'>New Dispatch<br/><br/><t color='#33CC33'><t align='left'><t size='1'>To: <t color='#ffffff'>All Officers<br/><t color='#33CC33'>From: <t color='#ffffff'>%s<br/><br/><t color='#33CC33'>Message:<br/><t color='#ffffff'>%s", from, message)
        
    elseif messageType == 2 then
        -- Staff Request
        notificationData = {
            message = string.format("%s Has Requested A Staff Member!", from),
            type = "admin",
            duration = 5000
        }
        titleText = string.format("<t color='#ffcefe'><t size='2'><t align='center'>Staff Request<br/><br/><t color='#33CC33'><t align='left'><t size='1'>To: <t color='#ffffff'>Olympus Staff<br/><t color='#33CC33'>From: <t color='#ffffff'>%s<br/><br/><t color='#33CC33'>Message:<br/><t color='#ffffff'>%s", from, message)
        
    elseif messageType == 3 or messageType == 4 or messageType == 11 then
        -- Staff Message
        local recipient = "You"
        if messageType == 4 then recipient = "All Players" end
        if messageType == 11 then recipient = "All Civilians" end
        
        notificationData = {
            message = "You Have Received A Message From A Staff Member!",
            type = "admin",
            duration = 5000
        }
        titleText = string.format("<t color='#FF0000'><t size='2'><t align='center'>Staff Message<br/><br/><t color='#33CC33'><t align='left'><t size='1'>To: <t color='#ffffff'>%s<br/><t color='#33CC33'>From: <t color='#ffffff'>Olympus Staff<br/><br/><t color='#33CC33'>Message:<br/><t color='#ffffff'>%s", recipient, message)
        
    elseif messageType == 5 then
        -- R&R Request
        notificationData = {
            message = string.format("R.N.R. Request from %s", from),
            type = "medical",
            duration = 5000
        }
        
        if cop2ems then
            titleText = string.format("<t color='#316dff'><t size='2'><t align='center'>R.N.R. Request<br/><br/><t color='#33CC33'><t align='left'><t size='1'>To: <t color='#ffffff'>All R.N.R. Units<br/><t color='#33CC33'>From: <t color='#316dff'>%s [APD OFFICER]<br/><br/><t color='#33CC33'>Message:<br/><t color='#ffffff'>%s", from, message)
        else
            titleText = string.format("<t color='#FFCC00'><t size='2'><t align='center'>R.N.R. Request<br/><br/><t color='#33CC33'><t align='left'><t size='1'>To: <t color='#ffffff'>All R.N.R. Units<br/><t color='#33CC33'>From: <t color='#ffffff'>%s<br/><br/><t color='#33CC33'>Message:<br/><t color='#ffffff'>%s", from, message)
        end
        
    elseif messageType == 7 or messageType == 8 then
        -- Event Message
        notificationData = {
            message = string.format("Event Message from %s", from),
            type = "info",
            duration = 5000
        }
        
        local recipient = messageType == 7 and "All Players" or "All Participants"
        titleText = string.format("<t color='#00CCCC'><t size='2'><t align='center'>Event Message<br/><br/><t color='#33CC33'><t align='left'><t size='1'>To: <t color='#ffffff'>%s<br/><t color='#33CC33'>From: <t color='#ffffff'>%s<br/><br/><t color='#33CC33'>Message:<br/><t color='#ffffff'>%s", recipient, from, message)
        
    elseif messageType == 9 then
        -- Panic Button
        notificationData = {
            message = string.format("New Panic Button Activation From: %s", from),
            type = "police",
            duration = 5000
        }
        titleText = string.format("<t color='#316dff'><t size='2'><t align='center'>PANIC BUTTON ALERT<br/><br/><t color='#33CC33'><t align='left'><t size='1'>To: <t color='#ffffff'>All Officers<br/><t color='#33CC33'>From: <t color='#ffffff'>%s<br/><br/><t color='#33CC33'>Message:<br/><t color='#ffffff'>%s", from, message)
        
    elseif messageType == 10 then
        -- R&R Response
        notificationData = {
            message = string.format("New RnR Response From: %s", from),
            type = "medical",
            duration = 5000
        }
        titleText = message
    end
    
    -- Send notification
    TriggerClientEvent('olympus-ui:client:showNotification', source, notificationData)
    
    -- Send title text
    if titleText ~= "" then
        TriggerClientEvent('olympus-ui:client:showTitleText', source, {
            text = titleText,
            duration = 5000
        })
    end
end

-- ========================================
-- Y MENU SYSTEM
-- ========================================

function OlympusUI.ShowYMenu(source, data)
    TriggerClientEvent('olympus-ui:client:showYMenu', source, data or {})
end

function OlympusUI.HideYMenu(source)
    TriggerClientEvent('olympus-ui:client:hideYMenu', source)
end

function OlympusUI.UpdateYMenuContent(source, data)
    TriggerClientEvent('olympus-ui:client:updateYMenuContent', source, data)
end

-- ========================================
-- EXPORTS
-- ========================================

exports('Notify', OlympusUI.Notify)
exports('NotifyAll', OlympusUI.NotifyAll)
exports('ShowTitleText', OlympusUI.ShowTitleText)
exports('ShowTitleTextAll', OlympusUI.ShowTitleTextAll)
exports('SendClientMessage', OlympusUI.SendClientMessage)
exports('ShowYMenu', OlympusUI.ShowYMenu)
exports('HideYMenu', OlympusUI.HideYMenu)
exports('UpdateYMenuContent', OlympusUI.UpdateYMenuContent)

-- ========================================
-- EVENT HANDLERS
-- ========================================

RegisterServerEvent('olympus-ui:server:requestYMenuContent', function(tabId)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)
    
    if not playerData then return end
    
    local content = ""
    
    -- Generate content based on tab
    if tabId == 'licenses' then
        content = OlympusUI.GenerateLicensesContent(playerData)
    elseif tabId == 'keychain' then
        content = OlympusUI.GenerateKeychainContent(playerData)
    elseif tabId == 'gang' then
        content = OlympusUI.GenerateGangContent(playerData)
    elseif tabId == 'cell' then
        content = OlympusUI.GenerateCellPhoneContent(playerData)
    elseif tabId == 'wanted' then
        content = OlympusUI.GenerateWantedContent(playerData)
    elseif tabId == 'admin' then
        content = OlympusUI.GenerateAdminContent(playerData)
    elseif tabId == 'settings' then
        content = OlympusUI.GenerateSettingsContent(playerData)
    elseif tabId == 'market' then
        content = OlympusUI.GenerateMarketContent(playerData)
    elseif tabId == 'stats' then
        content = OlympusUI.GenerateStatsContent(playerData)
    elseif tabId == 'smartphone' then
        content = OlympusUI.GenerateSmartPhoneContent(playerData)
    elseif tabId == 'gps' then
        content = OlympusUI.GenerateGPSContent(playerData)
    elseif tabId == 'sync' then
        content = OlympusUI.GenerateSyncContent(playerData)
    else
        content = "<h2>Unknown Tab</h2><p>This tab is not implemented yet.</p>"
    end
    
    OlympusUI.UpdateYMenuContent(source, {
        title = OlympusUI.GetTabTitle(tabId),
        content = content
    })
end)

RegisterServerEvent('olympus-ui:server:yMenuAction', function(action, data)
    local source = source
    
    -- Handle Y Menu actions
    if action == 'syncData' then
        -- Trigger data sync
        TriggerEvent('olympus-core:server:syncPlayerData', source)
        OlympusUI.Notify(source, "Player data synchronized successfully!", "success")
        
    elseif action == 'toggleSetting' then
        -- Handle setting toggles
        local playerData = exports['olympus-core']:GetPlayerData(source)
        if playerData then
            -- Update setting in database
            -- This would need to be implemented based on your settings system
        end
        
    else
        -- Forward to appropriate system
        TriggerEvent('olympus-ui:server:handleYMenuAction', source, action, data)
    end
end)

-- ========================================
-- Y MENU CONTENT GENERATORS
-- ========================================

function OlympusUI.GenerateLicensesContent(playerData)
    return "<h2>Licenses</h2><p>License management system will be implemented here.</p>"
end

function OlympusUI.GenerateKeychainContent(playerData)
    return "<h2>Key Chain</h2><p>Vehicle and house key management will be implemented here.</p>"
end

function OlympusUI.GenerateGangContent(playerData)
    return "<h2>Gang Management</h2><p>Gang system will be implemented here.</p>"
end

function OlympusUI.GenerateCellPhoneContent(playerData)
    return "<h2>Cell Phone</h2><p>Messaging system will be implemented here.</p>"
end

function OlympusUI.GenerateWantedContent(playerData)
    return "<h2>Wanted List</h2><p>APD wanted list will be implemented here.</p>"
end

function OlympusUI.GenerateAdminContent(playerData)
    if not playerData.adminLevel or playerData.adminLevel < 1 then
        return "<h2>Access Denied</h2><p>You do not have permission to access this menu.</p>"
    end
    return "<h2>Admin Menu</h2><p>Admin tools will be implemented here.</p>"
end

function OlympusUI.GenerateSettingsContent(playerData)
    return [[
        <h2>Settings</h2>
        <div>
            <h3>Display Settings</h3>
            <label><input type="checkbox" name="showHUD" checked> Show HUD</label><br>
            <label><input type="checkbox" name="showNotifications" checked> Show Notifications</label><br>
            <label><input type="checkbox" name="playNotificationSounds" checked> Play Notification Sounds</label><br>
            
            <h3>Gameplay Settings</h3>
            <label><input type="checkbox" name="autoRun"> Auto Run</label><br>
            <label><input type="checkbox" name="earplugs"> Earplugs</label><br>
            
            <button data-action="saveSettings" style="margin-top: 10px; padding: 5px 10px;">Save Settings</button>
        </div>
    ]]
end

function OlympusUI.GenerateMarketContent(playerData)
    return "<h2>Market</h2><p>Market system will be implemented here.</p>"
end

function OlympusUI.GenerateStatsContent(playerData)
    return string.format([[
        <h2>Player Statistics</h2>
        <div>
            <p><strong>Player ID:</strong> %s</p>
            <p><strong>Cash:</strong> $%s</p>
            <p><strong>Bank:</strong> $%s</p>
            <p><strong>Admin Level:</strong> %s</p>
            <p><strong>Playtime:</strong> %s hours</p>
        </div>
    ]], playerData.source or "Unknown", 
        playerData.cash or 0, 
        playerData.bank or 0, 
        playerData.adminLevel or 0,
        math.floor((playerData.playtime or 0) / 60))
end

function OlympusUI.GenerateSmartPhoneContent(playerData)
    return "<h2>Smart Phone</h2><p>Advanced phone features will be implemented here.</p>"
end

function OlympusUI.GenerateGPSContent(playerData)
    return "<h2>GPS</h2><p>GPS waypoint system will be implemented here.</p>"
end

function OlympusUI.GenerateSyncContent(playerData)
    return [[
        <h2>Sync Data</h2>
        <div>
            <p>Synchronize your player data with the server.</p>
            <button data-action="syncData" style="padding: 10px 20px; margin-top: 10px;">Sync Now</button>
        </div>
    ]]
end

function OlympusUI.GetTabTitle(tabId)
    local titles = {
        licenses = "Licenses",
        keychain = "Key Chain",
        gang = "Gang Management",
        cell = "Cell Phone",
        wanted = "Wanted List",
        admin = "Admin Menu",
        settings = "Settings",
        market = "Market",
        stats = "Statistics",
        smartphone = "Smart Phone",
        gps = "GPS",
        sync = "Sync Data"
    }
    
    return titles[tabId] or "Unknown"
end
