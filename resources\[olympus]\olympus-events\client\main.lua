-- ========================================
-- OLYMPUS FEDERAL EVENTS SYSTEM - CLIENT MAIN
-- Complete recreation based on original federal event client functions
-- Handles federal event UI, interactions, and mechanics
-- ========================================

local OlympusEvents = {}
OlympusEvents.ActiveEvents = {} -- Currently active federal events
OlympusEvents.EventBlips = {} -- Event blips on map
OlympusEvents.EventMarkers = {} -- 3D markers for events
OlympusEvents.PlayerInEventZone = false
OlympusEvents.CurrentEventZone = nil
OlympusEvents.AntiAirActive = false
OlympusEvents.BombTimers = {} -- Active bomb timers

-- Configuration is loaded via shared_scripts in fxmanifest.lua

-- Initialize federal events client system
function InitializeFederalEventsClient()
    print("^2[Olympus Events]^7 Initializing client federal events system...")

    -- Create event blips
    CreateEventBlips()

    -- Start event zone monitoring
    StartEventZoneMonitoring()

    -- Start interaction handler
    StartInteractionHandler()

    -- Start anti-air monitoring
    StartAntiAirMonitoring()

    print("^2[Olympus Events]^7 Client federal events system initialized!")
end

-- Create event blips on map
function CreateEventBlips()
    -- Federal Reserve blip
    local fedBlip = AddBlipForCoord(Config.FederalReserve.location.x, Config.FederalReserve.location.y, Config.FederalReserve.location.z)
    SetBlipSprite(fedBlip, 500) -- Bank icon
    SetBlipDisplay(fedBlip, 4)
    SetBlipScale(fedBlip, 0.8)
    SetBlipColour(fedBlip, 1) -- Red
    SetBlipAsShortRange(fedBlip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("Federal Reserve Bank")
    EndTextCommandSetBlipName(fedBlip)
    OlympusEvents.EventBlips['federal_reserve'] = fedBlip

    -- Blackwater Armory blip
    local blackwaterBlip = AddBlipForCoord(Config.BlackwaterArmory.location.x, Config.BlackwaterArmory.location.y, Config.BlackwaterArmory.location.z)
    SetBlipSprite(blackwaterBlip, 110) -- Military base icon
    SetBlipDisplay(blackwaterBlip, 4)
    SetBlipScale(blackwaterBlip, 0.8)
    SetBlipColour(blackwaterBlip, 5) -- Yellow
    SetBlipAsShortRange(blackwaterBlip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("Blackwater Armory")
    EndTextCommandSetBlipName(blackwaterBlip)
    OlympusEvents.EventBlips['blackwater_armory'] = blackwaterBlip

    -- Evidence Lockup blip
    local evidenceBlip = AddBlipForCoord(Config.EvidenceLockup.location.x, Config.EvidenceLockup.location.y, Config.EvidenceLockup.location.z)
    SetBlipSprite(evidenceBlip, 60) -- Police station icon
    SetBlipDisplay(evidenceBlip, 4)
    SetBlipScale(evidenceBlip, 0.8)
    SetBlipColour(evidenceBlip, 3) -- Blue
    SetBlipAsShortRange(evidenceBlip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("Evidence Lockup")
    EndTextCommandSetBlipName(evidenceBlip)
    OlympusEvents.EventBlips['evidence_lockup'] = evidenceBlip

    -- Jail Break blip
    local jailBlip = AddBlipForCoord(Config.JailBreak.location.x, Config.JailBreak.location.y, Config.JailBreak.location.z)
    SetBlipSprite(jailBlip, 188) -- Prison icon
    SetBlipDisplay(jailBlip, 4)
    SetBlipScale(jailBlip, 0.8)
    SetBlipColour(jailBlip, 6) -- Purple
    SetBlipAsShortRange(jailBlip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("Altis Penitentiary")
    EndTextCommandSetBlipName(jailBlip)
    OlympusEvents.EventBlips['jail_break'] = jailBlip
end

-- Start event zone monitoring
function StartEventZoneMonitoring()
    CreateThread(function()
        while true do
            Wait(1000) -- Check every second

            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)
            local wasInZone = OlympusEvents.PlayerInEventZone
            local currentZone = nil

            -- Check Federal Reserve zone
            local fedDist = #(playerCoords - Config.FederalReserve.location)
            if fedDist <= Config.FederalReserve.kosZones.innerDome.radius then
                currentZone = 'federal_reserve'
                OlympusEvents.PlayerInEventZone = true
            -- Check Blackwater zone
            elseif #(playerCoords - Config.BlackwaterArmory.location) <= 100.0 then
                currentZone = 'blackwater_armory'
                OlympusEvents.PlayerInEventZone = true
            -- Check Evidence Lockup zone
            elseif #(playerCoords - Config.EvidenceLockup.location) <= 75.0 then
                currentZone = 'evidence_lockup'
                OlympusEvents.PlayerInEventZone = true
            -- Check Jail Break zone
            elseif #(playerCoords - Config.JailBreak.location) <= 100.0 then
                currentZone = 'jail_break'
                OlympusEvents.PlayerInEventZone = true
            else
                OlympusEvents.PlayerInEventZone = false
                currentZone = nil
            end

            -- Handle zone enter/exit
            if OlympusEvents.PlayerInEventZone and not wasInZone then
                OnEnterEventZone(currentZone)
            elseif not OlympusEvents.PlayerInEventZone and wasInZone then
                OnExitEventZone(OlympusEvents.CurrentEventZone)
            end

            OlympusEvents.CurrentEventZone = currentZone
        end
    end)
end

-- Handle entering event zone
function OnEnterEventZone(zoneName)
    -- Show zone notification
    local zoneDisplayName = GetEventZoneDisplayName(zoneName)
    exports['olympus-ui']:ShowNotification({
        type = 'info',
        title = 'Federal Zone',
        message = string.format('Entered %s', zoneDisplayName),
        duration = 3000
    })

    -- Check if event is active
    if OlympusEvents.ActiveEvents[zoneName] then
        exports['olympus-ui']:ShowNotification({
            type = 'warning',
            title = 'Active Federal Event',
            message = 'Federal event in progress - KOS zone active!',
            duration = 5000
        })
    end
end

-- Handle exiting event zone
function OnExitEventZone(zoneName)
    if not zoneName then return end

    local zoneDisplayName = GetEventZoneDisplayName(zoneName)
    exports['olympus-ui']:ShowNotification({
        type = 'info',
        title = 'Federal Zone',
        message = string.format('Exited %s', zoneDisplayName),
        duration = 3000
    })
end

-- Get event zone display name
function GetEventZoneDisplayName(zoneName)
    local names = {
        federal_reserve = 'Federal Reserve Bank',
        blackwater_armory = 'Blackwater Armory',
        evidence_lockup = 'Evidence Lockup',
        jail_break = 'Altis Penitentiary'
    }
    return names[zoneName] or 'Unknown Zone'
end

-- Start interaction handler
function StartInteractionHandler()
    CreateThread(function()
        while true do
            Wait(0)

            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)

            -- Check for federal reserve interactions
            if #(playerCoords - Config.FederalReserve.location) <= 5.0 then
                DrawText3D(Config.FederalReserve.location.x, Config.FederalReserve.location.y, Config.FederalReserve.location.z + 1.0,
                    "[E] Start Federal Reserve")

                if IsControlJustPressed(0, 38) then -- E key
                    TriggerServerEvent('olympus:server:startFederalReserve')
                end
            end

            -- Check for anti-air hacking terminal
            local hackTerminal = Config.FederalReserve.antiAir.hackingTerminal.location
            if #(playerCoords - hackTerminal) <= 3.0 then
                DrawText3D(hackTerminal.x, hackTerminal.y, hackTerminal.z + 1.0,
                    "[E] Hack Anti-Air System")

                if IsControlJustPressed(0, 38) then -- E key
                    TriggerServerEvent('olympus:server:hackAntiAir')
                end
            end

            -- Check for bomb defusal (APD only)
            local playerData = exports['olympus-core']:GetPlayerData()
            if playerData and playerData.job == 'cop' then
                for eventName, eventData in pairs(OlympusEvents.ActiveEvents) do
                    if eventName == 'federal_reserve' then
                        local bombLocation = Config.FederalReserve.location
                        if #(playerCoords - bombLocation) <= 3.0 then
                            DrawText3D(bombLocation.x, bombLocation.y, bombLocation.z + 1.0,
                                "[E] Defuse Bomb")

                            if IsControlJustPressed(0, 38) then -- E key
                                TriggerServerEvent('olympus:server:defuseBomb', eventName)
                            end
                        end
                    end
                end
            end
        end
    end)
end

-- Start anti-air monitoring
function StartAntiAirMonitoring()
    CreateThread(function()
        while true do
            Wait(1000) -- Check every second

            if OlympusEvents.AntiAirActive then
                local playerPed = PlayerPedId()
                local vehicle = GetVehiclePedIsIn(playerPed, false)

                if vehicle ~= 0 and IsVehicleModel(vehicle, GetHashKey("buzzard")) then -- Example helicopter
                    local vehicleCoords = GetEntityCoords(vehicle)
                    local fedCoords = Config.FederalReserve.location
                    local distance = #(vehicleCoords - fedCoords)

                    if distance <= Config.FederalReserve.antiAir.radius then
                        -- Player is in anti-air zone with aircraft
                        TriggerAntiAirMissile(vehicle)
                    end
                end
            end
        end
    end)
end

-- Trigger anti-air missile
function TriggerAntiAirMissile(vehicle)
    -- Show warning first
    exports['olympus-ui']:ShowNotification({
        type = 'error',
        title = 'Anti-Air System',
        message = 'MISSILE LOCK DETECTED! EVADE IMMEDIATELY!',
        duration = 3000
    })

    -- Wait for lock time
    Wait(Config.FederalReserve.antiAir.missiles.lockTime * 1000)

    -- Fire missile (destroy vehicle)
    SetEntityHealth(vehicle, 0)

    exports['olympus-ui']:ShowNotification({
        type = 'error',
        title = 'Anti-Air System',
        message = 'Aircraft destroyed by anti-air missile!',
        duration = 5000
    })
end

-- Draw 3D text
function DrawText3D(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())

    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x, _y)

    local factor = (string.len(text)) / 370
    DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 41, 11, 41, 68)
end

-- Event handlers from server
RegisterNetEvent('olympus:client:federalEventAvailable')
AddEventHandler('olympus:client:federalEventAvailable', function(eventData)
    -- Show notification that event is available
    exports['olympus-ui']:ShowNotification({
        type = 'info',
        title = 'Federal Event Available',
        message = string.format('%s can be started (%d APD online)',
            eventData.displayName, eventData.apdOnline),
        duration = 10000
    })
end)

RegisterNetEvent('olympus:client:federalEventStarted')
AddEventHandler('olympus:client:federalEventStarted', function(eventData)
    -- Store active event
    OlympusEvents.ActiveEvents[eventData.eventName] = eventData

    -- Update blip color to indicate active event
    if OlympusEvents.EventBlips[eventData.eventName] then
        SetBlipColour(OlympusEvents.EventBlips[eventData.eventName], 1) -- Red for active
        SetBlipFlashes(OlympusEvents.EventBlips[eventData.eventName], true)
    end

    -- Show notification
    exports['olympus-ui']:ShowNotification({
        type = 'warning',
        title = 'Federal Event Started',
        message = string.format('%s started by %s [%s]',
            eventData.displayName, eventData.startedBy, eventData.gang),
        duration = 10000
    })

    -- Start bomb timer display
    if eventData.bombTimer then
        StartBombTimerDisplay(eventData.eventName, eventData.bombTimer)
    end

    -- Activate anti-air if federal reserve
    if eventData.eventName == 'federal_reserve' then
        OlympusEvents.AntiAirActive = true
    end
end)

RegisterNetEvent('olympus:client:federalEventCompleted')
AddEventHandler('olympus:client:federalEventCompleted', function(eventData)
    -- Remove from active events
    OlympusEvents.ActiveEvents[eventData.eventName] = nil

    -- Update blip
    if OlympusEvents.EventBlips[eventData.eventName] then
        SetBlipColour(OlympusEvents.EventBlips[eventData.eventName], 8) -- Gray for inactive
        SetBlipFlashes(OlympusEvents.EventBlips[eventData.eventName], false)
    end

    -- Show completion notification
    local message = eventData.success and
        string.format('Federal event completed! %d gold bars spawned', eventData.rewards or 0) or
        string.format('Federal event failed: %s', eventData.reason or 'Unknown reason')

    exports['olympus-ui']:ShowNotification({
        type = eventData.success and 'success' or 'error',
        title = 'Federal Event Completed',
        message = message,
        duration = 10000
    })

    -- Stop bomb timer
    StopBombTimerDisplay(eventData.eventName)

    -- Deactivate anti-air if federal reserve
    if eventData.eventName == 'federal_reserve' then
        OlympusEvents.AntiAirActive = false
    end
end)

RegisterNetEvent('olympus:client:updateBombTimer')
AddEventHandler('olympus:client:updateBombTimer', function(timerData)
    if OlympusEvents.BombTimers[timerData.eventName] then
        OlympusEvents.BombTimers[timerData.eventName].timeLeft = timerData.timeLeft
    end
end)

RegisterNetEvent('olympus:client:apdFederalAlert')
AddEventHandler('olympus:client:apdFederalAlert', function(alertData)
    -- Show APD-specific federal alert
    exports['olympus-ui']:ShowNotification({
        type = 'error',
        title = 'APD ALERT',
        message = alertData.message,
        duration = 15000
    })

    -- Play alert sound
    PlaySoundFrontend(-1, "TIMER_STOP", "HUD_MINI_GAME_SOUNDSET", 1)
end)

RegisterNetEvent('olympus:client:antiAirDisabled')
AddEventHandler('olympus:client:antiAirDisabled', function(data)
    OlympusEvents.AntiAirActive = false

    exports['olympus-ui']:ShowNotification({
        type = 'success',
        title = 'Anti-Air System',
        message = string.format('Anti-air system disabled for %d seconds', data.disableTime),
        duration = 5000
    })
end)

RegisterNetEvent('olympus:client:antiAirEnabled')
AddEventHandler('olympus:client:antiAirEnabled', function()
    OlympusEvents.AntiAirActive = true

    exports['olympus-ui']:ShowNotification({
        type = 'warning',
        title = 'Anti-Air System',
        message = 'Anti-air system reactivated!',
        duration = 5000
    })
end)

RegisterNetEvent('olympus:client:startDefusal')
AddEventHandler('olympus:client:startDefusal', function(defusalData)
    -- Start defusal progress bar
    exports['olympus-ui']:ShowProgressBar({
        title = 'Defusing Bomb',
        duration = defusalData.defuseTime * 1000,
        useWhileDead = false,
        canCancel = true,
        disableMovement = true,
        disableCarMovement = true,
        disableMouse = false,
        disableCombat = true,
        animation = {
            animDict = "anim@amb@clubhouse@tutorial@bkr_tut_ig3@",
            anim = "machinic_loop_mechandplayer",
            flags = 49
        }
    })
end)

RegisterNetEvent('olympus:client:startHacking')
AddEventHandler('olympus:client:startHacking', function(hackData)
    -- Start hacking progress bar
    exports['olympus-ui']:ShowProgressBar({
        title = 'Hacking Anti-Air System',
        duration = hackData.hackTime * 1000,
        useWhileDead = false,
        canCancel = true,
        disableMovement = true,
        disableCarMovement = true,
        disableMouse = false,
        disableCombat = true,
        animation = {
            animDict = "anim@heists@prison_heiststation@cop_reactions",
            anim = "cop_b_idle",
            flags = 49
        }
    })
end)

RegisterNetEvent('olympus:client:spawnGoldBar')
AddEventHandler('olympus:client:spawnGoldBar', function(goldData)
    -- This would spawn a physical gold bar object in the world
    -- For now, we'll just show a notification
    exports['olympus-ui']:ShowNotification({
        type = 'success',
        title = 'Gold Bar',
        message = string.format('Gold bar spawned (Value: $%s)', goldData.value),
        duration = 3000
    })
end)

RegisterNetEvent('olympus:client:cleanupFederalEvent')
AddEventHandler('olympus:client:cleanupFederalEvent', function(eventName)
    -- Clean up any client-side event objects
    OlympusEvents.ActiveEvents[eventName] = nil
    StopBombTimerDisplay(eventName)

    if eventName == 'federal_reserve' then
        OlympusEvents.AntiAirActive = false
    end
end)

-- Start bomb timer display
function StartBombTimerDisplay(eventName, duration)
    OlympusEvents.BombTimers[eventName] = {
        timeLeft = duration,
        active = true
    }

    CreateThread(function()
        while OlympusEvents.BombTimers[eventName] and OlympusEvents.BombTimers[eventName].active do
            Wait(0)

            local timer = OlympusEvents.BombTimers[eventName]
            if timer.timeLeft > 0 then
                local minutes = math.floor(timer.timeLeft / 60)
                local seconds = timer.timeLeft % 60

                -- Draw timer on screen
                SetTextFont(4)
                SetTextProportional(1)
                SetTextScale(0.8, 0.8)
                SetTextColour(255, 0, 0, 255)
                SetTextDropshadow(0, 0, 0, 0, 255)
                SetTextEdge(1, 0, 0, 0, 255)
                SetTextEntry("STRING")
                AddTextComponentString(string.format("BOMB TIMER: %02d:%02d", minutes, seconds))
                DrawText(0.5, 0.1)
            end
        end
    end)
end

-- Stop bomb timer display
function StopBombTimerDisplay(eventName)
    if OlympusEvents.BombTimers[eventName] then
        OlympusEvents.BombTimers[eventName].active = false
        OlympusEvents.BombTimers[eventName] = nil
    end
end

-- Export functions
exports('GetActiveEvents', function()
    return OlympusEvents.ActiveEvents
end)

exports('IsInEventZone', function()
    return OlympusEvents.PlayerInEventZone
end)

exports('GetCurrentEventZone', function()
    return OlympusEvents.CurrentEventZone
end)

exports('IsAntiAirActive', function()
    return OlympusEvents.AntiAirActive
end)

-- Initialize system when player is loaded
CreateThread(function()
    while not exports['olympus-core']:IsPlayerLoaded() do
        Wait(1000)
    end

    Wait(2000) -- Additional wait for other systems
    InitializeFederalEventsClient()
end)
