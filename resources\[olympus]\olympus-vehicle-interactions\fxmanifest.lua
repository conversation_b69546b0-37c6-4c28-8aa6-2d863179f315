fx_version 'cerulean'
game 'gta5'

author 'Olympus Development'
description 'Olympus Vehicle Interaction System - Complete vehicle interaction mechanics'
version '1.0.0'

dependencies {
    'olympus-core',
    'olympus-vehicles',
    'olympus-apd'
}

shared_scripts {
    'config/shared.lua'
}

client_scripts {
    'client/main.lua'
}

server_scripts {
    'server/main.lua'
}

exports {
    'FlipVehicle',
    'PushVehicle',
    'SearchVehicle',
    'ImpoundVehicle',
    'RepairVehicle',
    'CanInteractWithVehicle',
    'GetVehicleOwners',
    'IsVehicleStolen'
}
