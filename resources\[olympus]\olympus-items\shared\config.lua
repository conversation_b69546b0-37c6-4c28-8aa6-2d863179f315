-- Olympus Items Configuration
-- Based on original Olympus Altis Life special items and mechanics

Config = {}

-- Item Configuration
Config.Items = {
    -- Tools & Equipment
    lockpick = {
        name = "Lockpick",
        description = "Used to pick locks on vehicles and handcuffs",
        weight = 0.5,
        stackable = true,
        maxStack = 5,
        usable = true,
        durability = 5, -- Uses before breaking
        successChance = {
            base = 30,
            tier1 = 35, -- With lockpick perk tier 1
            tier2 = 40  -- With lockpick perk tier 2
        }
    },
    
    boltcutter = {
        name = "Bolt Cutter",
        description = "Heavy duty tool for cutting through locks and barriers",
        weight = 5.0,
        stackable = false,
        usable = true,
        durability = 5, -- Uses before breaking
        requiredCops = {
            house = 0,
            fed = 5,
            blackwater = 7
        }
    },
    
    blastingcharge = {
        name = "Blasting Charge",
        description = "Explosive device for breaching secure vaults",
        weight = 10.0,
        stackable = true,
        maxStack = 3,
        usable = true,
        requiredCops = {
            bank = 5,
            fed = 5,
            blackwater = 7,
            jail = 5
        },
        requiredCivs = {
            bank = 3,
            fed = 2,
            blackwater = 2,
            jail = 2
        }
    },
    
    -- GPS & Tracking
    gpstracker = {
        name = "GPS Tracker",
        description = "Device for tracking vehicles remotely",
        weight = 0.3,
        stackable = true,
        maxStack = 10,
        usable = true,
        trackingRange = 5000 -- meters
    },
    
    egpstracker = {
        name = "Enhanced GPS Tracker",
        description = "Advanced GPS tracker with extended range",
        weight = 0.5,
        stackable = true,
        maxStack = 5,
        usable = true,
        trackingRange = 10000 -- meters
    },
    
    gpsjammer = {
        name = "GPS Jammer",
        description = "Device that blocks GPS tracking signals",
        weight = 1.0,
        stackable = true,
        maxStack = 3,
        usable = true,
        jamRadius = 500, -- meters
        duration = 300 -- seconds
    },
    
    -- Explosives & Weapons
    speedbomb = {
        name = "Speed Bomb",
        description = "Explosive that activates when vehicle exceeds speed limit",
        weight = 2.0,
        stackable = true,
        maxStack = 3,
        usable = true,
        activationSpeed = 70, -- km/h
        detonationSpeed = 50  -- km/h
    },
    
    flashbang = {
        name = "Flashbang",
        description = "Non-lethal explosive that blinds and deafens targets",
        weight = 0.8,
        stackable = true,
        maxStack = 5,
        usable = true,
        effectRadius = 25, -- meters
        duration = 15 -- seconds
    },
    
    -- Medical Items
    bloodbag = {
        name = "Blood Bag",
        description = "Medical supply for restoring health over time",
        weight = 0.5,
        stackable = true,
        maxStack = 10,
        usable = true,
        healAmount = 0.005, -- per tick
        duration = 60, -- seconds
        cooldown = 120 -- seconds
    },
    
    epipen = {
        name = "EpiPen",
        description = "Emergency medical injector for reviving downed players",
        weight = 0.2,
        stackable = true,
        maxStack = 5,
        usable = true,
        reviveHealth = 25 -- percentage
    },
    
    dopeshot = {
        name = "Dope Shot",
        description = "Illegal stimulant injection",
        weight = 0.1,
        stackable = true,
        maxStack = 10,
        usable = true,
        effects = {
            speed = 1.2,
            duration = 300 -- seconds
        }
    },
    
    lethalinjector = {
        name = "Lethal Injector",
        description = "Deadly injection device",
        weight = 0.3,
        stackable = true,
        maxStack = 3,
        usable = true,
        restricted = true -- Only certain factions
    },
    
    -- Restraints & Control
    blindfold = {
        name = "Blindfold",
        description = "Used to obstruct vision of restrained targets",
        weight = 0.1,
        stackable = true,
        maxStack = 10,
        usable = true
    },
    
    ziptie = {
        name = "Zip Tie",
        description = "Plastic restraint for detaining suspects",
        weight = 0.1,
        stackable = true,
        maxStack = 20,
        usable = true
    },
    
    -- Utility Items
    defusekit = {
        name = "Defuse Kit",
        description = "Specialized tool for disarming explosive devices",
        weight = 2.0,
        stackable = true,
        maxStack = 3,
        usable = true,
        defuseTime = 30 -- seconds
    },
    
    spikestrip = {
        name = "Spike Strip",
        description = "Road spike device for stopping vehicles",
        weight = 3.0,
        stackable = true,
        maxStack = 5,
        usable = true,
        duration = 600 -- seconds before despawn
    },
    
    helitowhook = {
        name = "Helicopter Tow Hook",
        description = "Attachment for helicopter vehicle recovery",
        weight = 5.0,
        stackable = true,
        maxStack = 3,
        usable = true
    },
    
    fireaxe = {
        name = "Fire Axe",
        description = "Emergency tool for breaking down barriers",
        weight = 3.0,
        stackable = false,
        usable = true,
        durability = 10
    },
    
    slimjim = {
        name = "Slim Jim",
        description = "Tool for unlocking vehicle doors",
        weight = 0.5,
        stackable = true,
        maxStack = 5,
        usable = true,
        successChance = 60
    },
    
    -- Special Items
    baitcar = {
        name = "Bait Car Remote",
        description = "Remote control for police bait vehicles",
        weight = 0.3,
        stackable = true,
        maxStack = 3,
        usable = true,
        restricted = true -- Police only
    },
    
    pocketgokart = {
        name = "Pocket Go-Kart",
        description = "Deployable mini vehicle",
        weight = 15.0,
        stackable = false,
        usable = true
    },
    
    takeoverterminal = {
        name = "Takeover Terminal",
        description = "Hacking device for territory control",
        weight = 5.0,
        stackable = true,
        maxStack = 2,
        usable = true,
        hackTime = 120 -- seconds
    },
    
    vehammo = {
        name = "Vehicle Ammunition",
        description = "Ammunition for armed vehicles",
        weight = 10.0,
        stackable = true,
        maxStack = 5,
        usable = true,
        restricted = true -- Police/Military only
    },
    
    fireworks = {
        name = "Fireworks",
        description = "Celebratory explosive display",
        weight = 1.0,
        stackable = true,
        maxStack = 10,
        usable = true
    }
}

-- Faction Restrictions
Config.FactionRestrictions = {
    police = {"baitcar", "vehammo", "defusekit", "spikestrip"},
    medic = {"epipen", "bloodbag", "fireaxe"},
    rebel = {"blastingcharge", "speedbomb", "gpsjammer", "lethalinjector"},
    vigilante = {"blindfold", "ziptie", "boltcutter", "gpstracker"}
}

-- Cooldowns (in seconds)
Config.Cooldowns = {
    lockpick = 5,
    boltcutter = 10,
    blastingcharge = 300,
    bloodbag = 120,
    flashbang = 30,
    speedbomb = 60
}

-- Animation Configuration
Config.Animations = {
    lockpick = {dict = "anim@amb@clubhouse@tutorial@bkr_tut_ig3@", name = "machinic_loop_mechandplayer", duration = 1500},
    boltcutter = {dict = "anim@amb@clubhouse@tutorial@bkr_tut_ig3@", name = "machinic_loop_mechandplayer", duration = 1500},
    medical = {dict = "amb@medic@standing@kneel@base", name = "base", duration = 1500},
    planting = {dict = "anim@amb@clubhouse@tutorial@bkr_tut_ig3@", name = "machinic_loop_mechandplayer", duration = 1500}
}

-- Progress Bar Configuration
Config.ProgressBar = {
    lockpick = {
        label = "Picking lock...",
        duration = 15000,
        useWhileDead = false,
        canCancel = true
    },
    boltcutter = {
        label = "Cutting lock...",
        duration = 30000,
        useWhileDead = false,
        canCancel = true
    },
    blastingcharge = {
        label = "Planting explosive...",
        duration = 10000,
        useWhileDead = false,
        canCancel = true
    }
}
