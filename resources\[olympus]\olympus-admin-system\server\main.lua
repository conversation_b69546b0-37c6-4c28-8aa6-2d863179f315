-- ========================================
-- OLYMPUS ADMIN SYSTEM - SERVER MAIN
-- Complete recreation based on original Olympus admin mechanics
-- Handles all administrative functions, punishments, and moderation
-- ========================================

local OlympusAdmin = {}
OlympusAdmin.VanishedPlayers = {}
OlympusAdmin.SpectatingPlayers = {}
OlympusAdmin.FrozenPlayers = {}
OlympusAdmin.ActiveReports = {}
OlympusAdmin.AdminChat = {}

-- Load configuration
local Config = require('config.shared')

-- Initialize admin system
function InitializeAdminSystem()
    print("^2[Olympus Admin]^7 Initializing admin system...")

    -- Initialize database tables
    InitializeAdminDatabase()

    -- Load admin ranks and permissions
    LoadAdminRanks()

    -- Initialize punishment system
    InitializePunishmentSystem()

    -- Initialize report system
    InitializeReportSystem()

    -- Initialize logging system
    InitializeLoggingSystem()

    print("^2[Olympus Admin]^7 Admin system initialized!")
end

-- Initialize admin database tables
function InitializeAdminDatabase()
    -- Admin logs table
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS admin_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            admin_name VARCHAR(255) NOT NULL,
            admin_identifier VARCHAR(255) NOT NULL,
            admin_rank INT NOT NULL,
            action VARCHAR(255) NOT NULL,
            target_name VARCHAR(255),
            target_identifier VARCHAR(255),
            details TEXT,
            timestamp BIGINT NOT NULL,
            server_id VARCHAR(50) DEFAULT 'olympus-1'
        )
    ]])

    -- Player bans table
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS player_bans (
            id INT AUTO_INCREMENT PRIMARY KEY,
            identifier VARCHAR(255) NOT NULL,
            name VARCHAR(255) NOT NULL,
            reason TEXT NOT NULL,
            banned_by VARCHAR(255) NOT NULL,
            ban_time BIGINT NOT NULL,
            unban_time BIGINT DEFAULT 0,
            is_permanent BOOLEAN DEFAULT FALSE,
            is_active BOOLEAN DEFAULT TRUE,
            server_id VARCHAR(50) DEFAULT 'olympus-1'
        )
    ]])

    -- Player warnings table
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS player_warnings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            identifier VARCHAR(255) NOT NULL,
            name VARCHAR(255) NOT NULL,
            reason TEXT NOT NULL,
            warned_by VARCHAR(255) NOT NULL,
            warning_time BIGINT NOT NULL,
            points INT DEFAULT 1,
            server_id VARCHAR(50) DEFAULT 'olympus-1'
        )
    ]])

    -- Player reports table
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS player_reports (
            id INT AUTO_INCREMENT PRIMARY KEY,
            reporter_identifier VARCHAR(255) NOT NULL,
            reporter_name VARCHAR(255) NOT NULL,
            reported_identifier VARCHAR(255) NOT NULL,
            reported_name VARCHAR(255) NOT NULL,
            category VARCHAR(100) NOT NULL,
            reason TEXT NOT NULL,
            evidence TEXT,
            status VARCHAR(50) DEFAULT 'open',
            assigned_admin VARCHAR(255),
            report_time BIGINT NOT NULL,
            resolved_time BIGINT DEFAULT 0,
            server_id VARCHAR(50) DEFAULT 'olympus-1'
        )
    ]])

    print("^3[Olympus Admin]^7 Database tables initialized")
end

-- Load admin ranks
function LoadAdminRanks()
    OlympusAdmin.AdminRanks = {
        [0] = 'Player',
        [1] = 'Support Team',
        [2] = 'Moderator',
        [3] = 'Administrator',
        [4] = 'Senior Administrator',
        [5] = 'Community Manager',
        [6] = 'Server Owner'
    }

    print("^3[Olympus Admin]^7 Admin ranks loaded")
end

-- Initialize punishment system
function InitializePunishmentSystem()
    OlympusAdmin.PunishmentSystem = {
        pointThresholds = {
            warning = 5,
            tempBan = 10,
            permBan = 20
        },
        banDurations = {
            short = 3600,      -- 1 hour
            medium = 86400,    -- 24 hours
            long = 604800,     -- 7 days
            extended = 2592000 -- 30 days
        }
    }

    print("^3[Olympus Admin]^7 Punishment system initialized")
end

-- Initialize report system
function InitializeReportSystem()
    OlympusAdmin.ReportCategories = {
        'RDM/VDM',
        'Exploiting/Hacking',
        'FailRP/Trolling',
        'Harassment',
        'Bug Report',
        'Other'
    }

    print("^3[Olympus Admin]^7 Report system initialized")
end

-- Initialize logging system
function InitializeLoggingSystem()
    OlympusAdmin.LoggingEnabled = true

    print("^3[Olympus Admin]^7 Logging system initialized")
end

-- Check if player is admin
function IsPlayerAdmin(source)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData then return false end
    return playerData.adminRank and playerData.adminRank > 0
end

-- Get player admin rank
function GetPlayerAdminRank(source)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData then return 0 end
    return playerData.adminRank or 0
end

-- Get admin rank name
function GetAdminRankName(rank)
    return OlympusAdmin.AdminRanks[rank] or 'Unknown'
end

-- Check admin permission
function HasAdminPermission(source, requiredRank)
    local playerRank = GetPlayerAdminRank(source)
    return playerRank >= requiredRank
end

-- Log admin action
function LogAdminAction(source, action, details, targetSource)
    if not OlympusAdmin.LoggingEnabled then return end

    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData then return end

    local targetData = nil
    if targetSource then
        targetData = exports['olympus-core']:GetPlayerData(targetSource)
    end

    local logData = {
        admin_name = playerData.name,
        admin_identifier = playerData.identifier,
        admin_rank = playerData.adminRank or 0,
        action = action,
        target_name = targetData and targetData.name or nil,
        target_identifier = targetData and targetData.identifier or nil,
        details = details,
        timestamp = os.time()
    }

    -- Insert into database
    exports.oxmysql:execute('INSERT INTO admin_logs (admin_name, admin_identifier, admin_rank, action, target_name, target_identifier, details, timestamp) VALUES (?, ?, ?, ?, ?, ?, ?, ?)', {
        logData.admin_name,
        logData.admin_identifier,
        logData.admin_rank,
        logData.action,
        logData.target_name,
        logData.target_identifier,
        logData.details,
        logData.timestamp
    })

    -- Console log
    local logMessage = string.format("^3[ADMIN LOG]^7 %s (%s): %s", logData.admin_name, GetAdminRankName(logData.admin_rank), logData.action)
    if logData.target_name then
        logMessage = logMessage .. string.format(" -> %s", logData.target_name)
    end
    if logData.details then
        logMessage = logMessage .. string.format(" (%s)", logData.details)
    end
    print(logMessage)

    -- Notify other admins
    NotifyAdmins(logMessage, source)
end

-- Notify all online admins
function NotifyAdmins(message, excludeSource)
    local players = GetPlayers()
    for _, playerId in ipairs(players) do
        local playerSource = tonumber(playerId)
        if playerSource ~= excludeSource and IsPlayerAdmin(playerSource) then
            TriggerClientEvent('olympus:client:notify', playerSource, {
                type = 'info',
                title = 'Admin Action',
                message = message,
                duration = 5000
            })
        end
    end
end

-- Toggle vanish mode
RegisterNetEvent('olympus-admin:server:toggleVanish')
AddEventHandler('olympus-admin:server:toggleVanish', function()
    local source = source
    if not IsPlayerAdmin(source) then return end

    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData then return end

    local isVanished = OlympusAdmin.VanishedPlayers[source] or false
    OlympusAdmin.VanishedPlayers[source] = not isVanished

    -- Update client
    TriggerClientEvent('olympus-admin:client:setVanish', source, not isVanished)

    -- Update all other clients
    local players = GetPlayers()
    for _, playerId in ipairs(players) do
        local playerSource = tonumber(playerId)
        if playerSource ~= source then
            TriggerClientEvent('olympus-admin:client:updateVanishedPlayer', playerSource, source, not isVanished)
        end
    end

    -- Log action
    LogAdminAction(source, 'Vanish', not isVanished and 'Enabled' or 'Disabled')

    -- Notify admin
    TriggerClientEvent('olympus:client:notify', source, {
        type = 'success',
        title = 'Vanish Mode',
        message = not isVanished and 'Vanish enabled' or 'Vanish disabled',
        duration = 3000
    })
end)

-- Toggle spectate mode
RegisterNetEvent('olympus-admin:server:toggleSpectate')
AddEventHandler('olympus-admin:server:toggleSpectate', function(targetId)
    local source = source
    if not HasAdminPermission(source, 1) then return end

    local playerData = exports['olympus-core']:GetPlayerData(source)
    local targetData = exports['olympus-core']:GetPlayerData(targetId)

    if not playerData or not targetData then return end

    local isSpectating = OlympusAdmin.SpectatingPlayers[source]

    if isSpectating then
        -- Stop spectating
        OlympusAdmin.SpectatingPlayers[source] = nil
        TriggerClientEvent('olympus-admin:client:stopSpectate', source)

        LogAdminAction(source, 'Spectate', 'Stopped spectating')

        TriggerClientEvent('olympus:client:notify', source, {
            type = 'info',
            title = 'Spectate Mode',
            message = 'Stopped spectating',
            duration = 3000
        })
    else
        -- Start spectating
        OlympusAdmin.SpectatingPlayers[source] = targetId
        TriggerClientEvent('olympus-admin:client:startSpectate', source, targetId)

        LogAdminAction(source, 'Spectate', 'Started spectating ' .. targetData.name, targetId)

        TriggerClientEvent('olympus:client:notify', source, {
            type = 'success',
            title = 'Spectate Mode',
            message = 'Now spectating ' .. targetData.name,
            duration = 3000
        })
    end
end)

-- Teleport to player
RegisterNetEvent('olympus-admin:server:teleportToPlayer')
AddEventHandler('olympus-admin:server:teleportToPlayer', function(targetId)
    local source = source
    if not HasAdminPermission(source, 1) then return end

    local playerData = exports['olympus-core']:GetPlayerData(source)
    local targetData = exports['olympus-core']:GetPlayerData(targetId)

    if not playerData or not targetData then return end

    -- Get target position
    local targetPed = GetPlayerPed(targetId)
    local targetCoords = GetEntityCoords(targetPed)

    -- Teleport admin to target
    TriggerClientEvent('olympus-admin:client:teleportToCoords', source, targetCoords)

    LogAdminAction(source, 'Teleport', 'Teleported to ' .. targetData.name, targetId)

    TriggerClientEvent('olympus:client:notify', source, {
        type = 'success',
        title = 'Teleport',
        message = 'Teleported to ' .. targetData.name,
        duration = 3000
    })
end)

-- Teleport player to admin
RegisterNetEvent('olympus-admin:server:teleportPlayerToMe')
AddEventHandler('olympus-admin:server:teleportPlayerToMe', function(targetId)
    local source = source
    if not HasAdminPermission(source, 2) then return end

    local playerData = exports['olympus-core']:GetPlayerData(source)
    local targetData = exports['olympus-core']:GetPlayerData(targetId)

    if not playerData or not targetData then return end

    -- Get admin position
    local adminPed = GetPlayerPed(source)
    local adminCoords = GetEntityCoords(adminPed)

    -- Teleport target to admin
    TriggerClientEvent('olympus-admin:client:teleportToCoords', targetId, adminCoords)

    LogAdminAction(source, 'Teleport', 'Teleported ' .. targetData.name .. ' to self', targetId)

    TriggerClientEvent('olympus:client:notify', source, {
        type = 'success',
        title = 'Teleport',
        message = 'Teleported ' .. targetData.name .. ' to you',
        duration = 3000
    })

    TriggerClientEvent('olympus:client:notify', targetId, {
        type = 'info',
        title = 'Teleported',
        message = 'You were teleported by ' .. playerData.name,
        duration = 5000
    })
end)

-- Freeze/Unfreeze player
RegisterNetEvent('olympus-admin:server:toggleFreeze')
AddEventHandler('olympus-admin:server:toggleFreeze', function(targetId)
    local source = source
    if not HasAdminPermission(source, 1) then return end

    local playerData = exports['olympus-core']:GetPlayerData(source)
    local targetData = exports['olympus-core']:GetPlayerData(targetId)

    if not playerData or not targetData then return end

    local isFrozen = OlympusAdmin.FrozenPlayers[targetId] or false
    OlympusAdmin.FrozenPlayers[targetId] = not isFrozen

    -- Update target client
    TriggerClientEvent('olympus-admin:client:setFreeze', targetId, not isFrozen)

    LogAdminAction(source, 'Freeze', (not isFrozen and 'Froze ' or 'Unfroze ') .. targetData.name, targetId)

    TriggerClientEvent('olympus:client:notify', source, {
        type = 'success',
        title = 'Player ' .. (not isFrozen and 'Frozen' or 'Unfrozen'),
        message = (not isFrozen and 'Froze ' or 'Unfroze ') .. targetData.name,
        duration = 3000
    })

    TriggerClientEvent('olympus:client:notify', targetId, {
        type = not isFrozen and 'warning' or 'info',
        title = not isFrozen and 'Frozen' or 'Unfrozen',
        message = 'You have been ' .. (not isFrozen and 'frozen' or 'unfrozen') .. ' by ' .. playerData.name,
        duration = 5000
    })
end)

-- Kick player
RegisterNetEvent('olympus-admin:server:kickPlayer')
AddEventHandler('olympus-admin:server:kickPlayer', function(targetId, reason)
    local source = source
    if not HasAdminPermission(source, 1) then return end

    local playerData = exports['olympus-core']:GetPlayerData(source)
    local targetData = exports['olympus-core']:GetPlayerData(targetId)

    if not playerData or not targetData then return end

    reason = reason or 'No reason provided'

    LogAdminAction(source, 'Kick', 'Kicked ' .. targetData.name .. ' - Reason: ' .. reason, targetId)

    -- Notify target before kick
    TriggerClientEvent('olympus:client:notify', targetId, {
        type = 'error',
        title = 'Kicked from Server',
        message = 'Reason: ' .. reason .. ' | By: ' .. playerData.name,
        duration = 10000
    })

    -- Kick after delay
    CreateThread(function()
        Wait(2000)
        DropPlayer(targetId, 'Kicked by ' .. playerData.name .. ' - Reason: ' .. reason)
    end)

    TriggerClientEvent('olympus:client:notify', source, {
        type = 'success',
        title = 'Player Kicked',
        message = 'Kicked ' .. targetData.name,
        duration = 3000
    })
end)

-- Warn player
RegisterNetEvent('olympus-admin:server:warnPlayer')
AddEventHandler('olympus-admin:server:warnPlayer', function(targetId, reason, points)
    local source = source
    if not HasAdminPermission(source, 1) then return end

    local playerData = exports['olympus-core']:GetPlayerData(source)
    local targetData = exports['olympus-core']:GetPlayerData(targetId)

    if not playerData or not targetData then return end

    reason = reason or 'No reason provided'
    points = points or 1

    -- Insert warning into database
    exports.oxmysql:execute('INSERT INTO player_warnings (identifier, name, reason, warned_by, warning_time, points) VALUES (?, ?, ?, ?, ?, ?)', {
        targetData.identifier,
        targetData.name,
        reason,
        playerData.name,
        os.time(),
        points
    })

    LogAdminAction(source, 'Warning', 'Warned ' .. targetData.name .. ' (' .. points .. ' points) - Reason: ' .. reason, targetId)

    TriggerClientEvent('olympus:client:notify', targetId, {
        type = 'warning',
        title = 'Official Warning',
        message = 'Reason: ' .. reason .. ' | Points: ' .. points .. ' | By: ' .. playerData.name,
        duration = 10000
    })

    TriggerClientEvent('olympus:client:notify', source, {
        type = 'success',
        title = 'Warning Issued',
        message = 'Warned ' .. targetData.name .. ' (' .. points .. ' points)',
        duration = 3000
    })

    -- Check if player should be auto-punished based on points
    CheckPlayerPoints(targetId)
end)

-- Ban player
RegisterNetEvent('olympus-admin:server:banPlayer')
AddEventHandler('olympus-admin:server:banPlayer', function(targetId, reason, duration, isPermanent)
    local source = source
    if not HasAdminPermission(source, 2) then return end

    local playerData = exports['olympus-core']:GetPlayerData(source)
    local targetData = exports['olympus-core']:GetPlayerData(targetId)

    if not playerData or not targetData then return end

    reason = reason or 'No reason provided'
    duration = duration or 0
    isPermanent = isPermanent or false

    local unbanTime = 0
    if not isPermanent and duration > 0 then
        unbanTime = os.time() + duration
    end

    -- Insert ban into database
    exports.oxmysql:execute('INSERT INTO player_bans (identifier, name, reason, banned_by, ban_time, unban_time, is_permanent, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?)', {
        targetData.identifier,
        targetData.name,
        reason,
        playerData.name,
        os.time(),
        unbanTime,
        isPermanent,
        true
    })

    local banType = isPermanent and 'Permanent Ban' or 'Temporary Ban'
    local durationText = isPermanent and 'Permanent' or (duration > 0 and FormatTime(duration) or 'Unknown')

    LogAdminAction(source, 'Ban', banType .. ' - ' .. targetData.name .. ' (' .. durationText .. ') - Reason: ' .. reason, targetId)

    -- Notify target before ban
    TriggerClientEvent('olympus:client:notify', targetId, {
        type = 'error',
        title = banType,
        message = 'Reason: ' .. reason .. ' | Duration: ' .. durationText .. ' | By: ' .. playerData.name,
        duration = 15000
    })

    -- Ban after delay
    CreateThread(function()
        Wait(3000)
        DropPlayer(targetId, banType .. ' by ' .. playerData.name .. ' - Reason: ' .. reason .. (not isPermanent and duration > 0 and ' - Duration: ' .. FormatTime(duration) or ''))
    end)

    TriggerClientEvent('olympus:client:notify', source, {
        type = 'success',
        title = 'Player Banned',
        message = 'Banned ' .. targetData.name .. ' (' .. durationText .. ')',
        duration = 3000
    })
end)

-- Check player warning points for auto-punishment
function CheckPlayerPoints(targetId)
    local targetData = exports['olympus-core']:GetPlayerData(targetId)
    if not targetData then return end

    exports.oxmysql:query('SELECT SUM(points) as total_points FROM player_warnings WHERE identifier = ? AND warning_time > ?', {
        targetData.identifier,
        os.time() - (30 * 24 * 60 * 60) -- Last 30 days
    }, function(result)
        if result and result[1] then
            local totalPoints = result[1].total_points or 0

            if totalPoints >= OlympusAdmin.PunishmentSystem.pointThresholds.permBan then
                -- Auto permanent ban
                TriggerEvent('olympus-admin:server:banPlayer', 0, targetId, 'Automatic ban - Too many warning points (' .. totalPoints .. ')', 0, true)
            elseif totalPoints >= OlympusAdmin.PunishmentSystem.pointThresholds.tempBan then
                -- Auto temporary ban (7 days)
                TriggerEvent('olympus-admin:server:banPlayer', 0, targetId, 'Automatic temporary ban - Warning points threshold reached (' .. totalPoints .. ')', OlympusAdmin.PunishmentSystem.banDurations.long, false)
            elseif totalPoints >= OlympusAdmin.PunishmentSystem.pointThresholds.warning then
                -- Auto kick
                TriggerEvent('olympus-admin:server:kickPlayer', 0, targetId, 'Automatic kick - Warning points threshold reached (' .. totalPoints .. ')')
            end
        end
    end)
end

-- Format time duration
function FormatTime(seconds)
    local days = math.floor(seconds / 86400)
    local hours = math.floor((seconds % 86400) / 3600)
    local minutes = math.floor((seconds % 3600) / 60)

    if days > 0 then
        return days .. ' day' .. (days > 1 and 's' or '') .. (hours > 0 and ', ' .. hours .. ' hour' .. (hours > 1 and 's' or '') or '')
    elseif hours > 0 then
        return hours .. ' hour' .. (hours > 1 and 's' or '') .. (minutes > 0 and ', ' .. minutes .. ' minute' .. (minutes > 1 and 's' or '') or '')
    else
        return minutes .. ' minute' .. (minutes > 1 and 's' or '')
    end
end

-- Submit player report
RegisterNetEvent('olympus-admin:server:submitReport')
AddEventHandler('olympus-admin:server:submitReport', function(reportedId, category, reason, evidence)
    local source = source
    local reporterData = exports['olympus-core']:GetPlayerData(source)
    local reportedData = exports['olympus-core']:GetPlayerData(reportedId)

    if not reporterData or not reportedData then return end

    reason = reason or 'No reason provided'
    evidence = evidence or ''

    -- Insert report into database
    exports.oxmysql:execute('INSERT INTO player_reports (reporter_identifier, reporter_name, reported_identifier, reported_name, category, reason, evidence, report_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)', {
        reporterData.identifier,
        reporterData.name,
        reportedData.identifier,
        reportedData.name,
        category,
        reason,
        evidence,
        os.time()
    }, function(reportId)
        if reportId then
            OlympusAdmin.ActiveReports[reportId] = {
                id = reportId,
                reporter = reporterData.name,
                reported = reportedData.name,
                category = category,
                reason = reason,
                evidence = evidence,
                status = 'open',
                timestamp = os.time()
            }

            -- Notify reporter
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'success',
                title = 'Report Submitted',
                message = 'Your report against ' .. reportedData.name .. ' has been submitted (ID: ' .. reportId .. ')',
                duration = 8000
            })

            -- Notify all online admins
            local adminMessage = string.format("^3[NEW REPORT]^7 %s reported %s for %s (ID: %s)", reporterData.name, reportedData.name, category, reportId)
            print(adminMessage)

            local players = GetPlayers()
            for _, playerId in ipairs(players) do
                local playerSource = tonumber(playerId)
                if IsPlayerAdmin(playerSource) then
                    TriggerClientEvent('olympus:client:notify', playerSource, {
                        type = 'warning',
                        title = 'New Report',
                        message = reporterData.name .. ' reported ' .. reportedData.name .. ' for ' .. category,
                        duration = 10000
                    })
                end
            end
        end
    end)
end)

-- Claim report
RegisterNetEvent('olympus-admin:server:claimReport')
AddEventHandler('olympus-admin:server:claimReport', function(reportId)
    local source = source
    if not HasAdminPermission(source, 1) then return end

    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData then return end

    -- Update report in database
    exports.oxmysql:execute('UPDATE player_reports SET status = ?, assigned_admin = ? WHERE id = ?', {
        'claimed',
        playerData.name,
        reportId
    })

    -- Update local cache
    if OlympusAdmin.ActiveReports[reportId] then
        OlympusAdmin.ActiveReports[reportId].status = 'claimed'
        OlympusAdmin.ActiveReports[reportId].assigned_admin = playerData.name
    end

    LogAdminAction(source, 'Report', 'Claimed report ID: ' .. reportId)

    TriggerClientEvent('olympus:client:notify', source, {
        type = 'success',
        title = 'Report Claimed',
        message = 'You have claimed report ID: ' .. reportId,
        duration = 5000
    })
end)

-- Close report
RegisterNetEvent('olympus-admin:server:closeReport')
AddEventHandler('olympus-admin:server:closeReport', function(reportId, resolution)
    local source = source
    if not HasAdminPermission(source, 1) then return end

    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData then return end

    resolution = resolution or 'Resolved'

    -- Update report in database
    exports.oxmysql:execute('UPDATE player_reports SET status = ?, resolved_time = ? WHERE id = ?', {
        'closed',
        os.time(),
        reportId
    })

    -- Remove from active reports
    OlympusAdmin.ActiveReports[reportId] = nil

    LogAdminAction(source, 'Report', 'Closed report ID: ' .. reportId .. ' - Resolution: ' .. resolution)

    TriggerClientEvent('olympus:client:notify', source, {
        type = 'success',
        title = 'Report Closed',
        message = 'Closed report ID: ' .. reportId,
        duration = 5000
    })
end)

-- Admin chat
RegisterNetEvent('olympus-admin:server:adminChat')
AddEventHandler('olympus-admin:server:adminChat', function(message)
    local source = source
    if not IsPlayerAdmin(source) then return end

    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData then return end

    local adminRankName = GetAdminRankName(playerData.adminRank or 0)
    local chatMessage = string.format("^3[ADMIN CHAT]^7 %s (%s): %s", playerData.name, adminRankName, message)

    print(chatMessage)

    -- Send to all online admins
    local players = GetPlayers()
    for _, playerId in ipairs(players) do
        local playerSource = tonumber(playerId)
        if IsPlayerAdmin(playerSource) then
            TriggerClientEvent('chat:addMessage', playerSource, {
                color = {255, 165, 0},
                multiline = true,
                args = {"[ADMIN CHAT] " .. playerData.name .. " (" .. adminRankName .. ")", message}
            })
        end
    end
end)

-- Get online players for admin panel
RegisterNetEvent('olympus-admin:server:getOnlinePlayers')
AddEventHandler('olympus-admin:server:getOnlinePlayers', function()
    local source = source
    if not IsPlayerAdmin(source) then return end

    local players = GetPlayers()
    local playerList = {}

    for _, playerId in ipairs(players) do
        local playerSource = tonumber(playerId)
        local playerData = exports['olympus-core']:GetPlayerData(playerSource)

        if playerData then
            table.insert(playerList, {
                id = playerSource,
                name = playerData.name,
                identifier = playerData.identifier,
                money = playerData.money or 0,
                bank = playerData.bank or 0,
                faction = playerData.faction or 'civilian',
                adminRank = playerData.adminRank or 0,
                isVanished = OlympusAdmin.VanishedPlayers[playerSource] or false,
                isFrozen = OlympusAdmin.FrozenPlayers[playerSource] or false
            })
        end
    end

    TriggerClientEvent('olympus-admin:client:updatePlayerList', source, playerList)
end)

-- Get active reports for admin panel
RegisterNetEvent('olympus-admin:server:getActiveReports')
AddEventHandler('olympus-admin:server:getActiveReports', function()
    local source = source
    if not IsPlayerAdmin(source) then return end

    exports.oxmysql:query('SELECT * FROM player_reports WHERE status IN (?, ?) ORDER BY report_time DESC LIMIT 50', {
        'open',
        'claimed'
    }, function(reports)
        TriggerClientEvent('olympus-admin:client:updateReportList', source, reports or {})
    end)
end)

-- Check for expired bans on player connect
AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local source = source
    local identifiers = GetPlayerIdentifiers(source)
    local license = nil

    for _, id in ipairs(identifiers) do
        if string.find(id, 'license:') then
            license = id
            break
        end
    end

    if license then
        deferrals.defer()
        deferrals.update('Checking ban status...')

        exports.oxmysql:query('SELECT * FROM player_bans WHERE identifier = ? AND is_active = ? ORDER BY ban_time DESC LIMIT 1', {
            license,
            true
        }, function(result)
            if result and result[1] then
                local ban = result[1]
                local currentTime = os.time()

                if ban.is_permanent then
                    deferrals.done('You are permanently banned from this server.\nReason: ' .. ban.reason .. '\nBanned by: ' .. ban.banned_by .. '\nBan Date: ' .. os.date('%Y-%m-%d %H:%M:%S', ban.ban_time))
                elseif ban.unban_time > 0 and currentTime < ban.unban_time then
                    local timeLeft = ban.unban_time - currentTime
                    deferrals.done('You are temporarily banned from this server.\nReason: ' .. ban.reason .. '\nBanned by: ' .. ban.banned_by .. '\nTime remaining: ' .. FormatTime(timeLeft))
                else
                    -- Ban expired, deactivate it
                    exports.oxmysql:execute('UPDATE player_bans SET is_active = ? WHERE id = ?', {false, ban.id})
                    deferrals.done()
                end
            else
                deferrals.done()
            end
        end)
    else
        deferrals.done()
    end
end)

-- Player disconnect cleanup
AddEventHandler('playerDropped', function(reason)
    local source = source

    -- Clean up admin states
    OlympusAdmin.VanishedPlayers[source] = nil
    OlympusAdmin.SpectatingPlayers[source] = nil
    OlympusAdmin.FrozenPlayers[source] = nil

    -- Update other clients about vanish state
    local players = GetPlayers()
    for _, playerId in ipairs(players) do
        local playerSource = tonumber(playerId)
        TriggerClientEvent('olympus-admin:client:updateVanishedPlayer', playerSource, source, false)
    end
end)

-- Initialize system when server starts
CreateThread(function()
    -- Wait for core system
    while not exports['olympus-core'] do
        Wait(100)
    end

    Wait(2000) -- Additional wait for database
    InitializeAdminSystem()
end)

-- Export functions
exports('IsPlayerAdmin', IsPlayerAdmin)
exports('GetPlayerAdminRank', GetPlayerAdminRank)
exports('HasAdminPermission', HasAdminPermission)
exports('LogAdminAction', LogAdminAction)
exports('SendAdminMessage', function(message)
    NotifyAdmins(message)
end)

-- Server exports
exports('BanPlayer', function(adminSource, targetId, reason, duration, isPermanent)
    TriggerEvent('olympus-admin:server:banPlayer', adminSource, targetId, reason, duration, isPermanent)
end)

exports('KickPlayer', function(adminSource, targetId, reason)
    TriggerEvent('olympus-admin:server:kickPlayer', adminSource, targetId, reason)
end)

exports('WarnPlayer', function(adminSource, targetId, reason, points)
    TriggerEvent('olympus-admin:server:warnPlayer', adminSource, targetId, reason, points)
end)

exports('FreezePlayer', function(adminSource, targetId)
    TriggerEvent('olympus-admin:server:toggleFreeze', adminSource, targetId)
end)

exports('TeleportPlayer', function(adminSource, targetId)
    TriggerEvent('olympus-admin:server:teleportToPlayer', adminSource, targetId)
end)

exports('SpectatePlayer', function(adminSource, targetId)
    TriggerEvent('olympus-admin:server:toggleSpectate', adminSource, targetId)
end)

exports('GetPlayerInfo', function(targetId)
    local playerData = exports['olympus-core']:GetPlayerData(targetId)
    if not playerData then return nil end

    return {
        id = targetId,
        name = playerData.name,
        identifier = playerData.identifier,
        money = playerData.money or 0,
        bank = playerData.bank or 0,
        faction = playerData.faction or 'civilian',
        adminRank = playerData.adminRank or 0,
        isVanished = OlympusAdmin.VanishedPlayers[targetId] or false,
        isFrozen = OlympusAdmin.FrozenPlayers[targetId] or false
    }
end)

exports('GetServerStats', function()
    local players = GetPlayers()
    local adminCount = 0
    local reportCount = 0

    for _, playerId in ipairs(players) do
        local playerSource = tonumber(playerId)
        if IsPlayerAdmin(playerSource) then
            adminCount = adminCount + 1
        end
    end

    for _ in pairs(OlympusAdmin.ActiveReports) do
        reportCount = reportCount + 1
    end

    return {
        playerCount = #players,
        adminCount = adminCount,
        reportCount = reportCount,
        vanishedCount = table.count(OlympusAdmin.VanishedPlayers),
        frozenCount = table.count(OlympusAdmin.FrozenPlayers)
    }
end)

-- Utility function for table counting
function table.count(t)
    local count = 0
    for _ in pairs(t) do
        count = count + 1
    end
    return count
end

print("^2[Olympus Admin]^7 Server module loaded")

-- Teleport to player
RegisterNetEvent('olympus-admin:server:teleportToPlayer', function(targetId)
    local src = source
    if not IsPlayerAdmin(src) then return end
    
    local targetCoords = GetEntityCoords(GetPlayerPed(targetId))
    TriggerClientEvent('olympus-admin:client:teleport', src, targetCoords)
    
    LogAdminAction(src, 'Teleport', 'Teleported to player ' .. GetPlayerName(targetId))
end)

-- Bring player
RegisterNetEvent('olympus-admin:server:bringPlayer', function(targetId)
    local src = source
    if not IsPlayerAdmin(src) then return end
    
    local adminCoords = GetEntityCoords(GetPlayerPed(src))
    TriggerClientEvent('olympus-admin:client:teleport', targetId, adminCoords)
    
    LogAdminAction(src, 'Bring', 'Brought player ' .. GetPlayerName(targetId))
end)

-- Kick player
RegisterNetEvent('olympus-admin:server:kickPlayer', function(targetId, reason)
    local src = source
    if not IsPlayerAdmin(src) then return end
    
    DropPlayer(targetId, 'Kicked by admin: ' .. (reason or 'No reason provided'))
    
    LogAdminAction(src, 'Kick', 'Kicked player ' .. GetPlayerName(targetId) .. ' - Reason: ' .. (reason or 'No reason'))
end)

-- Ban player
RegisterNetEvent('olympus-admin:server:banPlayer', function(targetId, reason, duration)
    local src = source
    if GetPlayerAdminRank(src) < 2 then return end -- Moderator+ required
    
    local Player = OlympusCore.Functions.GetPlayer(targetId)
    if not Player then return end
    
    local banData = {
        license = Player.PlayerData.license,
        reason = reason or 'No reason provided',
        bannedBy = GetPlayerName(src),
        banTime = os.time(),
        duration = duration or 0 -- 0 = permanent
    }
    
    -- Add to ban database
    exports.oxmysql:execute('INSERT INTO player_bans (license, reason, banned_by, ban_time, duration) VALUES (?, ?, ?, ?, ?)', {
        banData.license,
        banData.reason,
        banData.bannedBy,
        banData.banTime,
        banData.duration
    })
    
    DropPlayer(targetId, 'Banned: ' .. banData.reason)
    
    LogAdminAction(src, 'Ban', 'Banned player ' .. GetPlayerName(targetId) .. ' - Reason: ' .. banData.reason)
end)

-- Admin chat
RegisterNetEvent('olympus-admin:server:adminChat', function(message)
    local src = source
    if not IsPlayerAdmin(src) then return end
    
    local playerName = GetPlayerName(src)
    local adminRank = GetPlayerAdminRank(src)
    
    -- Send to all online admins
    local players = OlympusCore.Functions.GetPlayers()
    for _, playerId in pairs(players) do
        if IsPlayerAdmin(playerId) then
            TriggerClientEvent('olympus-admin:client:adminChat', playerId, playerName, message, adminRank)
        end
    end
    
    LogAdminAction(src, 'Admin Chat', message)
end)

-- Log admin actions
function LogAdminAction(source, action, details)
    local Player = OlympusCore.Functions.GetPlayer(source)
    if not Player then return end
    
    local logData = {
        adminName = GetPlayerName(source),
        adminLicense = Player.PlayerData.license,
        action = action,
        details = details,
        timestamp = os.time()
    }
    
    exports.oxmysql:execute('INSERT INTO admin_logs (admin_name, admin_license, action, details, timestamp) VALUES (?, ?, ?, ?, ?)', {
        logData.adminName,
        logData.adminLicense,
        logData.action,
        logData.details,
        logData.timestamp
    })
    
    print(string.format('[ADMIN LOG] %s (%s): %s - %s', logData.adminName, logData.adminLicense, logData.action, logData.details))
end

-- Exports
exports('IsPlayerAdmin', IsPlayerAdmin)
exports('GetPlayerAdminRank', GetPlayerAdminRank)
exports('LogAdminAction', LogAdminAction)

-- Commands
OlympusCore.Commands.Add('noclip', 'Toggle noclip (Admin Only)', {}, false, function(source, args)
    if not IsPlayerAdmin(source) then return end
    TriggerClientEvent('olympus-admin:client:toggleNoclip', source)
    LogAdminAction(source, 'Noclip', 'Toggled noclip')
end, 'admin')

OlympusCore.Commands.Add('heal', 'Heal player (Admin Only)', {{name = 'id', help = 'Player ID (optional)'}}, false, function(source, args)
    if not IsPlayerAdmin(source) then return end
    
    local targetId = args[1] and tonumber(args[1]) or source
    TriggerClientEvent('olympus-medical:client:heal', targetId)
    
    LogAdminAction(source, 'Heal', 'Healed player ' .. GetPlayerName(targetId))
end, 'admin')

OlympusCore.Commands.Add('revive', 'Revive player (Admin Only)', {{name = 'id', help = 'Player ID (optional)'}}, false, function(source, args)
    if not IsPlayerAdmin(source) then return end
    
    local targetId = args[1] and tonumber(args[1]) or source
    TriggerClientEvent('olympus-medical:client:revive', targetId)
    
    LogAdminAction(source, 'Revive', 'Revived player ' .. GetPlayerName(targetId))
end, 'admin')

print("[Olympus Admin] Server module loaded")
