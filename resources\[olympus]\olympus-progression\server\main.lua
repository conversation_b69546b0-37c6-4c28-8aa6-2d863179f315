-- Olympus Progression System - Server Main

CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end
    
    print("[Olympus Progression] Server initialized")
end)

exports('PromotePlayer', function(source, newRank)
    print("[Olympus Progression] Promoting player to rank:", newRank)
end)

exports('UpdateSkillXP', function(source, skill, xp)
    print("[Olympus Progression] Updating skill XP:", skill)
end)

exports('GrantUnlock', function(source, unlockType)
    print("[Olympus Progression] Granting unlock:", unlockType)
end)

exports('GetRankRequirements', function(rank)
    return {}
end)

print("[Olympus Progression] Server module loaded")
