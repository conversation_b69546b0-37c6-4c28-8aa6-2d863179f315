-- ========================================
-- OLYMPUS APD SEARCH & SEIZURE SERVER
-- Based on Original Olympus Functions
-- ========================================

local OlympusSearchSeizure = {}

-- ========================================
-- DATABASE INITIALIZATION
-- ========================================
function OlympusSearchSeizure.InitDatabase()
    -- Create search and seizure logs table
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS `apd_search_seizure_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `officer_id` varchar(50) NOT NULL,
            `officer_name` varchar(100) NOT NULL,
            `target_id` varchar(50) NOT NULL,
            `target_name` varchar(100) NOT NULL,
            `action_type` enum('search_player','seize_vehicle','seize_items') NOT NULL,
            `items_seized` text DEFAULT NULL,
            `contraband_value` int(11) DEFAULT 0,
            `reward_amount` int(11) DEFAULT 0,
            `position` varchar(100) NOT NULL,
            `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
            PRIMARY KEY (`id`),
            KEY `officer_id` (`officer_id`),
            KEY `target_id` (`target_id`),
            KEY `action_type` (`action_type`),
            KEY `timestamp` (`timestamp`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ]])
end

-- ========================================
-- UTILITY FUNCTIONS
-- ========================================
function OlympusSearchSeizure.GetPlayerData(src)
    local success, result = pcall(function()
        return exports['olympus-core']:GetPlayerData(src)
    end)
    return success and result or nil
end

function OlympusSearchSeizure.GetPlayerFaction(src)
    local playerData = OlympusSearchSeizure.GetPlayerData(src)
    if not playerData then return 'civilian' end
    return playerData.faction or 'civilian'
end

function OlympusSearchSeizure.AddPlayerMoney(src, account, amount)
    local success, result = pcall(function()
        return exports['olympus-core']:AddMoney(src, account, amount)
    end)
    return success and result
end

function OlympusSearchSeizure.GetPlayerInventory(src)
    local success, result = pcall(function()
        return exports['olympus-core']:GetPlayerInventory(src)
    end)
    return success and result or {}
end

function OlympusSearchSeizure.RemoveItem(src, item, amount)
    local success, result = pcall(function()
        return exports['olympus-core']:RemoveItem(src, item, amount or 1)
    end)
    return success and result
end

function OlympusSearchSeizure.HasLicense(src, license)
    local success, result = pcall(function()
        return exports['olympus-licenses']:HasLicense(src, license)
    end)
    return success and result or false
end

function OlympusSearchSeizure.GetVehicleOwners(vehicle)
    local success, result = pcall(function()
        return exports['olympus-vehicles']:GetVehicleOwners(vehicle)
    end)
    return success and result or {}
end

function OlympusSearchSeizure.GetVehicleValue(model)
    local success, result = pcall(function()
        return exports['olympus-vehicles']:GetVehicleValue(model)
    end)
    return success and result or 50000
end

function OlympusSearchSeizure.IsBlackwaterVehicle(vehicle)
    -- Check if vehicle is a Blackwater vehicle
    local success, result = pcall(function()
        return exports['olympus-vehicles']:IsBlackwaterVehicle(vehicle)
    end)
    return success and result or false
end

function OlympusSearchSeizure.LogAction(officerId, officerName, targetId, targetName, actionType, itemsSeized, contrabandValue, rewardAmount, position)
    exports.oxmysql:execute('INSERT INTO apd_search_seizure_logs (officer_id, officer_name, target_id, target_name, action_type, items_seized, contraband_value, reward_amount, position) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)', {
        officerId, officerName, targetId, targetName, actionType, json.encode(itemsSeized or {}), contrabandValue or 0, rewardAmount or 0, json.encode(position)
    })
end

-- ========================================
-- PLAYER SEARCH SYSTEM
-- Based on fn_searchAction.sqf and fn_searchClient.sqf
-- ========================================
function OlympusSearchSeizure.SearchPlayer(src, targetId)
    local target = GetPlayerPed(targetId)
    if not target or target == 0 then
        TriggerClientEvent('olympus-apd-search-seizure:notify', src, Config.Notifications.search.tooFar, 'error')
        return false
    end
    
    local officerFaction = OlympusSearchSeizure.GetPlayerFaction(src)
    local targetFaction = OlympusSearchSeizure.GetPlayerFaction(targetId)
    
    -- Check if officer has permission to search
    if officerFaction ~= 'police' then
        -- Check if vigilante and target is civilian
        if officerFaction == 'vigilante' and targetFaction == 'civilian' and Config.SearchSeizure.playerSearch.allowVigilante then
            -- Vigilantes can search civilians for illegal items only
        else
            TriggerClientEvent('olympus-apd-search-seizure:notify', src, Config.Notifications.search.noPermission, 'error')
            return false
        end
    end
    
    -- Check if target is alive
    if GetEntityHealth(target) <= 0 then
        TriggerClientEvent('olympus-apd-search-seizure:notify', src, Config.Notifications.search.notAlive, 'error')
        return false
    end
    
    -- Get target's inventory and check for illegal items
    local inventory = OlympusSearchSeizure.GetPlayerInventory(targetId)
    local illegalItems = {}
    local contrabandValue = 0
    local isRobber = false
    
    -- Check for illegal items
    for item, amount in pairs(inventory) do
        if table.contains(Config.SearchSeizure.itemSeizure.illegalItems, item) then
            illegalItems[item] = amount
            local itemValue = Config.IllegalItemValues[item] or 0
            contrabandValue = contrabandValue + (amount * itemValue)
        end
    end
    
    -- Check if player is a robbery suspect (has no ATM access)
    local playerData = OlympusSearchSeizure.GetPlayerData(targetId)
    if playerData and playerData.robber then
        isRobber = true
    end
    
    -- Get player equipment information
    TriggerClientEvent('olympus-apd-search-seizure:requestPlayerEquipment', targetId, src)
    
    -- Send search results to officer
    TriggerClientEvent('olympus-apd-search-seizure:searchResult', src, {
        targetId = targetId,
        targetName = GetPlayerName(targetId),
        illegalItems = illegalItems,
        contrabandValue = contrabandValue,
        isRobber = isRobber
    })
    
    -- Broadcast contraband found
    if contrabandValue > 0 then
        local message = string.format(Config.Notifications.search.contraband, GetPlayerName(targetId), contrabandValue)
        TriggerClientEvent('olympus-apd-search-seizure:broadcast', -1, message, 'police')
    end
    
    -- Broadcast robbery suspect
    if isRobber then
        local message = string.format(Config.Notifications.search.robber, GetPlayerName(targetId))
        TriggerClientEvent('olympus-apd-search-seizure:broadcast', -1, message, 'all')
    end
    
    -- Log search action
    OlympusSearchSeizure.LogAction(
        GetPlayerIdentifier(src, 0), GetPlayerName(src),
        GetPlayerIdentifier(targetId, 0), GetPlayerName(targetId),
        'search_player', illegalItems, contrabandValue, 0,
        GetEntityCoords(GetPlayerPed(src))
    )
    
    return true
end

-- ========================================
-- VEHICLE SEIZURE SYSTEM
-- Based on fn_seizeAction.sqf
-- ========================================
function OlympusSearchSeizure.SeizeVehicle(src, vehicleNetId)
    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    if not vehicle or vehicle == 0 then
        TriggerClientEvent('olympus-apd-search-seizure:notify', src, Config.Notifications.seizure.tooFar, 'error')
        return false
    end
    
    local officerFaction = OlympusSearchSeizure.GetPlayerFaction(src)
    
    -- Check if officer has permission to seize
    if officerFaction ~= 'police' then
        TriggerClientEvent('olympus-apd-search-seizure:notify', src, Config.Notifications.seizure.noPermission, 'error')
        return false
    end
    
    -- Check vehicle damage
    local damage = GetVehicleEngineHealth(vehicle) / 1000.0
    if damage < 0.15 then
        TriggerClientEvent('olympus-apd-search-seizure:notify', src, Config.Notifications.seizure.tooRekt, 'error')
        return false
    end
    
    -- Check if vehicle has occupants
    local occupants = GetVehicleNumberOfPassengers(vehicle)
    if occupants > 0 then
        TriggerClientEvent('olympus-apd-search-seizure:notify', src, Config.Notifications.seizure.occupied, 'error')
        return false
    end
    
    -- Get vehicle information
    local vehicleModel = GetEntityModel(vehicle)
    local vehicleName = GetDisplayNameFromVehicleModel(vehicleModel)
    local vehicleClass = GetVehicleClass(vehicle)
    local owners = OlympusSearchSeizure.GetVehicleOwners(vehicle)
    local isBlackwater = OlympusSearchSeizure.IsBlackwaterVehicle(vehicle)
    
    local reward = 0
    local targetInfo = ""
    
    if isBlackwater then
        -- Blackwater vehicle seizure
        reward = Config.SearchSeizure.vehicleSeizure.blackwaterReward
        
        -- Check if it's an armed Blackwater vehicle
        local vehicleHash = GetHashKey(GetEntityModel(vehicle))
        if vehicleHash == GetHashKey("insurgent2") or vehicleHash == GetHashKey("technical") then
            reward = Config.SearchSeizure.vehicleSeizure.blackwaterArmedReward
        end
        
        targetInfo = "Blackwater"
        
        -- Delete Blackwater vehicle immediately
        DeleteEntity(vehicle)
        
        TriggerClientEvent('olympus-apd-search-seizure:notify', src, Config.Notifications.seizure.blackwater, 'success')
    else
        -- Regular vehicle seizure
        local vehicleValue = OlympusSearchSeizure.GetVehicleValue(vehicleModel)
        reward = math.floor(vehicleValue * Config.SearchSeizure.vehicleSeizure.valueMultiplier)
        
        -- Get owner information
        if #owners > 0 then
            targetInfo = owners[1].name
        else
            targetInfo = "Unknown"
        end
        
        -- Remove vehicle from world via vehicle system
        local success, result = pcall(function()
            return exports['olympus-vehicles']:SeizeVehicle(vehicle, src)
        end)
        
        if not success or not result then
            TriggerClientEvent('olympus-apd-search-seizure:notify', src, "Failed to seize vehicle", 'error')
            return false
        end
    end
    
    -- Give reward to officer
    if reward > 0 then
        -- Split pay with other officers (original Olympus mechanic)
        local success, result = pcall(function()
            return exports['olympus-apd']:SplitPay(src, reward, 1, 150)
        end)
        
        if not success then
            OlympusSearchSeizure.AddPlayerMoney(src, 'bank', reward)
        end
    end
    
    -- Send notification
    local message = string.format(Config.Notifications.seizure.reward, reward)
    TriggerClientEvent('olympus-apd-search-seizure:notify', src, message, 'success')
    
    -- Broadcast seizure
    local broadcastMessage = string.format("%s has seized %s's %s", GetPlayerName(src), targetInfo, vehicleName)
    TriggerClientEvent('olympus-apd-search-seizure:broadcast', -1, broadcastMessage, 'all')
    
    -- Log seizure action
    OlympusSearchSeizure.LogAction(
        GetPlayerIdentifier(src, 0), GetPlayerName(src),
        targetInfo, targetInfo,
        'seize_vehicle', {vehicleName}, 0, reward,
        GetEntityCoords(GetPlayerPed(src))
    )
    
    return true
end

-- ========================================
-- ITEM SEIZURE SYSTEM
-- Based on fn_seizePlayerItems.sqf
-- ========================================
function OlympusSearchSeizure.SeizePlayerItems(src, targetId)
    local target = GetPlayerPed(targetId)
    if not target or target == 0 then
        TriggerClientEvent('olympus-apd-search-seizure:notify', src, Config.Notifications.search.tooFar, 'error')
        return false
    end

    local officerFaction = OlympusSearchSeizure.GetPlayerFaction(src)

    -- Check if officer has permission to seize items
    if not table.contains(Config.SearchSeizure.itemSeizure.allowedFactions, officerFaction) then
        TriggerClientEvent('olympus-apd-search-seizure:notify', src, Config.Notifications.itemSeizure.noPermission, 'error')
        return false
    end

    -- Get target's inventory and seize illegal items
    local inventory = OlympusSearchSeizure.GetPlayerInventory(targetId)
    local seizedItems = {}
    local totalValue = 0

    -- Seize illegal items
    for item, amount in pairs(inventory) do
        if table.contains(Config.SearchSeizure.itemSeizure.illegalItems, item) then
            if OlympusSearchSeizure.RemoveItem(targetId, item, amount) then
                seizedItems[item] = amount
                local itemValue = Config.IllegalItemValues[item] or 0
                totalValue = totalValue + (amount * itemValue)
            end
        end
    end

    if next(seizedItems) == nil then
        TriggerClientEvent('olympus-apd-search-seizure:notify', src, Config.Notifications.itemSeizure.noItems, 'warning')
        return false
    end

    -- Send notifications
    local message = string.format(Config.Notifications.itemSeizure.success, GetPlayerName(targetId))
    TriggerClientEvent('olympus-apd-search-seizure:notify', src, message, 'success')
    TriggerClientEvent('olympus-apd-search-seizure:notify', targetId, "Your illegal items have been seized by the APD", 'error')

    -- Log seizure action
    OlympusSearchSeizure.LogAction(
        GetPlayerIdentifier(src, 0), GetPlayerName(src),
        GetPlayerIdentifier(targetId, 0), GetPlayerName(targetId),
        'seize_items', seizedItems, totalValue, 0,
        GetEntityCoords(GetPlayerPed(src))
    )

    return true
end

-- ========================================
-- UTILITY HELPER FUNCTIONS
-- ========================================
function table.contains(table, element)
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

-- ========================================
-- EVENT HANDLERS
-- ========================================
RegisterNetEvent('olympus-apd-search-seizure:searchPlayer', function(targetId)
    local src = source
    OlympusSearchSeizure.SearchPlayer(src, targetId)
end)

RegisterNetEvent('olympus-apd-search-seizure:seizeVehicle', function(vehicleNetId)
    local src = source
    OlympusSearchSeizure.SeizeVehicle(src, vehicleNetId)
end)

RegisterNetEvent('olympus-apd-search-seizure:seizePlayerItems', function(targetId)
    local src = source
    OlympusSearchSeizure.SeizePlayerItems(src, targetId)
end)

RegisterNetEvent('olympus-apd-search-seizure:playerEquipmentResponse', function(officerId, equipmentData)
    local src = source
    TriggerClientEvent('olympus-apd-search-seizure:playerEquipmentData', officerId, src, equipmentData)
end)

-- ========================================
-- EXPORT FUNCTIONS
-- ========================================
exports('SearchPlayer', function(src, targetId)
    return OlympusSearchSeizure.SearchPlayer(src, targetId)
end)

exports('SeizeVehicle', function(src, vehicleNetId)
    return OlympusSearchSeizure.SeizeVehicle(src, vehicleNetId)
end)

exports('SeizePlayerItems', function(src, targetId)
    return OlympusSearchSeizure.SeizePlayerItems(src, targetId)
end)

exports('CanSearchPlayer', function(src, targetId)
    local officerFaction = OlympusSearchSeizure.GetPlayerFaction(src)
    local targetFaction = OlympusSearchSeizure.GetPlayerFaction(targetId)

    if officerFaction == 'police' then
        return true
    elseif officerFaction == 'vigilante' and targetFaction == 'civilian' and Config.SearchSeizure.playerSearch.allowVigilante then
        return true
    end

    return false
end)

exports('CanSeizeVehicle', function(src)
    local officerFaction = OlympusSearchSeizure.GetPlayerFaction(src)
    return officerFaction == 'police'
end)

exports('GetPlayerIllegalItems', function(targetId)
    local inventory = OlympusSearchSeizure.GetPlayerInventory(targetId)
    local illegalItems = {}

    for item, amount in pairs(inventory) do
        if table.contains(Config.SearchSeizure.itemSeizure.illegalItems, item) then
            illegalItems[item] = amount
        end
    end

    return illegalItems
end)

exports('GetVehicleSeizeValue', function(vehicleNetId)
    local vehicle = NetworkGetEntityFromNetworkId(vehicleNetId)
    if not vehicle or vehicle == 0 then return 0 end

    local isBlackwater = OlympusSearchSeizure.IsBlackwaterVehicle(vehicle)
    if isBlackwater then
        local vehicleHash = GetHashKey(GetEntityModel(vehicle))
        if vehicleHash == GetHashKey("insurgent2") or vehicleHash == GetHashKey("technical") then
            return Config.SearchSeizure.vehicleSeizure.blackwaterArmedReward
        else
            return Config.SearchSeizure.vehicleSeizure.blackwaterReward
        end
    else
        local vehicleModel = GetEntityModel(vehicle)
        local vehicleValue = OlympusSearchSeizure.GetVehicleValue(vehicleModel)
        return math.floor(vehicleValue * Config.SearchSeizure.vehicleSeizure.valueMultiplier)
    end
end)

-- ========================================
-- INITIALIZATION
-- ========================================
CreateThread(function()
    OlympusSearchSeizure.InitDatabase()
    print("^2[Olympus APD Search & Seizure]^7 Server system initialized")
end)
