-- Olympus Shop Robbery System - Client Main
-- Based on original fn_robShops.sqf

local OlympusShopRobbery = {}
local currentRobbery = nil
local robberyBlips = {}
local robberyMarkers = {}

-- Initialize system
CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end
    
    print("[Olympus Shop Robbery] Client initialized")
    
    -- Create shop blips
    OlympusShopRobbery.CreateShopBlips()
    
    -- Start interaction thread
    CreateThread(OlympusShopRobbery.InteractionThread)
end)

-- Utility Functions
function OlympusShopRobbery.Notify(message, type)
    local success = pcall(function()
        exports['olympus-core']:Notify(message, type)
    end)
    
    if not success then
        -- Fallback notification
        SetNotificationTextEntry("STRING")
        AddTextComponentString(message)
        DrawNotification(false, false)
    end
end

function OlympusShopRobbery.PlayAnimation(dict, anim, duration)
    RequestAnimDict(dict)
    while not HasAnimDictLoaded(dict) do
        Wait(10)
    end
    
    local playerPed = PlayerPedId()
    TaskPlayAnim(playerPed, dict, anim, 8.0, -8.0, duration or -1, 1, 0, false, false, false)
end

function OlympusShopRobbery.ShowProgressBar(text, duration, onComplete)
    local success = pcall(function()
        exports['progressbar']:Progress({
            name = "shop_robbery",
            duration = duration,
            label = text,
            useWhileDead = false,
            canCancel = true,
            controlDisables = {
                disableMovement = false,
                disableCarMovement = false,
                disableMouse = false,
                disableCombat = true,
            }
        }, function(cancelled)
            if not cancelled and onComplete then
                onComplete()
            elseif cancelled then
                TriggerServerEvent('olympus-shop-robbery:server:cancelRobbery', currentRobbery.shopIndex, 'Robbery cancelled')
                currentRobbery = nil
            end
        end)
    end)
    
    if not success then
        -- Fallback progress system
        local startTime = GetGameTimer()
        CreateThread(function()
            while GetGameTimer() - startTime < duration do
                Wait(100)
                if not currentRobbery then
                    return
                end
            end
            if onComplete then
                onComplete()
            end
        end)
    end
end

function OlympusShopRobbery.GetClosestShop()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    local closestShop = nil
    local closestDistance = math.huge
    
    for i, shop in pairs(Config.ShopRobbery.shops) do
        local distance = #(coords - shop.coords)
        if distance < closestDistance then
            closestDistance = distance
            closestShop = {index = i, shop = shop, distance = distance}
        end
    end
    
    return closestShop
end

-- Shop Blip System
function OlympusShopRobbery.CreateShopBlips()
    for i, shop in pairs(Config.ShopRobbery.shops) do
        local blip = AddBlipForCoord(shop.coords.x, shop.coords.y, shop.coords.z)
        SetBlipSprite(blip, shop.blip.sprite)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, shop.blip.scale)
        SetBlipColour(blip, shop.blip.color)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(shop.name)
        EndTextCommandSetBlipName(blip)
        
        robberyBlips[i] = blip
    end
end

-- Robbery System
function OlympusShopRobbery.StartRobbery(shopIndex)
    if currentRobbery then
        OlympusShopRobbery.Notify("You're already robbing a store!", "error")
        return
    end
    
    local shop = Config.ShopRobbery.shops[shopIndex]
    if not shop then return end
    
    -- Trigger server-side robbery start
    TriggerServerEvent('olympus-shop-robbery:server:startRobbery', shopIndex)
end

function OlympusShopRobbery.StartRobberyProgress(shopIndex, duration)
    local shop = Config.ShopRobbery.shops[shopIndex]
    if not shop then return end
    
    currentRobbery = {
        shopIndex = shopIndex,
        shop = shop,
        startTime = GetGameTimer(),
        duration = duration * 1000 -- Convert to milliseconds
    }
    
    -- Play robbery animation
    OlympusShopRobbery.PlayAnimation("mp_common", "givetake1_a", currentRobbery.duration)
    
    -- Show progress bar
    OlympusShopRobbery.ShowProgressBar("Robbing store...", currentRobbery.duration, function()
        -- Robbery completed successfully
        TriggerServerEvent('olympus-shop-robbery:server:completeRobbery', shopIndex, true)
        currentRobbery = nil
        ClearPedTasks(PlayerPedId())
    end)
    
    -- Start distance checking thread
    CreateThread(function()
        while currentRobbery and currentRobbery.shopIndex == shopIndex do
            Wait(2000) -- Check every 2 seconds
            
            local playerPed = PlayerPedId()
            local coords = GetEntityCoords(playerPed)
            local distance = #(coords - shop.coords)
            
            if distance > Config.ShopRobbery.settings.maxDistance then
                -- Player moved too far
                TriggerServerEvent('olympus-shop-robbery:server:cancelRobbery', shopIndex, 'You moved too far from the store!')
                currentRobbery = nil
                ClearPedTasks(PlayerPedId())
                break
            end
        end
    end)
end

-- Robbery Marker System
function OlympusShopRobbery.CreateRobberyMarker(markerName, coords, shopName)
    if robberyMarkers[markerName] then
        OlympusShopRobbery.RemoveRobberyMarker(markerName)
    end
    
    -- Create map blip for robbery
    local blip = AddBlipForCoord(coords.x, coords.y, coords.z)
    SetBlipSprite(blip, 161) -- Warning icon
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, 1.0)
    SetBlipColour(blip, 17) -- Orange color
    SetBlipFlashes(blip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("Store Robbery - " .. shopName)
    EndTextCommandSetBlipName(blip)
    
    robberyMarkers[markerName] = {
        blip = blip,
        coords = coords,
        shopName = shopName,
        created = GetGameTimer()
    }
    
    -- Auto-remove after duration
    SetTimeout(Config.ShopRobbery.notifications.markerDuration * 1000, function()
        OlympusShopRobbery.RemoveRobberyMarker(markerName)
    end)
end

function OlympusShopRobbery.RemoveRobberyMarker(markerName)
    local marker = robberyMarkers[markerName]
    if not marker then return end
    
    if DoesBlipExist(marker.blip) then
        RemoveBlip(marker.blip)
    end
    
    robberyMarkers[markerName] = nil
end

-- Interaction System
function OlympusShopRobbery.InteractionThread()
    while true do
        Wait(0)
        
        local playerPed = PlayerPedId()
        local coords = GetEntityCoords(playerPed)
        local closestShop = OlympusShopRobbery.GetClosestShop()
        
        if closestShop and closestShop.distance <= 2.0 then
            -- Show interaction prompt
            DrawText3D(closestShop.shop.coords.x, closestShop.shop.coords.y, closestShop.shop.coords.z + 0.5, 
                "[E] Rob Store")
            
            if IsControlJustReleased(0, 38) then -- E key
                OlympusShopRobbery.StartRobbery(closestShop.index)
            end
        else
            Wait(500) -- Reduce frequency when not near shops
        end
    end
end

function DrawText3D(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())
    
    if onScreen then
        SetTextScale(0.35, 0.35)
        SetTextFont(4)
        SetTextProportional(1)
        SetTextColour(255, 255, 255, 215)
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x, _y)
        
        local factor = (string.len(text)) / 370
        DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 41, 11, 41, 68)
    end
end

-- Event Handlers
RegisterNetEvent('olympus-shop-robbery:client:notify', function(message, type)
    OlympusShopRobbery.Notify(message, type)
end)

RegisterNetEvent('olympus-shop-robbery:client:startRobberyProgress', function(shopIndex, duration)
    OlympusShopRobbery.StartRobberyProgress(shopIndex, duration)
end)

RegisterNetEvent('olympus-shop-robbery:client:createRobberyMarker', function(markerName, coords, shopName)
    OlympusShopRobbery.CreateRobberyMarker(markerName, coords, shopName)
end)

RegisterNetEvent('olympus-shop-robbery:client:removeRobberyMarker', function(markerName)
    OlympusShopRobbery.RemoveRobberyMarker(markerName)
end)

-- Commands
RegisterCommand('robstore', function()
    local closestShop = OlympusShopRobbery.GetClosestShop()
    if closestShop and closestShop.distance <= 5.0 then
        OlympusShopRobbery.StartRobbery(closestShop.index)
    else
        OlympusShopRobbery.Notify("You're not near a store!", "error")
    end
end, false)

-- Export Functions
exports('IsShopBeingRobbed', function(shopIndex)
    return currentRobbery and currentRobbery.shopIndex == shopIndex
end)

exports('GetRobberyStatus', function()
    return currentRobbery
end)

exports('StartRobbery', function(shopIndex)
    OlympusShopRobbery.StartRobbery(shopIndex)
    return true
end)

print("[Olympus Shop Robbery] Client module loaded")
