fx_version 'cerulean'
game 'gta5'

name 'Olympus Lottery System'
description 'Complete lottery and powerball system'
author 'Olympus Development Team'
version '1.0.0'

-- Dependencies
dependencies {
    'olympus-core',
    'olympus-ui',
    'oxmysql'
}

-- Shared scripts
shared_scripts {
    'config/shared.lua'
}

-- Client scripts
client_scripts {
    'client/main.lua',
    'client/lottery_ui.lua',
    'client/gas_station_integration.lua'
}

-- Server scripts
server_scripts {
    '@oxmysql/lib/MySQL.lua',
    'server/main.lua'
}

-- UI files
ui_page 'html/index.html'

files {
    'html/index.html'
}

-- Exports
exports {
    'BuyLotteryTicket',
    'GetCurrentJackpot',
    'GetPlayerTickets'
}

server_exports {
    'StartLottery',
    'DrawWinner',
    'GetLotteryStats'
}
