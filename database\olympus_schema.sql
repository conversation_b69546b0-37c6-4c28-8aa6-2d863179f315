-- ========================================
-- OLYMPUS ALTIS LIFE DATABASE SCHEMA
-- Complete recreation based on original dump
-- ========================================

CREATE DATABASE IF NOT EXISTS `olympus_altis_life` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `olympus_altis_life`;

-- ========================================
-- CORE PLAYER TABLE
-- ========================================
CREATE TABLE IF NOT EXISTS `players` (
  `uid` int(12) NOT NULL AUTO_INCREMENT,
  `name` varchar(32) NOT NULL,
  `playerid` varchar(50) NOT NULL,
  `cash` int(100) NOT NULL DEFAULT 0,
  `bankacc` int(100) NOT NULL DEFAULT ********,
  `coplevel` enum('0','1','2','3','4','5','6','7','8','9','10') NOT NULL DEFAULT '0',
  `deposit_box` int(11) unsigned NOT NULL DEFAULT 0,
  `cop_licenses` text DEFAULT NULL,
  `civ_licenses` text DEFAULT NULL,
  `med_licenses` text DEFAULT NULL,
  `cop_gear` text NOT NULL DEFAULT '[]',
  `med_gear` text NOT NULL DEFAULT '[]',
  `arrested` text NOT NULL DEFAULT '[]',
  `aliases` text NOT NULL DEFAULT '[]',
  `mediclevel` enum('0','1','2','3','4','5','6','7') NOT NULL DEFAULT '0',
  `adminlevel` enum('0','1','2','3','4') NOT NULL DEFAULT '0',
  `restrictions_level` enum('0','1') NOT NULL DEFAULT '0',
  `civcouncil_level` enum('0','1','2','3','4') NOT NULL DEFAULT '0',
  `designer_level` enum('0','1','2','3','4') NOT NULL DEFAULT '0',
  `developer_level` enum('0','1','2','3','4') NOT NULL DEFAULT '0',
  `donatorlvl` int(100) NOT NULL DEFAULT 0,
  `civ_gear` text NOT NULL DEFAULT '[]',
  `blacklist` tinyint(1) NOT NULL DEFAULT 0,
  `coordinates` text DEFAULT NULL,
  `player_stats` text DEFAULT NULL,
  `wanted` text DEFAULT NULL,
  `last_active` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `joined` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_side` varchar(8) NOT NULL DEFAULT 'civ',
  `last_server` int(1) NOT NULL DEFAULT 1,
  `cop_gear_tanoa` text NOT NULL DEFAULT '[]',
  `med_gear_tanoa` text NOT NULL DEFAULT '[]',
  `civ_gear_tanoa` text NOT NULL DEFAULT '[]',
  `coordinates_tanoa` text DEFAULT NULL,
  `newslevel` enum('0','1','2','3','4','5','6') NOT NULL DEFAULT '0',
  `warpts` int(100) NOT NULL DEFAULT 0,
  `warkills` int(11) NOT NULL DEFAULT 0,
  `wardeaths` int(11) NOT NULL DEFAULT 0,
  `supportteam` enum('0','1','2','3','4','5','6') NOT NULL DEFAULT '0',
  `muted` timestamp NOT NULL DEFAULT current_timestamp(),
  `vigiarrests` int(12) NOT NULL DEFAULT 0,
  `vigiarrests_stored` int(12) NOT NULL DEFAULT 0,
  `current_title` varchar(50) DEFAULT NULL,
  `realtor_cash` int(100) NOT NULL DEFAULT 0,
  `newdonor` decimal(10,2) NOT NULL DEFAULT 0.00,
  `hex_icon` varchar(50) DEFAULT NULL,
  `hex_icon_redemptions` smallint(5) unsigned NOT NULL DEFAULT 5,
  `conq_gear` text NOT NULL DEFAULT '"[]"',
  `coordinates_malden` text NOT NULL DEFAULT '"[]"',
  PRIMARY KEY (`uid`),
  UNIQUE KEY `playerid` (`playerid`),
  KEY `name` (`name`),
  KEY `blacklist` (`blacklist`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ========================================
-- GANG SYSTEM TABLES
-- ========================================
CREATE TABLE IF NOT EXISTS `gangs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(32) DEFAULT NULL,
  `bank` int(100) DEFAULT 0,
  `active` tinyint(4) DEFAULT 1,
  `kills` int(11) NOT NULL DEFAULT 0,
  `deaths` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name_UNIQUE` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `gangmembers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `playerid` varchar(50) NOT NULL,
  `name` varchar(50) NOT NULL,
  `gangname` varchar(50) DEFAULT NULL,
  `gangid` int(11) DEFAULT 8,
  `rank` int(2) DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name_UNIQUE` (`playerid`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `gangwars` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `instigator` varchar(50) NOT NULL,
  `init_gangid` int(11) NOT NULL,
  `init_gangname` varchar(50) NOT NULL,
  `acceptor` varchar(50) NOT NULL,
  `acpt_gangid` int(11) NOT NULL,
  `acpt_gangname` varchar(50) NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 0,
  `started` timestamp NOT NULL DEFAULT current_timestamp(),
  `start_time` int(11) DEFAULT NULL,
  `end_time` int(11) DEFAULT NULL,
  `winner` varchar(10) DEFAULT NULL,
  `init_kills` int(11) DEFAULT 0,
  `acpt_kills` int(11) DEFAULT 0,
  `init_points` int(11) DEFAULT 0,
  `acpt_points` int(11) DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `territories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `server` varchar(50) DEFAULT 'olympus',
  `territory_name` varchar(32) DEFAULT NULL,
  `gang_id` int(11) DEFAULT 0,
  `gang_name` varchar(32) DEFAULT 'Neutral',
  `capture_progress` int(11) DEFAULT 50,
  PRIMARY KEY (`id`),
  UNIQUE KEY `territory_server` (`territory_name`, `server`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- Insert default territories (matches original Olympus)
INSERT IGNORE INTO `territories` (`server`, `territory_name`, `gang_id`, `gang_name`, `capture_progress`) VALUES
('olympus', 'Meth', 0, 'Neutral', 50),
('olympus', 'Mushroom', 0, 'Neutral', 50),
('olympus', 'Moonshine', 0, 'Neutral', 50),
('olympus', 'Arms', 0, 'Neutral', 50);

-- ========================================
-- VEHICLE SYSTEM
-- ========================================
CREATE TABLE IF NOT EXISTS `vehicles` (
  `id` int(12) NOT NULL AUTO_INCREMENT,
  `side` varchar(15) NOT NULL,
  `classname` varchar(32) NOT NULL,
  `type` varchar(12) NOT NULL,
  `pid` varchar(50) NOT NULL,
  `alive` tinyint(1) NOT NULL DEFAULT 1,
  `active` tinyint(1) NOT NULL DEFAULT 0,
  `plate` int(20) NOT NULL,
  `color` text NOT NULL,
  `inventory` text NOT NULL,
  `insured` tinyint(1) NOT NULL DEFAULT 0,
  `modifications` text NOT NULL,
  `persistentServer` varchar(50) DEFAULT 'olympus',
  `persistentPosition` text DEFAULT NULL,
  `persistentDirection` int(5) DEFAULT 0,
  `inAH` int(1) NOT NULL DEFAULT 0,
  `customName` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `side` (`side`),
  KEY `pid` (`pid`),
  KEY `type` (`type`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ========================================
-- HOUSING SYSTEM
-- ========================================
CREATE TABLE IF NOT EXISTS `houses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pid` varchar(50) DEFAULT NULL,
  `pos` longtext DEFAULT NULL,
  `owned` enum('0','1') DEFAULT '0',
  `player_keys` longtext DEFAULT '[]',
  `inventory` longtext DEFAULT '{}',
  `storageCapacity` int(11) DEFAULT 100,
  `inAH` int(11) DEFAULT 0,
  `oil` tinyint(1) DEFAULT 0,
  `physical_inventory` longtext DEFAULT '{}',
  `physicalStorageCapacity` int(11) DEFAULT 100,
  `expires_on` datetime DEFAULT NULL,
  `server` varchar(50) DEFAULT 'olympus',
  `locked` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`id`),
  KEY `pid` (`pid`),
  KEY `owned` (`owned`),
  KEY `server` (`server`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;



-- ========================================
-- AUCTION HOUSE SYSTEM
-- ========================================
CREATE TABLE IF NOT EXISTS `auctions` (
  `aucID` int(16) NOT NULL AUTO_INCREMENT,
  `playerID` varchar(50) NOT NULL,
  `className` varchar(32) NOT NULL,
  `price` int(11) NOT NULL,
  `type` int(1) NOT NULL DEFAULT 0,
  `started` datetime NOT NULL DEFAULT current_timestamp(),
  `curBidPrice` int(16) NOT NULL DEFAULT 0,
  `server` int(1) NOT NULL DEFAULT -1,
  `playerName` varchar(32) DEFAULT NULL,
  `bidderName` varchar(32) DEFAULT NULL,
  `bidderID` varchar(50) DEFAULT NULL,
  `active` int(1) NOT NULL DEFAULT 0,
  `quantity` int(3) NOT NULL DEFAULT 1,
  PRIMARY KEY (`aucID`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ========================================
-- FEDERAL EVENTS SYSTEM
-- ========================================
CREATE TABLE IF NOT EXISTS `fed_results` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `event_name` varchar(50) NOT NULL,
  `action` varchar(20) NOT NULL,
  `player_id` varchar(50) DEFAULT NULL,
  `player_name` varchar(50) DEFAULT NULL,
  `gang_id` int(11) DEFAULT NULL,
  `gang_name` varchar(50) DEFAULT NULL,
  `participants` text DEFAULT NULL,
  `rewards` text DEFAULT NULL,
  `apd_online` int(11) DEFAULT 0,
  `duration` int(11) DEFAULT 0,
  `success` tinyint(1) DEFAULT 0,
  `defused_by` varchar(50) DEFAULT NULL,
  `gold_bars` int(11) DEFAULT 0,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `cooldown_end` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `event_name` (`event_name`),
  KEY `cooldown_end` (`cooldown_end`),
  KEY `timestamp` (`timestamp`),
  KEY `success` (`success`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ========================================
-- LOGGING SYSTEM
-- ========================================
CREATE TABLE IF NOT EXISTS `logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(32) NOT NULL,
  `player` varchar(50) DEFAULT NULL,
  `target` varchar(50) DEFAULT NULL,
  `message` text NOT NULL,
  `data` text DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `type` (`type`),
  KEY `player` (`player`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ========================================
-- CONFIGURATION TABLE
-- ========================================
CREATE TABLE IF NOT EXISTS `config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `restrictions` text DEFAULT NULL,
  `admins` text DEFAULT NULL,
  `mods` text DEFAULT NULL,
  `designers` text DEFAULT NULL,
  `developers` text DEFAULT NULL,
  `support_member` varchar(25) DEFAULT NULL,
  `island_staff` text DEFAULT NULL,
  `island_vehicles` text DEFAULT NULL,
  `island_crates` text DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ========================================
-- GANG BUILDINGS & STORAGE
-- ========================================
CREATE TABLE IF NOT EXISTS `gangbldgs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `gang_id` int(11) NOT NULL,
  `gang_name` varchar(50) NOT NULL,
  `building_id` int(11) NOT NULL,
  `owned` tinyint(1) NOT NULL DEFAULT 0,
  `locked` tinyint(1) NOT NULL DEFAULT 0,
  `server` int(1) NOT NULL DEFAULT 1,
  `rent_due` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `gangcrates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `gang_id` int(11) NOT NULL,
  `pos` varchar(64) DEFAULT NULL,
  `inventory` text DEFAULT NULL,
  `owned` tinyint(4) DEFAULT 0,
  `last_active` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `gangvehicles` (
  `id` int(12) NOT NULL AUTO_INCREMENT,
  `side` varchar(15) NOT NULL,
  `classname` varchar(32) NOT NULL,
  `type` varchar(12) NOT NULL,
  `gang_id` int(12) NOT NULL,
  `alive` tinyint(1) NOT NULL DEFAULT 1,
  `active` tinyint(1) NOT NULL DEFAULT 0,
  `plate` int(20) NOT NULL,
  `color` text NOT NULL,
  `inventory` text NOT NULL,
  `insured` tinyint(1) NOT NULL DEFAULT 0,
  `modifications` text NOT NULL,
  `persistentServer` varchar(50) DEFAULT 'olympus',
  `persistentPosition` text DEFAULT NULL,
  `persistentDirection` int(5) DEFAULT 0,
  `inAH` int(1) NOT NULL DEFAULT 0,
  `customName` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ========================================
-- CONQUEST SYSTEM
-- ========================================
CREATE TABLE IF NOT EXISTS `conquests` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `date_started` timestamp NOT NULL DEFAULT current_timestamp(),
  `server` tinyint(3) unsigned NOT NULL,
  `pot` int(10) unsigned NOT NULL DEFAULT 0,
  `winner_id` int(11) NOT NULL,
  `total_points` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `date_started` (`date_started`),
  KEY `server` (`server`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `conquest_gangs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `conquest_id` int(11) unsigned NOT NULL,
  `gang_id` int(11) NOT NULL,
  `points` smallint(5) unsigned NOT NULL,
  `payout` int(10) unsigned NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `conquest_id` (`conquest_id`),
  KEY `gang_id` (`gang_id`),
  CONSTRAINT `conquest_id` FOREIGN KEY (`conquest_id`) REFERENCES `conquests` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT `gang_id` FOREIGN KEY (`gang_id`) REFERENCES `gangs` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ========================================
-- MARKET & ECONOMY SYSTEM
-- ========================================
CREATE TABLE IF NOT EXISTS `market` (
  `id` int(11) NOT NULL,
  `market_data` longtext DEFAULT NULL,
  `reset` tinyint(1) NOT NULL DEFAULT 0,
  `last_update` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default market data
INSERT INTO `market` (`id`, `market_data`, `reset`) VALUES
(1, '[124,188,139,139,433,480,1390,930,1430,1387,1849,1544,1348,1446,1482,1440,2267,1629,1691,2776,2590,2580,2364,2004,7980,8405,9320,30000,69063]', 0)
ON DUPLICATE KEY UPDATE `id` = `id`;

-- ========================================
-- MESSAGING & COMMUNICATION
-- ========================================
CREATE TABLE IF NOT EXISTS `messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `from_player` varchar(50) NOT NULL,
  `to_player` varchar(50) NOT NULL,
  `subject` varchar(100) NOT NULL,
  `message` text NOT NULL,
  `read_status` tinyint(1) NOT NULL DEFAULT 0,
  `sent` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `to_player` (`to_player`),
  KEY `read_status` (`read_status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `mail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `playerid` varchar(50) NOT NULL,
  `sender` varchar(50) NOT NULL,
  `subject` varchar(100) NOT NULL,
  `message` text NOT NULL,
  `read_status` tinyint(1) NOT NULL DEFAULT 0,
  `sent` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ========================================
-- WANTED & LEGAL SYSTEM
-- ========================================
CREATE TABLE IF NOT EXISTS `wanted` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `playerid` varchar(50) NOT NULL,
  `charges` text NOT NULL,
  `bounty` int(11) NOT NULL DEFAULT 0,
  `issued_by` varchar(50) NOT NULL,
  `issued` timestamp NOT NULL DEFAULT current_timestamp(),
  `active` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  KEY `playerid` (`playerid`),
  KEY `active` (`active`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ========================================
-- STATISTICS SYSTEM
-- ========================================
CREATE TABLE IF NOT EXISTS `stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `playerid` varchar(50) NOT NULL,
  `cop_kills` int(11) NOT NULL DEFAULT 0,
  `civ_kills` int(11) NOT NULL DEFAULT 0,
  `med_kills` int(11) NOT NULL DEFAULT 0,
  `deaths` int(11) NOT NULL DEFAULT 0,
  `arrests` int(11) NOT NULL DEFAULT 0,
  `money_earned` bigint(20) NOT NULL DEFAULT 0,
  `money_spent` bigint(20) NOT NULL DEFAULT 0,
  `playtime` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `playerid` (`playerid`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ========================================
-- LOADOUT SYSTEM
-- ========================================
CREATE TABLE IF NOT EXISTS `loadouts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `playerid` varchar(50) NOT NULL,
  `loadout_name` varchar(50) NOT NULL,
  `loadout_data` text NOT NULL,
  `faction` varchar(20) NOT NULL,
  `created` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `playerid` (`playerid`),
  KEY `faction` (`faction`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- ========================================
-- ADMIN SYSTEM TABLES
-- ========================================
CREATE TABLE IF NOT EXISTS `admin_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_identifier` varchar(50) NOT NULL,
  `target_identifier` varchar(50) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `reason` text DEFAULT NULL,
  `timestamp` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `admin_identifier` (`admin_identifier`),
  KEY `target_identifier` (`target_identifier`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `player_bans` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `identifier` varchar(50) NOT NULL,
  `admin_identifier` varchar(50) NOT NULL,
  `reason` text NOT NULL,
  `ban_type` enum('temporary','permanent') NOT NULL DEFAULT 'temporary',
  `expires_at` int(11) DEFAULT NULL,
  `created_at` int(11) NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`),
  UNIQUE KEY `identifier` (`identifier`),
  KEY `admin_identifier` (`admin_identifier`),
  KEY `expires_at` (`expires_at`),
  KEY `active` (`active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `player_warnings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `identifier` varchar(50) NOT NULL,
  `admin_identifier` varchar(50) NOT NULL,
  `reason` text NOT NULL,
  `points` int(11) NOT NULL DEFAULT 1,
  `created_at` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `identifier` (`identifier`),
  KEY `admin_identifier` (`admin_identifier`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `player_reports` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `reporter_identifier` varchar(50) NOT NULL,
  `reported_identifier` varchar(50) NOT NULL,
  `admin_identifier` varchar(50) DEFAULT NULL,
  `category` varchar(50) NOT NULL,
  `reason` text NOT NULL,
  `status` enum('open','claimed','closed') NOT NULL DEFAULT 'open',
  `created_at` int(11) NOT NULL,
  `updated_at` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `reporter_identifier` (`reporter_identifier`),
  KEY `reported_identifier` (`reported_identifier`),
  KEY `admin_identifier` (`admin_identifier`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ========================================
-- MEDICAL SYSTEM TABLES
-- ========================================
CREATE TABLE IF NOT EXISTS `medical_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `medic_identifier` varchar(50) NOT NULL,
  `patient_identifier` varchar(50) NOT NULL,
  `action_type` varchar(50) NOT NULL,
  `amount` int(11) DEFAULT 0,
  `timestamp` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `medic_identifier` (`medic_identifier`),
  KEY `patient_identifier` (`patient_identifier`),
  KEY `action_type` (`action_type`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `medical_invoices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `medic_identifier` varchar(50) NOT NULL,
  `patient_identifier` varchar(50) NOT NULL,
  `amount` int(11) NOT NULL,
  `reason` varchar(255) DEFAULT 'Medical services',
  `timestamp` int(11) NOT NULL,
  `paid` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `medic_identifier` (`medic_identifier`),
  KEY `patient_identifier` (`patient_identifier`),
  KEY `paid` (`paid`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `medical_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `medic_identifier` varchar(50) NOT NULL,
  `revives` int(11) DEFAULT 0,
  `treatments` int(11) DEFAULT 0,
  `dopamine_given` int(11) DEFAULT 0,
  `response_time_avg` float DEFAULT 0,
  `timestamp` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `medic_identifier` (`medic_identifier`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ========================================
-- APD SYSTEM TABLES
-- ========================================
CREATE TABLE IF NOT EXISTS `wanted` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `identifier` varchar(50) NOT NULL,
  `name` varchar(100) NOT NULL,
  `charges` text NOT NULL,
  `bounty` int(11) NOT NULL DEFAULT 0,
  `active` tinyint(1) DEFAULT 1,
  `created_at` int(11) NOT NULL,
  `updated_at` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `identifier` (`identifier`),
  KEY `active` (`active`),
  KEY `bounty` (`bounty`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `apd_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `officer_identifier` varchar(50) NOT NULL,
  `action` varchar(50) NOT NULL,
  `details` text,
  `timestamp` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `officer_identifier` (`officer_identifier`),
  KEY `action` (`action`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `processing_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `officer_identifier` varchar(50) NOT NULL,
  `suspect_identifier` varchar(50) NOT NULL,
  `charges` text NOT NULL,
  `total_fine` int(11) NOT NULL DEFAULT 0,
  `outcome` varchar(20) NOT NULL,
  `started_at` int(11) NOT NULL,
  `completed_at` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `officer_identifier` (`officer_identifier`),
  KEY `suspect_identifier` (`suspect_identifier`),
  KEY `outcome` (`outcome`),
  KEY `started_at` (`started_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ========================================
-- VIGILANTE SYSTEM TABLES
-- ========================================
CREATE TABLE IF NOT EXISTS `vigilante_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vigilante_id` varchar(50) NOT NULL,
  `target_id` varchar(50) NOT NULL,
  `bounty_amount` int(11) NOT NULL,
  `arrest_type` enum('arrest','kill') NOT NULL,
  `location` varchar(255) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `vigilante_id` (`vigilante_id`),
  KEY `target_id` (`target_id`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `vigilante_buddy_requests` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `requester_id` varchar(50) NOT NULL,
  `target_id` varchar(50) NOT NULL,
  `status` enum('pending','accepted','declined') NOT NULL DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `requester_id` (`requester_id`),
  KEY `target_id` (`target_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ========================================
-- FISHING & HUNTING SYSTEM TABLES
-- ========================================
CREATE TABLE IF NOT EXISTS `fishing_hunting_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `player_name` varchar(100) NOT NULL,
  `action_type` enum('fish_catch','turtle_catch','animal_gut','net_fishing','item_sale') NOT NULL,
  `item_caught` varchar(50) NOT NULL,
  `quantity` int(11) DEFAULT 1,
  `location` varchar(255) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `action_type` (`action_type`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `fishing_hunting_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `total_fish_caught` int(11) DEFAULT 0,
  `total_turtles_caught` int(11) DEFAULT 0,
  `total_animals_gutted` int(11) DEFAULT 0,
  `total_nets_used` int(11) DEFAULT 0,
  `best_fish` varchar(50) DEFAULT NULL,
  `last_activity` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `player_id` (`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ========================================
-- SHOP ROBBERY SYSTEM TABLES
-- ========================================
CREATE TABLE IF NOT EXISTS `shop_robbery_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `player_name` varchar(100) NOT NULL,
  `shop_name` varchar(255) NOT NULL,
  `shop_coords` varchar(100) NOT NULL,
  `payout_amount` int(11) NOT NULL,
  `cops_online` int(11) DEFAULT 0,
  `robbery_duration` int(11) DEFAULT 0,
  `success` tinyint(1) DEFAULT 1,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `shop_name` (`shop_name`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ========================================
-- SHIPWRECK & TREASURE SYSTEM TABLES
-- ========================================
CREATE TABLE IF NOT EXISTS `shipwreck_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `player_name` varchar(100) NOT NULL,
  `zone_name` varchar(255) NOT NULL,
  `item_found` varchar(50) NOT NULL,
  `item_value` int(11) DEFAULT 0,
  `depth` float DEFAULT 0,
  `search_duration` int(11) DEFAULT 0,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `zone_name` (`zone_name`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `shipwreck_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `total_searches` int(11) DEFAULT 0,
  `total_items_found` int(11) DEFAULT 0,
  `total_value_found` int(11) DEFAULT 0,
  `best_find` varchar(50) DEFAULT NULL,
  `deepest_search` float DEFAULT 0,
  `last_search` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `player_id` (`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ========================================
-- LOTTERY SYSTEM TABLES
-- ========================================
CREATE TABLE IF NOT EXISTS `lottery_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `player_name` varchar(100) NOT NULL,
  `lottery_id` varchar(50) NOT NULL,
  `tickets_purchased` int(11) NOT NULL DEFAULT 1,
  `total_cost` int(11) NOT NULL,
  `won` tinyint(1) NOT NULL DEFAULT 0,
  `winnings` int(11) NOT NULL DEFAULT 0,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `lottery_id` (`lottery_id`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `lottery_history` (
  `lottery_id` varchar(50) NOT NULL,
  `start_time` timestamp NOT NULL,
  `end_time` timestamp NULL DEFAULT NULL,
  `total_tickets` int(11) NOT NULL DEFAULT 0,
  `prize_pool` int(11) NOT NULL DEFAULT 0,
  `winner_id` varchar(50) NULL DEFAULT NULL,
  `winner_name` varchar(100) NULL DEFAULT NULL,
  `winner_payout` int(11) NOT NULL DEFAULT 0,
  `status` enum('active','completed','cancelled') NOT NULL DEFAULT 'active',
  PRIMARY KEY (`lottery_id`),
  KEY `start_time` (`start_time`),
  KEY `winner_id` (`winner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ========================================
-- PLAYER INTERACTION SYSTEM TABLES
-- ========================================
CREATE TABLE IF NOT EXISTS `player_interaction_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `source_player_id` varchar(50) NOT NULL,
  `source_player_name` varchar(100) NOT NULL,
  `target_player_id` varchar(50) NOT NULL,
  `target_player_name` varchar(100) NOT NULL,
  `interaction_type` enum('rob','escort','restrain','search') NOT NULL,
  `amount` int(11) DEFAULT 0,
  `success` tinyint(1) NOT NULL DEFAULT 1,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `source_player_id` (`source_player_id`),
  KEY `target_player_id` (`target_player_id`),
  KEY `interaction_type` (`interaction_type`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ========================================
-- VEHICLE INTERACTION SYSTEM TABLES
-- ========================================
CREATE TABLE IF NOT EXISTS `vehicle_interaction_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `player_name` varchar(100) NOT NULL,
  `vehicle_plate` varchar(20) NOT NULL,
  `vehicle_model` varchar(50) NOT NULL,
  `interaction_type` enum('flip','push','search','impound','repair') NOT NULL,
  `reward_amount` int(11) DEFAULT 0,
  `position` varchar(100) NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `interaction_type` (`interaction_type`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ========================================
-- APD SEARCH & SEIZURE SYSTEM TABLES
-- ========================================
CREATE TABLE IF NOT EXISTS `apd_search_seizure_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `officer_id` varchar(50) NOT NULL,
  `officer_name` varchar(100) NOT NULL,
  `target_id` varchar(50) NOT NULL,
  `target_name` varchar(100) NOT NULL,
  `action_type` enum('search_player','seize_vehicle','seize_items') NOT NULL,
  `items_seized` text DEFAULT NULL,
  `contraband_value` int(11) DEFAULT 0,
  `reward_amount` int(11) DEFAULT 0,
  `position` varchar(100) NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `officer_id` (`officer_id`),
  KEY `target_id` (`target_id`),
  KEY `action_type` (`action_type`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ========================================
-- CARTEL & HIDEOUT SYSTEM TABLES
-- ========================================
CREATE TABLE IF NOT EXISTS `cartel_hideouts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `hideout_id` int(11) NOT NULL,
  `hideout_name` varchar(100) NOT NULL,
  `gang_id` int(11) DEFAULT 0,
  `gang_name` varchar(100) DEFAULT 'Neutral',
  `captured_at` timestamp NULL DEFAULT NULL,
  `last_notification` timestamp NULL DEFAULT NULL,
  `server` varchar(50) DEFAULT 'olympus',
  PRIMARY KEY (`id`),
  UNIQUE KEY `hideout_server` (`hideout_id`, `server`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `black_markets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `market_id` varchar(50) NOT NULL,
  `market_name` varchar(100) NOT NULL,
  `drug_type` varchar(50) NOT NULL,
  `gang_id` int(11) DEFAULT 0,
  `gang_name` varchar(100) DEFAULT 'Neutral',
  `captured_at` timestamp NULL DEFAULT NULL,
  `last_notification` timestamp NULL DEFAULT NULL,
  `server` varchar(50) DEFAULT 'olympus',
  PRIMARY KEY (`id`),
  UNIQUE KEY `market_server` (`market_id`, `server`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `cartel_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `player_name` varchar(100) NOT NULL,
  `gang_id` int(11) NOT NULL,
  `gang_name` varchar(100) NOT NULL,
  `action_type` enum('capture_hideout','capture_blackmarket','lose_hideout','lose_blackmarket') NOT NULL,
  `location_id` varchar(50) NOT NULL,
  `location_name` varchar(100) NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `gang_id` (`gang_id`),
  KEY `action_type` (`action_type`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ========================================
-- PLAYER BETTING SYSTEM TABLES
-- ========================================
CREATE TABLE IF NOT EXISTS `betting_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `player_name` varchar(100) NOT NULL,
  `target_id` varchar(50) NOT NULL,
  `target_name` varchar(100) NOT NULL,
  `amount` int(11) NOT NULL,
  `winner_id` varchar(50) NOT NULL,
  `winner_name` varchar(100) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `server` varchar(50) DEFAULT 'olympus-1',
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `target_id` (`target_id`),
  KEY `winner_id` (`winner_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ========================================
-- SUICIDE VEST SYSTEM TABLES
-- ========================================
CREATE TABLE IF NOT EXISTS `suicide_vest_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `player_name` varchar(100) NOT NULL,
  `action_type` enum('detonation','attempt','jammer_block') NOT NULL,
  `location` varchar(255) NOT NULL,
  `jammer_zone` varchar(100) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `server` varchar(50) DEFAULT 'olympus-1',
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `action_type` (`action_type`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `suicide_vest_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `total_detonations` int(11) DEFAULT 0,
  `total_attempts` int(11) DEFAULT 0,
  `jammer_blocks` int(11) DEFAULT 0,
  `last_detonation` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `player_id` (`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ========================================
-- GROUP/PARTY SYSTEM TABLES
-- ========================================
CREATE TABLE IF NOT EXISTS `group_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_id` varchar(50) NOT NULL,
  `group_name` varchar(100) NOT NULL,
  `player_id` varchar(50) NOT NULL,
  `player_name` varchar(100) NOT NULL,
  `action_type` varchar(50) NOT NULL,
  `details` text DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `server` varchar(50) DEFAULT 'olympus-1',
  PRIMARY KEY (`id`),
  KEY `group_id` (`group_id`),
  KEY `player_id` (`player_id`),
  KEY `action_type` (`action_type`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `group_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `groups_created` int(11) DEFAULT 0,
  `groups_joined` int(11) DEFAULT 0,
  `total_time_in_groups` int(11) DEFAULT 0,
  `last_group_activity` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `player_id` (`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ========================================
-- ENHANCED PHONE SYSTEM TABLES
-- ========================================
CREATE TABLE IF NOT EXISTS `phone_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fromID` varchar(50) NOT NULL,
  `toID` varchar(50) NOT NULL,
  `fromName` varchar(100) NOT NULL,
  `toName` varchar(100) NOT NULL,
  `message` text NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `read_status` tinyint(1) DEFAULT 0,
  `message_type` varchar(20) DEFAULT 'private',
  `server` varchar(50) DEFAULT 'olympus-1',
  PRIMARY KEY (`id`),
  KEY `fromID` (`fromID`),
  KEY `toID` (`toID`),
  KEY `timestamp` (`timestamp`),
  KEY `message_type` (`message_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `dispatch_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) NOT NULL,
  `player_name` varchar(100) NOT NULL,
  `faction` varchar(20) NOT NULL,
  `message` text NOT NULL,
  `location` varchar(255) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `responded_by` varchar(50) DEFAULT NULL,
  `response_time` timestamp NULL DEFAULT NULL,
  `server` varchar(50) DEFAULT 'olympus-1',
  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `faction` (`faction`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
