<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Olympus Mini-Games</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: transparent;
            color: white;
            margin: 0;
            padding: 0;
        }
        
        .minigames-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            border-radius: 10px;
            padding: 20px;
            min-width: 400px;
            display: none;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div id="minigames-container" class="minigames-container">
        <h2>Mini-Games</h2>
        <p>Mini-games system loaded</p>
        <button class="btn" onclick="closeMinigames()">Close</button>
    </div>

    <script>
        function closeMinigames() {
            document.getElementById('minigames-container').style.display = 'none';
        }
    </script>
</body>
</html>
