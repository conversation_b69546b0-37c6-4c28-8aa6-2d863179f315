<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Olympus Admin Panel</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div id="admin-panel" class="admin-panel hidden">
        <div class="panel-header">
            <h2>Olympus Admin Panel</h2>
            <button id="close-btn" class="close-btn">&times;</button>
        </div>
        
        <div class="panel-content">
            <div class="tab-buttons">
                <button class="tab-btn active" data-tab="players">Players</button>
                <button class="tab-btn" data-tab="server">Server</button>
                <button class="tab-btn" data-tab="logs">Logs</button>
            </div>
            
            <div id="players-tab" class="tab-content active">
                <div class="player-list">
                    <div class="player-search">
                        <input type="text" id="player-search" placeholder="Search players...">
                    </div>
                    <div id="players-container">
                        <!-- Players will be populated here -->
                    </div>
                </div>
                
                <div class="player-actions">
                    <h3>Player Actions</h3>
                    <div class="action-buttons">
                        <button id="teleport-to" class="action-btn">Teleport To</button>
                        <button id="bring-player" class="action-btn">Bring Player</button>
                        <button id="spectate" class="action-btn">Spectate</button>
                        <button id="kick-player" class="action-btn danger">Kick</button>
                        <button id="ban-player" class="action-btn danger">Ban</button>
                    </div>
                </div>
            </div>
            
            <div id="server-tab" class="tab-content">
                <div class="server-info">
                    <h3>Server Information</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>Players Online:</label>
                            <span id="players-online">0</span>
                        </div>
                        <div class="info-item">
                            <label>Server Uptime:</label>
                            <span id="server-uptime">00:00:00</span>
                        </div>
                        <div class="info-item">
                            <label>Server Performance:</label>
                            <span id="server-performance">Good</span>
                        </div>
                    </div>
                </div>
                
                <div class="server-actions">
                    <h3>Server Actions</h3>
                    <div class="action-buttons">
                        <button id="restart-warning" class="action-btn warning">Restart Warning</button>
                        <button id="server-announcement" class="action-btn">Server Announcement</button>
                        <button id="weather-control" class="action-btn">Weather Control</button>
                        <button id="time-control" class="action-btn">Time Control</button>
                    </div>
                </div>
            </div>
            
            <div id="logs-tab" class="tab-content">
                <div class="logs-container">
                    <h3>Admin Logs</h3>
                    <div class="log-filters">
                        <select id="log-type">
                            <option value="all">All Actions</option>
                            <option value="ban">Bans</option>
                            <option value="kick">Kicks</option>
                            <option value="teleport">Teleports</option>
                            <option value="vanish">Vanish</option>
                        </select>
                        <input type="date" id="log-date">
                    </div>
                    <div id="logs-list">
                        <!-- Logs will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modals -->
    <div id="kick-modal" class="modal hidden">
        <div class="modal-content">
            <h3>Kick Player</h3>
            <input type="text" id="kick-reason" placeholder="Reason for kick...">
            <div class="modal-buttons">
                <button id="confirm-kick" class="btn danger">Kick</button>
                <button id="cancel-kick" class="btn">Cancel</button>
            </div>
        </div>
    </div>
    
    <div id="ban-modal" class="modal hidden">
        <div class="modal-content">
            <h3>Ban Player</h3>
            <input type="text" id="ban-reason" placeholder="Reason for ban...">
            <select id="ban-duration">
                <option value="0">Permanent</option>
                <option value="3600">1 Hour</option>
                <option value="86400">1 Day</option>
                <option value="604800">1 Week</option>
                <option value="2592000">1 Month</option>
            </select>
            <div class="modal-buttons">
                <button id="confirm-ban" class="btn danger">Ban</button>
                <button id="cancel-ban" class="btn">Cancel</button>
            </div>
        </div>
    </div>

    <script src="js/script.js"></script>
</body>
</html>
