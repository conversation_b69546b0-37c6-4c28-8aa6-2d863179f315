-- Olympus Group/Party System - Server Main
-- Based on original Olympus group functions (fn_createGroup.sqf, fn_joinGroup.sqf, etc.)

local OlympusGroups = {}

-- Server state
local ActiveGroups = {} -- [groupId] = {name, leader, members, locked, password, created}
local PlayerGroups = {} -- [playerId] = groupId
local GroupIdCounter = 1

-- Database initialization
CreateThread(function()
    -- Wait for database to be ready
    while not exports['olympus-core']:IsDBReady() do
        Wait(100)
    end
    
    -- Initialize group logs table
    exports['olympus-core']:ExecuteQuery([[
        CREATE TABLE IF NOT EXISTS group_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            group_id VARCHAR(50) NOT NULL,
            group_name VARCHAR(100) NOT NULL,
            player_id VARCHAR(50) NOT NULL,
            player_name VARCHAR(100) NOT NULL,
            action_type VARCHAR(50) NOT NULL,
            details TEXT DEFAULT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            server VARCHAR(50) DEFAULT 'olympus-1'
        )
    ]], {})
    
    -- Initialize group stats table
    exports['olympus-core']:ExecuteQuery([[
        CREATE TABLE IF NOT EXISTS group_stats (
            id INT AUTO_INCREMENT PRIMARY KEY,
            player_id VARCHAR(50) NOT NULL,
            groups_created INT DEFAULT 0,
            groups_joined INT DEFAULT 0,
            total_time_in_groups INT DEFAULT 0,
            last_group_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY player_id (player_id)
        )
    ]], {})
    
    print("[Olympus Groups] Database initialized")
end)

-- Utility Functions
function OlympusGroups.GetPlayerData(src)
    local success, result = pcall(exports['olympus-core'].GetPlayerData, src)
    if success and result then
        return result
    end
    return nil
end

function OlympusGroups.Notify(src, message, type)
    TriggerClientEvent('olympus-core:client:notify', src, message, type or 'info')
end

function OlympusGroups.GenerateGroupId()
    local groupId = "group_" .. GroupIdCounter
    GroupIdCounter = GroupIdCounter + 1
    return groupId
end

function OlympusGroups.ValidateGroupName(name)
    if not name or name == "" then
        return false, Config.Groups.messages.nameRequired
    end
    
    if string.len(name) > Config.Groups.creation.maxNameLength then
        return false, Config.Groups.messages.nameTooLong
    end
    
    -- Check for invalid characters (exact Olympus implementation)
    local allowedChars = Config.Groups.creation.allowedCharacters
    for i = 1, string.len(name) do
        local char = string.sub(name, i, i)
        if not string.find(allowedChars, char, 1, true) then
            return false, Config.Groups.messages.nameInvalid
        end
    end
    
    -- Check if name is already taken
    for groupId, group in pairs(ActiveGroups) do
        if group.name == name then
            return false, Config.Groups.messages.nameTaken
        end
    end
    
    return true, "Valid"
end

function OlympusGroups.ValidatePassword(password)
    if not password then password = "" end
    
    if string.len(password) > Config.Groups.creation.maxPasswordLength then
        return false, Config.Groups.messages.passwordTooLong
    end
    
    -- Check for invalid characters (exact Olympus implementation)
    local allowedChars = Config.Groups.creation.allowedCharacters
    for i = 1, string.len(password) do
        local char = string.sub(password, i, i)
        if not string.find(allowedChars, char, 1, true) then
            return false, Config.Groups.messages.passwordInvalid
        end
    end
    
    return true, "Valid"
end

-- Group Management Functions (Exact Olympus Implementation)

-- Create Group (Based on fn_createGroup.sqf)
function OlympusGroups.CreateGroup(src, groupName, password)
    local playerData = OlympusGroups.GetPlayerData(src)
    if not playerData then return false, "Player data not found" end
    
    -- Check if player is already in a group
    if PlayerGroups[src] then
        return false, Config.Groups.messages.alreadyInGroup
    end
    
    -- Validate group name
    local nameValid, nameError = OlympusGroups.ValidateGroupName(groupName)
    if not nameValid then
        return false, nameError
    end
    
    -- Validate password
    local passwordValid, passwordError = OlympusGroups.ValidatePassword(password)
    if not passwordValid then
        return false, passwordError
    end
    
    -- Create new group
    local groupId = OlympusGroups.GenerateGroupId()
    local playerName = GetPlayerName(src)
    
    ActiveGroups[groupId] = {
        id = groupId,
        name = groupName,
        leader = src,
        leaderName = playerName,
        members = {
            [src] = {
                id = src,
                name = playerName,
                rank = Config.Groups.creation.leaderRank,
                joinedAt = os.time()
            }
        },
        locked = Config.Groups.creation.autoLock,
        password = password or "",
        created = os.time(),
        memberCount = 1
    }
    
    -- Add player to group
    PlayerGroups[src] = groupId
    
    -- Log group creation
    if Config.Groups.database.logGroupActions then
        OlympusGroups.LogGroupAction(src, groupId, groupName, Config.GroupActions.CREATE, "Group created")
    end
    
    -- Update player statistics
    if Config.Groups.database.trackStatistics then
        OlympusGroups.UpdatePlayerStats(src, 'groups_created')
    end
    
    -- Notify player
    OlympusGroups.Notify(src, string.format(Config.Groups.messages.createSuccess, groupName), 'success')
    
    -- Update all clients with new group list
    OlympusGroups.BroadcastGroupList()
    
    print(string.format("[Olympus Groups] %s created group '%s' (ID: %s)", playerName, groupName, groupId))
    return true, groupId
end

-- Join Group (Based on fn_joinGroup.sqf)
function OlympusGroups.JoinGroup(src, groupId, password)
    local playerData = OlympusGroups.GetPlayerData(src)
    if not playerData then return false, "Player data not found" end
    
    -- Check if player is already in a group
    if PlayerGroups[src] then
        return false, Config.Groups.messages.alreadyInGroup
    end
    
    -- Check if group exists
    local group = ActiveGroups[groupId]
    if not group then
        return false, Config.Groups.messages.groupNotValid
    end
    
    -- Check if group is full
    if group.memberCount >= Config.Groups.management.maxMembers then
        return false, Config.Groups.messages.groupFull
    end
    
    -- Check password for locked groups (exact Olympus implementation)
    if group.locked then
        if not password or password ~= group.password or group.password == "" then
            return false, Config.Groups.messages.passwordIncorrect
        end
    end
    
    -- Add player to group
    local playerName = GetPlayerName(src)
    group.members[src] = {
        id = src,
        name = playerName,
        rank = "PRIVATE",
        joinedAt = os.time()
    }
    group.memberCount = group.memberCount + 1
    PlayerGroups[src] = groupId
    
    -- Log group join
    if Config.Groups.database.logGroupActions then
        local logDetails = group.locked and "Joined with password" or "Joined without password"
        OlympusGroups.LogGroupAction(src, groupId, group.name, Config.GroupActions.JOIN, logDetails)
    end
    
    -- Update player statistics
    if Config.Groups.database.trackStatistics then
        OlympusGroups.UpdatePlayerStats(src, 'groups_joined')
    end
    
    -- Notify player and group members
    OlympusGroups.Notify(src, string.format(Config.Groups.messages.joinSuccess, group.name), 'success')
    
    if Config.Groups.joining.notifyMembers then
        for memberId, member in pairs(group.members) do
            if memberId ~= src then
                OlympusGroups.Notify(memberId, string.format("%s has joined the group", playerName), 'info')
            end
        end
    end
    
    -- Update all clients
    OlympusGroups.BroadcastGroupList()
    OlympusGroups.UpdateGroupMembers(groupId)
    
    print(string.format("[Olympus Groups] %s joined group '%s' (ID: %s)", playerName, group.name, groupId))
    return true, "Joined successfully"
end

-- Leave Group (Based on fn_leaveGroup.sqf)
function OlympusGroups.LeaveGroup(src)
    local groupId = PlayerGroups[src]
    if not groupId then
        return false, Config.Groups.messages.notInGroup
    end
    
    local group = ActiveGroups[groupId]
    if not group then
        return false, Config.Groups.messages.groupNotValid
    end
    
    local playerName = GetPlayerName(src)
    local wasLeader = (group.leader == src)
    
    -- Remove player from group
    group.members[src] = nil
    group.memberCount = group.memberCount - 1
    PlayerGroups[src] = nil
    
    -- Handle leadership transfer (exact Olympus implementation)
    if wasLeader and group.memberCount > 0 then
        -- Transfer leadership to next member
        local newLeader = nil
        for memberId, member in pairs(group.members) do
            newLeader = memberId
            break
        end
        
        if newLeader then
            group.leader = newLeader
            group.leaderName = GetPlayerName(newLeader)
            group.members[newLeader].rank = Config.Groups.creation.leaderRank
            
            -- Notify new leader
            OlympusGroups.Notify(newLeader, string.format(Config.Groups.messages.leadershipTransferred, GetPlayerName(newLeader)), 'info')
            
            -- Log leadership transfer
            if Config.Groups.database.logGroupActions then
                OlympusGroups.LogGroupAction(newLeader, groupId, group.name, Config.GroupActions.TRANSFER_LEADERSHIP, "Leadership transferred due to leader leaving")
            end
        end
    end
    
    -- Disband group if empty
    if group.memberCount == 0 then
        ActiveGroups[groupId] = nil
        
        -- Log group disband
        if Config.Groups.database.logGroupActions then
            OlympusGroups.LogGroupAction(src, groupId, group.name, Config.GroupActions.DISBAND, "Group disbanded - no members")
        end
    end
    
    -- Log group leave
    if Config.Groups.database.logGroupActions then
        OlympusGroups.LogGroupAction(src, groupId, group.name, Config.GroupActions.LEAVE, "Player left group")
    end
    
    -- Notify player
    OlympusGroups.Notify(src, Config.Groups.messages.leaveSuccess, 'success')
    
    -- Notify remaining group members
    if group.memberCount > 0 then
        for memberId, member in pairs(group.members) do
            OlympusGroups.Notify(memberId, string.format("%s has left the group", playerName), 'info')
        end
    end
    
    -- Update all clients
    OlympusGroups.BroadcastGroupList()
    if group.memberCount > 0 then
        OlympusGroups.UpdateGroupMembers(groupId)
    end
    
    print(string.format("[Olympus Groups] %s left group '%s' (ID: %s)", playerName, group.name, groupId))
    return true, "Left successfully"
end

-- Kick Member (Based on fn_kickGroup.sqf)
function OlympusGroups.KickMember(src, targetId)
    local groupId = PlayerGroups[src]
    if not groupId then
        return false, Config.Groups.messages.notInGroup
    end

    local group = ActiveGroups[groupId]
    if not group then
        return false, Config.Groups.messages.groupNotValid
    end

    -- Check if player is leader
    if group.leader ~= src then
        return false, Config.Groups.messages.notLeader
    end

    -- Check if trying to kick self
    if src == targetId then
        return false, Config.Groups.messages.cannotKickSelf
    end

    -- Check if target is in group
    if not group.members[targetId] then
        return false, Config.Groups.messages.memberNotFound
    end

    local targetName = GetPlayerName(targetId)

    -- Remove target from group
    group.members[targetId] = nil
    group.memberCount = group.memberCount - 1
    PlayerGroups[targetId] = nil

    -- Log kick action
    if Config.Groups.database.logGroupActions then
        OlympusGroups.LogGroupAction(src, groupId, group.name, Config.GroupActions.KICK, string.format("Kicked %s", targetName))
    end

    -- Notify players
    OlympusGroups.Notify(src, string.format(Config.Groups.messages.kickSuccess, targetName), 'success')
    OlympusGroups.Notify(targetId, "You have been kicked from the group", 'error')

    -- Notify other group members
    for memberId, member in pairs(group.members) do
        if memberId ~= src then
            OlympusGroups.Notify(memberId, string.format("%s was kicked from the group", targetName), 'info')
        end
    end

    -- Update clients
    OlympusGroups.BroadcastGroupList()
    OlympusGroups.UpdateGroupMembers(groupId)

    return true, "Member kicked"
end

-- Lock Group (Based on fn_lockGroup.sqf)
function OlympusGroups.LockGroup(src)
    local groupId = PlayerGroups[src]
    if not groupId then
        return false, Config.Groups.messages.notInGroup
    end

    local group = ActiveGroups[groupId]
    if not group then
        return false, Config.Groups.messages.groupNotValid
    end

    -- Check if player is leader
    if group.leader ~= src then
        return false, Config.Groups.messages.notLeader
    end

    group.locked = true

    -- Log lock action
    if Config.Groups.database.logGroupActions then
        OlympusGroups.LogGroupAction(src, groupId, group.name, Config.GroupActions.LOCK, "Group locked")
    end

    -- Notify group members
    for memberId, member in pairs(group.members) do
        OlympusGroups.Notify(memberId, Config.Groups.messages.groupLocked, 'info')
    end

    -- Update clients
    OlympusGroups.BroadcastGroupList()

    return true, "Group locked"
end

-- Unlock Group (Based on fn_unlockGroup.sqf)
function OlympusGroups.UnlockGroup(src)
    local groupId = PlayerGroups[src]
    if not groupId then
        return false, Config.Groups.messages.notInGroup
    end

    local group = ActiveGroups[groupId]
    if not group then
        return false, Config.Groups.messages.groupNotValid
    end

    -- Check if player is leader
    if group.leader ~= src then
        return false, Config.Groups.messages.notLeader
    end

    group.locked = false

    -- Log unlock action
    if Config.Groups.database.logGroupActions then
        OlympusGroups.LogGroupAction(src, groupId, group.name, Config.GroupActions.UNLOCK, "Group unlocked")
    end

    -- Notify group members
    for memberId, member in pairs(group.members) do
        OlympusGroups.Notify(memberId, Config.Groups.messages.groupUnlocked, 'info')
    end

    -- Update clients
    OlympusGroups.BroadcastGroupList()

    return true, "Group unlocked"
end

-- Database Functions
function OlympusGroups.LogGroupAction(playerId, groupId, groupName, action, details)
    local playerData = OlympusGroups.GetPlayerData(playerId)
    if not playerData then return end

    exports['olympus-core']:ExecuteQuery([[
        INSERT INTO group_logs (group_id, group_name, player_id, player_name, action_type, details)
        VALUES (?, ?, ?, ?, ?, ?)
    ]], {
        groupId,
        groupName,
        playerData.citizenid,
        GetPlayerName(playerId),
        action,
        details
    })
end

function OlympusGroups.UpdatePlayerStats(playerId, statType)
    local playerData = OlympusGroups.GetPlayerData(playerId)
    if not playerData then return end

    local updateField = ""
    if statType == 'groups_created' then
        updateField = "groups_created = groups_created + 1"
    elseif statType == 'groups_joined' then
        updateField = "groups_joined = groups_joined + 1"
    end

    if updateField ~= "" then
        exports['olympus-core']:ExecuteQuery(string.format([[
            INSERT INTO group_stats (player_id, %s)
            VALUES (?, 1)
            ON DUPLICATE KEY UPDATE %s
        ]], updateField:gsub("= [^,]+", "= 1"), updateField), {
            playerData.citizenid
        })
    end
end

-- Broadcast Functions
function OlympusGroups.BroadcastGroupList()
    local groupList = {}
    for groupId, group in pairs(ActiveGroups) do
        table.insert(groupList, {
            id = groupId,
            name = group.name,
            leader = group.leaderName,
            memberCount = group.memberCount,
            maxMembers = Config.Groups.management.maxMembers,
            locked = group.locked,
            created = group.created
        })
    end

    TriggerClientEvent('olympus-groups:client:updateGroupList', -1, groupList)
end

function OlympusGroups.UpdateGroupMembers(groupId)
    local group = ActiveGroups[groupId]
    if not group then return end

    local memberList = {}
    for memberId, member in pairs(group.members) do
        table.insert(memberList, {
            id = memberId,
            name = member.name,
            rank = member.rank,
            joinedAt = member.joinedAt,
            isLeader = (memberId == group.leader)
        })
    end

    -- Send to all group members
    for memberId, member in pairs(group.members) do
        TriggerClientEvent('olympus-groups:client:updateGroupMembers', memberId, memberList, group)
    end
end

-- Event Handlers
RegisterNetEvent('olympus-groups:server:createGroup', function(groupName, password)
    local src = source
    local success, result = OlympusGroups.CreateGroup(src, groupName, password)
    TriggerClientEvent('olympus-groups:client:createGroupResult', src, success, result)
end)

RegisterNetEvent('olympus-groups:server:joinGroup', function(groupId, password)
    local src = source
    local success, result = OlympusGroups.JoinGroup(src, groupId, password)
    TriggerClientEvent('olympus-groups:client:joinGroupResult', src, success, result)
end)

RegisterNetEvent('olympus-groups:server:leaveGroup', function()
    local src = source
    local success, result = OlympusGroups.LeaveGroup(src)
    TriggerClientEvent('olympus-groups:client:leaveGroupResult', src, success, result)
end)

RegisterNetEvent('olympus-groups:server:kickMember', function(targetId)
    local src = source
    local success, result = OlympusGroups.KickMember(src, targetId)
    TriggerClientEvent('olympus-groups:client:kickMemberResult', src, success, result)
end)

RegisterNetEvent('olympus-groups:server:lockGroup', function()
    local src = source
    local success, result = OlympusGroups.LockGroup(src)
    TriggerClientEvent('olympus-groups:client:lockGroupResult', src, success, result)
end)

RegisterNetEvent('olympus-groups:server:unlockGroup', function()
    local src = source
    local success, result = OlympusGroups.UnlockGroup(src)
    TriggerClientEvent('olympus-groups:client:unlockGroupResult', src, success, result)
end)

RegisterNetEvent('olympus-groups:server:requestGroupList', function()
    local src = source
    OlympusGroups.BroadcastGroupList()
end)

RegisterNetEvent('olympus-groups:server:requestGroupData', function()
    local src = source
    local groupId = PlayerGroups[src]
    if groupId then
        OlympusGroups.UpdateGroupMembers(groupId)
    end
end)

-- Player disconnect cleanup
AddEventHandler('playerDropped', function(reason)
    local src = source
    local groupId = PlayerGroups[src]

    if groupId then
        -- Player was in a group, remove them
        OlympusGroups.LeaveGroup(src)
    end
end)

-- Export Functions
exports('CreateGroup', function(src, groupName, password)
    return OlympusGroups.CreateGroup(src, groupName, password)
end)

exports('JoinGroup', function(src, groupId, password)
    return OlympusGroups.JoinGroup(src, groupId, password)
end)

exports('LeaveGroup', function(src)
    return OlympusGroups.LeaveGroup(src)
end)

exports('KickFromGroup', function(src, targetId)
    return OlympusGroups.KickMember(src, targetId)
end)

exports('LockGroup', function(src)
    return OlympusGroups.LockGroup(src)
end)

exports('UnlockGroup', function(src)
    return OlympusGroups.UnlockGroup(src)
end)

exports('GetGroupList', function()
    local groupList = {}
    for groupId, group in pairs(ActiveGroups) do
        table.insert(groupList, {
            id = groupId,
            name = group.name,
            leader = group.leaderName,
            memberCount = group.memberCount,
            maxMembers = Config.Groups.management.maxMembers,
            locked = group.locked,
            created = group.created
        })
    end
    return groupList
end)

exports('GetGroupData', function(src)
    local groupId = PlayerGroups[src]
    if not groupId then return nil end

    local group = ActiveGroups[groupId]
    if not group then return nil end

    return {
        id = groupId,
        name = group.name,
        leader = group.leader,
        leaderName = group.leaderName,
        memberCount = group.memberCount,
        locked = group.locked,
        created = group.created,
        isLeader = (group.leader == src)
    }
end)

exports('IsInGroup', function(src)
    return PlayerGroups[src] ~= nil
end)

exports('GetPlayerGroup', function(src)
    return PlayerGroups[src]
end)

-- Initialize group list broadcast
CreateThread(function()
    while true do
        Wait(Config.Groups.display.refreshInterval)
        OlympusGroups.BroadcastGroupList()
    end
end)

print("[Olympus Groups] Server module loaded - Based on original Olympus group functions")
