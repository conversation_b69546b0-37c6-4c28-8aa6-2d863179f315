fx_version 'cerulean'
game 'gta5'

author 'Olympus Development'
description 'Olympus Fishing & Hunting System'
version '1.0.0'

dependencies {
    'olympus-core',
    'olympus-licenses'
}

shared_scripts {
    'config/shared.lua'
}

client_scripts {
    'client/main.lua'
}

server_scripts {
    'server/main.lua'
}

exports {
    'GetFishingStatus',
    'GetHuntingStatus',
    'StartFishing',
    'StartHunting',
    'ProcessCatch'
}

server_exports {
    'ProcessFishCatch',
    'ProcessAnimalGut',
    'ProcessTurtleCatch',
    'GetPlayerFishingData',
    'GetPlayerHuntingData'
}
