# Olympus Altis Life Framework for FiveM

A complete, custom-built framework that perfectly replicates the Olympus Altis Life experience from Arma 3 in FiveM. This framework is built from scratch and does not depend on QBCore, ESX, or any other existing framework.

## 🎯 Project Overview

This framework recreates every aspect of Olympus Altis Life (2015-Present) including:

- **Complete APD (Police) System** with ranks, equipment, processing, and SOPs
- **R&R Medical System** with revive mechanics and medical procedures  
- **Gang and Cartel System** with territory control and criminal activities
- **Federal Events** (Fed, Blackwater, Jail Break, Evidence Lockup)
- **Dynamic Economy** with market fluctuations and banking
- **Vehicle System** with ownership, garages, and modifications
- **Comprehensive UI** matching Olympus design and functionality
- **Player Progression** with jobs, licenses, and faction advancement

## 🏗️ Framework Architecture

### Core Components

- **olympus-core**: Foundation framework with player management, database, and core systems
- **olympus-apd**: Complete Altis Police Department system
- **olympus-medical**: Rescue & Recovery medical system
- **olympus-gangs**: Gang creation, management, and cartel capture mechanics
- **olympus-economy**: Banking, jobs, shops, and dynamic market system
- **olympus-vehicles**: Vehicle ownership, garages, and modification system
- **olympus-events**: Federal events and server-wide activities
- **olympus-ui**: Complete user interface system with HUD, menus, and notifications

### Database Schema

The framework uses MySQL with the following main tables:
- `olympus_players` - Player data and progression
- `olympus_gangs` - Gang information and membership
- `olympus_vehicles` - Vehicle ownership and data
- `olympus_houses` - Property ownership
- `olympus_bans` - Ban management
- `olympus_logs` - Action logging
- `olympus_cartels` - Cartel control and status

## 🚀 Installation

### Prerequisites

- FiveM Server (Build 2944 or higher)
- MySQL Database
- OneSync Enabled (for 200+ players)

### Setup Instructions

1. **Database Setup**
   ```sql
   CREATE DATABASE olympus_altis_life;
   ```

2. **Configure Database Connection**
   Edit `server.cfg` and update the MySQL connection string:
   ```
   set mysql_connection_string "mysql://username:password@localhost/olympus_altis_life?charset=utf8mb4"
   ```

3. **Install Resources**
   - Place all `[olympus]` resources in your `resources` folder
   - Ensure `mysql-async` and `oxmysql` are installed

4. **Configure Server**
   - Update `server.cfg` with your server details
   - Set your Steam Web API key
   - Configure admin permissions

5. **Start Server**
   ```
   ensure olympus-loader
   ensure olympus-core
   ensure olympus-apd
   ensure olympus-medical
   ensure olympus-gangs
   ensure olympus-economy
   ensure olympus-vehicles
   ensure olympus-events
   ensure olympus-ui
   ```

## ⚙️ Configuration

### Core Settings

Edit `resources/[olympus]/olympus-core/config/shared.lua`:

```lua
Config.StartingMoney = 5000
Config.MaxPlayers = 200
Config.RespawnTime = 300 -- 5 minutes
Config.NewLifeRuleTime = 900 -- 15 minutes
```

### APD Configuration

Edit `resources/[olympus]/olympus-apd/config/shared.lua`:

```lua
Config.MinAPDForEvents = 4
Config.ProcessingTime = 900 -- 15 minutes
Config.MaxTicketAmount = 500000
```

### Gang Settings

Edit `resources/[olympus]/olympus-gangs/config/shared.lua`:

```lua
Config.MaxGangs = 20
Config.MaxGangMembers = 15
Config.GangCreationCost = 100000
```

## 🎮 Features

### APD System
- **Rank Structure**: Deputy → Patrol Officer → Corporal → Sergeant → Lieutenant → Captain → Deputy Chief → Chief
- **Equipment Access**: Rank-based weapon and vehicle access
- **Processing System**: 15-minute processing with charges and fines
- **Dispatch System**: Integrated call management
- **Evidence System**: Collection and storage of evidence
- **Wave Rules**: Respawn delays and equipment restrictions

### Medical System (R&R)
- **Rank Structure**: EMT → Paramedic → Senior Paramedic → Supervisor → Chief of Medicine
- **Revive Mechanics**: Multiple revive methods (Epipen, Dopamine, Defibrillator)
- **Medical Procedures**: Treatment options based on rank
- **Hospital System**: Multiple hospital locations with beds
- **Emergency Response**: Integrated with APD for accident scenes

### Gang System
- **Gang Creation**: Form gangs with up to 15 members
- **Rank System**: Member → Trusted → Lieutenant → Captain → Leader
- **Territory Control**: Capture and defend territories
- **Cartel System**: Capture drug cartels for income
- **Gang Wars**: Organized conflicts between gangs
- **Criminal Activities**: Drug trafficking, weapon smuggling, money laundering

### Economy System
- **Dynamic Markets**: Fluctuating prices based on supply/demand
- **Banking System**: Interest, transfers, and transactions
- **Job System**: Legal employment opportunities
- **Shop System**: General stores, weapon shops, vehicle dealerships
- **Auction System**: Player-to-player trading
- **Tax System**: Automated taxation on transactions

### Federal Events
- **Federal Reserve**: High-value bank robbery
- **Blackwater Armory**: Military compound assault
- **Jail Break**: Prison break operations
- **Evidence Lockup**: Police station infiltration

### Vehicle System
- **Ownership**: Buy, sell, and transfer vehicles
- **Garage System**: Store and retrieve vehicles
- **Modifications**: Engine, brakes, armor, and cosmetic upgrades
- **Fuel System**: Realistic fuel consumption
- **Insurance**: Vehicle protection and claims
- **Impound System**: Police vehicle impounding

## 🔧 Administration

### Admin Commands

- `/olympus save` - Save all player data
- `/olympus reload` - Reload framework
- `/olympus:status` - Check framework status (console)
- `/olympus:debug` - Debug information (admin only)

### Admin Levels

1. **Moderator** - Basic moderation tools
2. **Administrator** - Full admin access
3. **Senior Admin** - Advanced tools
4. **Head Admin** - Management tools
5. **Owner** - Full server control

## 📊 Performance

### Optimizations

- **Efficient Database Queries**: Optimized MySQL operations
- **Resource Management**: Proper cleanup and memory management
- **OneSync Integration**: Support for 200+ players
- **Caching System**: Reduced database load
- **Event Optimization**: Minimal network traffic

### Monitoring

- Built-in performance monitoring
- Resource usage tracking
- Player action logging
- Error reporting and debugging

## 🤝 Contributing

This framework is designed to be modular and extensible. To add new features:

1. Follow the existing code structure
2. Use the framework's event system
3. Implement proper error handling
4. Add configuration options
5. Document new features

## 📝 License

This framework is created for educational and community purposes, inspired by the original Olympus Altis Life experience. Please respect the intellectual property of Olympus Entertainment.

## 🆘 Support

For support and updates:
- Visit: https://olympus-entertainment.com
- Discord: https://discord.gg/olympus
- Forums: https://olympus-entertainment.com/forums

## 🎉 Acknowledgments

- **Olympus Entertainment** - Original Altis Life concept and mechanics
- **FiveM Community** - Platform and development tools
- **Contributors** - Framework development and testing

---

**Note**: This framework aims to provide an authentic Olympus Altis Life experience in FiveM. All systems are designed to match the original gameplay mechanics and balance from the Arma 3 version.
