fx_version 'cerulean'
game 'gta5'

name 'Olympus Quest System'
description 'Complete quest system with tutorials and missions'
author 'Olympus Development Team'
version '1.0.0'

-- Dependencies
dependencies {
    'olympus-core',
    'olympus-ui',
    'oxmysql'
}

-- Shared scripts
shared_scripts {
    'config/shared.lua'
}

-- Client scripts
client_scripts {
    'client/main.lua',
    'client/quest_ui.lua',
    'client/tutorial_quest.lua',
    'client/progress_tracking.lua',
    'client/map_integration.lua',
    'client/y_menu_integration.lua'
}

-- Server scripts
server_scripts {
    '@oxmysql/lib/MySQL.lua',
    'server/main.lua',
    'server/quest_system.lua',
    'server/quest_validation.lua',
    'server/reward_system.lua',
    'server/tutorial_protection.lua'
}

-- UI files
ui_page 'html/index.html'

files {
    'html/index.html',
    'html/css/*.css',
    'html/js/*.js'
}

-- Exports
exports {
    'StartQuest',
    'GetActiveQuests',
    'GetQuestProgress',
    'OpenQuestMenu'
}

server_exports {
    'CreateQuest',
    'CompleteQuestStep',
    'CompleteQuest',
    'GetPlayerQuests'
}
