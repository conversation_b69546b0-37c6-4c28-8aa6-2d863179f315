-- ========================================
-- OLYMPUS ECONOMY SYSTEM - CLIENT MAIN
-- Complete recreation based on original Olympus market system
-- Handles market interactions, shop access, and price displays
-- ========================================

local OlympusEconomy = {}
OlympusEconomy.MarketPrices = {} -- Current market prices
OlympusEconomy.BlackmarketOpen = false
OlympusEconomy.ShopMenuOpen = false
OlympusEconomy.PlayerLoaded = false

-- Configuration is loaded via shared_scripts in fxmanifest.lua

-- Initialize client system
CreateThread(function()
    while not exports['olympus-core']:IsPlayerLoaded() do
        Wait(1000)
    end

    OlympusEconomy.PlayerLoaded = true
    print("^2[Olympus Economy]^7 Client initialized")

    -- Initialize all shop interactions
    InitializeShops()
    InitializeBlackmarket()
    InitializeMarketDisplays()
end)

-- Initialize all shops with interactions
function InitializeShops()
    CreateThread(function()
        -- Wait for UI system
        while not exports['olympus-ui'] or not exports['olympus-ui']:IsLoaded() do
            Wait(1000)
        end

        -- Initialize each shop type
        for shopName, shopData in pairs(Config.Shops) do
            if shopName ~= 'black_market' then -- Black market handled separately
                InitializeShop(shopName, shopData)
            end
        end
    end)
end

-- Initialize individual shop
function InitializeShop(shopName, shopData)
    for _, location in ipairs(shopData.locations) do
        -- Create shop blip if not hidden
        if not shopData.hidden then
            local blip = AddBlipForCoord(location.x, location.y, location.z)
            SetBlipSprite(blip, shopData.blip and shopData.blip.sprite or 52)
            SetBlipDisplay(blip, 4)
            SetBlipScale(blip, shopData.blip and shopData.blip.scale or 0.8)
            SetBlipColour(blip, shopData.blip and shopData.blip.color or 2)
            SetBlipAsShortRange(blip, true)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(shopData.name)
            EndTextCommandSetBlipName(blip)
        end

        -- Create interaction point
        exports['olympus-ui']:AddInteraction({
            coords = location,
            distance = 2.5,
            text = 'Press [E] to access ' .. shopData.name,
            action = function()
                OpenShop(shopName, shopData)
            end
        })

        -- Create 3D marker
        CreateThread(function()
            while true do
                local playerCoords = GetEntityCoords(PlayerPedId())
                local distance = #(playerCoords - location)

                if distance < 10.0 then
                    DrawMarker(1, location.x, location.y, location.z - 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 2.0, 2.0, 1.0, 0, 255, 0, 100, false, true, 2, false, nil, nil, false)

                    if distance < 2.5 then
                        DrawText3D(location.x, location.y, location.z + 0.5, shopData.name)
                    end

                    Wait(0)
                else
                    Wait(500)
                end
            end
        end)
    end
end

-- Open shop menu
function OpenShop(shopName, shopData)
    if OlympusEconomy.ShopMenuOpen then return end

    -- Check access requirements
    local canAccess, reason = CanAccessShop(shopData)
    if not canAccess then
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Access Denied',
            message = reason,
            duration = 5000
        })
        return
    end

    OlympusEconomy.ShopMenuOpen = true

    -- Prepare shop data with current prices
    local shopItems = {}
    for _, item in ipairs(shopData.items) do
        local currentPrice = item.price

        -- Apply market pricing if it's a market item
        if OlympusEconomy.MarketPrices[item.item] then
            currentPrice = OlympusEconomy.MarketPrices[item.item]
        end

        table.insert(shopItems, {
            item = item.item,
            price = currentPrice,
            buyPrice = item.buyPrice or math.floor(currentPrice * 0.6), -- 60% of sell price
            canBuy = item.canBuy ~= false,
            canSell = item.canSell ~= false
        })
    end

    -- Open shop UI
    exports['olympus-ui']:OpenShop({
        shopName = shopData.name,
        shopType = shopName,
        items = shopItems,
        canBuyItems = shopData.canBuyItems ~= false,
        canSellItems = shopData.canSellItems ~= false
    })
end

-- Check if player can access shop
function CanAccessShop(shopData)
    if not shopData.requirements then return true end

    local playerData = exports['olympus-core']:GetPlayerData()
    if not playerData then return false, 'Player data not available' end

    -- Check faction requirements
    if shopData.requirements.faction then
        if playerData.faction ~= shopData.requirements.faction then
            return false, 'You must be a member of ' .. shopData.requirements.faction .. ' to access this shop'
        end
    end

    -- Check excluded factions
    if shopData.requirements.excludedFactions then
        for _, faction in ipairs(shopData.requirements.excludedFactions) do
            if playerData.faction == faction then
                return false, 'Members of ' .. faction .. ' cannot access this shop'
            end
        end
    end

    -- Check license requirements
    if shopData.requirements.license then
        if not playerData.licenses or not playerData.licenses[shopData.requirements.license] then
            return false, 'You need a ' .. shopData.requirements.license .. ' license to access this shop'
        end
    end

    -- Check item requirements
    if shopData.requirements.item then
        local hasItem = exports['olympus-core']:HasItem(shopData.requirements.item)
        if not hasItem then
            return false, 'You need a ' .. shopData.requirements.item .. ' to access this shop'
        end
    end

    return true
end

-- Initialize blackmarket
function InitializeBlackmarket()
    CreateThread(function()
        -- Wait for UI system
        while not exports['olympus-ui'] or not exports['olympus-ui']:IsLoaded() do
            Wait(1000)
        end

        local blackMarket = Config.Shops.black_market

        for _, location in ipairs(blackMarket.locations) do
            -- Create interaction point (no blip for black market)
            exports['olympus-ui']:AddInteraction({
                coords = location,
                distance = 2.0,
                text = 'Press [E] to access Black Market',
                action = function()
                    TriggerServerEvent('olympus-economy:server:requestBlackmarketAccess')
                end
            })

            -- Create subtle marker
            CreateThread(function()
                while true do
                    local playerCoords = GetEntityCoords(PlayerPedId())
                    local distance = #(playerCoords - location)

                    if distance < 5.0 then
                        DrawMarker(1, location.x, location.y, location.z - 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5, 1.5, 0.5, 255, 0, 0, 100, false, true, 2, false, nil, nil, false)

                        if distance < 2.0 then
                            DrawText3D(location.x, location.y, location.z + 0.5, "Black Market")
                        end

                        Wait(0)
                    else
                        Wait(500)
                    end
                end
            end)
        end
    end)
end

-- Initialize market price displays
function InitializeMarketDisplays()
    -- Request current market prices from server
    TriggerServerEvent('olympus-economy:server:requestMarketPrices')
end

-- Draw 3D text
function DrawText3D(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())

    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x, _y)

    local factor = (string.len(text)) / 370
    DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 41, 11, 41, 68)
end

-- Event Handlers
RegisterNetEvent('olympus-economy:client:updateMarketPrices')
AddEventHandler('olympus-economy:client:updateMarketPrices', function(prices)
    OlympusEconomy.MarketPrices = prices
    print("^3[Olympus Economy]^7 Market prices updated")
end)

RegisterNetEvent('olympus-economy:client:openBlackmarket')
AddEventHandler('olympus-economy:client:openBlackmarket', function(blackMarketData)
    if OlympusEconomy.BlackmarketOpen then return end

    OlympusEconomy.BlackmarketOpen = true

    -- Open blackmarket UI
    exports['olympus-ui']:OpenShop({
        shopName = blackMarketData.name,
        shopType = 'black_market',
        items = blackMarketData.items,
        canBuyItems = true,
        canSellItems = false,
        isBlackMarket = true
    })
end)

RegisterNetEvent('olympus-economy:client:closeShop')
AddEventHandler('olympus-economy:client:closeShop', function()
    CloseShop()
end)

-- NUI Callbacks
RegisterNUICallback('closeShop', function(data, cb)
    CloseShop()
    cb('ok')
end)

RegisterNUICallback('buyItem', function(data, cb)
    if data.shopType == 'black_market' then
        TriggerServerEvent('olympus-economy:server:purchaseBlackmarketItem', data.item, data.price)
    else
        TriggerServerEvent('olympus-economy:server:buyItem', data.item, data.amount, data.shopType)
    end
    cb('ok')
end)

RegisterNUICallback('sellItem', function(data, cb)
    TriggerServerEvent('olympus-economy:server:sellItem', data.item, data.amount, data.shopType)
    cb('ok')
end)

-- Close shop
function CloseShop()
    OlympusEconomy.ShopMenuOpen = false
    OlympusEconomy.BlackmarketOpen = false

    -- Close UI
    exports['olympus-ui']:CloseShop()

    -- Restore NUI focus
    SetNuiFocus(false, false)
end

-- Handle ESC key to close shops
CreateThread(function()
    while true do
        Wait(0)
        if OlympusEconomy.ShopMenuOpen or OlympusEconomy.BlackmarketOpen then
            if IsControlJustPressed(0, 322) then -- ESC key
                CloseShop()
            end
        else
            Wait(500)
        end
    end
end)

-- Market price display command
RegisterCommand('marketprices', function()
    if not OlympusEconomy.PlayerLoaded then return end

    local priceList = {}
    for itemName, price in pairs(OlympusEconomy.MarketPrices) do
        if itemName ~= 'foodDiv' and itemName ~= 'legalDiv' and itemName ~= 'illegalDiv' then
            table.insert(priceList, {item = itemName, price = price})
        end
    end

    -- Sort by price (highest first)
    table.sort(priceList, function(a, b) return a.price > b.price end)

    -- Display top 10 prices
    exports['olympus-ui']:ShowNotification({
        type = 'info',
        title = 'Market Prices (Top 10)',
        message = 'Check console for full price list',
        duration = 5000
    })

    print("^3[Olympus Economy]^7 Current Market Prices:")
    for i = 1, math.min(10, #priceList) do
        local item = priceList[i]
        print(string.format("^2%s^7: $%s", item.item, item.price))
    end
end, false)

-- Export functions
exports('GetMarketPrice', function(itemName)
    return OlympusEconomy.MarketPrices[itemName]
end)

exports('GetMarketPrices', function()
    return OlympusEconomy.MarketPrices
end)

exports('OpenShop', function(shopName)
    if Config.Shops[shopName] then
        OpenShop(shopName, Config.Shops[shopName])
    end
end)

exports('OpenBlackmarket', function()
    TriggerServerEvent('olympus-economy:server:requestBlackmarketAccess')
end)

exports('IsShopOpen', function()
    return OlympusEconomy.ShopMenuOpen or OlympusEconomy.BlackmarketOpen
end)

print("^2[Olympus Economy]^7 Client module loaded")

-- Initialize blackmarket locations
function InitializeBlackmarket()
    CreateThread(function()
        -- Wait for olympus-ui to be loaded
        while not exports['olympus-ui'] or not exports['olympus-ui']:IsLoaded() do
            Wait(1000)
        end

        -- Create blackmarket interactions
        for i, location in pairs(Config.Shops.black_market.locations) do
            exports['olympus-ui']:AddInteraction({
                coords = location,
                distance = 2.0,
                text = 'Press [E] to access Black Market',
                action = function()
                    TriggerServerEvent('olympus-economy:server:requestBlackmarketAccess')
                end
            })
        end

        print("^2[Olympus Economy]^7 Blackmarket interactions initialized")
    end)
end

-- Handle blackmarket opening
RegisterNetEvent('olympus-economy:client:openBlackmarket')
AddEventHandler('olympus-economy:client:openBlackmarket', function(blackmarketData)
    if blackmarketOpen then return end

    blackmarketOpen = true
    SetNuiFocus(true, true)

    -- Send blackmarket data to NUI
    SendNUIMessage({
        type = 'openBlackmarket',
        data = blackmarketData
    })
end)

-- Handle blackmarket closing
function CloseBlackmarket()
    if not blackmarketOpen then return end

    blackmarketOpen = false
    SetNuiFocus(false, false)

    SendNUIMessage({
        type = 'closeBlackmarket'
    })
end

-- NUI Callbacks
RegisterNUICallback('closeBlackmarket', function(data, cb)
    CloseBlackmarket()
    cb('ok')
end)

RegisterNUICallback('purchaseItem', function(data, cb)
    TriggerServerEvent('olympus-economy:server:purchaseBlackmarketItem', data.item, data.price)
    cb('ok')
end)

-- Close blackmarket on ESC
CreateThread(function()
    while true do
        Wait(0)
        if blackmarketOpen then
            if IsControlJustPressed(0, 322) then -- ESC key
                CloseBlackmarket()
            end
        else
            Wait(500)
        end
    end
end)

exports('IsLoaded', function() return isLoaded end)
exports('OpenBlackmarket', function()
    TriggerServerEvent('olympus-economy:server:requestBlackmarketAccess')
end)
