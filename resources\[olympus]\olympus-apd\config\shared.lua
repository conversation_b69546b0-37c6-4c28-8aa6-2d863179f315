-- Olympus APD System - Complete Implementation
-- Based on the complete 22-chapter APD Handbook from Olympus Entertainment

Config = {}

-- APD Settings (Exact Olympus Requirements)
Config.APDFaction = 'apd'
Config.MaxAPDOnline = 50
Config.MinAPDForEvents = 4
Config.ProcessingTime = 900 -- 15 minutes exactly (Chapter V)
Config.MaxTicketAmount = 500000
Config.FactionSwitchCooldown = 900 -- 15 minutes (Chapter I - Metagaming)

-- APD Ranks (Exact Olympus Hierarchy with Complete Requirements)
Config.Ranks = {
    [1] = { -- Deputy
        name = 'Deputy',
        label = 'Deputy',
        salary = 1000,
        description = 'Entry level APD officer, should be learning the rules',
        requirements = {
            playtime = 1800, -- Minimum 1,800 minutes on server
            serverTime = 0,
            interview = true
        },
        permissions = {
            patrol = true,
            processing = true,
            equipment = {'taser', 'restraints', 'spike_strips'},
            vehicles = {'hatchback', 'suv'},
            lethals = false, -- No lethals except in specific situations
            undercover = false,
            checkpoints = false,
            redeployments = false
        }
    },
    [2] = { -- Patrol Officer
        name = 'Patrol Officer',
        label = 'Patrol Officer',
        salary = 1500,
        description = 'Conducts illegal area patrols and mentors deputies',
        requirements = {
            playtime = 1200, -- Minimum 1,200 minutes
            timeInGrade = 604800, -- 7 days time in grade
            previousRank = 1
        },
        permissions = {
            patrol = true,
            illegalAreas = true,
            processing = true,
            mentoring = true,
            equipment = {'taser', 'restraints', 'spike_strips', 'flashbangs'},
            vehicles = {'hatchback', 'suv', 'offroad'},
            lethals = false, -- Except in specific federal situations
            undercover = false,
            checkpoints = true,
            redeployments = false
        }
    },
    [3] = { -- Corporal
        name = 'Corporal',
        label = 'Corporal',
        salary = 2000,
        description = 'Leader among lower ranks, first lethal loading officer',
        requirements = {
            playtime = 10000, -- Minimum 10,000 minutes
            timeInGrade = 2592000, -- 30 days time in grade
            previousRank = 2,
            recommendation = true
        },
        permissions = {
            patrol = true,
            illegalAreas = true,
            processing = true,
            mentoring = true,
            leadership = true,
            equipment = {'taser', 'restraints', 'spike_strips', 'flashbangs', 'sdar', 'road_kits'},
            vehicles = {'hatchback', 'suv', 'offroad', 'quilin', 'orca'},
            lethals = true, -- First rank with lethal authorization
            undercover = true,
            checkpoints = true,
            redeployments = true,
            fto_eligible = true,
            armed_jeep = true
        }
    },
    [4] = { -- Sergeant
        name = 'Sergeant',
        label = 'Sergeant',
        salary = 2500,
        description = 'Interviews new applicants, handles everyday issues',
        requirements = {
            appointment = true, -- Selected by Chief and Senior APD
            previousRank = 3
        },
        permissions = {
            patrol = true,
            illegalAreas = true,
            processing = true,
            mentoring = true,
            leadership = true,
            interviews = true,
            equipment = {'all_standard', 'hazmat_suit', 'wetsuit', 'heli_crew_helmet', 'basic_helmet', 'skate_helmet'},
            vehicles = {'all_standard', 'armed_plane'},
            lethals = true,
            undercover = true,
            checkpoints = true,
            redeployments = true,
            bait_cars = true,
            trial_by_combat = true,
            hostage_negotiation = true
        }
    },
    [5] = { -- Lieutenant
        name = 'Lieutenant',
        label = 'Lieutenant',
        salary = 3000,
        description = 'Accepts applications and handles moderate to severe issues',
        requirements = {
            appointment = true, -- Appointed by Chief and Deputy Chiefs
            previousRank = 4
        },
        permissions = {
            patrol = true,
            illegalAreas = true,
            processing = true,
            mentoring = true,
            leadership = true,
            interviews = true,
            applications = true,
            equipment = {'all_equipment'},
            vehicles = {'all_vehicles'},
            lethals = true,
            undercover = true,
            checkpoints = true,
            redeployments = true,
            bait_cars = true,
            disciplinary_action = true
        }
    },
    [6] = { -- Captain
        name = 'Captain',
        label = 'Captain',
        salary = 3500,
        description = 'Responsible for a section of the APD',
        requirements = {
            appointment = true, -- Appointed by Chief and Deputy Chiefs
            previousRank = 5
        },
        permissions = {
            patrol = true,
            illegalAreas = true,
            processing = true,
            mentoring = true,
            leadership = true,
            interviews = true,
            applications = true,
            section_management = true,
            equipment = {'all_equipment'},
            vehicles = {'all_vehicles'},
            lethals = true,
            undercover = true,
            checkpoints = true,
            redeployments = true,
            bait_cars = true,
            disciplinary_action = true
        }
    },
    [7] = { -- Deputy Chief
        name = 'Deputy Chief',
        label = 'Deputy Chief of Police',
        salary = 4000,
        description = 'Advisers to the Chief',
        requirements = {
            appointment = true, -- Appointed by Chief
            previousRank = 6
        },
        permissions = {
            all_permissions = true,
            chief_advisor = true,
            appointment_authority = true -- Can appoint Lieutenants and Captains
        }
    },
    [8] = { -- Chief
        name = 'Chief',
        label = 'Chief of Police',
        salary = 5000,
        description = 'The final say on all things APD',
        requirements = {
            appointment = true, -- The ultimate authority
            previousRank = 7
        },
        permissions = {
            all_permissions = true,
            executive_decision = true, -- Can supersede APD handbook
            final_authority = true,
            appointment_authority = true -- Can appoint all ranks
        }
    }
}

-- Use of Force Rules (Chapter III - Exact Olympus Implementation)
Config.UseOfForce = {
    -- General Rules
    nonLethalFirst = true, -- Always attempt non-lethal first
    escalationRequired = true, -- Must escalate through force levels

    -- Lethal Force Authorization (Exact Olympus Conditions)
    lethalAuthorized = {
        -- Immediate Lethal Authorization
        'federal_events', -- All federal events (Fed, BW, Evidence, Jail)
        'gang_bases', -- Gang base raids
        'cartels', -- Cartel captures
        'warzone', -- Active warzone
        'officer_down', -- Officer down situations
        'hostage_situation', -- Active hostage situations
        'armed_aircraft', -- Armed aircraft engagement

        -- Conditional Lethal Authorization
        'fleeing_felon', -- Fleeing felon (if poses danger to public)
        'armed_suspect', -- Armed suspect (if weapon drawn)
        'vehicle_ramming', -- Suspect ramming officers/civilians
        'imminent_threat', -- Imminent threat to life
        'self_defense', -- Self defense or defense of others
        'prison_break', -- Active prison break
        'bank_robbery' -- Bank robbery in progress
    },

    -- Rank-Based Lethal Restrictions
    lethalByRank = {
        [1] = false, -- Deputy - No lethals (except federal events)
        [2] = false, -- Patrol Officer - No lethals (except federal events)
        [3] = true,  -- Corporal+ - Full lethal authorization
        [4] = true,  -- Sergeant+ - Full lethal authorization
        [5] = true,  -- Lieutenant+ - Full lethal authorization
        [6] = true,  -- Captain+ - Full lethal authorization
        [7] = true,  -- Deputy Chief+ - Full lethal authorization
        [8] = true   -- Chief - Full lethal authorization
    },

    -- Federal Event Exceptions (Chapter XVIII)
    federalExceptions = {
        deputyLethals = true, -- Deputies can use lethals at federal events
        patrolOfficerLethals = true, -- Patrol Officers can use lethals at federal events
        immediateEngagement = true, -- No warning required in KOS zones
        noWaveRule = false -- Wave rule still applies (except Evidence Lockup)
    },

    -- Vehicle Engagement Rules (Chapter XI)
    vehicleEngagement = {
        disablingShots = true, -- Shoot tires first
        engineBlock = true, -- Can shoot engine block
        driverShooting = false, -- Cannot shoot driver unless lethal authorized
        ramming = {
            authorized = true, -- PIT maneuvers authorized
            speedLimit = 80, -- No ramming above 80 km/h
            rankRequired = 3 -- Corporal+ for ramming
        }
    },

    -- Aircraft Engagement Rules (Chapter XII)
    aircraftEngagement = {
        warningShots = true, -- Warning shots required first
        disablingShots = true, -- Shoot rotors/engines first
        pilotShooting = false, -- Cannot shoot pilot unless lethal authorized
        antiAir = {
            authorized = true, -- Can use anti-air weapons
            rankRequired = 4, -- Sergeant+ for anti-air
            federalOnly = false -- Can use outside federal events
        }
    }
}

-- Wave Rules (Chapter IV - Critical Olympus APD Rule)
Config.WaveRules = {
    enabled = true,

    -- Areas Where Wave Rule Applies
    waveAreas = {
        'illegal_areas', -- All illegal areas (drug fields, processors, etc.)
        'gang_bases', -- Gang base raids
        'cartels', -- Cartel captures
        'federal_events', -- Federal events (except Evidence Lockup)
        'bank_robberies', -- Bank robberies
        'prison_breaks', -- Prison breaks
        'high_value_targets' -- HVT operations
    },

    -- Wave Rule Mechanics
    mechanics = {
        mustReturnToHQ = true, -- Must return to HQ between waves
        waveDelay = 30, -- 30 seconds minimum between waves
        maxWaveSize = 999, -- No limit on wave size
        reinforcements = false, -- Cannot call reinforcements mid-wave

        -- Exceptions to Wave Rule
        exceptions = {
            pursuit = true, -- Can re-enter if in active pursuit
            firstWaveBackup = true, -- First wave can call backup
            officerDown = true, -- Can re-enter if officer down
            evidenceLockup = true, -- No wave rule at Evidence Lockup
            blueZone = true -- No wave rule in blue zones
        }
    },

    -- HQ Locations (Must return to one of these)
    headquarters = {
        vector3(425.1, -979.5, 30.7), -- LSPD HQ
        vector3(1854.1, 3678.9, 34.3), -- Sandy Shores PD
        vector3(-448.1, 6008.5, 31.7), -- Paleto Bay PD
        vector3(1692.6, 2581.4, 45.9), -- Prison
        vector3(-1037.0, -2737.0, 20.0) -- Airport PD
    },

    -- Penalties for Wave Rule Violations
    penalties = {
        enabled = true,
        firstViolation = 'warning',
        secondViolation = 'suspension_24h',
        thirdViolation = 'demotion',
        fourthViolation = 'removal'
    }
}

-- Use of Force Rules
Config.UseOfForce = {
    nonLethalFirst = true,
    lethalAuthorizedWhen = {
        'warzone',
        'cartels',
        'gang_base',
        'rebel_outpost',
        'server_restart_5min',
        'tasers_inadequate',
        'roof_access',
        'fired_upon',
        'armed_vehicle_gunner',
        'two_failed_attempts',
        'officer_hostage',
        'outnumbered_3to1',
        'ghosthawk_guns_hot'
    }
}

-- Equipment Access by Rank
Config.EquipmentAccess = {
    [1] = { -- Deputy
        weapons = {'weapon_stungun', 'weapon_flashlight', 'weapon_nightstick'},
        vehicles = {'police', 'police2'},
        gear = {'radio', 'handcuffs', 'citation_book'}
    },
    [2] = { -- Patrol Officer
        weapons = {'weapon_stungun', 'weapon_flashlight', 'weapon_nightstick', 'weapon_pistol'},
        vehicles = {'police', 'police2', 'police3'},
        gear = {'radio', 'handcuffs', 'citation_book', 'spike_strips'}
    },
    [3] = { -- Corporal
        weapons = {'weapon_stungun', 'weapon_flashlight', 'weapon_nightstick', 'weapon_pistol', 'weapon_carbinerifle'},
        vehicles = {'police', 'police2', 'police3', 'police4', 'polmav'},
        gear = {'radio', 'handcuffs', 'citation_book', 'spike_strips', 'defuse_kit', 'road_kit'}
    },
    [4] = { -- Sergeant
        weapons = {'weapon_stungun', 'weapon_flashlight', 'weapon_nightstick', 'weapon_pistol', 'weapon_carbinerifle', 'weapon_sniperrifle'},
        vehicles = {'police', 'police2', 'police3', 'police4', 'polmav', 'riot'},
        gear = {'radio', 'handcuffs', 'citation_book', 'spike_strips', 'defuse_kit', 'road_kit', 'breaching_charge'}
    }
}

-- Vehicle Access by Rank
Config.VehicleAccess = {
    [1] = {'police', 'police2'}, -- Deputy
    [2] = {'police', 'police2', 'police3'}, -- Patrol Officer
    [3] = {'police', 'police2', 'police3', 'police4', 'polmav'}, -- Corporal
    [4] = {'police', 'police2', 'police3', 'police4', 'polmav', 'riot'}, -- Sergeant
    [5] = {'police', 'police2', 'police3', 'police4', 'polmav', 'riot', 'policet'}, -- Lieutenant
    [6] = {'police', 'police2', 'police3', 'police4', 'polmav', 'riot', 'policet'}, -- Captain
    [7] = {'police', 'police2', 'police3', 'police4', 'polmav', 'riot', 'policet'}, -- Deputy Chief
    [8] = {'police', 'police2', 'police3', 'police4', 'polmav', 'riot', 'policet'} -- Chief
}

-- Processing Rules
Config.Processing = {
    maxTime = 900, -- 15 minutes
    locations = {
        {name = 'Mission Row PD', coords = vector3(425.1, -979.5, 30.7)},
        {name = 'Vespucci PD', coords = vector3(-1096.6, -834.8, 37.7)},
        {name = 'Davis PD', coords = vector3(397.4, -1607.9, 29.3)},
        {name = 'Sandy Shores PD', coords = vector3(1854.1, 3678.9, 34.3)},
        {name = 'Paleto Bay PD', coords = vector3(-448.1, 6008.5, 31.7)}
    },
    requiredSteps = {'licenses', 'inventory', 'seize', 'ticket'}
}

-- Ticket System
Config.Tickets = {
    maxAmount = 500000,
    categories = {
        ['traffic'] = {
            name = 'Traffic Violations',
            violations = {
                ['speeding'] = {name = 'Speeding', fine = 500},
                ['reckless_driving'] = {name = 'Reckless Driving', fine = 1500},
                ['hit_and_run'] = {name = 'Hit and Run', fine = 5000},
                ['no_license'] = {name = 'Driving Without License', fine = 2000}
            }
        },
        ['criminal'] = {
            name = 'Criminal Violations',
            violations = {
                ['assault'] = {name = 'Assault', fine = 10000},
                ['battery'] = {name = 'Battery', fine = 15000},
                ['robbery'] = {name = 'Robbery', fine = 50000},
                ['murder'] = {name = 'Murder', fine = 100000}
            }
        },
        ['drug'] = {
            name = 'Drug Violations',
            violations = {
                ['possession'] = {name = 'Drug Possession', fine = 5000},
                ['trafficking'] = {name = 'Drug Trafficking', fine = 25000},
                ['manufacturing'] = {name = 'Drug Manufacturing', fine = 50000}
            }
        }
    }
}

-- Dispatch System
Config.Dispatch = {
    types = {
        ['911'] = {name = '911 Call', priority = 1, color = '#FF0000'},
        ['backup'] = {name = 'Officer Needs Backup', priority = 2, color = '#FF6600'},
        ['pursuit'] = {name = 'Vehicle Pursuit', priority = 2, color = '#FF9900'},
        ['shots_fired'] = {name = 'Shots Fired', priority = 1, color = '#FF0000'},
        ['robbery'] = {name = 'Robbery in Progress', priority = 1, color = '#FF3300'},
        ['traffic_stop'] = {name = 'Traffic Stop', priority = 3, color = '#0066FF'},
        ['welfare_check'] = {name = 'Welfare Check', priority = 4, color = '#00FF00'}
    }
}

-- Federal Events
Config.FederalEvents = {
    ['federal_reserve'] = {
        name = 'Federal Reserve',
        location = vector3(243.2, 225.5, 106.3),
        priority = 1,
        minAPD = 4,
        waveRule = true
    },
    ['blackwater'] = {
        name = 'Blackwater Armory',
        location = vector3(2447.9, 1576.9, 33.0),
        priority = 1,
        minAPD = 4,
        waveRule = true
    },
    ['jail'] = {
        name = 'Altis Penitentiary',
        location = vector3(1845.9, 2585.9, 46.0),
        priority = 1,
        minAPD = 4,
        waveRule = true
    },
    ['evidence_lockup'] = {
        name = 'Evidence Lockup',
        location = vector3(471.0, -1007.6, 26.3),
        priority = 1,
        minAPD = 4,
        waveRule = false -- Blue zone rules
    }
}

-- Red Zones (Illegal Areas)
Config.RedZones = {
    ['warzone'] = {
        name = 'Warzone',
        coords = vector3(2447.9, 1576.9, 33.0),
        radius = 500.0,
        patrolCooldown = 900, -- 15 minutes
        requiresCode3 = true
    },
    ['gang_base'] = {
        name = 'Gang Base',
        coords = vector3(1392.3, 1141.9, 114.3),
        radius = 200.0,
        patrolCooldown = 900,
        requiresCode3 = true
    }
}

-- Blue Zones
Config.BlueZones = {
    ['apd_escort'] = {
        name = 'APD Escort Event',
        killOnSight = true, -- Civs can kill cops on sight
        noRP = true
    },
    ['pharmaceutical'] = {
        name = 'Pharmaceutical Robbery',
        killOnSight = true,
        noRP = true
    },
    ['art_gallery'] = {
        name = 'Art Gallery',
        killOnSight = true,
        noRP = true
    },
    ['vehicle_yard'] = {
        name = 'Vehicle Yard',
        killOnSight = true,
        noRP = true
    }
}

-- Hostage Situation Rules
Config.HostageRules = {
    maxDuration = 900, -- 15 minutes
    negotiatorRanks = {4, 5, 6, 7, 8}, -- Sergeant+
    cannotBeTaken = {'negotiator'},
    valuableHostages = {3, 4, 5, 6, 7, 8} -- Corporal+ are valuable
}

-- Speed Limits
Config.SpeedLimits = {
    city = 75, -- km/h
    backroads = 100, -- km/h
    highway = 125 -- km/h
}

-- Checkpoint Rules
Config.Checkpoints = {
    minOfficers = 3, -- Including PO+
    minRank = 2, -- Patrol Officer+
    requiresLights = true,
    noRedZoneDistance = 1000 -- meters
}

-- Evidence System
Config.Evidence = {
    types = {
        ['weapon'] = {name = 'Weapon Evidence', weight = 5},
        ['drug'] = {name = 'Drug Evidence', weight = 2},
        ['blood'] = {name = 'Blood Sample', weight = 1},
        ['fingerprint'] = {name = 'Fingerprint', weight = 1},
        ['photo'] = {name = 'Photograph', weight = 1}
    },
    storageLocations = {
        {name = 'Mission Row Evidence', coords = vector3(471.0, -1007.6, 26.3)},
        {name = 'Davis Evidence', coords = vector3(397.4, -1607.9, 29.3)}
    }
}

-- ========================================
-- CHARGES SYSTEM (Based on original fn_wantedAdd.sqf)
-- ========================================
Config.Charges = {
    -- Traffic Violations
    ['1'] = {name = 'Vehicular Manslaughter', fine = 35000, jailTime = 35},
    ['25'] = {name = 'Speeding', fine = 1500, jailTime = 2},
    ['26'] = {name = 'Reckless Driving', fine = 3000, jailTime = 3},
    ['13'] = {name = 'Hit and Run', fine = 7500, jailTime = 8},
    ['30'] = {name = 'Hit and Run', fine = 7500, jailTime = 8},
    ['19'] = {name = 'Driving w/o license', fine = 6250, jailTime = 6},
    ['20'] = {name = 'Driving w/o lights', fine = 2000, jailTime = 2},
    ['48'] = {name = 'Obstruction of Traffic', fine = 4625, jailTime = 5},
    ['50'] = {name = 'Avoiding a Checkpoint', fine = 30000, jailTime = 30},

    -- Violent Crimes
    ['2'] = {name = 'Manslaughter', fine = 30000, jailTime = 30},
    ['4'] = {name = 'Assault', fine = 500, jailTime = 1},
    ['5'] = {name = 'Attempted Rape', fine = 3000, jailTime = 3},
    ['24'] = {name = 'Attp. Manslaughter', fine = 26250, jailTime = 26},
    ['53'] = {name = 'LEO Manslaughter', fine = 37500, jailTime = 38},
    ['63'] = {name = 'Gang Homicide', fine = 15000, jailTime = 15},

    -- Property Crimes
    ['6'] = {name = 'Attempted Grand Theft Auto', fine = 5000, jailTime = 5},
    ['11'] = {name = 'Grand Theft Auto', fine = 17500, jailTime = 18},
    ['22'] = {name = 'Veh. Theft', fine = 17500, jailTime = 18},
    ['23'] = {name = 'Attp. Veh. Theft', fine = 5000, jailTime = 5},
    ['12'] = {name = 'Petty Theft', fine = 7000, jailTime = 7},
    ['17'] = {name = 'Burglary', fine = 175000, jailTime = 175},
    ['35'] = {name = 'Destruction of property', fine = 63750, jailTime = 64},
    ['55'] = {name = 'Destruction of Gov\'t Property', fine = 63750, jailTime = 64},

    -- Drug Crimes
    ['14'] = {name = 'Possession of Contraband', fine = 31500, jailTime = 32},
    ['15'] = {name = 'Drug Possession', fine = 45000, jailTime = 45},
    ['16'] = {name = 'Drug Trafficking', fine = 34000, jailTime = 34},
    ['51'] = {name = 'Usage of Drugs in Public', fine = 10000, jailTime = 10},

    -- Weapons Crimes
    ['36'] = {name = 'Pos. of firearms w/o license', fine = 11000, jailTime = 11},
    ['37'] = {name = 'Pos. of an ilg. weapon', fine = 12000, jailTime = 12},
    ['38'] = {name = 'Use of firearms within city', fine = 5000, jailTime = 5},
    ['49'] = {name = 'Weapon Trafficking', fine = 15125, jailTime = 15},
    ['69'] = {name = 'Pos. of Explosives', fine = 30000, jailTime = 30},
    ['7'] = {name = 'Use of illegal explosives', fine = 8000, jailTime = 8},

    -- Robbery and Theft
    ['8'] = {name = 'Robbery', fine = 30000, jailTime = 30},
    ['21'] = {name = 'Attp. Robbery', fine = 8000, jailTime = 8},
    ['60'] = {name = 'Gas Station Robbery', fine = 18750, jailTime = 19},
    ['71'] = {name = 'Attp. Bank Robbery', fine = 32500, jailTime = 33},
    ['72'] = {name = 'Aiding in Bank Robbery', fine = 81250, jailTime = 81},

    -- Federal Crimes
    ['44'] = {name = 'Aiding in Reserve Robbery', fine = 112500, jailTime = 113},
    ['45'] = {name = 'Attp. Reserve Robbery', fine = 82500, jailTime = 83},
    ['59'] = {name = 'Aiding in BW Robbery', fine = 112500, jailTime = 113},
    ['65'] = {name = 'Attp. BW Robbery', fine = 82500, jailTime = 83},
    ['68'] = {name = 'Aiding in Pharm. Robbery', fine = 40000, jailTime = 40},
    ['3'] = {name = 'Escaping Jail', fine = 56000, jailTime = 56},
    ['42'] = {name = 'Aiding in jail break', fine = 86000, jailTime = 86},
    ['66'] = {name = 'Attp. Jail Break', fine = 63750, jailTime = 64},

    -- Kidnapping and Hostage
    ['9'] = {name = 'Kidnapping', fine = 11250, jailTime = 11},
    ['10'] = {name = 'Attempted Kidnapping', fine = 4000, jailTime = 4},
    ['39'] = {name = 'Hostage Situation', fine = 86500, jailTime = 87},
    ['67'] = {name = 'Kidnapping Gov\'t Official', fine = 92750, jailTime = 93},

    -- APD Related
    ['27'] = {name = 'Pos. of APD Equip.', fine = 25500, jailTime = 26},
    ['31'] = {name = 'Resisting Arrest', fine = 16500, jailTime = 17},
    ['34'] = {name = 'Entering a Police Area', fine = 6000, jailTime = 6},
    ['47'] = {name = 'Disobeying an Officer', fine = 8000, jailTime = 8},
    ['57'] = {name = 'Obstruction of Justice', fine = 15750, jailTime = 16},
    ['64'] = {name = 'Unlawful Taser Usage', fine = 30000, jailTime = 30},

    -- Aviation Crimes
    ['28'] = {name = 'Ilg. Aerial Veh. Landing', fine = 48750, jailTime = 49},
    ['41'] = {name = 'Flying/Hovering below 150m', fine = 15000, jailTime = 15},
    ['43'] = {name = 'Flying w/o a pilot license', fine = 10500, jailTime = 11},
    ['70'] = {name = 'Flying w/o Collision Lights', fine = 2000, jailTime = 2},

    -- Vehicle Related
    ['29'] = {name = 'Operating an ilg. veh.', fine = 31500, jailTime = 32},
    ['73'] = {name = 'Pos. of Ilg. Equipment', fine = 15000, jailTime = 15},

    -- Threats and Terrorism
    ['32'] = {name = 'Verbal Threats', fine = 8000, jailTime = 8},
    ['33'] = {name = 'Verbal Insults', fine = 3000, jailTime = 3},
    ['40'] = {name = 'Terrorist Acts', fine = 93750, jailTime = 94},
    ['52'] = {name = 'Disturbing the Peace', fine = 1125, jailTime = 1},

    -- Fraud and Cyber Crimes
    ['46'] = {name = 'Insurance Fraud', fine = 1500, jailTime = 2},
    ['54'] = {name = 'Gov\'t Cyber Attack', fine = 30000, jailTime = 30},
    ['58'] = {name = 'Misuse of Emergency System', fine = 40000, jailTime = 40},

    -- Medical and Organ Crimes
    ['18'] = {name = 'Organ Dealing', fine = 17000, jailTime = 17},
    ['61'] = {name = 'Organ Harvesting', fine = 11250, jailTime = 11},
    ['62'] = {name = 'Pos. of Illegal Organ', fine = 22500, jailTime = 23},

    -- Miscellaneous
    ['56'] = {name = 'Party to a Crime', fine = 15000, jailTime = 15},
    ['74'] = {name = 'Public Urination', fine = 2500, jailTime = 3},
    ['75'] = {name = 'Titan Hit', fine = 15000, jailTime = 15}
}

-- Jail Location
Config.JailLocation = vector3(1845.9, 2585.9, 46.0)
