-- Olympus Medical System - Complete R&R Implementation
-- Based on the complete 17-chapter R&R Handbook from Olympus Entertainment

Config = {}

-- R&R Mission Statement (Exact from Handbook)
Config.MissionStatement = "Rescue & Recovery represents Olympus as an elite organization of professionals that strive to provide exceptional and unbiased medical services in a consistent and timely manner through the utilization of equipment, technology, tactics, trust, communication, perseverance, and teamwork"

-- R&R Settings (Exact Olympus Implementation)
Config.MedicalFaction = 'rnr'
Config.MaxMedicsOnline = 30
Config.ReviveCost = 15000 -- Exact $15,000 co-pay from handbook
Config.DopamineCost = 10000 -- $10,000 insurance subsidy

-- Federal Event Locations (for reduced payout calculation)
Config.FederalEventLocations = {
    {
        name = 'Federal Reserve',
        coords = vector3(3540.0, 3675.0, 20.0),
        radius = 700
    },
    {
        name = 'Blackwater Armory',
        coords = vector3(4500.0, 4500.0, 20.0),
        radius = 700
    },
    {
        name = 'Jail',
        coords = vector3(1679.0, 2513.0, 45.0),
        radius = 700
    }
}
Config.ReviveTime = 10 -- seconds
Config.FactionSwitchCooldown = 900 -- 15 minutes (Chapter I - Metagaming)
Config.HostageTimeLimit = 1800 -- 30 minutes max hostage time
Config.APDAssistTime = 900 -- 15 minutes minimum APD assist
Config.NewLifeRule = 900 -- 15 minutes NLR
Config.MaxReviveDistance = 5.0

-- Rank Requirements (Exact R&R Handbook Chapter XII)
Config.Ranks = {
    [1] = { -- EMT
        name = 'Emergency Medical Technician',
        shortName = 'EMT',
        tag = '[Medic]',
        minTime = 0, -- Inception rank
        salary = 300, -- Base pay + $300 every 5 minutes
        description = 'Entry level medic position',
        requirements = {
            interview = true,
            application = true
        },
        permissions = {
            revive = true,
            heal = true,
            repair = false,
            illegalAreas = false, -- Must have APD escort
            hostage = true,
            apdAssist = true,
            equipment = {'medkit', 'epipen', 'food', 'water', 'lollipop'},
            vehicles = {'hatchback', 'suv', 'van', 'sdv'},
            uniform = 'emt_blue'
        }
    },
    [2] = { -- Basic Paramedic
        name = 'Basic Paramedic',
        shortName = 'Basic Paramedic',
        tag = '[Medic]',
        minTime = 1200, -- 1,200 minutes (20 hours)
        salary = 600, -- Base pay + $600 every 5 minutes
        description = 'Can enter illegal areas with escort, firefighting available',
        requirements = {
            timeInGrade = 0,
            previousRank = 1
        },
        permissions = {
            revive = true,
            heal = true,
            repair = true,
            illegalAreas = true, -- Can enter with APD escort or higher ranking medic
            hostage = true,
            apdAssist = true,
            firefighting = true,
            equipment = {'medkit', 'epipen', 'food', 'water', 'lollipop', 'toolkit', 'fire_axe'},
            vehicles = {'hatchback', 'suv', 'van', 'sdv', 'offroad', 'speedboat', 'rhib', 'quilin', 'quadbike', 'tempest_fuel'},
            uniform = 'paramedic_red'
        }
    },
    [3] = { -- Advanced Paramedic
        name = 'Advanced Paramedic',
        shortName = 'Advanced Paramedic',
        tag = '[Medic]',
        minTime = 3000, -- 3,000 minutes (50 hours)
        salary = 900, -- Base pay + $900 every 5 minutes
        description = 'Flight certified, can enter illegal areas alone',
        requirements = {
            timeInGrade = 0,
            previousRank = 2,
            flightCertification = true, -- Must pass APC flight and verbal testing
            advancedCertification = true
        },
        permissions = {
            revive = true,
            heal = true,
            repair = true,
            illegalAreas = true, -- Can enter alone (no escort needed)
            hostage = true,
            apdAssist = true,
            firefighting = true,
            flight = true,
            impounding = true, -- Can windows key impound vehicles
            equipment = {'medkit', 'epipen', 'food', 'water', 'lollipop', 'toolkit', 'fire_axe', 'smoke_grenades', 'chem_lights'},
            vehicles = {'all_ground', 'm900', 'hummingbird', 'hellcat', 'hemmt_flatbed'},
            uniform = 'pilot_coveralls_yellow'
        }
    },
    [4] = { -- Search & Rescue
        name = 'Search & Rescue',
        shortName = 'Search & Rescue',
        tag = '[Medic]',
        minTime = 6000, -- 6,000 minutes (100 hours)
        salary = 1200, -- Base pay + $1,200 every 5 minutes
        description = 'Can use medical crates and advanced equipment',
        requirements = {
            timeInGrade = 2592000, -- 30 days time in grade
            previousRank = 3,
            flightCertified = true,
            noDisciplinaryAction = true
        },
        permissions = {
            revive = true,
            heal = true,
            repair = true,
            illegalAreas = true,
            hostage = true,
            apdAssist = true,
            firefighting = true,
            flight = true,
            impounding = true,
            medicalCrates = true, -- Can purchase, transport, and deliver medical crates
            equipment = {'all_standard', 'medical_crates', 'sling_hooks'},
            vehicles = {'all_ground', 'all_air', 'taru', 'taru_medical', 'taru_transport', 'taru_repair', 'taru_fuel', 'mohawk', 'orca', 'strider'},
            uniform = 'search_rescue_red'
        }
    },
    [5] = { -- Pararescue
        name = 'Pararescue',
        shortName = 'Pararescue',
        tag = '[Medic]',
        minTime = 7000, -- 7,000+ minutes (116+ hours)
        timeInGrade = 5184000, -- 60+ days time in grade
        salary = 1200, -- Base pay + $1,200 every 5 minutes
        description = 'Appointed position with leadership responsibilities',
        requirements = {
            appointment = true, -- Must apply and be appointed
            specialTest = true, -- Must pass specially designed test
            leadership = true, -- Must demonstrate leadership
            professionalism = true,
            dedication = true,
            previousRank = 4
        },
        permissions = {
            revive = true,
            heal = true,
            repair = true,
            illegalAreas = true,
            hostage = true,
            apdAssist = true,
            firefighting = true,
            flight = true,
            impounding = true,
            medicalCrates = true,
            leadership = true,
            equipment = {'all_standard', 'medical_crates', 'sling_hooks', 'flare_gun'},
            vehicles = {'all_ground', 'all_air', 'hunter', 'gokart', 'huron', 'ghosthawk'},
            uniform = 'pararescue_blue_white'
        }
    },
    [6] = { -- Supervisor
        name = 'Supervisor',
        shortName = 'Supervisor',
        tag = '[Sr. Medic]', -- Special senior tag
        minTime = 10000, -- 10,000+ minutes (166+ hours)
        salary = 1500, -- Base pay + $1,500 every 5 minutes
        description = 'Can conduct interviews and certifications',
        requirements = {
            appointment = true, -- Appointed position
            flightProficiency = true, -- Must be proficient pilot
            interviews = true, -- Can facilitate interviews
            certifications = true, -- Can conduct APC tests
            deskDuties = true, -- Can complete desk duties
            recommendations = true, -- Can make promotion/demotion recommendations
            mentoring = true, -- Must mentor other medics
            previousRank = 5
        },
        permissions = {
            all_permissions = true,
            interviews = true,
            certifications = true,
            promotions = true,
            demotions = true,
            disciplinary_action = true,
            equipment = {'all_equipment'},
            vehicles = {'all_vehicles', 'kajman'},
            uniform = 'supervisor_green'
        }
    },
    [7] = { -- Coordinator
        name = 'Coordinator',
        shortName = 'Coordinator',
        tag = '[Sr. Medic]',
        minTime = 20000, -- 20,000+ minutes (333+ hours)
        salary = 1800, -- Base pay + $1,800 every 5 minutes
        description = 'Senior leadership position',
        requirements = {
            appointment = true, -- Appointed by Director
            seniorLeadership = true,
            previousRank = 6
        },
        permissions = {
            all_permissions = true,
            senior_leadership = true,
            equipment = {'all_equipment'},
            vehicles = {'all_vehicles', 'y32_xian'},
            uniform = 'coordinator_black'
        }
    },
    [8] = { -- Director
        name = 'Director',
        shortName = 'Director',
        tag = '[Sr. Medic]',
        minTime = 30000, -- 30,000+ minutes (500+ hours)
        salary = 1800, -- Base pay + $1,800 every 5 minutes
        description = 'Head of R&R operations',
        requirements = {
            appointment = true, -- Ultimate authority
            previousRank = 7
        },
        permissions = {
            all_permissions = true,
            executive_decision = true, -- Can supersede R&R handbook
            final_authority = true,
            equipment = {'all_equipment'},
            vehicles = {'all_vehicles', 'blackfoot'},
            uniform = 'director_black'
        }
    }
}

-- Equipment Access by Rank
Config.EquipmentAccess = {
    [1] = { -- EMT
        items = {'first_aid_kit', 'bloodbag', 'defibrillator'},
        vehicles = {'ambulance'},
        gear = {'medical_radio', 'medical_bag'}
    },
    [2] = { -- Paramedic
        items = {'first_aid_kit', 'bloodbag', 'defibrillator', 'epipen'},
        vehicles = {'ambulance', 'firetruk'},
        gear = {'medical_radio', 'medical_bag', 'stretcher'}
    },
    [3] = { -- Senior Paramedic
        items = {'first_aid_kit', 'bloodbag', 'defibrillator', 'epipen', 'dopamine_shot'},
        vehicles = {'ambulance', 'firetruk', 'polmav'},
        gear = {'medical_radio', 'medical_bag', 'stretcher', 'medical_scanner'}
    },
    [4] = { -- Supervisor
        items = {'first_aid_kit', 'bloodbag', 'defibrillator', 'epipen', 'dopamine_shot', 'surgical_kit'},
        vehicles = {'ambulance', 'firetruk', 'polmav'},
        gear = {'medical_radio', 'medical_bag', 'stretcher', 'medical_scanner', 'supervisor_kit'}
    },
    [5] = { -- Chief of Medicine
        items = {'first_aid_kit', 'bloodbag', 'defibrillator', 'epipen', 'dopamine_shot', 'surgical_kit'},
        vehicles = {'ambulance', 'firetruk', 'polmav'},
        gear = {'medical_radio', 'medical_bag', 'stretcher', 'medical_scanner', 'supervisor_kit', 'chief_kit'}
    }
}

-- Hospital Locations
Config.Hospitals = {
    {
        name = 'Pillbox Medical Center',
        coords = vector3(298.6, -1448.1, 29.9),
        spawn = vector3(295.8, -1446.9, 29.9),
        garage = vector3(307.7, -1433.4, 29.9),
        beds = {
            vector3(307.7, -1433.4, 29.9),
            vector3(311.0, -1433.4, 29.9),
            vector3(314.3, -1433.4, 29.9),
            vector3(317.6, -1433.4, 29.9)
        }
    },
    {
        name = 'Sandy Shores Medical',
        coords = vector3(1839.6, 3672.9, 34.3),
        spawn = vector3(1836.8, 3670.1, 34.3),
        garage = vector3(1851.4, 3678.5, 33.9),
        beds = {
            vector3(1851.4, 3678.5, 33.9),
            vector3(1854.7, 3678.5, 33.9)
        }
    },
    {
        name = 'Paleto Bay Medical',
        coords = vector3(-254.8, 6324.5, 32.4),
        spawn = vector3(-251.9, 6321.7, 32.4),
        garage = vector3(-267.4, 6330.2, 32.4),
        beds = {
            vector3(-267.4, 6330.2, 32.4),
            vector3(-270.7, 6330.2, 32.4)
        }
    }
}

-- Medical Vehicle Access
Config.VehicleAccess = {
    [1] = {'ambulance'}, -- EMT
    [2] = {'ambulance', 'firetruk'}, -- Paramedic
    [3] = {'ambulance', 'firetruk', 'polmav'}, -- Senior Paramedic
    [4] = {'ambulance', 'firetruk', 'polmav'}, -- Supervisor
    [5] = {'ambulance', 'firetruk', 'polmav'} -- Chief of Medicine
}

-- Medical Dispatch Types
Config.DispatchTypes = {
    ['medical_emergency'] = {
        name = 'Medical Emergency',
        priority = 1,
        color = '#FF0000',
        sound = 'medical_emergency'
    },
    ['cardiac_arrest'] = {
        name = 'Cardiac Arrest',
        priority = 1,
        color = '#FF0000',
        sound = 'cardiac_arrest'
    },
    ['vehicle_accident'] = {
        name = 'Vehicle Accident',
        priority = 2,
        color = '#FF6600',
        sound = 'vehicle_accident'
    },
    ['overdose'] = {
        name = 'Drug Overdose',
        priority = 2,
        color = '#FF9900',
        sound = 'overdose'
    },
    ['wellness_check'] = {
        name = 'Wellness Check',
        priority = 3,
        color = '#00FF00',
        sound = 'wellness_check'
    },
    ['transport'] = {
        name = 'Medical Transport',
        priority = 4,
        color = '#0066FF',
        sound = 'transport'
    }
}

-- Injury System
Config.InjurySystem = {
    enabled = true,
    types = {
        ['head'] = {
            name = 'Head Injury',
            effects = {'blurred_vision', 'confusion'},
            severity = 3,
            treatmentTime = 30
        },
        ['chest'] = {
            name = 'Chest Injury',
            effects = {'breathing_difficulty', 'pain'},
            severity = 2,
            treatmentTime = 20
        },
        ['leg'] = {
            name = 'Leg Injury',
            effects = {'limping', 'reduced_speed'},
            severity = 1,
            treatmentTime = 15
        },
        ['arm'] = {
            name = 'Arm Injury',
            effects = {'reduced_accuracy', 'pain'},
            severity = 1,
            treatmentTime = 15
        }
    }
}

-- Death System
Config.DeathSystem = {
    respawnTime = 300, -- 5 minutes
    newLifeRule = 900, -- 15 minutes
    deathCauses = {
        ['gunshot'] = 'Gunshot Wound',
        ['vehicle'] = 'Vehicle Accident',
        ['fall'] = 'Fall Damage',
        ['explosion'] = 'Explosion',
        ['drowning'] = 'Drowning',
        ['fire'] = 'Fire Damage',
        ['unknown'] = 'Unknown Cause'
    },
    deathLocations = {
        vector3(298.6, -1448.1, 29.9), -- Pillbox
        vector3(1839.6, 3672.9, 34.3), -- Sandy Shores
        vector3(-254.8, 6324.5, 32.4) -- Paleto Bay
    }
}

-- Medical Items and Effects
Config.MedicalItems = {
    ['first_aid_kit'] = {
        name = 'First Aid Kit',
        healAmount = 75,
        useTime = 10,
        canUseOnOthers = true,
        canUseInVehicle = false
    },
    ['bloodbag'] = {
        name = 'Bloodbag',
        healAmount = 100,
        useTime = 15,
        canUseOnOthers = true,
        canUseInVehicle = true
    },
    ['epipen'] = {
        name = 'Epipen',
        revive = true,
        useTime = 5,
        canUseOnOthers = true,
        canUseInVehicle = false,
        cooldown = 1800 -- 30 minutes
    },
    ['dopamine_shot'] = {
        name = 'Dopamine Shot',
        revive = true,
        useTime = 3,
        canUseOnOthers = true,
        canUseInVehicle = false,
        cooldown = 1800, -- 30 minutes
        rankRequired = 3 -- Senior Paramedic+
    },
    ['defibrillator'] = {
        name = 'Defibrillator',
        revive = true,
        useTime = 8,
        canUseOnOthers = true,
        canUseInVehicle = false,
        rankRequired = 1 -- EMT+
    }
}

-- Treatment Costs
Config.TreatmentCosts = {
    ['revive'] = 2500,
    ['heal'] = 500,
    ['surgery'] = 10000,
    ['checkup'] = 100,
    ['bloodwork'] = 250
}

-- Medical Procedures
Config.MedicalProcedures = {
    ['basic_treatment'] = {
        name = 'Basic Treatment',
        time = 10,
        cost = 500,
        healAmount = 50,
        rankRequired = 1
    },
    ['advanced_treatment'] = {
        name = 'Advanced Treatment',
        time = 20,
        cost = 1500,
        healAmount = 100,
        rankRequired = 2
    },
    ['surgery'] = {
        name = 'Surgery',
        time = 60,
        cost = 10000,
        healAmount = 100,
        rankRequired = 3,
        location = 'hospital'
    },
    ['psychological_evaluation'] = {
        name = 'Psychological Evaluation',
        time = 30,
        cost = 2500,
        rankRequired = 4,
        location = 'hospital'
    }
}

-- Emergency Response Zones
Config.EmergencyZones = {
    ['hospital_zone'] = {
        name = 'Hospital Emergency Zone',
        coords = vector3(298.6, -1448.1, 29.9),
        radius = 100.0,
        noWeapons = true,
        safeZone = true
    },
    ['accident_zone'] = {
        name = 'Accident Response Zone',
        temporary = true,
        radius = 50.0,
        duration = 1800 -- 30 minutes
    }
}

-- Medical Training Requirements
Config.TrainingRequirements = {
    [2] = { -- Paramedic
        courses = {'basic_life_support', 'trauma_care'},
        practicalHours = 10
    },
    [3] = { -- Senior Paramedic
        courses = {'advanced_life_support', 'emergency_medicine'},
        practicalHours = 25
    },
    [4] = { -- Supervisor
        courses = {'leadership', 'emergency_management'},
        practicalHours = 50
    },
    [5] = { -- Chief of Medicine
        courses = {'administration', 'medical_ethics'},
        practicalHours = 100
    }
}

-- Medical Statistics Tracking
Config.Statistics = {
    trackRevives = true,
    trackTreatments = true,
    trackResponseTimes = true,
    trackPatientOutcomes = true,
    reportingPeriod = 604800 -- 1 week in seconds
}

-- Integration Settings
Config.Integration = {
    apd = true, -- Work with APD for accident scenes
    dispatch = true, -- Integrated dispatch system
    insurance = true, -- Medical insurance system
    billing = true -- Medical billing system
}

return Config
