-- ========================================
-- OLYMPUS APD CLIENT SYSTEM
-- Complete implementation based on original Olympus APD functions
-- ========================================

local OlympusAPD = {}
OlympusAPD.OnDuty = false
OlympusAPD.Rank = 0
OlympusAPD.Callsign = nil
OlympusAPD.Equipment = {}
OlympusAPD.WantedList = {}
OlympusAPD.ProcessingData = nil
OlympusAPD.IsJailed = false
OlympusAPD.JailData = nil

-- ========================================
-- INITIALIZATION
-- ========================================

-- Initialize APD client
CreateThread(function()
    while not exports['olympus-core']:IsPlayerLoaded() do
        Wait(100)
    end

    local playerData = exports['olympus-core']:GetPlayerData()
    if playerData.faction == 'apd' then
        OlympusAPD.Rank = playerData.rank or 0
        TriggerServerEvent('olympus:apd:playerJoined')

        -- Initialize APD blips and locations
        OlympusAPD.InitializeAPDLocations()
    end
end)

-- Initialize APD locations and blips
function OlympusAPD.InitializeAPDLocations()
    -- APD Headquarters blips
    local apdLocations = {
        {name = "Mission Row PD", coords = vector3(425.1, -979.5, 30.7), sprite = 60, color = 29},
        {name = "Vespucci PD", coords = vector3(-1096.6, -834.8, 37.7), sprite = 60, color = 29},
        {name = "Davis PD", coords = vector3(397.4, -1607.9, 29.3), sprite = 60, color = 29},
        {name = "Sandy Shores PD", coords = vector3(1854.1, 3678.9, 34.3), sprite = 60, color = 29},
        {name = "Paleto Bay PD", coords = vector3(-448.1, 6008.5, 31.7), sprite = 60, color = 29}
    }

    for _, location in ipairs(apdLocations) do
        local blip = AddBlipForCoord(location.coords.x, location.coords.y, location.coords.z)
        SetBlipSprite(blip, location.sprite)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, 0.8)
        SetBlipColour(blip, location.color)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(location.name)
        EndTextCommandSetBlipName(blip)
    end
end

-- ========================================
-- COMMANDS
-- ========================================

-- APD Duty Toggle
RegisterCommand('apdduty', function()
    if OlympusAPD.Rank > 0 then
        TriggerServerEvent('olympus:apd:toggleDuty')
    else
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'APD',
            message = 'You are not a member of the APD'
        })
    end
end)

-- Request Backup
RegisterCommand('backup', function(source, args)
    if OlympusAPD.OnDuty then
        local coords = GetEntityCoords(PlayerPedId())
        local priority = args[1] or 'normal'
        TriggerServerEvent('olympus:apd:requestBackup', coords, priority)

        exports['olympus-ui']:ShowNotification({
            type = 'info',
            title = 'Backup Requested',
            message = 'Backup request sent with priority: ' .. priority
        })
    else
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'APD',
            message = 'You must be on duty to request backup'
        })
    end
end)

-- APD Chat
RegisterCommand('apdchat', function(source, args)
    if OlympusAPD.OnDuty and #args > 0 then
        local message = table.concat(args, ' ')
        TriggerServerEvent('olympus:apd:sendMessage', message)
    else
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'APD Chat',
            message = 'You must be on duty to use APD chat'
        })
    end
end)

-- Equipment Request
RegisterCommand('apdequip', function()
    if OlympusAPD.OnDuty then
        TriggerServerEvent('olympus:apd:requestEquipment')
    else
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'APD Equipment',
            message = 'You must be on duty to request equipment'
        })
    end
end)

-- Vehicle Spawn
RegisterCommand('apdveh', function(source, args)
    if OlympusAPD.OnDuty and args[1] then
        TriggerServerEvent('olympus:apd:spawnVehicle', args[1])
    else
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'APD Vehicle',
            message = 'Usage: /apdveh [vehicle_model]'
        })
    end
end)

-- Processing Commands
RegisterCommand('process', function()
    if OlympusAPD.OnDuty then
        local nearestPlayer = OlympusAPD.GetNearestPlayer()
        if nearestPlayer then
            TriggerServerEvent('olympus:apd:startProcessing', GetPlayerServerId(nearestPlayer))
            exports['olympus-ui']:ShowNotification({
                type = 'info',
                title = 'Processing',
                message = 'Started processing ' .. GetPlayerName(nearestPlayer)
            })
        else
            exports['olympus-ui']:ShowNotification({
                type = 'error',
                title = 'Processing',
                message = 'No player nearby to process'
            })
        end
    else
        exports['olympus-ui']:ShowNotification({
            type = 'error',
            title = 'Processing',
            message = 'You must be on duty to process suspects'
        })
    end
end)

-- Add Wanted Charge
RegisterCommand('addwanted', function(source, args)
    if OlympusAPD.OnDuty and args[1] and args[2] then
        local targetId = tonumber(args[1])
        local chargeType = args[2]

        if targetId then
            TriggerServerEvent('olympus:apd:addWanted', targetId, chargeType)
        else
            exports['olympus-ui']:ShowNotification({
                type = 'error',
                title = 'Wanted System',
                message = 'Usage: /addwanted [player_id] [charge_type]'
            })
        end
    end
end)

-- ========================================
-- EVENT HANDLERS
-- ========================================

-- Duty Status Events
RegisterNetEvent('olympus:apd:dutyStatusChanged')
AddEventHandler('olympus:apd:dutyStatusChanged', function(onDuty, callsign)
    OlympusAPD.OnDuty = onDuty
    OlympusAPD.Callsign = callsign

    if onDuty then
        exports['olympus-ui']:ShowNotification({
            type = 'success',
            title = 'APD',
            message = 'You are now on duty - Callsign: ' .. callsign
        })
    else
        exports['olympus-ui']:ShowNotification({
            type = 'info',
            title = 'APD',
            message = 'You are now off duty'
        })
    end
end)

-- Equipment System
RegisterNetEvent('olympus:apd:giveWeapon')
AddEventHandler('olympus:apd:giveWeapon', function(weapon)
    GiveWeaponToPed(PlayerPedId(), GetHashKey(weapon), 250, false, true)
end)

RegisterNetEvent('olympus:apd:giveGear')
AddEventHandler('olympus:apd:giveGear', function(gear)
    -- Handle gear giving logic
    print("^2[Olympus APD]^7 Received gear: " .. gear)
end)

-- Vehicle System
RegisterNetEvent('olympus:apd:spawnVehicle')
AddEventHandler('olympus:apd:spawnVehicle', function(vehicleModel)
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    local heading = GetEntityHeading(playerPed)

    RequestModel(GetHashKey(vehicleModel))
    while not HasModelLoaded(GetHashKey(vehicleModel)) do
        Wait(1)
    end

    local vehicle = CreateVehicle(GetHashKey(vehicleModel), coords.x + 5, coords.y, coords.z, heading, true, false)
    SetVehicleOnGroundProperly(vehicle)
    SetPedIntoVehicle(playerPed, vehicle, -1)

    -- Set as APD vehicle
    SetVehicleNumberPlateText(vehicle, "APD" .. math.random(100, 999))

    exports['olympus-ui']:ShowNotification({
        type = 'success',
        title = 'APD Vehicle',
        message = 'Vehicle spawned: ' .. vehicleModel
    })
end)

-- Get nearest player function
function GetNearestPlayer()
    local players = GetActivePlayers()
    local ped = PlayerPedId()
    local pos = GetEntityCoords(ped)
    local nearestPlayer = nil
    local nearestDistance = 5.0
    
    for _, player in ipairs(players) do
        if player ~= PlayerId() then
            local targetPed = GetPlayerPed(player)
            local targetPos = GetEntityCoords(targetPed)
            local distance = #(pos - targetPos)
            
            if distance < nearestDistance then
                nearestDistance = distance
                nearestPlayer = player
            end
        end
    end
    
    return nearestPlayer
end

-- Events
RegisterNetEvent('olympus:apd:dutyStatus')
AddEventHandler('olympus:apd:dutyStatus', function(status)
    onDuty = status
    local statusText = status and "on duty" or "off duty"

    print("^3[Olympus APD Debug]^7 Received duty status: " .. tostring(status))

    -- Update the main HUD system with duty status
    local playerData = exports['olympus-core']:GetPlayerData()
    if playerData then
        exports['olympus-ui']:UpdateHUD({
            faction = playerData.faction,
            faction_rank = playerData.faction_rank,
            duty_status = status
        })

        print("^3[Olympus APD Debug]^7 Updated HUD with duty status: " .. tostring(status))
    end

    -- Show notification (proper format)
    local notificationMessage = 'You are now ' .. statusText
    exports['olympus-ui']:ShowNotification(notificationMessage, 'info', 3000)
end)

-- Give weapon event
RegisterNetEvent('olympus:apd:giveWeapon')
AddEventHandler('olympus:apd:giveWeapon', function(weaponHash)
    local ped = PlayerPedId()
    GiveWeaponToPed(ped, GetHashKey(weaponHash), 250, false, true)
    print("^2[Olympus APD]^7 Received weapon: " .. weaponHash)
end)

-- Give gear event
RegisterNetEvent('olympus:apd:giveGear')
AddEventHandler('olympus:apd:giveGear', function(gearType)
    -- Add gear to inventory (placeholder - would integrate with inventory system)
    print("^2[Olympus APD]^7 Received gear: " .. gearType)
end)

-- Spawn vehicle event
RegisterNetEvent('olympus:apd:spawnVehicle')
AddEventHandler('olympus:apd:spawnVehicle', function(vehicleModel)
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    local heading = GetEntityHeading(ped)

    -- Find a clear spawn location
    local spawnCoords = GetOffsetFromEntityInWorldCoords(ped, 0.0, 5.0, 0.0)

    -- Request model
    RequestModel(GetHashKey(vehicleModel))
    while not HasModelLoaded(GetHashKey(vehicleModel)) do
        Wait(1)
    end

    -- Create vehicle
    local vehicle = CreateVehicle(GetHashKey(vehicleModel), spawnCoords.x, spawnCoords.y, spawnCoords.z, heading, true, false)

    -- Set as mission entity and give keys
    SetEntityAsMissionEntity(vehicle, true, true)
    SetVehicleOnGroundProperly(vehicle)

    -- Set license plate
    SetVehicleNumberPlateText(vehicle, "APD-" .. math.random(100, 999))

    print("^2[Olympus APD]^7 Spawned vehicle: " .. vehicleModel)
end)

-- Backup request event
RegisterNetEvent('olympus:apd:backupRequest')
AddEventHandler('olympus:apd:backupRequest', function(data)
    exports['olympus-ui']:ShowNotification('BACKUP REQUEST: ' .. data.officer .. ' (' .. data.callsign .. ') at ' .. data.location, 'warning', 10000)

    -- Set waypoint to backup location
    SetNewWaypoint(data.coords.x, data.coords.y)

    print("^3[Olympus APD]^7 Backup request from " .. data.officer .. " at " .. data.location)
end)

-- Helper function to get nearest player
function GetNearestPlayer()
    local players = GetActivePlayers()
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    local closestDistance = -1
    local closestPlayer = -1

    for i = 1, #players, 1 do
        local target = GetPlayerPed(players[i])

        if target ~= ped then
            local targetCoords = GetEntityCoords(target)
            local distance = GetDistanceBetweenCoords(targetCoords, coords.x, coords.y, coords.z, true)

            if closestDistance == -1 or closestDistance > distance then
                closestPlayer = players[i]
                closestDistance = distance
            end
        end
    end

    if closestDistance ~= -1 and closestDistance <= 5.0 then
        return closestPlayer
    else
        return nil
    end
end

-- Exports
exports('IsAPD', function()
    return isAPD
end)

exports('GetAPDRank', function()
    return apdRank
end)

exports('IsOnDuty', function()
    return onDuty
end)

-- Jail System Events
RegisterNetEvent('olympus:apd:sendToJail')
AddEventHandler('olympus:apd:sendToJail', function(jailData)
    OlympusAPD.IsJailed = true
    OlympusAPD.JailData = jailData

    -- Teleport to jail
    SetEntityCoords(PlayerPedId(), jailData.location.x, jailData.location.y, jailData.location.z)

    -- Disable controls
    CreateThread(function()
        while OlympusAPD.IsJailed do
            DisableControlAction(0, 75, true) -- Disable exit vehicle
            DisableControlAction(0, 22, true) -- Disable jump
            DisableControlAction(0, 24, true) -- Disable attack
            DisableControlAction(0, 25, true) -- Disable aim
            DisableControlAction(0, 47, true) -- Disable weapon wheel
            DisableControlAction(0, 58, true) -- Disable weapon wheel
            DisableControlAction(0, 263, true) -- Disable melee attack 1
            DisableControlAction(0, 264, true) -- Disable melee attack 2
            DisableControlAction(0, 257, true) -- Disable melee attack light
            DisableControlAction(0, 140, true) -- Disable melee attack heavy
            DisableControlAction(0, 141, true) -- Disable melee attack alternate
            DisableControlAction(0, 142, true) -- Disable melee attack extra
            DisableControlAction(0, 143, true) -- Disable melee block
            Wait(0)
        end
    end)

    exports['olympus-ui']:ShowNotification('You have been sent to jail for ' .. jailData.jailTime .. ' minutes', 'error', 5000)
end)

RegisterNetEvent('olympus:apd:releaseFromJail')
AddEventHandler('olympus:apd:releaseFromJail', function()
    OlympusAPD.IsJailed = false
    OlympusAPD.JailData = nil

    exports['olympus-ui']:ShowNotification('You have been released from jail', 'success', 3000)
end)

-- Additional exports
exports('IsJailed', function() return OlympusAPD.IsJailed end)
exports('GetJailData', function() return OlympusAPD.JailData end)

print("^2[Olympus APD]^7 Client system loaded successfully!")
