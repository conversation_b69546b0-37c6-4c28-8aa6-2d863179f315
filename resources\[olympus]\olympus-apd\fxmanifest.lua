fx_version 'cerulean'
game 'gta5'

name 'Olympus APD System'
description 'Altis Police Department system for Olympus Framework'
author 'Olympus Development Team'
version '1.0.0'

dependencies {
    'olympus-core'
}

shared_scripts {
    'config/shared.lua'
}

server_scripts {
    'server/main.lua'
}

client_scripts {
    'client/main.lua'
}

-- No UI files (UI handled by olympus-ui)

exports {
    'IsPlayerAPD',
    'GetAPDRank',
    'CanUseEquipment',
    'CanUseVehicle',
    'ProcessSuspect',
    'IssueCitation',
    'SendDispatch'
}

server_exports {
    'GetOnlineAPD',
    'GetAPDByRank',
    'AddAPDMember',
    'RemoveAPDMember',
    'PromoteAPD',
    'DemoteAPD',
    'LogAPDAction'
}
