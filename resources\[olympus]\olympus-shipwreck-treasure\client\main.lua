-- Olympus Shipwreck & Treasure System - Client Main
-- Based on original fn_searchShipWreck.sqf

local OlympusShipwreck = {}
local isSearching = false
local searchingZone = nil
local shipwreckBlips = {}
local processingBlips = {}

-- Initialize system
CreateThread(function()
    while not OlympusCore do
        Wait(100)
    end
    
    print("[Olympus Shipwreck] Client initialized")
    
    -- Create shipwreck zone blips
    OlympusShipwreck.CreateShipwreckBlips()
    
    -- Create processing location blips
    OlympusShipwreck.CreateProcessingBlips()
    
    -- Start interaction thread
    CreateThread(OlympusShipwreck.InteractionThread)
    
    -- Start zone monitoring thread
    CreateThread(OlympusShipwreck.ZoneMonitoringThread)
end)

-- Utility Functions
function OlympusShipwreck.Notify(message, type)
    local success = pcall(function()
        exports['olympus-core']:Notify(message, type)
    end)
    
    if not success then
        -- Fallback notification
        SetNotificationTextEntry("STRING")
        AddTextComponentString(message)
        DrawNotification(false, false)
    end
end

function OlympusShipwreck.PlayAnimation(dict, anim, duration)
    RequestAnimDict(dict)
    while not HasAnimDictLoaded(dict) do
        Wait(10)
    end
    
    local playerPed = PlayerPedId()
    TaskPlayAnim(playerPed, dict, anim, 8.0, -8.0, duration or -1, 1, 0, false, false, false)
end

function OlympusShipwreck.ShowProgressBar(text, duration, onComplete)
    local success = pcall(function()
        exports['progressbar']:Progress({
            name = "treasure_hunt",
            duration = duration,
            label = text,
            useWhileDead = false,
            canCancel = true,
            controlDisables = {
                disableMovement = false,
                disableCarMovement = false,
                disableMouse = false,
                disableCombat = true,
            }
        }, function(cancelled)
            if not cancelled and onComplete then
                onComplete()
            elseif cancelled then
                TriggerServerEvent('olympus-shipwreck:server:stopTreasureHunt', 'Search cancelled')
                isSearching = false
                searchingZone = nil
            end
        end)
    end)
    
    if not success then
        -- Fallback progress system
        local startTime = GetGameTimer()
        CreateThread(function()
            while GetGameTimer() - startTime < duration do
                Wait(100)
                if not isSearching then
                    return
                end
            end
            if onComplete then
                onComplete()
            end
        end)
    end
end

function OlympusShipwreck.GetPlayerZone(zoneType)
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    
    if zoneType == "shipwreck" then
        for _, zone in pairs(Config.Shipwreck.zones) do
            local distance = #(coords - zone.coords)
            if distance <= zone.radius then
                return zone
            end
        end
    elseif zoneType == "processing" then
        for _, location in pairs(Config.Shipwreck.processingLocations) do
            local distance = #(coords - location.coords)
            if distance <= 5.0 then
                return location
            end
        end
    end
    
    return nil
end

function OlympusShipwreck.GetPlayerDepth()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    return coords.z
end

function OlympusShipwreck.IsUnderwater()
    local playerPed = PlayerPedId()
    return IsPedSwimming(playerPed) and OlympusShipwreck.GetPlayerDepth() < -5.0
end

-- Blip Management
function OlympusShipwreck.CreateShipwreckBlips()
    for i, zone in pairs(Config.Shipwreck.zones) do
        local blip = AddBlipForCoord(zone.coords.x, zone.coords.y, zone.coords.z)
        SetBlipSprite(blip, zone.blip.sprite)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, zone.blip.scale)
        SetBlipColour(blip, zone.blip.color)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(zone.name)
        EndTextCommandSetBlipName(blip)
        
        shipwreckBlips[i] = blip
    end
end

function OlympusShipwreck.CreateProcessingBlips()
    for i, location in pairs(Config.Shipwreck.processingLocations) do
        local blip = AddBlipForCoord(location.coords.x, location.coords.y, location.coords.z)
        SetBlipSprite(blip, location.blip.sprite)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, location.blip.scale)
        SetBlipColour(blip, location.blip.color)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(location.name)
        EndTextCommandSetBlipName(blip)
        
        processingBlips[i] = blip
    end
end

-- Treasure Hunting System
function OlympusShipwreck.StartTreasureHunt()
    if isSearching then
        OlympusShipwreck.Notify("You're already searching for treasure!", "error")
        return
    end
    
    -- Trigger server-side treasure hunt start
    TriggerServerEvent('olympus-shipwreck:server:startTreasureHunt')
end

function OlympusShipwreck.StartTreasureHuntProcess(zoneName)
    isSearching = true
    searchingZone = zoneName
    
    OlympusShipwreck.Notify("Started treasure hunting in " .. zoneName, "primary")
    
    -- Start continuous searching loop (matching original while loop)
    CreateThread(function()
        while isSearching do
            -- Play search animation
            OlympusShipwreck.PlayAnimation(
                Config.Shipwreck.animations.searchAnimation.dict,
                Config.Shipwreck.animations.searchAnimation.anim,
                Config.Shipwreck.settings.searchDuration
            )
            
            -- Show progress bar
            OlympusShipwreck.ShowProgressBar("Searching for treasure...", Config.Shipwreck.settings.searchDuration, function()
                if isSearching then
                    -- Process treasure find on server
                    TriggerServerEvent('olympus-shipwreck:server:processTreasureFind')
                    
                    -- Wait before next search (matching original uiSleep 4)
                    Wait(Config.Shipwreck.settings.cooldownBetweenSearches)
                end
            end)
            
            -- Wait for search duration plus cooldown
            Wait(Config.Shipwreck.settings.searchDuration + Config.Shipwreck.settings.cooldownBetweenSearches)
        end
    end)
end

function OlympusShipwreck.StopTreasureHunt()
    isSearching = false
    searchingZone = nil
    ClearPedTasks(PlayerPedId())
end

-- Processing System
function OlympusShipwreck.OpenProcessingMenu(location)
    local menuItems = {}
    
    for _, loot in pairs(Config.Shipwreck.lootTable) do
        local sellPrice = math.floor(loot.sellPrice * location.sellMultiplier)
        table.insert(menuItems, {
            header = loot.name,
            txt = "Sell for $" .. sellPrice .. " each",
            params = {
                event = "olympus-shipwreck:client:sellItem",
                args = {
                    itemType = loot.item,
                    location = location.name,
                    sellPrice = sellPrice
                }
            }
        })
    end
    
    table.insert(menuItems, {
        header = "Close",
        txt = "",
        params = {
            event = "qb-menu:client:closeMenu"
        }
    })
    
    exports['qb-menu']:openMenu(menuItems)
end

-- Zone Monitoring System
function OlympusShipwreck.ZoneMonitoringThread()
    local lastZone = nil
    
    while true do
        Wait(1000) -- Check every second
        
        local currentZone = OlympusShipwreck.GetPlayerZone("shipwreck")
        local zoneName = currentZone and currentZone.name or nil
        
        -- Notify zone changes
        if zoneName ~= lastZone then
            if zoneName and Config.Shipwreck.notifications.showZoneNotifications then
                OlympusShipwreck.Notify("Entered " .. zoneName, "primary")
            elseif lastZone and Config.Shipwreck.notifications.showZoneNotifications then
                OlympusShipwreck.Notify("Left shipwreck zone", "primary")
            end
            lastZone = zoneName
        end
        
        -- Check depth warnings
        if OlympusShipwreck.IsUnderwater() and Config.Shipwreck.notifications.showDepthWarnings then
            local depth = OlympusShipwreck.GetPlayerDepth()
            if depth > Config.Shipwreck.settings.minDepth and depth < -5.0 then
                -- Player is underwater but not deep enough
                if math.random(1, 10) == 1 then -- Show occasionally to avoid spam
                    OlympusShipwreck.Notify("You need to dive deeper to find treasure", "warning")
                end
            end
        end
    end
end

-- Interaction System
function OlympusShipwreck.InteractionThread()
    while true do
        Wait(0)
        
        local playerPed = PlayerPedId()
        local coords = GetEntityCoords(playerPed)
        
        -- Check for shipwreck zone interaction
        local shipwreckZone = OlympusShipwreck.GetPlayerZone("shipwreck")
        if shipwreckZone and OlympusShipwreck.IsUnderwater() then
            local depth = OlympusShipwreck.GetPlayerDepth()
            if depth <= Config.Shipwreck.settings.minDepth then
                -- Show interaction prompt
                DrawText3D(coords.x, coords.y, coords.z + 1.0, "[E] Search for Treasure")
                
                if IsControlJustReleased(0, 38) then -- E key
                    OlympusShipwreck.StartTreasureHunt()
                end
            end
        end
        
        -- Check for processing location interaction
        local processingLocation = OlympusShipwreck.GetPlayerZone("processing")
        if processingLocation then
            DrawText3D(processingLocation.coords.x, processingLocation.coords.y, processingLocation.coords.z + 1.0, 
                "[E] Sell Treasure")
            
            if IsControlJustReleased(0, 38) then -- E key
                OlympusShipwreck.OpenProcessingMenu(processingLocation)
            end
        else
            Wait(500) -- Reduce frequency when not near anything
        end
    end
end

function DrawText3D(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())
    
    if onScreen then
        SetTextScale(0.35, 0.35)
        SetTextFont(4)
        SetTextProportional(1)
        SetTextColour(255, 255, 255, 215)
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x, _y)
        
        local factor = (string.len(text)) / 370
        DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 41, 11, 41, 68)
    end
end

-- Event Handlers
RegisterNetEvent('olympus-shipwreck:client:notify', function(message, type)
    OlympusShipwreck.Notify(message, type)
end)

RegisterNetEvent('olympus-shipwreck:client:startTreasureHunt', function(zoneName)
    OlympusShipwreck.StartTreasureHuntProcess(zoneName)
end)

RegisterNetEvent('olympus-shipwreck:client:stopTreasureHunt', function()
    OlympusShipwreck.StopTreasureHunt()
end)

RegisterNetEvent('olympus-shipwreck:client:receivePlayerStats', function(stats)
    local message = string.format(
        "Treasure Hunting Stats:\nTotal Searches: %d\nItems Found: %d\nTotal Value: $%d\nBest Find: %s\nDeepest Search: %.1fm",
        stats.total_searches,
        stats.total_items_found,
        stats.total_value_found,
        stats.best_find or "None",
        math.abs(stats.deepest_search or 0)
    )

    OlympusShipwreck.Notify(message, "primary")
end)

RegisterNetEvent('olympus-shipwreck:client:sellItem', function(data)
    local input = exports['qb-input']:ShowInput({
        header = "Sell " .. data.itemType,
        submitText = "Sell",
        inputs = {
            {
                text = "Quantity",
                name = "quantity",
                type = "number",
                isRequired = true,
                default = 1
            }
        }
    })

    if input and input.quantity then
        local quantity = tonumber(input.quantity)
        if quantity and quantity > 0 then
            TriggerServerEvent('olympus-shipwreck:server:sellTreasure', data.itemType, quantity, data.location)
        end
    end
end)

-- Commands
RegisterCommand('treasurestats', function()
    TriggerServerEvent('olympus-shipwreck:server:requestPlayerStats')
end, false)

RegisterCommand('treasure', function()
    local shipwreckZone = OlympusShipwreck.GetPlayerZone("shipwreck")
    if shipwreckZone and OlympusShipwreck.IsUnderwater() then
        local depth = OlympusShipwreck.GetPlayerDepth()
        if depth <= Config.Shipwreck.settings.minDepth then
            OlympusShipwreck.StartTreasureHunt()
        else
            OlympusShipwreck.Notify("You need to dive deeper!", "error")
        end
    else
        OlympusShipwreck.Notify("You need to be underwater in a shipwreck zone!", "error")
    end
end, false)

-- Export Functions
exports('IsPlayerSearching', function()
    return isSearching
end)

exports('GetTreasureStatus', function()
    return {
        isSearching = isSearching,
        zone = searchingZone
    }
end)

exports('StartTreasureHunt', function()
    OlympusShipwreck.StartTreasureHunt()
    return true
end)

exports('StopTreasureHunt', function()
    if isSearching then
        TriggerServerEvent('olympus-shipwreck:server:stopTreasureHunt', 'Stopped by external call')
        OlympusShipwreck.StopTreasureHunt()
        return true
    end
    return false
end)

exports('IsInShipwreckZone', function()
    return OlympusShipwreck.GetPlayerZone("shipwreck") ~= nil
end)

exports('IsInProcessingZone', function()
    return OlympusShipwreck.GetPlayerZone("processing") ~= nil
end)

exports('GetCurrentDepth', function()
    return OlympusShipwreck.GetPlayerDepth()
end)

exports('IsUnderwater', function()
    return OlympusShipwreck.IsUnderwater()
end)

print("[Olympus Shipwreck] Client module loaded")
