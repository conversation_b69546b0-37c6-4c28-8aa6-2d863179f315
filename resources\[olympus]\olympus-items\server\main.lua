-- Olympus Items - Main Server Script
-- Based on original Olympus Altis Life special items system

local OlympusItems = {}
local federalCooldowns = {}
local vaultStates = {}
local itemCooldowns = {}

-- Initialize
CreateThread(function()
    while not exports['olympus-core'] do
        Wait(100)
    end
    
    -- Initialize vault states
    InitializeVaultStates()
    
    -- Start cleanup threads
    CreateThread(CooldownCleanup)
    CreateThread(VaultStateCleanup)
end)

-- Initialize vault states
function InitializeVaultStates()
    -- Load vault states from database or set defaults
    vaultStates = {
        bank = {blown = false, lastBlown = 0, cooldown = 1800}, -- 30 minutes
        fed = {blown = false, lastBlown = 0, cooldown = 3600},  -- 1 hour
        blackwater = {blown = false, lastBlown = 0, cooldown = 7200}, -- 2 hours
        jail = {blown = false, lastBlown = 0, cooldown = 1800} -- 30 minutes
    }
end

-- Get player data
function GetPlayerData(src)
    if exports['olympus-core'] then
        return exports['olympus-core']:GetPlayerData(src)
    end
    return nil
end

-- Check if player has item
function HasItem(src, item, amount)
    local playerData = GetPlayerData(src)
    if not playerData or not playerData.inventory then return false end
    
    local itemCount = 0
    for _, invItem in pairs(playerData.inventory) do
        if invItem.name == item then
            itemCount = itemCount + (invItem.amount or 1)
        end
    end
    
    return itemCount >= (amount or 1)
end

-- Remove item from player
function RemoveItem(src, item, amount)
    if exports['olympus-core'] then
        return exports['olympus-core']:RemoveItem(src, item, amount or 1)
    end
    return false
end

-- Add item to player
function AddItem(src, item, amount)
    if exports['olympus-core'] then
        return exports['olympus-core']:AddItem(src, item, amount or 1)
    end
    return false
end

-- Get online cop count
function GetOnlineCopCount()
    local copCount = 0
    local players = exports['olympus-core']:GetPlayers()
    
    for _, player in pairs(players) do
        if player.faction == 'police' and player.onDuty then
            copCount = copCount + 1
        end
    end
    
    return copCount
end

-- Check federal cooldown
function IsOnFederalCooldown(facilityType)
    if not federalCooldowns[facilityType] then return false end
    return os.time() < federalCooldowns[facilityType]
end

-- Set federal cooldown
function SetFederalCooldown(facilityType, duration)
    federalCooldowns[facilityType] = os.time() + duration
end

-- Check vault state
function IsVaultBlown(vaultType)
    if not vaultStates[vaultType] then return false end
    
    local vault = vaultStates[vaultType]
    if not vault.blown then return false end
    
    -- Check if cooldown has expired
    if os.time() >= (vault.lastBlown + vault.cooldown) then
        vault.blown = false
        return false
    end
    
    return true
end

-- Set vault blown
function SetVaultBlown(vaultType)
    if vaultStates[vaultType] then
        vaultStates[vaultType].blown = true
        vaultStates[vaultType].lastBlown = os.time()
    end
end

-- Cooldown cleanup thread
function CooldownCleanup()
    while true do
        local currentTime = os.time()
        
        -- Clean federal cooldowns
        for facility, expireTime in pairs(federalCooldowns) do
            if currentTime >= expireTime then
                federalCooldowns[facility] = nil
            end
        end
        
        -- Clean item cooldowns
        for playerId, playerCooldowns in pairs(itemCooldowns) do
            for item, expireTime in pairs(playerCooldowns) do
                if currentTime >= expireTime then
                    itemCooldowns[playerId][item] = nil
                end
            end
            
            -- Remove empty player cooldown tables
            if next(itemCooldowns[playerId]) == nil then
                itemCooldowns[playerId] = nil
            end
        end
        
        Wait(30000) -- Check every 30 seconds
    end
end

-- Vault state cleanup thread
function VaultStateCleanup()
    while true do
        local currentTime = os.time()
        
        for vaultType, vault in pairs(vaultStates) do
            if vault.blown and currentTime >= (vault.lastBlown + vault.cooldown) then
                vault.blown = false
                print(string.format("Vault %s is now available again", vaultType))
            end
        end
        
        Wait(60000) -- Check every minute
    end
end

-- Send notification to player
function SendNotification(src, message, type)
    TriggerClientEvent('olympus-items:client:notification', src, message, type)
end

-- Send notification to all players
function SendNotificationToAll(message, type)
    TriggerClientEvent('olympus-items:client:notification', -1, message, type)
end

-- Alert police/dispatch
function AlertPolice(data)
    local players = exports['olympus-core']:GetPlayers()
    
    for src, player in pairs(players) do
        if player.faction == 'police' and player.onDuty then
            TriggerClientEvent('olympus-dispatch:client:addCall', src, {
                type = data.type or 'federal_breach',
                location = data.location,
                description = data.description or 'Federal facility breach in progress',
                priority = data.priority or 'high',
                units = {'police'}
            })
        end
    end
end

-- Log criminal activity
function LogCriminalActivity(src, activity, data)
    local playerData = GetPlayerData(src)
    if not playerData then return end
    
    -- Add to player's criminal record
    if exports['olympus-core'] then
        exports['olympus-core']:AddCriminalRecord(src, {
            type = activity,
            data = data,
            timestamp = os.time()
        })
    end
    
    -- Log to database
    exports.oxmysql:execute('INSERT INTO criminal_logs (player_id, activity, data, timestamp) VALUES (?, ?, ?, ?)', {
        playerData.citizenid,
        activity,
        json.encode(data),
        os.time()
    })
end

-- Export functions
exports('HasItem', HasItem)
exports('RemoveItem', RemoveItem)
exports('AddItem', AddItem)
exports('GetOnlineCopCount', GetOnlineCopCount)
exports('IsOnFederalCooldown', IsOnFederalCooldown)
exports('SetFederalCooldown', SetFederalCooldown)
exports('IsVaultBlown', IsVaultBlown)
exports('SetVaultBlown', SetVaultBlown)
exports('SendNotification', SendNotification)
exports('AlertPolice', AlertPolice)
exports('LogCriminalActivity', LogCriminalActivity)

-- Event handlers
RegisterNetEvent('olympus-items:server:useItem', function(item, target)
    local src = source
    
    if not HasItem(src, item) then
        SendNotification(src, "You don't have this item!", "error")
        return
    end
    
    -- Trigger client-side item use
    TriggerClientEvent('olympus-items:client:useItem', src, item, target)
end)

RegisterNetEvent('olympus-items:server:consumeItem', function(item, success)
    local src = source
    
    if not HasItem(src, item) then
        return
    end
    
    local itemConfig = Config.Items[item]
    if not itemConfig then return end
    
    -- Handle durability
    if itemConfig.durability then
        -- Reduce durability instead of removing item
        -- This would require a more complex inventory system
        -- For now, we'll just remove the item after durability uses
        RemoveItem(src, item, 1)
    else
        -- Remove item immediately
        RemoveItem(src, item, 1)
    end
    
    -- Log item usage
    LogCriminalActivity(src, 'item_usage', {
        item = item,
        success = success,
        timestamp = os.time()
    })
end)

RegisterNetEvent('olympus-items:server:checkCopCount', function(required)
    local src = source
    local copCount = GetOnlineCopCount()
    
    TriggerClientEvent('olympus-items:client:copCountResult', src, copCount >= required, copCount)
end)

RegisterNetEvent('olympus-items:server:alertFederalBreach', function(data)
    local src = source
    
    -- Alert all police
    AlertPolice({
        type = 'federal_breach',
        location = data.location,
        description = string.format('Federal facility breach in progress - %s', data.action or 'unknown activity'),
        priority = 'high'
    })
    
    -- Set federal cooldown
    if data.vaultType then
        SetFederalCooldown(data.vaultType, 300) -- 5 minute cooldown
    end
    
    -- Log the breach
    LogCriminalActivity(src, 'federal_breach', data)
end)

RegisterNetEvent('olympus-items:server:alertHouseBreach', function(data)
    local src = source

    -- Alert police
    AlertPolice({
        type = 'house_breach',
        location = data.location,
        description = 'House break-in in progress',
        priority = 'medium'
    })

    -- Log the breach
    LogCriminalActivity(src, 'house_breach', data)
end)

-- Lockpick events
RegisterNetEvent('olympus-items:server:lockpickSuccess', function(data)
    local src = source

    -- Log successful lockpick
    LogCriminalActivity(src, 'vehicle_theft', {
        vehicle = data.vehicle,
        plate = data.plate,
        method = 'lockpick',
        success = true
    })

    -- Alert police
    AlertPolice({
        type = 'vehicle_theft',
        location = data.location or GetEntityCoords(GetPlayerPed(src)),
        description = string.format('Vehicle theft in progress - Plate: %s', data.plate),
        priority = 'medium'
    })
end)

RegisterNetEvent('olympus-items:server:lockpickFailed', function(data)
    local src = source

    -- Log failed lockpick
    LogCriminalActivity(src, 'attempted_vehicle_theft', {
        vehicle = data.vehicle,
        plate = data.plate,
        method = 'lockpick',
        success = false
    })

    -- Alert police with higher priority for failed attempts
    AlertPolice({
        type = 'attempted_vehicle_theft',
        location = data.location,
        description = string.format('Attempted vehicle theft - Plate: %s', data.plate),
        priority = 'high'
    })
end)

-- Boltcutter events
RegisterNetEvent('olympus-items:server:unlockDoor', function(data)
    local src = source

    -- Log door breach
    LogCriminalActivity(src, 'breaking_and_entering', {
        building = data.building,
        doorIndex = data.doorIndex,
        method = 'boltcutter'
    })

    -- Alert police
    AlertPolice({
        type = 'breaking_and_entering',
        location = data.location,
        description = 'Breaking and entering in progress',
        priority = 'high'
    })
end)

-- Blasting charge events
RegisterNetEvent('olympus-items:server:plantBlastingCharge', function(data)
    local src = source

    -- Set vault as being breached
    SetVaultBlown(data.vaultType)

    -- Log explosive planting
    LogCriminalActivity(src, 'explosive_planting', {
        vault = data.vault,
        vaultType = data.vaultType,
        location = data.location
    })

    -- Alert all players about explosion
    SendNotificationToAll(string.format("BREAKING NEWS: Explosion reported at %s facility!", data.vaultType:upper()), "warning")
end)

RegisterNetEvent('olympus-items:server:vaultDetonated', function(data)
    local src = source

    -- Trigger vault explosion for all clients
    TriggerClientEvent('olympus-items:client:vaultExplosion', -1, data.location)

    -- Log successful vault breach
    LogCriminalActivity(src, 'vault_breach', {
        vault = data.vault,
        vaultType = data.vaultType,
        success = true
    })

    -- Start federal response
    if data.vaultType == 'fed' or data.vaultType == 'blackwater' then
        TriggerEvent('olympus-federal:server:startResponse', data.vaultType, data.location)
    end
end)

-- Flashbang events
RegisterNetEvent('olympus-items:server:flashbangDetonated', function(position)
    local src = source

    -- Apply flashbang effects to nearby players
    TriggerClientEvent('olympus-items:client:flashbangEffect', -1, position)

    -- Log flashbang use
    LogCriminalActivity(src, 'flashbang_use', {
        location = position
    })
end)

RegisterNetEvent('olympus-items:server:applyFlashbangEffect', function(targetId, strength)
    local src = source

    -- Apply flashbang effect to specific player
    TriggerClientEvent('olympus-items:client:applyFlashbangEffect', targetId, strength)
end)

-- Medical item events
RegisterNetEvent('olympus-items:server:playerRevived', function(targetId)
    local src = source

    -- Log medical assistance
    LogCriminalActivity(src, 'medical_assistance', {
        target = targetId,
        item = 'epipen'
    })

    -- Notify target player
    SendNotification(targetId, "You have been revived with an EpiPen!", "success")
end)

RegisterNetEvent('olympus-items:server:playerExecuted', function(targetId)
    local src = source

    -- Log execution
    LogCriminalActivity(src, 'execution', {
        target = targetId,
        method = 'lethal_injection'
    })

    -- Alert police
    AlertPolice({
        type = 'murder',
        location = GetEntityCoords(GetPlayerPed(src)),
        description = 'Murder reported - lethal injection',
        priority = 'critical'
    })

    -- Notify target player
    SendNotification(targetId, "You have been executed.", "error")
end)

-- Tool events
RegisterNetEvent('olympus-items:server:breakDownDoor', function(data)
    local src = source

    -- Log door destruction
    LogCriminalActivity(src, 'property_damage', {
        door = data.door,
        method = 'fire_axe',
        location = data.location
    })

    -- Alert police
    AlertPolice({
        type = 'property_damage',
        location = data.location,
        description = 'Property damage reported - door destroyed',
        priority = 'medium'
    })
end)

RegisterNetEvent('olympus-items:server:reduceDurability', function(item)
    local src = source

    -- Handle item durability reduction
    local playerData = GetPlayerData(src)
    if not playerData then return end

    -- This would typically update item durability in inventory
    -- For now, we'll just log the usage
    LogCriminalActivity(src, 'tool_usage', {
        item = item,
        durability_reduced = true
    })
end)

RegisterNetEvent('olympus-items:server:slimJimSuccess', function(data)
    local src = source

    -- Log successful slim jim use
    LogCriminalActivity(src, 'vehicle_unlock', {
        vehicle = data.vehicle,
        plate = data.plate,
        method = 'slim_jim',
        success = true
    })
end)

RegisterNetEvent('olympus-items:server:slimJimFailed', function(data)
    local src = source

    -- Log failed slim jim use
    LogCriminalActivity(src, 'attempted_vehicle_unlock', {
        vehicle = data.vehicle,
        method = 'slim_jim',
        success = false
    })

    -- Alert police
    AlertPolice({
        type = 'suspicious_activity',
        location = data.location,
        description = 'Suspicious activity around vehicle',
        priority = 'low'
    })
end)

RegisterNetEvent('olympus-items:server:spikeStripHit', function(data)
    local src = source

    -- Log spike strip hit
    LogCriminalActivity(src, 'spike_strip_deployment', {
        vehicle = data.vehicle,
        spikeStrip = data.spikeStrip,
        result = 'vehicle_disabled'
    })

    -- Alert police
    AlertPolice({
        type = 'spike_strip_hit',
        location = GetEntityCoords(GetPlayerPed(src)),
        description = 'Vehicle disabled by spike strip',
        priority = 'medium'
    })
end)

RegisterNetEvent('olympus-items:server:giveVehicleKeys', function(vehicleNetId)
    local src = source

    -- Give player temporary vehicle access
    if exports['olympus-vehicles'] then
        exports['olympus-vehicles']:GiveTempVehicleAccess(src, vehicleNetId)
    end

    -- Log vehicle deployment
    LogCriminalActivity(src, 'vehicle_deployment', {
        vehicle = vehicleNetId,
        type = 'pocket_gokart'
    })
end)
