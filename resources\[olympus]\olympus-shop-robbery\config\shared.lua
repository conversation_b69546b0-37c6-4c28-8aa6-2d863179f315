Config = {}

-- Shop Robbery System Configuration
Config.ShopRobbery = {
    -- Robbery settings
    settings = {
        cooldownTime = 900, -- 15 minutes between robberies (same shop)
        robberyDuration = 250, -- 4 minutes and 10 seconds (250 * 0.1 = 25 seconds * 10 = 250 seconds)
        maxDistance = 10.0, -- Max distance from shop to maintain robbery
        requiredWeapons = {
            "weapon_pistol",
            "weapon_combatpistol", 
            "weapon_appistol",
            "weapon_pistol50",
            "weapon_snspistol",
            "weapon_heavypistol",
            "weapon_vintagepistol",
            "weapon_flaregun",
            "weapon_marksmanpistol",
            "weapon_revolver",
            "weapon_microsmg",
            "weapon_smg",
            "weapon_assaultsmg",
            "weapon_combatpdw",
            "weapon_machinepistol",
            "weapon_minismg",
            "weapon_assaultrifle",
            "weapon_carbinerifle",
            "weapon_advancedrifle",
            "weapon_specialcarbine",
            "weapon_bullpuprifle",
            "weapon_compactrifle",
            "weapon_mg",
            "weapon_combatmg",
            "weapon_gusenberg",
            "weapon_pumpshotgun",
            "weapon_sawnoffshotgun",
            "weapon_assaultshotgun",
            "weapon_bullpupshotgun",
            "weapon_musket",
            "weapon_heavyshotgun",
            "weapon_dbshotgun",
            "weapon_autoshotgun",
            "weapon_sniperrifle",
            "weapon_heavysniper",
            "weapon_marksmanrifle",
            "weapon_rpg",
            "weapon_grenadelauncher",
            "weapon_grenadelauncher_smoke",
            "weapon_minigun",
            "weapon_firework",
            "weapon_railgun",
            "weapon_hominglauncher",
            "weapon_compactlauncher",
            "weapon_rayminigun"
        },
        fakeWeapons = {
            "weapon_stungun",
            "weapon_flashlight",
            "weapon_knife",
            "weapon_nightstick",
            "weapon_hammer",
            "weapon_bat",
            "weapon_crowbar",
            "weapon_golfclub",
            "weapon_bottle",
            "weapon_dagger",
            "weapon_hatchet",
            "weapon_knuckle",
            "weapon_machete",
            "weapon_flashlight",
            "weapon_switchblade"
        },
        atmCooldown = 180 -- 3 minutes ATM cooldown after robbery
    },
    
    -- Payout calculation based on cops online
    payouts = {
        basePayout = 35000, -- Base payout with low cop count
        maxPayout = 85000, -- Max payout with high cop count
        copMultiplier = 10000, -- Additional money per cop online
        minCopsForMax = 6 -- Minimum cops online for max payout
    },
    
    -- Shop locations (gas stations and convenience stores)
    shops = {
        -- Los Santos Gas Stations
        {
            name = "LTD Gasoline (Mirror Park)",
            coords = vector3(1163.4, -323.8, 69.2),
            heading = 100.0,
            blip = {sprite = 52, color = 2, scale = 0.8}
        },
        {
            name = "Rob's Liquor (Great Ocean Highway)",
            coords = vector3(-1487.5, -379.1, 40.2),
            heading = 133.0,
            blip = {sprite = 52, color = 2, scale = 0.8}
        },
        {
            name = "24/7 Supermarket (Innocence Blvd)",
            coords = vector3(28.2, -1339.2, 29.5),
            heading = 270.0,
            blip = {sprite = 52, color = 2, scale = 0.8}
        },
        {
            name = "24/7 Supermarket (Clinton Ave)",
            coords = vector3(373.5, 325.6, 103.6),
            heading = 255.0,
            blip = {sprite = 52, color = 2, scale = 0.8}
        },
        {
            name = "LTD Gasoline (Grapeseed)",
            coords = vector3(1698.3, 4924.4, 42.1),
            heading = 325.0,
            blip = {sprite = 52, color = 2, scale = 0.8}
        },
        {
            name = "Rob's Liquor (El Rancho Blvd)",
            coords = vector3(1135.8, -982.3, 46.4),
            heading = 277.0,
            blip = {sprite = 52, color = 2, scale = 0.8}
        },
        {
            name = "24/7 Supermarket (Barbareno Rd)",
            coords = vector3(-3038.9, 585.9, 7.9),
            heading = 17.0,
            blip = {sprite = 52, color = 2, scale = 0.8}
        },
        {
            name = "24/7 Supermarket (Ineseno Road)",
            coords = vector3(-3241.9, 1001.5, 12.8),
            heading = 357.0,
            blip = {sprite = 52, color = 2, scale = 0.8}
        },
        {
            name = "Rob's Liquor (Great Ocean Highway)",
            coords = vector3(-2967.8, 390.9, 15.0),
            heading = 87.0,
            blip = {sprite = 52, color = 2, scale = 0.8}
        },
        {
            name = "24/7 Supermarket (Senora Fwy)",
            coords = vector3(2557.5, 382.0, 108.6),
            heading = 357.0,
            blip = {sprite = 52, color = 2, scale = 0.8}
        },
        {
            name = "24/7 Supermarket (Route 68)",
            coords = vector3(2678.9, 3280.7, 55.2),
            heading = 330.0,
            blip = {sprite = 52, color = 2, scale = 0.8}
        },
        {
            name = "LTD Gasoline (Banham Canyon Dr)",
            coords = vector3(-1820.5, 792.5, 138.1),
            heading = 135.0,
            blip = {sprite = 52, color = 2, scale = 0.8}
        },
        {
            name = "Rob's Liquor (Prosperity St)",
            coords = vector3(-1222.9, -906.9, 12.3),
            heading = 35.0,
            blip = {sprite = 52, color = 2, scale = 0.8}
        },
        {
            name = "24/7 Supermarket (Davis Ave)",
            coords = vector3(-47.5, -1757.5, 29.4),
            heading = 50.0,
            blip = {sprite = 52, color = 2, scale = 0.8}
        },
        {
            name = "LTD Gasoline (Grove St)",
            coords = vector3(-707.5, -914.3, 19.2),
            heading = 90.0,
            blip = {sprite = 52, color = 2, scale = 0.8}
        }
    },
    
    -- Robbery notifications
    notifications = {
        policeDispatch = true, -- Send dispatch to police
        showRobberyMarker = true, -- Show map marker during robbery
        markerDuration = 300, -- 5 minutes marker duration
        markerColor = "ColorOrange",
        markerType = "mil_warning"
    },
    
    -- Restrictions
    restrictions = {
        factionRestrictions = {
            police = true, -- Police cannot rob shops
            ambulance = false -- R&R can rob shops (if they want to)
        },
        vehicleRestriction = true, -- Must exit vehicle to rob
        restraintRestriction = true, -- Cannot rob while restrained
        aliveRestriction = true -- Must be alive to rob
    }
}

-- Debug settings
Config.Debug = false
