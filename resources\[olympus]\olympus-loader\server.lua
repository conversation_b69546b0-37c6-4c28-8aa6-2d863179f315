-- Olympus Framework Loader - Server
-- Ensures proper initialization order and validates framework components

local loadOrder = {
    'olympus-core',
    'olympus-ui',
    'olympus-apd',
    'olympus-medical',
    'olympus-gangs',
    'olympus-economy',
    'olympus-vehicles',
    'olympus-events'
}

local loadedResources = {}
local frameworkReady = false

-- Send loading progress to clients
function SendLoadingProgress(step, progress, message)
    TriggerClientEvent('olympus:client:loadingProgress', -1, step, progress, message)
end

-- Check if all required resources are loaded
CreateThread(function()
    Wait(2000) -- Initial wait

    print("^3[Olympus Loader]^7 Starting framework initialization...")
    SendLoadingProgress(1, 10, 'Initializing Core Systems...')

    Wait(1000)

    print("^3[Olympus Loader]^7 Checking framework components...")

    local allLoaded = true
    local stepProgress = 20
    local progressIncrement = 70 / #loadOrder -- Distribute 70% across resources

    for i, resource in ipairs(loadOrder) do
        if GetResourceState(resource) == 'started' then
            loadedResources[resource] = true
            print("^2[Olympus Loader]^7 ✓ " .. resource .. " loaded successfully")

            -- Send progress update
            stepProgress = stepProgress + progressIncrement
            local stepName = resource:gsub('olympus%-', ''):gsub('^%l', string.upper) .. ' System'
            SendLoadingProgress(i + 1, math.floor(stepProgress), 'Loading ' .. stepName .. '...')

            Wait(500) -- Small delay between checks
        else
            allLoaded = false
            print("^1[Olympus Loader]^7 ✗ " .. resource .. " failed to load")
        end
    end
    
    if allLoaded then
        frameworkReady = true
        print("^2[Olympus Loader]^7 All framework components loaded successfully!")
        print("^2[Olympus Loader]^7 Olympus Altis Life Framework is ready!")

        -- Send final loading progress
        SendLoadingProgress(9, 95, 'Finalizing Framework...')
        Wait(1000)

        -- Trigger framework ready event
        TriggerEvent('olympus:framework:ready')

        -- Send framework ready to all players
        SetTimeout(1000, function()
            TriggerClientEvent('olympus:client:frameworkReady', -1)
        end)
    else
        print("^1[Olympus Loader]^7 Framework initialization failed! Some components are missing.")
        SendLoadingProgress(1, 0, 'Framework initialization failed!')
    end
end)

-- Framework ready event
RegisterServerEvent('olympus:framework:ready')
AddEventHandler('olympus:framework:ready', function()
    print("^2[Olympus Loader]^7 Framework ready event triggered")
    
    -- Initialize any cross-resource dependencies here
    TriggerEvent('olympus:core:frameworkReady')
    TriggerEvent('olympus:apd:frameworkReady')
    TriggerEvent('olympus:medical:frameworkReady')
    TriggerEvent('olympus:gangs:frameworkReady')
    TriggerEvent('olympus:economy:frameworkReady')
    TriggerEvent('olympus:vehicles:frameworkReady')
    TriggerEvent('olympus:events:frameworkReady')
end)

-- Export to check if framework is ready
exports('IsFrameworkReady', function()
    return frameworkReady
end)

-- Export to get loaded resources
exports('GetLoadedResources', function()
    return loadedResources
end)

-- Command to check framework status
RegisterCommand('olympus:status', function(source, args, rawCommand)
    if source == 0 then -- Console only
        print("^3[Olympus Loader]^7 Framework Status:")
        print("^3[Olympus Loader]^7 Ready: " .. (frameworkReady and "Yes" or "No"))
        print("^3[Olympus Loader]^7 Loaded Resources:")
        
        for resource, loaded in pairs(loadedResources) do
            local status = loaded and "^2✓^7" or "^1✗^7"
            print("^3[Olympus Loader]^7 " .. status .. " " .. resource)
        end
    end
end, false)

-- Monitor resource states
CreateThread(function()
    while true do
        Wait(30000) -- Check every 30 seconds
        
        for _, resource in ipairs(loadOrder) do
            local state = GetResourceState(resource)
            if state ~= 'started' and loadedResources[resource] then
                print("^1[Olympus Loader]^7 WARNING: " .. resource .. " has stopped working!")
                loadedResources[resource] = false
                frameworkReady = false
            elseif state == 'started' and not loadedResources[resource] then
                print("^2[Olympus Loader]^7 " .. resource .. " has been restarted")
                loadedResources[resource] = true
            end
        end
    end
end)
