-- Olympus Gang System - Complete Cartel Implementation
-- Based on exact Olympus Altis Life cartel mechanics and wiki documentation

Config = {}

-- Warzone Configuration (Exact Olympus Implementation)
Config.Warzone = {
    enabled = true,
    noNLR = true, -- No New Life Rule in warzone
    noRDM = true, -- No Random Death Match rule in warzone
    noEngagement = true, -- No engagement required to kill

    -- Warzone boundaries (approximate from wiki map)
    boundaries = {
        center = vector3(2200.0, 1800.0, 35.0),
        radius = 1500 -- 1.5km radius warzone
    }
}

-- Passive Income Calculation (Exact Olympus Formula)
-- Formula: (Current Players / 100) * 30,000 every 10 minutes
Config.PassiveIncome = {
    enabled = true,
    payoutInterval = 600, -- 10 minutes in seconds
    baseAmount = 30000, -- Base $30k
    playerDivisor = 100, -- Divide player count by 100

    -- Example: 50 players = (50/100) * 30000 = $15,000 per 10 minutes = $90,000/hour
}

-- Cartel Definitions
Config.Cartels = {
    -- OG Arms Cartel (Always Fixed Location - Never Rotates)
    arms = {
        name = 'Arms Cartel',
        displayName = 'OG Arms',
        location = vector3(2447.9, 1576.9, 33.0), -- Exact Olympus coordinates
        captureRadius = 95.0, -- Exact 95 meter radius from wiki
        type = 'arms',
        fixed = true, -- Never rotates location

        -- Exact Olympus Rewards
        rewards = {
            passiveIncome = true, -- Gets passive income based on server population
            rebelDiscount = 0.15, -- 15% discount at rebel outpost
            taxIncome = 0.15, -- 15% tax on all rebel purchases goes to cartel owner
            magazineRefill = {
                enabled = true,
                tier1 = {cost = 5000, cooldown = 600}, -- $5k, 10 min cooldown
                tier2 = {cost = 3000, cooldown = 180}, -- $3k, 3 min cooldown
                excludedMags = {'.50_bw', 'promet_shotgun'} -- Cannot refill these
            }
        },

        -- Exact Olympus Capture Requirements
        requirements = {
            gang = true, -- Must be in a gang to capture
            weapon = '5.56', -- Must have 5.56 caliber or greater weapon
            onFoot = true, -- Cannot capture from inside vehicle
            minDistance = 95 -- Must be within 95 meters of red triangle marker
        },

        -- Exact Olympus Capture Mechanics
        capture = {
            baseTime = 300, -- 5 minutes base capture time
            speedBonus = {
                [2] = 0.375, -- 37.5% faster with 2 gang members
                [3] = 0.375 -- Additional 37.5% with 3 gang members (max bonus)
            },
            contestRatio = 3, -- 3:1 ratio for advantage capture
            advantageSpeed = 0.5, -- 50% speed when advantaged (3:1 ratio)

            -- Progress bar states (exact Olympus text)
            states = {
                uncapping = 'uncapping', -- Reducing enemy gang percentage
                capturing = 'capturing', -- Increasing own gang percentage
                contested = 'contested' -- Multiple gangs present
            }
        },

        -- Vehicle spawning at cartel (exact Olympus feature)
        vehicles = {
            personalGarage = true, -- Can spawn personal vehicles from flagpole
            gangGarage = true -- Can spawn gang vehicles from flagpole
        },

        -- APD notification system
        apdNotification = {
            enabled = true, -- Can notify APD by scroll wheeling flag
            message = 'Gang is capturing the Arms Cartel'
        }
    },

    -- Church Cartel (Rotating Location)
    church = {
        name = 'Drug Cartel',
        displayName = 'Church',
        location = vector3(1850.0, 2100.0, 35.0), -- Church location
        captureRadius = 95.0,
        type = 'drugs',
        fixed = false, -- Can rotate with Alpha Point

        -- Drug taxation rewards
        rewards = {
            passiveIncome = true,
            drugTax = {
                cocaine = 0.15, -- 15% tax on cocaine sales
                meth = 0.15, -- 15% tax on meth sales
                weed = 0.15 -- 15% tax on weed sales
            },
            magazineRefill = {
                enabled = true,
                tier1 = {cost = 5000, cooldown = 600},
                tier2 = {cost = 3000, cooldown = 180},
                excludedMags = {'.50_bw', 'promet_shotgun'}
            }
        },

        requirements = {
            gang = true,
            weapon = '5.56',
            onFoot = true,
            minDistance = 95
        },

        capture = {
            baseTime = 300,
            speedBonus = {
                [2] = 0.375,
                [3] = 0.375
            },
            contestRatio = 3,
            advantageSpeed = 0.5,
            states = {
                uncapping = 'uncapping',
                capturing = 'capturing',
                contested = 'contested'
            }
        },

        vehicles = {
            personalGarage = true,
            gangGarage = true
        },

        apdNotification = {
            enabled = true,
            message = 'Gang is capturing the Church Cartel'
        },

        -- Indestructible walls (exact Olympus feature)
        indestructibleWalls = true
    },

    -- Alpha Point Cartel (Rotating Location)
    alpha = {
        name = 'Drug Cartel',
        displayName = 'Alpha Point',
        location = vector3(2300.0, 1600.0, 35.0), -- Alpha Point location
        captureRadius = 95.0,
        type = 'drugs',
        fixed = false, -- Can rotate with Church

        -- Drug taxation rewards (different drugs than Church)
        rewards = {
            passiveIncome = true,
            drugTax = {
                moonshine = 0.15, -- 15% tax on moonshine sales
                mushroom = 0.15, -- 15% tax on mushroom sales
                heroin = 0.15 -- 15% tax on heroin sales
            },
            magazineRefill = {
                enabled = true,
                tier1 = {cost = 5000, cooldown = 600},
                tier2 = {cost = 3000, cooldown = 180},
                excludedMags = {'.50_bw', 'promet_shotgun'}
            }
        },

        requirements = {
            gang = true,
            weapon = '5.56',
            onFoot = true,
            minDistance = 95
        },

        capture = {
            baseTime = 300,
            speedBonus = {
                [2] = 0.375,
                [3] = 0.375
            },
            contestRatio = 3,
            advantageSpeed = 0.5,
            states = {
                uncapping = 'uncapping',
                capturing = 'capturing',
                contested = 'contested'
            }
        },

        vehicles = {
            personalGarage = true,
            gangGarage = true
        },

        apdNotification = {
            enabled = true,
            message = 'Gang is capturing the Alpha Point Cartel'
        }
    }
}

-- Cartel Rotation System (Exact Olympus Implementation)
Config.CartelRotation = {
    enabled = true,
    
    -- Rotation pairs (exact Olympus wiki info)
    rotationPairs = {
        {
            cartels = {'church', 'alpha'},
            drugs = {
                'cocaine', 'heroin', 'weed', 'meth'
            },
            locations = {
                church = vector3(1958.5, 5179.8, 47.9),
                alpha = vector3(4991.2, -4920.8, 3.4)
            }
        }
    },

    -- Rotation schedule
    schedule = {
        interval = 604800, -- 1 week (7 days)
        time = '18:00', -- 6 PM EST
        day = 'sunday', -- Every Sunday
        
        -- Notification system
        notifications = {
            advance = {
                enabled = true,
                times = {86400, 3600, 1800} -- 24h, 1h, 30min before
            },
            rotation = {
                enabled = true,
                message = 'Cartel locations have rotated!'
            }
        }
    },

    -- Rotation mechanics
    mechanics = {
        instantSwap = true, -- Locations swap instantly
        preserveOwnership = false, -- Ownership resets on rotation
        clearCaptureProgress = true, -- Reset any ongoing captures
        
        -- Server restart integration
        serverRestart = {
            checkOnRestart = true, -- Check if rotation needed on restart
            forceRotation = false -- Don't force rotation on restart
        }
    },

    -- Live tracking integration
    tracking = {
        enabled = true,
        updateOnRotation = true, -- Update tracking immediately
        logRotations = true -- Log all rotations
    }
}
