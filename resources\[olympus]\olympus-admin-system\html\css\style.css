/* Olympus Admin Panel Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: transparent;
    color: #ffffff;
}

.hidden {
    display: none !important;
}

.admin-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 900px;
    height: 600px;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    border: 2px solid #0f3460;
    overflow: hidden;
}

.panel-header {
    background: linear-gradient(90deg, #0f3460 0%, #533483 100%);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #0f3460;
}

.panel-header h2 {
    color: #ffffff;
    font-size: 20px;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 24px;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.panel-content {
    height: calc(100% - 60px);
    display: flex;
    flex-direction: column;
}

.tab-buttons {
    display: flex;
    background: #16213e;
    border-bottom: 2px solid #0f3460;
}

.tab-btn {
    flex: 1;
    padding: 12px 20px;
    background: none;
    border: none;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 14px;
    font-weight: 500;
}

.tab-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.tab-btn.active {
    background: linear-gradient(90deg, #0f3460 0%, #533483 100%);
    border-bottom: 3px solid #e94560;
}

.tab-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.tab-content:not(.active) {
    display: none;
}

/* Players Tab */
.player-list {
    margin-bottom: 20px;
}

.player-search input {
    width: 100%;
    padding: 10px;
    background: #16213e;
    border: 2px solid #0f3460;
    border-radius: 8px;
    color: #ffffff;
    font-size: 14px;
    margin-bottom: 15px;
}

.player-search input::placeholder {
    color: #888;
}

#players-container {
    max-height: 200px;
    overflow-y: auto;
    background: #16213e;
    border-radius: 8px;
    border: 2px solid #0f3460;
}

.player-item {
    padding: 10px 15px;
    border-bottom: 1px solid #0f3460;
    cursor: pointer;
    transition: background-color 0.3s;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.player-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.player-item.selected {
    background: linear-gradient(90deg, #0f3460 0%, #533483 100%);
}

.player-info {
    display: flex;
    flex-direction: column;
}

.player-name {
    font-weight: 600;
    color: #ffffff;
}

.player-id {
    font-size: 12px;
    color: #888;
}

.player-actions h3 {
    margin-bottom: 15px;
    color: #ffffff;
    font-size: 18px;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.action-btn {
    padding: 10px 15px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s;
    background: linear-gradient(90deg, #0f3460 0%, #533483 100%);
    color: #ffffff;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.action-btn.danger {
    background: linear-gradient(90deg, #e94560 0%, #f27121 100%);
}

.action-btn.warning {
    background: linear-gradient(90deg, #f27121 0%, #f9ca24 100%);
}

/* Server Tab */
.server-info {
    margin-bottom: 30px;
}

.server-info h3 {
    margin-bottom: 15px;
    color: #ffffff;
    font-size: 18px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.info-item {
    background: #16213e;
    padding: 15px;
    border-radius: 8px;
    border: 2px solid #0f3460;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-item label {
    font-weight: 600;
    color: #ffffff;
}

.info-item span {
    color: #e94560;
    font-weight: 600;
}

/* Logs Tab */
.logs-container h3 {
    margin-bottom: 15px;
    color: #ffffff;
    font-size: 18px;
}

.log-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.log-filters select,
.log-filters input {
    padding: 8px 12px;
    background: #16213e;
    border: 2px solid #0f3460;
    border-radius: 8px;
    color: #ffffff;
    font-size: 14px;
}

#logs-list {
    background: #16213e;
    border-radius: 8px;
    border: 2px solid #0f3460;
    max-height: 300px;
    overflow-y: auto;
}

.log-item {
    padding: 10px 15px;
    border-bottom: 1px solid #0f3460;
    font-size: 13px;
}

.log-item:last-child {
    border-bottom: none;
}

.log-timestamp {
    color: #888;
    font-size: 11px;
}

.log-action {
    color: #e94560;
    font-weight: 600;
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    padding: 30px;
    border-radius: 15px;
    border: 2px solid #0f3460;
    min-width: 400px;
}

.modal-content h3 {
    margin-bottom: 20px;
    color: #ffffff;
    text-align: center;
}

.modal-content input,
.modal-content select {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    background: #16213e;
    border: 2px solid #0f3460;
    border-radius: 8px;
    color: #ffffff;
    font-size: 14px;
}

.modal-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s;
    background: linear-gradient(90deg, #0f3460 0%, #533483 100%);
    color: #ffffff;
}

.btn.danger {
    background: linear-gradient(90deg, #e94560 0%, #f27121 100%);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #16213e;
}

::-webkit-scrollbar-thumb {
    background: #0f3460;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #533483;
}
