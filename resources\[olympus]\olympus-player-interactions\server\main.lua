-- ========================================
-- OLYMPUS PLAYER INTERACTIONS SERVER
-- Based on Original Olympus Functions
-- ========================================

local OlympusPlayerInteractions = {}

-- ========================================
-- DATABASE INITIALIZATION
-- ========================================
function OlympusPlayerInteractions.InitDatabase()
    -- Create player interaction logs table
    exports.oxmysql:execute([[
        CREATE TABLE IF NOT EXISTS `player_interaction_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `source_player_id` varchar(50) NOT NULL,
            `source_player_name` varchar(100) NOT NULL,
            `target_player_id` varchar(50) NOT NULL,
            `target_player_name` varchar(100) NOT NULL,
            `interaction_type` enum('rob','escort','restrain','search') NOT NULL,
            `amount` int(11) DEFAULT 0,
            `success` tinyint(1) NOT NULL DEFAULT 1,
            `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
            PRIMARY KEY (`id`),
            KEY `source_player_id` (`source_player_id`),
            KEY `target_player_id` (`target_player_id`),
            KEY `interaction_type` (`interaction_type`),
            KEY `timestamp` (`timestamp`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ]])
end

-- ========================================
-- UTILITY FUNCTIONS
-- ========================================
function OlympusPlayerInteractions.GetPlayerData(src)
    local success, result = pcall(function()
        return exports['olympus-core']:GetPlayerData(src)
    end)
    return success and result or nil
end

function OlympusPlayerInteractions.GetPlayerMoney(src, account)
    local playerData = OlympusPlayerInteractions.GetPlayerData(src)
    if not playerData then return 0 end
    
    if account == 'cash' then
        return playerData.money or 0
    elseif account == 'bank' then
        return playerData.bank or 0
    end
    return 0
end

function OlympusPlayerInteractions.AddPlayerMoney(src, account, amount)
    local success, result = pcall(function()
        return exports['olympus-core']:AddMoney(src, account, amount)
    end)
    return success and result
end

function OlympusPlayerInteractions.RemovePlayerMoney(src, account, amount)
    local success, result = pcall(function()
        return exports['olympus-core']:RemoveMoney(src, account, amount)
    end)
    return success and result
end

function OlympusPlayerInteractions.HasItem(src, item)
    local success, result = pcall(function()
        return exports['olympus-core']:HasItem(src, item)
    end)
    return success and result or false
end

function OlympusPlayerInteractions.RemoveItem(src, item, amount)
    local success, result = pcall(function()
        return exports['olympus-core']:RemoveItem(src, item, amount or 1)
    end)
    return success and result
end

function OlympusPlayerInteractions.GetPlayerFaction(src)
    local playerData = OlympusPlayerInteractions.GetPlayerData(src)
    if not playerData then return 'civilian' end
    return playerData.faction or 'civilian'
end

function OlympusPlayerInteractions.LogInteraction(sourceId, sourceName, targetId, targetName, interactionType, amount, success)
    exports.oxmysql:execute('INSERT INTO player_interaction_logs (source_player_id, source_player_name, target_player_id, target_player_name, interaction_type, amount, success) VALUES (?, ?, ?, ?, ?, ?, ?)', {
        sourceId, sourceName, targetId, targetName, interactionType, amount or 0, success and 1 or 0
    })
end

-- ========================================
-- PLAYER ROBBING SYSTEM
-- Based on fn_robAction.sqf
-- ========================================
function OlympusPlayerInteractions.RobPlayer(src, targetId)
    local sourcePlayer = OlympusPlayerInteractions.GetPlayerData(src)
    local targetPlayer = OlympusPlayerInteractions.GetPlayerData(targetId)
    
    if not sourcePlayer or not targetPlayer then
        TriggerClientEvent('olympus-player-interactions:notify', src, Config.Notifications.robbing.failed, 'error')
        return false
    end
    
    -- Check if target was recently robbed
    if Player(targetId).state.robbed then
        TriggerClientEvent('olympus-player-interactions:notify', src, Config.Notifications.robbing.alreadyRobbed, 'error')
        return false
    end
    
    -- Check if source has weapon (if required)
    if Config.PlayerInteractions.robbing.requireWeapon then
        local hasWeapon = false
        for _, weapon in ipairs(Config.PlayerInteractions.robbing.allowedWeapons) do
            if HasPedGotWeapon(GetPlayerPed(src), weapon, false) then
                hasWeapon = true
                break
            end
        end
        
        if not hasWeapon then
            TriggerClientEvent('olympus-player-interactions:notify', src, Config.Notifications.robbing.noWeapon, 'error')
            return false
        end
    end
    
    -- Get target's cash
    local targetCash = OlympusPlayerInteractions.GetPlayerMoney(targetId, 'cash')
    if targetCash < Config.PlayerInteractions.robbing.minCash then
        TriggerClientEvent('olympus-player-interactions:notify', src, Config.Notifications.robbing.noMoney, 'error')
        return false
    end
    
    -- Calculate robbery amount
    local maxRobbable = math.min(targetCash * Config.PlayerInteractions.robbing.robPercentage, Config.PlayerInteractions.robbing.maxCash)
    local robbedAmount = math.random(Config.PlayerInteractions.robbing.minCash, math.floor(maxRobbable))
    
    -- Execute robbery
    if OlympusPlayerInteractions.RemovePlayerMoney(targetId, 'cash', robbedAmount) then
        OlympusPlayerInteractions.AddPlayerMoney(src, 'cash', robbedAmount)
        
        -- Set robbed cooldown
        Player(targetId).state:set('robbed', true, true)
        SetTimeout(Config.PlayerInteractions.robbing.cooldown, function()
            Player(targetId).state:set('robbed', false, true)
        end)
        
        -- Notifications
        TriggerClientEvent('olympus-player-interactions:notify', src, string.format(Config.Notifications.robbing.success, robbedAmount, GetPlayerName(targetId)), 'success')
        TriggerClientEvent('olympus-player-interactions:notify', targetId, string.format(Config.Notifications.robbing.beingRobbed, GetPlayerName(src)), 'error')
        
        -- Log interaction
        OlympusPlayerInteractions.LogInteraction(
            GetPlayerIdentifier(src, 0), GetPlayerName(src),
            GetPlayerIdentifier(targetId, 0), GetPlayerName(targetId),
            'rob', robbedAmount, true
        )
        
        return true
    end
    
    return false
end

-- ========================================
-- PLAYER ESCORTING SYSTEM
-- Based on fn_escortAction.sqf
-- ========================================
function OlympusPlayerInteractions.EscortPlayer(src, targetId)
    local sourceFaction = OlympusPlayerInteractions.GetPlayerFaction(src)

    -- Check if source can escort (APD/R&R only)
    if not table.contains(Config.PlayerInteractions.escorting.allowedFactions, sourceFaction) then
        TriggerClientEvent('olympus-player-interactions:notify', src, "You don't have permission to escort players", 'error')
        return false
    end

    -- Check if source is already escorting someone
    if Player(src).state.escorting then
        TriggerClientEvent('olympus-player-interactions:notify', src, Config.Notifications.escorting.alreadyEscorting, 'error')
        return false
    end

    -- Check if target is already being escorted
    if Player(targetId).state.beingEscorted then
        TriggerClientEvent('olympus-player-interactions:notify', src, Config.Notifications.escorting.beingEscorted, 'error')
        return false
    end

    -- Check if target is restrained (if required)
    if Config.PlayerInteractions.escorting.requireRestrained and not Player(targetId).state.restrained then
        TriggerClientEvent('olympus-player-interactions:notify', src, Config.Notifications.escorting.notRestrained, 'error')
        return false
    end

    -- Set escort states
    Player(src).state:set('escorting', targetId, true)
    Player(targetId).state:set('beingEscorted', src, true)

    -- Notify clients to start escort
    TriggerClientEvent('olympus-player-interactions:startEscort', src, targetId)
    TriggerClientEvent('olympus-player-interactions:beingEscorted', targetId, src)

    -- Log interaction
    OlympusPlayerInteractions.LogInteraction(
        GetPlayerIdentifier(src, 0), GetPlayerName(src),
        GetPlayerIdentifier(targetId, 0), GetPlayerName(targetId),
        'escort', 0, true
    )

    return true
end

function OlympusPlayerInteractions.StopEscorting(src)
    local targetId = Player(src).state.escorting

    if not targetId then
        return false
    end

    -- Clear escort states
    Player(src).state:set('escorting', false, true)
    Player(targetId).state:set('beingEscorted', false, true)

    -- Notify clients to stop escort
    TriggerClientEvent('olympus-player-interactions:stopEscort', src)
    TriggerClientEvent('olympus-player-interactions:stopBeingEscorted', targetId)

    return true
end

-- ========================================
-- PLAYER RESTRAINING SYSTEM
-- Based on APD restraining mechanics
-- ========================================
function OlympusPlayerInteractions.RestrainPlayer(src, targetId)
    local sourceFaction = OlympusPlayerInteractions.GetPlayerFaction(src)

    -- Check if source can restrain (APD only)
    if not table.contains(Config.PlayerInteractions.restraining.allowedFactions, sourceFaction) then
        TriggerClientEvent('olympus-player-interactions:notify', src, "You don't have permission to restrain players", 'error')
        return false
    end

    -- Check if source has zip ties (if required)
    if Config.PlayerInteractions.restraining.requireItem then
        if not OlympusPlayerInteractions.HasItem(src, Config.PlayerInteractions.restraining.zipTieItem) then
            TriggerClientEvent('olympus-player-interactions:notify', src, Config.Notifications.restraining.noItem, 'error')
            return false
        end
    end

    -- Check if target is already restrained
    if Player(targetId).state.restrained then
        TriggerClientEvent('olympus-player-interactions:notify', src, Config.Notifications.restraining.alreadyRestrained, 'error')
        return false
    end

    -- Check if target is surrendered (if required)
    if Config.PlayerInteractions.restraining.requireSurrender and not Player(targetId).state.surrendered then
        TriggerClientEvent('olympus-player-interactions:notify', src, "Player must surrender first", 'error')
        return false
    end

    -- Remove zip tie item
    if Config.PlayerInteractions.restraining.requireItem then
        OlympusPlayerInteractions.RemoveItem(src, Config.PlayerInteractions.restraining.zipTieItem, 1)
    end

    -- Set restrained state
    Player(targetId).state:set('restrained', true, true)

    -- Notify clients
    TriggerClientEvent('olympus-player-interactions:restrained', targetId, src)
    TriggerClientEvent('olympus-player-interactions:notify', src, Config.Notifications.restraining.success, 'success')
    TriggerClientEvent('olympus-player-interactions:notify', targetId, Config.Notifications.restraining.beingRestrained, 'info')

    -- Log interaction
    OlympusPlayerInteractions.LogInteraction(
        GetPlayerIdentifier(src, 0), GetPlayerName(src),
        GetPlayerIdentifier(targetId, 0), GetPlayerName(targetId),
        'restrain', 0, true
    )

    return true
end

function OlympusPlayerInteractions.UnrestrainPlayer(src, targetId)
    local sourceFaction = OlympusPlayerInteractions.GetPlayerFaction(src)

    -- Check if source can unrestrain (APD only)
    if not table.contains(Config.PlayerInteractions.restraining.allowedFactions, sourceFaction) then
        TriggerClientEvent('olympus-player-interactions:notify', src, "You don't have permission to unrestrain players", 'error')
        return false
    end

    -- Check if target is restrained
    if not Player(targetId).state.restrained then
        TriggerClientEvent('olympus-player-interactions:notify', src, "Player is not restrained", 'error')
        return false
    end

    -- Clear restrained state
    Player(targetId).state:set('restrained', false, true)

    -- Stop escorting if being escorted
    local escortingPlayer = Player(targetId).state.beingEscorted
    if escortingPlayer then
        OlympusPlayerInteractions.StopEscorting(escortingPlayer)
    end

    -- Notify clients
    TriggerClientEvent('olympus-player-interactions:unrestrained', targetId)
    TriggerClientEvent('olympus-player-interactions:notify', src, "Player has been unrestrained", 'success')
    TriggerClientEvent('olympus-player-interactions:notify', targetId, "You have been unrestrained", 'info')

    return true
end

-- ========================================
-- UTILITY HELPER FUNCTIONS
-- ========================================
function table.contains(table, element)
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

-- ========================================
-- EVENT HANDLERS
-- ========================================
RegisterNetEvent('olympus-player-interactions:robPlayer', function(targetId)
    local src = source
    if not targetId or targetId == src then return end

    OlympusPlayerInteractions.RobPlayer(src, targetId)
end)

RegisterNetEvent('olympus-player-interactions:escortPlayer', function(targetId)
    local src = source
    if not targetId or targetId == src then return end

    OlympusPlayerInteractions.EscortPlayer(src, targetId)
end)

RegisterNetEvent('olympus-player-interactions:stopEscorting', function()
    local src = source
    OlympusPlayerInteractions.StopEscorting(src)
end)

RegisterNetEvent('olympus-player-interactions:restrainPlayer', function(targetId)
    local src = source
    if not targetId or targetId == src then return end

    OlympusPlayerInteractions.RestrainPlayer(src, targetId)
end)

RegisterNetEvent('olympus-player-interactions:unrestrainPlayer', function(targetId)
    local src = source
    if not targetId or targetId == src then return end

    OlympusPlayerInteractions.UnrestrainPlayer(src, targetId)
end)

RegisterNetEvent('olympus-player-interactions:toggleSurrender', function()
    local src = source
    local currentState = Player(src).state.surrendered or false
    Player(src).state:set('surrendered', not currentState, true)

    TriggerClientEvent('olympus-player-interactions:surrenderToggled', src, not currentState)
end)

-- ========================================
-- PLAYER DISCONNECT CLEANUP
-- ========================================
AddEventHandler('playerDropped', function(reason)
    local src = source

    -- Stop escorting if player was escorting someone
    local escortingTarget = Player(src).state.escorting
    if escortingTarget then
        Player(escortingTarget).state:set('beingEscorted', false, true)
        TriggerClientEvent('olympus-player-interactions:stopBeingEscorted', escortingTarget)
    end

    -- Stop being escorted if player was being escorted
    local escortedBy = Player(src).state.beingEscorted
    if escortedBy then
        Player(escortedBy).state:set('escorting', false, true)
        TriggerClientEvent('olympus-player-interactions:stopEscort', escortedBy)
    end
end)

-- ========================================
-- EXPORT FUNCTIONS
-- ========================================
exports('RobPlayer', function(src, targetId)
    return OlympusPlayerInteractions.RobPlayer(src, targetId)
end)

exports('EscortPlayer', function(src, targetId)
    return OlympusPlayerInteractions.EscortPlayer(src, targetId)
end)

exports('StopEscorting', function(src)
    return OlympusPlayerInteractions.StopEscorting(src)
end)

exports('RestrainPlayer', function(src, targetId)
    return OlympusPlayerInteractions.RestrainPlayer(src, targetId)
end)

exports('UnrestrainPlayer', function(src, targetId)
    return OlympusPlayerInteractions.UnrestrainPlayer(src, targetId)
end)

exports('IsPlayerSurrendered', function(src)
    return Player(src).state.surrendered or false
end)

exports('IsPlayerEscorted', function(src)
    return Player(src).state.beingEscorted ~= nil
end)

exports('IsPlayerBeingRobbed', function(src)
    return Player(src).state.robbed or false
end)

exports('CanInteractWithPlayer', function(src, targetId)
    local sourcePos = GetEntityCoords(GetPlayerPed(src))
    local targetPos = GetEntityCoords(GetPlayerPed(targetId))
    local distance = #(sourcePos - targetPos)

    return distance <= Config.PlayerInteractions.robbing.maxDistance
end)

-- ========================================
-- INITIALIZATION
-- ========================================
CreateThread(function()
    OlympusPlayerInteractions.InitDatabase()
    print("^2[Olympus Player Interactions]^7 Server system initialized")
end)
