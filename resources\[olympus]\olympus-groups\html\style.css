/* Olympus Groups UI Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: transparent;
    color: #ffffff;
    overflow: hidden;
}

.menu-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.menu-panel {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border: 2px solid #0f3460;
    border-radius: 10px;
    width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.menu-header {
    background: linear-gradient(90deg, #0f3460, #16213e);
    padding: 15px 20px;
    border-bottom: 1px solid #0f3460;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.menu-header h2 {
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s;
}

.close-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.menu-content {
    padding: 20px;
    max-height: calc(80vh - 80px);
    overflow-y: auto;
}

/* Group List Styles */
.group-list-container h3 {
    margin-bottom: 15px;
    color: #ffffff;
    font-size: 16px;
}

.group-list {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 20px;
    border: 1px solid #0f3460;
    border-radius: 5px;
}

.group-item {
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid #0f3460;
    padding: 15px;
    cursor: pointer;
    transition: background-color 0.3s;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.group-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.group-item:last-child {
    border-bottom: none;
}

.group-info-left {
    flex: 1;
}

.group-name {
    font-weight: 600;
    font-size: 14px;
    color: #ffffff;
    margin-bottom: 5px;
}

.group-details {
    font-size: 12px;
    color: #cccccc;
}

.group-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
}

.status-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-locked {
    background: #dc3545;
    color: #ffffff;
}

.status-unlocked {
    background: #28a745;
    color: #ffffff;
}

.member-count {
    font-size: 11px;
    color: #cccccc;
}

/* Group Actions */
.group-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

/* Group Management Styles */
.group-info {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
}

.group-details {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.group-details span {
    font-size: 14px;
    color: #ffffff;
}

.group-controls, .member-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Member List Styles */
.member-list-container h3 {
    margin-bottom: 15px;
    color: #ffffff;
    font-size: 16px;
}

.member-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #0f3460;
    border-radius: 5px;
}

.member-item {
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid #0f3460;
    padding: 12px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.member-item:last-child {
    border-bottom: none;
}

.member-info {
    flex: 1;
}

.member-name {
    font-weight: 600;
    font-size: 14px;
    color: #ffffff;
    margin-bottom: 3px;
}

.member-rank {
    font-size: 12px;
    color: #cccccc;
}

.leader-badge {
    background: #ffc107;
    color: #000000;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;
    margin-left: 10px;
}

.member-actions {
    display: flex;
    gap: 5px;
}

/* Button Styles */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    transition: all 0.3s;
    min-width: 80px;
}

.btn-primary {
    background: #007bff;
    color: #ffffff;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: #ffffff;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-success {
    background: #28a745;
    color: #ffffff;
}

.btn-success:hover {
    background: #1e7e34;
}

.btn-warning {
    background: #ffc107;
    color: #000000;
}

.btn-warning:hover {
    background: #e0a800;
}

.btn-danger {
    background: #dc3545;
    color: #ffffff;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-small {
    padding: 4px 8px;
    font-size: 10px;
    min-width: 60px;
}

/* Dialog Styles */
.dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1100;
}

.dialog-content {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border: 2px solid #0f3460;
    border-radius: 10px;
    width: 400px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.7);
}

.dialog-header {
    background: linear-gradient(90deg, #0f3460, #16213e);
    padding: 15px 20px;
    border-bottom: 1px solid #0f3460;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dialog-header h3 {
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
}

.dialog-body {
    padding: 20px;
}

.dialog-footer {
    padding: 15px 20px;
    border-top: 1px solid #0f3460;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Form Styles */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #0f3460;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    font-size: 14px;
}

.form-group input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-group input::placeholder {
    color: #cccccc;
}

.form-info {
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid #ffc107;
    border-radius: 5px;
    padding: 10px;
    margin-top: 15px;
}

.form-info p {
    color: #ffc107;
    font-size: 12px;
    margin: 0;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #0f3460;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #16213e;
}
