Config = {}

-- Vehicle Interaction Settings
Config.VehicleInteractions = {
    -- Vehicle Flipping Settings
    flipping = {
        enabled = true,
        maxDistance = 5.0,
        duration = 4000, -- 4 seconds
        requireKeys = true,
        allowedFactions = {'police', 'medical'}, -- APD and R&R can flip any vehicle
        excludedVehicles = {
            'strider' -- Striders cannot be flipped in water
        }
    },
    
    -- Vehicle Pushing Settings
    pushing = {
        enabled = true,
        maxDistance = 5.0,
        duration = 3000, -- 3 seconds
        pushForce = 10.0,
        requireKeys = false -- Anyone can push vehicles
    },
    
    -- Vehicle Searching Settings
    searching = {
        enabled = true,
        maxDistance = 10.0,
        duration = 3000, -- 3 seconds
        requireLicense = true, -- Driver's license required for civilians
        allowedFactions = {'police', 'medical'} -- APD and R&R can search without license
    },
    
    -- Vehicle Impounding Settings
    impounding = {
        enabled = true,
        maxDistance = 10.0,
        duration = 9000, -- 9 seconds (0.01 * 100 * 0.09 = 9 seconds)
        allowedFactions = {'police', 'medical'}, -- Only APD and R&R can impound
        rewards = {
            car = 1000,
            boat = 800,
            air = 1500
        },
        cityBonus = 5000, -- Bonus for impounding in cities
        stolenReward = 0.1 -- 10% of vehicle value for stolen vehicles
    },
    
    -- Vehicle Repair Settings
    repair = {
        enabled = true,
        maxDistance = 5.0,
        requireToolkit = false, -- Toolkit improves repair but not required
        toolkitItem = 'toolkit',
        medicalFree = true, -- R&R can repair without toolkit for free
        medicalCost = 5000, -- Cost if R&R doesn't have toolkit
        fullRepairVehicles = {
            'luxor', 'luxor2', 'shamal', 'miljet' -- Vehicles that get full repair
        }
    }
}

-- City Polygons for Impound Bonus
Config.CityPolygons = {
    kavala = {
        {3681.936, 13084.984},
        {3681.936, 13084.984},
        {3681.936, 13084.984},
        {3681.936, 13084.984}
    },
    athira = {
        {14239.983, 18943.947},
        {14239.983, 18943.947},
        {14239.983, 18943.947},
        {14239.983, 18943.947}
    },
    sofia = {
        {25927.441, 21351.465},
        {25927.441, 21351.465},
        {25927.441, 21351.465},
        {25927.441, 21351.465}
    },
    pyrgos = {
        {16929.688, 12722.656},
        {16929.688, 12722.656},
        {16929.688, 12722.656},
        {16929.688, 12722.656}
    }
}

-- Animation Settings
Config.Animations = {
    flipping = {
        dict = "amb@world_human_hammering@male@base",
        anim = "base",
        flag = 49
    },
    pushing = {
        dict = "missfinale_c2ig_11",
        anim = "pushcar_offcliff_f",
        flag = 49
    },
    searching = {
        dict = "anim@gangops@facility@servers@bodysearch@",
        anim = "player_search",
        flag = 49
    },
    impounding = {
        dict = "anim@heists@narcotics@funding@gang_idle",
        anim = "gang_chatting_idle01",
        flag = 49
    },
    repairing = {
        dict = "mini@repair",
        anim = "fixing_a_ped",
        flag = 49
    }
}

-- Notification Settings
Config.Notifications = {
    flipping = {
        start = "Flipping vehicle...",
        success = "Vehicle flipped successfully",
        noKeys = "You don't have keys to this vehicle",
        tooFar = "Vehicle is too far away",
        notCar = "This is not a car",
        occupied = "There is someone in the car",
        tooRekt = "This car is too damaged to flip",
        striderWater = "Cannot flip a Strider in water"
    },
    pushing = {
        start = "Pushing vehicle...",
        success = "Vehicle pushed",
        tooFar = "Vehicle is too far away"
    },
    searching = {
        start = "Searching vehicle...",
        noLicense = "You need a driver's license to find the registration",
        tooFar = "Vehicle is too far away",
        noInfo = "No information could be found for this vehicle",
        stolen = "STOLEN!"
    },
    impounding = {
        start = "Impounding vehicle...",
        success = "Vehicle impounded successfully",
        cancelled = "Impounding cancelled",
        tooFar = "Vehicle is too far away",
        tooRekt = "This vehicle is far too damaged and cannot be impounded",
        blackwater = "You can only seize Blackwater vehicles",
        occupied = "Cannot impound vehicle with occupants",
        noPermission = "You don't have permission to impound vehicles"
    },
    repair = {
        start = "Repairing vehicle...",
        success = "Vehicle repaired successfully",
        partial = "Vehicle partially repaired - contact R&R for full repair",
        tooRekt = "Vehicle is too damaged for field repair",
        inVehicle = "Cannot repair while in a vehicle",
        noToolkit = "You don't have a toolkit - repair may be limited",
        medicalCost = "You paid $5,000 to repair the vehicle without a toolkit"
    }
}
