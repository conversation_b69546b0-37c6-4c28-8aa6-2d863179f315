-- Olympus Items - Blindfold Client
-- Based on original Olympus blindfold mechanics

local isBlindfolded = false
local blindfoldTarget = nil

-- Use Blindfold function
function UseBlindfold(target)
    local ped = PlayerPedId()
    local targetPed = target or GetPlayerPedInFront()
    
    if not targetPed or targetPed == 0 then
        exports['olympus-items']:ShowNotification("No player found!", "error")
        return false
    end
    
    -- Check if target is a player
    local targetPlayerId = NetworkGetPlayerIndexFromPed(targetPed)
    if targetPlayerId == -1 then
        exports['olympus-items']:ShowNotification("Target is not a player!", "error")
        return false
    end
    
    -- Check distance
    local playerPos = GetEntityCoords(ped)
    local targetPos = GetEntityCoords(targetPed)
    local distance = #(playerPos - targetPos)
    
    if distance > 3.0 then
        exports['olympus-items']:ShowNotification("You are too far from the target!", "error")
        return false
    end
    
    -- Check if target is restrained
    if not IsPlayerRestrained(targetPlayerId) then
        exports['olympus-items']:ShowNotification("Target must be restrained first!", "error")
        return false
    end
    
    -- Apply blindfold
    return ApplyBlindfold(targetPlayerId)
end

-- Get player ped in front
function GetPlayerPedInFront()
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    local forward = GetEntityForwardVector(ped)
    local destination = coords + forward * 3.0
    
    local rayHandle = StartExpensiveSynchronousShapeTestLosProbe(
        coords.x, coords.y, coords.z,
        destination.x, destination.y, destination.z,
        12, ped, 0
    )
    
    local _, hit, _, _, entity = GetShapeTestResult(rayHandle)
    
    if hit and IsPedAPlayer(entity) then
        return entity
    end
    
    return nil
end

-- Check if player is restrained
function IsPlayerRestrained(playerId)
    -- This would integrate with the restraint system
    -- For now, return true as placeholder
    return true
end

-- Apply blindfold
function ApplyBlindfold(targetPlayerId)
    local targetServerId = GetPlayerServerId(targetPlayerId)
    
    -- Notify server
    TriggerServerEvent('olympus-items:server:applyBlindfold', targetServerId)
    
    -- Consume item
    TriggerServerEvent('olympus-items:server:consumeItem', 'blindfold', true)
    
    exports['olympus-items']:ShowNotification("Blindfold applied!", "success")
    
    return true
end

-- Apply blindfold effect to self
function ApplyBlindfolded()
    if isBlindfolded then return end
    
    isBlindfolded = true
    
    -- Create black screen overlay
    CreateThread(function()
        while isBlindfolded do
            -- Draw black rectangle over entire screen
            DrawRect(0.5, 0.5, 1.0, 1.0, 0, 0, 0, 255)
            
            -- Show blindfolded text
            SetTextFont(4)
            SetTextProportional(1)
            SetTextScale(0.5, 0.5)
            SetTextColour(255, 255, 255, 255)
            SetTextDropshadow(0, 0, 0, 0, 255)
            SetTextEdge(1, 0, 0, 0, 255)
            SetTextDropShadow()
            SetTextOutline()
            SetTextEntry("STRING")
            AddTextComponentString("You are blindfolded")
            DrawText(0.5, 0.5)
            
            Wait(0)
        end
    end)
    
    exports['olympus-items']:ShowNotification("You have been blindfolded!", "warning")
end

-- Remove blindfold effect
function RemoveBlindfolded()
    if not isBlindfolded then return end
    
    isBlindfolded = false
    exports['olympus-items']:ShowNotification("Blindfold removed!", "info")
end

-- Export functions
exports('UseBlindfold', UseBlindfold)
exports('ApplyBlindfolded', ApplyBlindfolded)
exports('RemoveBlindfolded', RemoveBlindfolded)

-- Event handlers
RegisterNetEvent('olympus-items:client:useBlindfold', function(target)
    UseBlindfold(target)
end)

RegisterNetEvent('olympus-items:client:blindfolded', function()
    ApplyBlindfolded()
end)

RegisterNetEvent('olympus-items:client:removeBlindfolded', function()
    RemoveBlindfolded()
end)
