-- ========================================
-- OLYMPUS LICENSES SYSTEM - SERVER MAIN
-- Complete recreation based on original Olympus license mechanics
-- Handles license purchasing, revocation, and management
-- ========================================

local OlympusLicenses = {}
OlympusLicenses.ActiveTests = {} -- Track active license tests

-- Configuration is loaded as shared script in fxmanifest.lua
-- local Config = require('config/shared')

-- Initialize license system
function InitializeLicenseSystem()
    print("^2[Olympus Licenses]^7 Initializing license system...")

    -- Initialize DMV locations
    InitializeDMVLocations()

    -- Initialize gun store locations
    InitializeGunStoreLocations()

    print("^2[Olympus Licenses]^7 License system initialized!")
end

-- Initialize DMV locations
function InitializeDMVLocations()
    for i, location in ipairs(Config.Licensing.dmvLocations) do
        print(string.format("^3[Olympus Licenses]^7 DMV Location %d initialized at %s", i, tostring(location)))
    end
end

-- Initialize gun store locations
function InitializeGunStoreLocations()
    for i, location in ipairs(Config.Licensing.gunStoreLocations) do
        print(string.format("^3[Olympus Licenses]^7 Gun Store %d initialized at %s", i, tostring(location)))
    end
end

-- Get license price (exact Olympus fn_licensePrice.sqf)
function GetLicensePrice(licenseType, playerData)
    local prices = {
        ['driver'] = 10000,
        ['boat'] = 5000,
        ['pilot'] = 50000,
        ['gun'] = 25000,
        ['wpl'] = 50000,
        ['dive'] = 3000,
        ['oil'] = 10000,
        ['heroin'] = 25000,
        ['marijuana'] = 17500,
        ['medmarijuana'] = 1500,
        ['gang'] = 0,
        ['rebel'] = 75000,
        ['truck'] = 20000,
        ['diamond'] = 35000,
        ['salt'] = 12000,
        ['cocaine'] = 30000,
        ['sand'] = 14500,
        ['iron'] = 9500,
        ['copper'] = 8000,
        ['cement'] = 6500,
        ['home'] = 100000,
        ['frog'] = 24000,
        ['crystalmeth'] = 55000,
        ['methu'] = 30000,
        ['moonshine'] = 54000,
        ['mashu'] = 29000,
        ['platinum'] = 10000,
        ['silver'] = 9000,
        ['mushroom'] = 35000,
        ['ccocaine'] = 40000,
        ['lumber'] = 15000
    }

    -- Special case for vigilante license (price depends on rebel license)
    if licenseType == 'vigilante' then
        if playerData.licenses and playerData.licenses['rebel'] then
            return 120000 -- Higher price if player has rebel license
        else
            return 60000 -- Normal price
        end
    end

    return prices[licenseType] or 0
end

-- Get license variable name (exact Olympus fn_licenseType.sqf)
function GetLicenseVariable(licenseType)
    local variables = {
        ['driver'] = 'license_civ_driver',
        ['boat'] = 'license_civ_boat',
        ['pilot'] = 'license_civ_air',
        ['gun'] = 'license_civ_gun',
        ['wpl'] = 'license_civ_wpl',
        ['dive'] = 'license_civ_dive',
        ['oil'] = 'license_civ_oil',
        ['heroin'] = 'license_civ_heroin',
        ['marijuana'] = 'license_civ_marijuana',
        ['medmarijuana'] = 'license_civ_medmarijuana',
        ['gang'] = 'license_civ_gang',
        ['rebel'] = 'license_civ_rebel',
        ['truck'] = 'license_civ_truck',
        ['diamond'] = 'license_civ_diamond',
        ['salt'] = 'license_civ_salt',
        ['cocaine'] = 'license_civ_coke',
        ['sand'] = 'license_civ_sand',
        ['iron'] = 'license_civ_iron',
        ['copper'] = 'license_civ_copper',
        ['cement'] = 'license_civ_cement',
        ['home'] = 'license_civ_home',
        ['frog'] = 'license_civ_frog',
        ['crystalmeth'] = 'license_civ_crystalmeth',
        ['methu'] = 'license_civ_methu',
        ['moonshine'] = 'license_civ_moonshine',
        ['mashu'] = 'license_civ_mashu',
        ['platinum'] = 'license_civ_platinum',
        ['silver'] = 'license_civ_silver',
        ['vigilante'] = 'license_civ_vigilante',
        ['mushroom'] = 'license_civ_mushroom'
    }

    return variables[licenseType]
end

-- Get license display name
function GetLicenseDisplayName(licenseType)
    local displayNames = {
        ['driver'] = 'Driver License',
        ['boat'] = 'Boating License',
        ['pilot'] = 'Pilot License',
        ['gun'] = 'Firearm License',
        ['wpl'] = 'Worker\'s Protection License',
        ['dive'] = 'Diving License',
        ['oil'] = 'Oil Processing License',
        ['heroin'] = 'Heroin Processing License',
        ['marijuana'] = 'Marijuana Processing License',
        ['medmarijuana'] = 'Medical Marijuana License',
        ['gang'] = 'Gang License',
        ['rebel'] = 'Rebel License',
        ['truck'] = 'Truck License',
        ['diamond'] = 'Diamond Processing License',
        ['salt'] = 'Salt Processing License',
        ['cocaine'] = 'Cocaine Processing License',
        ['sand'] = 'Sand Processing License',
        ['iron'] = 'Iron Processing License',
        ['copper'] = 'Copper Processing License',
        ['cement'] = 'Cement Processing License',
        ['home'] = 'Homeowner\'s License',
        ['frog'] = 'Frog Processing License',
        ['crystalmeth'] = 'Crystal Meth Processing License',
        ['methu'] = 'Meth Processing License',
        ['moonshine'] = 'Moonshine Processing License',
        ['mashu'] = 'Mash Processing License',
        ['platinum'] = 'Platinum Processing License',
        ['silver'] = 'Silver Processing License',
        ['vigilante'] = 'Vigilante License',
        ['mushroom'] = 'Mushroom Processing License'
    }

    return displayNames[licenseType] or 'Unknown License'
end

-- Purchase license (based on original fn_buyLicense.sqf)
RegisterServerEvent('olympus-licenses:server:purchaseLicense')
AddEventHandler('olympus-licenses:server:purchaseLicense', function(licenseType, location)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData then return end

    -- Get license info
    local price = GetLicensePrice(licenseType, playerData)
    local displayName = GetLicenseDisplayName(licenseType)
    local variable = GetLicenseVariable(licenseType)

    if not variable then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Invalid License',
            message = 'This license type is not available',
            duration = 5000
        })
        return
    end

    -- Check if player already has the license
    if playerData.licenses and playerData.licenses[variable] then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'License Owned',
            message = 'You already own this license',
            duration = 5000
        })
        return
    end

    -- Check if player has enough money
    local totalMoney = (playerData.money or 0) + (playerData.bank or 0)
    if totalMoney < price then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Insufficient Funds',
            message = string.format('You need $%s to purchase this license', price),
            duration = 5000
        })
        return
    end

    -- Special license requirements and validations
    if licenseType == 'vigilante' then
        -- Check playtime requirement (2 hours)
        local playtime = playerData.playtime_civ or 0
        if playtime < 120 then -- 120 minutes = 2 hours
            TriggerClientEvent('olympus:client:notify', source, {
                type = 'error',
                title = 'Insufficient Playtime',
                message = 'You must have 2 hours on the server to buy a vigilante license',
                duration = 5000
            })
            return
        end

        -- Revoke rebel and WPL licenses if owned
        if playerData.licenses then
            if playerData.licenses['license_civ_rebel'] then
                playerData.licenses['license_civ_rebel'] = false
            end
            if playerData.licenses['license_civ_wpl'] then
                playerData.licenses['license_civ_wpl'] = false
            end
        end

        -- Set vigilante status
        exports['olympus-core']:SetPlayerVariable(source, 'isVigi', true)

        -- Initialize vigilante arrests if not set
        if not playerData.vigilanteArrests then
            exports['olympus-core']:SetPlayerVariable(source, 'vigilanteArrests', 0)
        end
    end

    if licenseType == 'rebel' then
        -- Revoke vigilante and WPL licenses if owned
        if playerData.licenses then
            if playerData.licenses['license_civ_vigilante'] then
                playerData.licenses['license_civ_vigilante'] = false
                exports['olympus-core']:SetPlayerVariable(source, 'isVigi', false)
            end
            if playerData.licenses['license_civ_wpl'] then
                playerData.licenses['license_civ_wpl'] = false
            end
        end
    end

    if licenseType == 'wpl' then
        -- Revoke vigilante and rebel licenses if owned
        if playerData.licenses then
            if playerData.licenses['license_civ_vigilante'] then
                playerData.licenses['license_civ_vigilante'] = false
                exports['olympus-core']:SetPlayerVariable(source, 'isVigi', false)
            end
            if playerData.licenses['license_civ_rebel'] then
                playerData.licenses['license_civ_rebel'] = false
            end
        end
    end

    -- Deduct money (prefer cash first, then bank)
    if playerData.money >= price then
        exports['olympus-core']:RemovePlayerMoney(source, price)
    else
        local cashAmount = playerData.money or 0
        local bankAmount = price - cashAmount

        if cashAmount > 0 then
            exports['olympus-core']:RemovePlayerMoney(source, cashAmount)
        end
        exports['olympus-core']:RemovePlayerBank(source, bankAmount)
    end

    -- Grant the license
    if not playerData.licenses then
        playerData.licenses = {}
    end
    playerData.licenses[variable] = true
    exports['olympus-core']:UpdatePlayerData(source, 'licenses', playerData.licenses)

    -- Log license purchase
    exports['olympus-core']:LogAction(source, 'license_purchase', {
        license = licenseType,
        displayName = displayName,
        price = price,
        location = location
    })

    -- Notify player
    TriggerClientEvent('olympus:client:notify', source, {
        type = 'success',
        title = 'License Purchased',
        message = string.format('You purchased: %s for $%s', displayName, price),
        duration = 5000
    })

    print(string.format("^3[Olympus Licenses]^7 %s purchased %s for $%s", playerData.name, displayName, price))
end)

-- Revoke license (APD authority)
RegisterServerEvent('olympus-licenses:server:revokeLicense')
AddEventHandler('olympus-licenses:server:revokeLicense', function(targetId, licenseType, reason)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData then return end

    -- Check if player has authority to revoke licenses (APD only)
    if playerData.faction ~= 'apd' then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Access Denied',
            message = 'Only APD can revoke licenses',
            duration = 5000
        })
        return
    end

    -- Get target player
    local targetData = exports['olympus-core']:GetPlayerData(targetId)
    if not targetData then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Player Not Found',
            message = 'Target player not found',
            duration = 5000
        })
        return
    end

    local variable = GetLicenseVariable(licenseType)
    local displayName = GetLicenseDisplayName(licenseType)

    if not variable then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Invalid License',
            message = 'This license type is not available',
            duration = 5000
        })
        return
    end

    -- Check if target has the license
    if not targetData.licenses or not targetData.licenses[variable] then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'License Not Owned',
            message = 'Target player does not own this license',
            duration = 5000
        })
        return
    end

    -- Revoke the license
    targetData.licenses[variable] = false
    exports['olympus-core']:UpdatePlayerData(targetId, 'licenses', targetData.licenses)

    -- Special handling for vigilante license
    if licenseType == 'vigilante' then
        exports['olympus-core']:SetPlayerVariable(targetId, 'isVigi', false)
    end

    -- Log license revocation
    exports['olympus-core']:LogAction(source, 'license_revoke', {
        target = targetData.name,
        targetId = targetData.player_id,
        license = licenseType,
        displayName = displayName,
        reason = reason,
        officer = playerData.name
    })

    -- Notify both players
    TriggerClientEvent('olympus:client:notify', source, {
        type = 'success',
        title = 'License Revoked',
        message = string.format('Revoked %s from %s', displayName, targetData.name),
        duration = 5000
    })

    TriggerClientEvent('olympus:client:notify', targetId, {
        type = 'warning',
        title = 'License Revoked',
        message = string.format('Your %s was revoked by %s. Reason: %s', displayName, playerData.name, reason),
        duration = 10000
    })

    print(string.format("^3[Olympus Licenses]^7 %s revoked %s from %s (Reason: %s)", playerData.name, displayName, targetData.name, reason))
end)

-- Get player licenses
RegisterServerEvent('olympus-licenses:server:requestLicenses')
AddEventHandler('olympus-licenses:server:requestLicenses', function()
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData then return end

    local licenses = {}

    -- Convert license variables to display format
    if playerData.licenses then
        for variable, owned in pairs(playerData.licenses) do
            if owned then
                -- Find license type from variable
                for licenseType, licenseVar in pairs({
                    ['driver'] = 'license_civ_driver',
                    ['boat'] = 'license_civ_boat',
                    ['pilot'] = 'license_civ_air',
                    ['gun'] = 'license_civ_gun',
                    ['wpl'] = 'license_civ_wpl',
                    ['dive'] = 'license_civ_dive',
                    ['oil'] = 'license_civ_oil',
                    ['heroin'] = 'license_civ_heroin',
                    ['marijuana'] = 'license_civ_marijuana',
                    ['medmarijuana'] = 'license_civ_medmarijuana',
                    ['gang'] = 'license_civ_gang',
                    ['rebel'] = 'license_civ_rebel',
                    ['truck'] = 'license_civ_truck',
                    ['diamond'] = 'license_civ_diamond',
                    ['salt'] = 'license_civ_salt',
                    ['cocaine'] = 'license_civ_coke',
                    ['sand'] = 'license_civ_sand',
                    ['iron'] = 'license_civ_iron',
                    ['copper'] = 'license_civ_copper',
                    ['cement'] = 'license_civ_cement',
                    ['home'] = 'license_civ_home',
                    ['frog'] = 'license_civ_frog',
                    ['crystalmeth'] = 'license_civ_crystalmeth',
                    ['methu'] = 'license_civ_methu',
                    ['moonshine'] = 'license_civ_moonshine',
                    ['mashu'] = 'license_civ_mashu',
                    ['platinum'] = 'license_civ_platinum',
                    ['silver'] = 'license_civ_silver',
                    ['vigilante'] = 'license_civ_vigilante',
                    ['mushroom'] = 'license_civ_mushroom'
                }) do
                    if variable == licenseVar then
                        table.insert(licenses, {
                            type = licenseType,
                            displayName = GetLicenseDisplayName(licenseType),
                            variable = variable,
                            owned = true
                        })
                        break
                    end
                end
            end
        end
    end

    TriggerClientEvent('olympus-licenses:client:updateLicenses', source, licenses)
end)

-- Check if player has license
function CheckPlayerLicense(source, licenseType)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData or not playerData.licenses then
        return false
    end

    local variable = GetLicenseVariable(licenseType)
    if not variable then
        return false
    end

    return playerData.licenses[variable] == true
end

-- Get available licenses for purchase
RegisterServerEvent('olympus-licenses:server:requestAvailableLicenses')
AddEventHandler('olympus-licenses:server:requestAvailableLicenses', function(location)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData then return end

    local availableLicenses = {}

    -- Define licenses available at each location
    local locationLicenses = {
        ['dmv'] = {'driver', 'boat', 'pilot', 'truck', 'dive', 'home'},
        ['gun_store'] = {'gun', 'wpl'},
        ['processing'] = {'oil', 'diamond', 'salt', 'cocaine', 'sand', 'iron', 'copper', 'cement', 'frog', 'crystalmeth', 'methu', 'moonshine', 'mashu', 'platinum', 'silver', 'mushroom'},
        ['illegal'] = {'heroin', 'marijuana', 'medmarijuana', 'rebel', 'vigilante'}
    }

    local licenses = locationLicenses[location] or {}

    for _, licenseType in ipairs(licenses) do
        local variable = GetLicenseVariable(licenseType)
        local owned = playerData.licenses and playerData.licenses[variable] == true

        if not owned then
            table.insert(availableLicenses, {
                type = licenseType,
                displayName = GetLicenseDisplayName(licenseType),
                price = GetLicensePrice(licenseType, playerData),
                variable = variable,
                owned = false
            })
        end
    end

    TriggerClientEvent('olympus-licenses:client:updateAvailableLicenses', source, availableLicenses)
end)

-- Initialize system when server starts
CreateThread(function()
    -- Wait for core system
    while not exports['olympus-core'] do
        Wait(100)
    end

    Wait(2000) -- Additional wait for database
    InitializeLicenseSystem()
end)

-- Export functions
exports('GrantLicense', function(source, licenseType)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData then return false end

    local variable = GetLicenseVariable(licenseType)
    if not variable then return false end

    if not playerData.licenses then
        playerData.licenses = {}
    end

    playerData.licenses[variable] = true
    exports['olympus-core']:UpdatePlayerData(source, 'licenses', playerData.licenses)

    print(string.format("^3[Olympus Licenses]^7 Granted %s to %s", GetLicenseDisplayName(licenseType), playerData.name))
    return true
end)

exports('RevokeLicense', function(source, licenseType)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData or not playerData.licenses then return false end

    local variable = GetLicenseVariable(licenseType)
    if not variable then return false end

    playerData.licenses[variable] = false
    exports['olympus-core']:UpdatePlayerData(source, 'licenses', playerData.licenses)

    -- Special handling for vigilante license
    if licenseType == 'vigilante' then
        exports['olympus-core']:SetPlayerVariable(source, 'isVigi', false)
    end

    print(string.format("^3[Olympus Licenses]^7 Revoked %s from %s", GetLicenseDisplayName(licenseType), playerData.name))
    return true
end)

exports('CheckLicense', function(source, licenseType)
    return CheckPlayerLicense(source, licenseType)
end)

exports('GetLicenseInfo', function(licenseType)
    return {
        type = licenseType,
        displayName = GetLicenseDisplayName(licenseType),
        variable = GetLicenseVariable(licenseType),
        price = GetLicensePrice(licenseType, {})
    }
end)

exports('GetPlayerLicenses', function(source)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData or not playerData.licenses then return {} end

    local licenses = {}
    for variable, owned in pairs(playerData.licenses) do
        if owned then
            table.insert(licenses, variable)
        end
    end

    return licenses
end)

-- Export: Check if player has specific license
exports('HasLicense', function(source, licenseType)
    local playerData = exports['olympus-core']:GetPlayerData(source)
    if not playerData or not playerData.licenses then return false end

    return playerData.licenses[licenseType] == true
end)

print("^2[Olympus Licenses]^7 Server module loaded")
