-- ========================================
-- OLYMPUS TERRITORY SYSTEM - SERVER
-- Complete recreation based on original fn_initTerritories.sqf
-- Handles gang territory capture, control, and management
-- ========================================

local OlympusTerritories = {}
OlympusTerritories.ActiveTerritories = {}
OlympusTerritories.SupportedTerritories = {"Meth", "Mushroom", "Moonshine", "Arms"}

-- Territory locations (matches original Olympus positions)
local TERRITORY_LOCATIONS = {
    [1] = {coords = vector3(8923.65, 7478.38, 0), name = "Church"}, -- Church location
    [2] = {coords = vector3(11207.3, 8697.7, 0.293), name = "Castle"}, -- Castle location (Arms)
    [3] = {coords = vector3(14273.0, 13030.6, 0), name = "Mushroom Island"}, -- Mushroom Island Penn
    [4] = {coords = vector3(12077, 10492, 0), name = "OG Heroin"} -- OG heroin location
}

-- Territory configuration
local TERRITORY_CONFIG = {
    captureRadius = 95.0, -- 95 meter capture radius (matches original)
    captureTime = 300, -- 5 minutes to capture (300 seconds)
    updateInterval = 300, -- Update database every 5 minutes (matches original)
    neutralProgress = 0.5, -- 50% = neutral
    minMembersToCapture = 3, -- Minimum gang members needed to capture
    maxDefenders = 8, -- Maximum defenders allowed
    maxAttackers = 8 -- Maximum attackers allowed
}

-- Initialize territory system
function InitializeTerritories()
    print("^2[Olympus Territories]^7 Initializing territory system...")
    
    -- Load territories from database
    LoadTerritoriesFromDatabase()
    
    -- Start territory update loop
    StartTerritoryUpdateLoop()
    
    print("^2[Olympus Territories]^7 Territory system initialized!")
end

-- Load territories from database (matches original query)
function LoadTerritoriesFromDatabase()
    local query = [[
        SELECT territory_name, gang_id, gang_name, capture_progress 
        FROM territories 
        WHERE server = ?
    ]]
    
    exports['olympus-core']:FetchQuery(query, {GetConvar('sv_servername', 'olympus')}, function(result)
        if result then
            local alreadyUsedLocations = {}
            local randomPersistent = OlympusTerritories.SupportedTerritories[math.random(#OlympusTerritories.SupportedTerritories)]
            
            for _, territory in ipairs(result) do
                local territoryName = territory.territory_name
                local gangId = territory.gang_id
                local gangName = territory.gang_name
                local captureProgress = territory.capture_progress / 100 -- Convert to 0-1 range
                
                if IsTerritorySupportedType(territoryName) then
                    local position = GetTerritoryPosition(territoryName, randomPersistent, alreadyUsedLocations)
                    
                    if position then
                        -- Create territory data
                        OlympusTerritories.ActiveTerritories[territoryName] = {
                            name = territoryName,
                            gang_id = gangId,
                            gang_name = gangName,
                            capture_progress = captureProgress,
                            position = position,
                            location_id = GetLocationId(position),
                            capturing_gang = nil,
                            capture_start_time = 0,
                            players_in_zone = {}
                        }
                        
                        -- Create territory blip and marker
                        CreateTerritoryMarkers(territoryName, position, gangName)
                        
                        print(string.format("^3[Olympus Territories]^7 Loaded territory: %s at %s (Gang: %s, Progress: %.1f%%)", 
                            territoryName, position, gangName, captureProgress * 100))
                    end
                end
            end
        end
        
        print(string.format("^2[Olympus Territories]^7 Loaded %d territories", 
            result and #result or 0))
    end)
end

-- Check if territory type is supported
function IsTerritorySupportedType(territoryName)
    for _, supportedType in ipairs(OlympusTerritories.SupportedTerritories) do
        if territoryName == supportedType then
            return true
        end
    end
    return false
end

-- Get territory position (matches original logic)
function GetTerritoryPosition(territoryName, randomPersistent, alreadyUsedLocations)
    local position = nil
    
    if territoryName == "Arms" then
        -- Arms always goes to Castle
        position = TERRITORY_LOCATIONS[2].coords
    elseif territoryName == randomPersistent then
        -- Random persistent territory goes to Church
        position = TERRITORY_LOCATIONS[1].coords
    else
        -- Other territories get random available locations
        local availableLocations = {}
        for i = 3, #TERRITORY_LOCATIONS do
            local loc = TERRITORY_LOCATIONS[i].coords
            local isUsed = false
            for _, usedLoc in ipairs(alreadyUsedLocations) do
                if loc.x == usedLoc.x and loc.y == usedLoc.y then
                    isUsed = true
                    break
                end
            end
            if not isUsed then
                table.insert(availableLocations, loc)
            end
        end
        
        if #availableLocations > 0 then
            position = availableLocations[math.random(#availableLocations)]
            table.insert(alreadyUsedLocations, position)
        end
    end
    
    return position
end

-- Get location ID from position
function GetLocationId(position)
    for id, location in pairs(TERRITORY_LOCATIONS) do
        if location.coords.x == position.x and location.coords.y == position.y then
            return id
        end
    end
    return -1
end

-- Create territory markers and blips
function CreateTerritoryMarkers(territoryName, position, gangName)
    -- This would create map markers in a real implementation
    -- For now, we'll just log the creation
    local markerName = GetTerritoryDisplayName(territoryName)
    print(string.format("^3[Olympus Territories]^7 Created marker for %s (%s) at %s", 
        markerName, gangName, position))
end

-- Get display name for territory
function GetTerritoryDisplayName(territoryName)
    local displayNames = {
        ["Meth"] = "Meth and Weed",
        ["Moonshine"] = "Moonshine and Heroin", 
        ["Mushroom"] = "Mushroom and Cocaine",
        ["Arms"] = "Arms Dealer"
    }
    return displayNames[territoryName] or territoryName
end

-- Start territory update loop (matches original 5-minute interval)
function StartTerritoryUpdateLoop()
    CreateThread(function()
        while true do
            Wait(TERRITORY_CONFIG.updateInterval * 1000) -- Convert to milliseconds
            
            -- Update all territories in database
            for territoryName, territory in pairs(OlympusTerritories.ActiveTerritories) do
                UpdateTerritoryInDatabase(territoryName, territory)
            end
        end
    end)
end

-- Update territory in database (matches original query)
function UpdateTerritoryInDatabase(territoryName, territory)
    local query = [[
        UPDATE territories 
        SET gang_id = ?, gang_name = ?, capture_progress = ? 
        WHERE server = ? AND territory_name = ?
    ]]
    
    local captureProgressPercent = math.floor(territory.capture_progress * 100)
    local serverName = GetConvar('sv_servername', 'olympus')
    
    exports['olympus-core']:ExecuteQuery(query, {
        territory.gang_id,
        territory.gang_name,
        captureProgressPercent,
        serverName,
        territoryName
    }, function(success)
        if success then
            print(string.format("^3[Olympus Territories]^7 Updated territory %s in database", territoryName))
        else
            print(string.format("^1[Olympus Territories]^7 Failed to update territory %s in database", territoryName))
        end
    end)
end

-- Check if player is in territory capture zone
function IsPlayerInTerritoryZone(source, territoryName)
    local territory = OlympusTerritories.ActiveTerritories[territoryName]
    if not territory then return false end
    
    local playerCoords = GetEntityCoords(GetPlayerPed(source))
    local distance = #(playerCoords - territory.position)
    
    return distance <= TERRITORY_CONFIG.captureRadius
end

-- Get territory at position
function GetTerritoryAtPosition(coords)
    for territoryName, territory in pairs(OlympusTerritories.ActiveTerritories) do
        local distance = #(coords - territory.position)
        if distance <= TERRITORY_CONFIG.captureRadius then
            return territoryName, territory
        end
    end
    return nil, nil
end

-- Start territory capture process
function StartTerritoryCapture(territoryName, attackingGangId, attackingGangName)
    local territory = OlympusTerritories.ActiveTerritories[territoryName]
    if not territory then return false end

    -- Check if territory is already being captured
    if territory.capturing_gang then
        return false, "Territory is already being captured"
    end

    -- Check if attacking gang already owns this territory
    if territory.gang_id == attackingGangId then
        return false, "Your gang already controls this territory"
    end

    -- Start capture process
    territory.capturing_gang = attackingGangId
    territory.capture_start_time = GetGameTimer()

    -- Notify all players in the area
    NotifyPlayersInTerritory(territoryName, string.format("%s is attempting to capture %s!",
        attackingGangName, GetTerritoryDisplayName(territoryName)))

    -- Start capture progress loop
    CreateThread(function()
        ProcessTerritoryCapture(territoryName)
    end)

    print(string.format("^3[Olympus Territories]^7 %s started capturing %s", attackingGangName, territoryName))
    return true
end

-- Process territory capture (continuous loop during capture)
function ProcessTerritoryCapture(territoryName)
    local territory = OlympusTerritories.ActiveTerritories[territoryName]
    if not territory then return end

    while territory.capturing_gang do
        Wait(1000) -- Check every second

        -- Count players in territory by gang
        local playersInZone = GetPlayersInTerritoryZone(territoryName)
        local attackers = 0
        local defenders = 0
        local attackingGangId = territory.capturing_gang
        local defendingGangId = territory.gang_id

        for _, playerId in ipairs(playersInZone) do
            local playerData = exports['olympus-core']:GetPlayerData(playerId)
            if playerData and playerData.gang_id then
                if playerData.gang_id == attackingGangId then
                    attackers = attackers + 1
                elseif playerData.gang_id == defendingGangId then
                    defenders = defenders + 1
                end
            end
        end

        -- Calculate capture progress
        local progressChange = 0
        if attackers > defenders and attackers >= TERRITORY_CONFIG.minMembersToCapture then
            -- Attackers are winning
            progressChange = (attackers - defenders) * 0.01 -- 1% per second per player advantage
        elseif defenders > attackers then
            -- Defenders are winning
            progressChange = -(defenders - attackers) * 0.01 -- Negative progress
        end

        -- Apply progress change
        if progressChange ~= 0 then
            territory.capture_progress = math.max(0, math.min(1, territory.capture_progress + progressChange))

            -- Check for capture completion
            if territory.capture_progress >= 1.0 then
                -- Territory captured!
                CompleteTerritoryCapture(territoryName, attackingGangId)
                break
            elseif territory.capture_progress <= 0.0 then
                -- Capture failed
                FailTerritoryCapture(territoryName)
                break
            end
        end

        -- Check for timeout or no players
        if #playersInZone == 0 then
            -- No players in zone, pause capture
            Wait(30000) -- Wait 30 seconds before canceling
            if #GetPlayersInTerritoryZone(territoryName) == 0 then
                CancelTerritoryCapture(territoryName)
                break
            end
        end
    end
end

-- Complete territory capture
function CompleteTerritoryCapture(territoryName, newGangId)
    local territory = OlympusTerritories.ActiveTerritories[territoryName]
    if not territory then return end

    -- Get new gang name
    exports['olympus-core']:GetGangData(newGangId, function(gangData)
        if gangData then
            local oldGangName = territory.gang_name

            -- Update territory ownership
            territory.gang_id = newGangId
            territory.gang_name = gangData.name
            territory.capture_progress = 1.0
            territory.capturing_gang = nil
            territory.capture_start_time = 0

            -- Update markers
            CreateTerritoryMarkers(territoryName, territory.position, gangData.name)

            -- Notify all players
            TriggerClientEvent('olympus:client:notify', -1, {
                type = 'success',
                title = 'Territory Captured',
                message = string.format('%s has captured %s from %s!',
                    gangData.name, GetTerritoryDisplayName(territoryName), oldGangName)
            })

            -- Update database immediately
            UpdateTerritoryInDatabase(territoryName, territory)

            print(string.format("^2[Olympus Territories]^7 %s captured %s from %s",
                gangData.name, territoryName, oldGangName))
        end
    end)
end

-- Fail territory capture
function FailTerritoryCapture(territoryName)
    local territory = OlympusTerritories.ActiveTerritories[territoryName]
    if not territory then return end

    territory.capture_progress = 0.0
    territory.capturing_gang = nil
    territory.capture_start_time = 0

    NotifyPlayersInTerritory(territoryName, string.format("Capture of %s has failed!",
        GetTerritoryDisplayName(territoryName)))

    print(string.format("^3[Olympus Territories]^7 Capture of %s failed", territoryName))
end

-- Cancel territory capture
function CancelTerritoryCapture(territoryName)
    local territory = OlympusTerritories.ActiveTerritories[territoryName]
    if not territory then return end

    territory.capturing_gang = nil
    territory.capture_start_time = 0
    -- Keep current progress

    NotifyPlayersInTerritory(territoryName, string.format("Capture of %s has been canceled!",
        GetTerritoryDisplayName(territoryName)))

    print(string.format("^3[Olympus Territories]^7 Capture of %s canceled", territoryName))
end

-- Get players in territory zone
function GetPlayersInTerritoryZone(territoryName)
    local territory = OlympusTerritories.ActiveTerritories[territoryName]
    if not territory then return {} end

    local playersInZone = {}
    for _, playerId in ipairs(GetPlayers()) do
        if IsPlayerInTerritoryZone(tonumber(playerId), territoryName) then
            table.insert(playersInZone, tonumber(playerId))
        end
    end

    return playersInZone
end

-- Notify players in territory
function NotifyPlayersInTerritory(territoryName, message)
    local playersInZone = GetPlayersInTerritoryZone(territoryName)
    for _, playerId in ipairs(playersInZone) do
        TriggerClientEvent('olympus:client:notify', playerId, {
            type = 'info',
            title = 'Territory Update',
            message = message
        })
    end
end

-- Server events for territory system
RegisterServerEvent('olympus:server:startTerritoryCapture')
AddEventHandler('olympus:server:startTerritoryCapture', function(territoryName)
    local source = source
    local playerData = exports['olympus-core']:GetPlayerData(source)

    if not playerData or not playerData.gang_id or playerData.gang_id <= 0 then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Territory Capture',
            message = 'You must be in a gang to capture territories'
        })
        return
    end

    -- Check if player has permission (rank 3+)
    if playerData.gang_rank < 3 then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Territory Capture',
            message = 'You need rank 3+ to start territory captures'
        })
        return
    end

    -- Check if player is in territory zone
    if not IsPlayerInTerritoryZone(source, territoryName) then
        TriggerClientEvent('olympus:client:notify', source, {
            type = 'error',
            title = 'Territory Capture',
            message = 'You must be in the territory to start capture'
        })
        return
    end

    -- Get gang name
    exports['olympus-core']:GetGangData(playerData.gang_id, function(gangData)
        if gangData then
            local success, errorMsg = StartTerritoryCapture(territoryName, playerData.gang_id, gangData.name)
            if success then
                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'success',
                    title = 'Territory Capture',
                    message = string.format('Started capturing %s', GetTerritoryDisplayName(territoryName))
                })
            else
                TriggerClientEvent('olympus:client:notify', source, {
                    type = 'error',
                    title = 'Territory Capture',
                    message = errorMsg or 'Failed to start capture'
                })
            end
        end
    end)
end)

-- Exports
exports('InitializeTerritories', InitializeTerritories)
exports('GetTerritoryAtPosition', GetTerritoryAtPosition)
exports('IsPlayerInTerritoryZone', IsPlayerInTerritoryZone)
exports('StartTerritoryCapture', StartTerritoryCapture)
exports('GetActiveTerritories', function()
    return OlympusTerritories.ActiveTerritories
end)

-- Initialize when loaded
CreateThread(function()
    -- Wait for core to be ready
    while not exports['olympus-core']:IsDBReady() do
        Wait(100)
    end

    Wait(5000) -- Additional delay for gang system to load
    InitializeTerritories()
end)

print("^2[Olympus Territories]^7 Territory system loaded!")
